{"ast": null, "code": "import { createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  id: \"app\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_router_link = _resolveComponent(\"router-link\");\n  const _component_router_view = _resolveComponent(\"router-view\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"nav\", null, [_createVNode(_component_router_link, {\n    to: \"/\"\n  }, {\n    default: _withCtx(() => _cache[0] || (_cache[0] = [_createTextVNode(\"首页\")])),\n    _: 1 /* STABLE */,\n    __: [0]\n  }), _cache[9] || (_cache[9] = _createTextVNode(\" | \")), _createVNode(_component_router_link, {\n    to: \"/club-culture\"\n  }, {\n    default: _withCtx(() => _cache[1] || (_cache[1] = [_createTextVNode(\"社团文化\")])),\n    _: 1 /* STABLE */,\n    __: [1]\n  }), _cache[10] || (_cache[10] = _createTextVNode(\" | \")), _createVNode(_component_router_link, {\n    to: \"/learning-resources\"\n  }, {\n    default: _withCtx(() => _cache[2] || (_cache[2] = [_createTextVNode(\"学习资源\")])),\n    _: 1 /* STABLE */,\n    __: [2]\n  }), _cache[11] || (_cache[11] = _createTextVNode(\" | \")), _createVNode(_component_router_link, {\n    to: \"/past-activities\"\n  }, {\n    default: _withCtx(() => _cache[3] || (_cache[3] = [_createTextVNode(\"往期活动回顾\")])),\n    _: 1 /* STABLE */,\n    __: [3]\n  }), _cache[12] || (_cache[12] = _createTextVNode(\" | \")), _createVNode(_component_router_link, {\n    to: \"/cyber-security-news\"\n  }, {\n    default: _withCtx(() => _cache[4] || (_cache[4] = [_createTextVNode(\"网安资讯\")])),\n    _: 1 /* STABLE */,\n    __: [4]\n  }), _cache[13] || (_cache[13] = _createTextVNode(\" | \")), _createVNode(_component_router_link, {\n    to: \"/login\"\n  }, {\n    default: _withCtx(() => _cache[5] || (_cache[5] = [_createTextVNode(\"登录\")])),\n    _: 1 /* STABLE */,\n    __: [5]\n  }), _cache[14] || (_cache[14] = _createTextVNode(\" | \")), _createVNode(_component_router_link, {\n    to: \"/admin\"\n  }, {\n    default: _withCtx(() => _cache[6] || (_cache[6] = [_createTextVNode(\"管理员后台\")])),\n    _: 1 /* STABLE */,\n    __: [6]\n  }), _cache[15] || (_cache[15] = _createTextVNode(\" | \")), _createVNode(_component_router_link, {\n    to: \"/playground\"\n  }, {\n    default: _withCtx(() => _cache[7] || (_cache[7] = [_createTextVNode(\"实操靶场\")])),\n    _: 1 /* STABLE */,\n    __: [7]\n  }), _cache[16] || (_cache[16] = _createTextVNode(\" | \")), _createVNode(_component_router_link, {\n    to: \"/dashboard\"\n  }, {\n    default: _withCtx(() => _cache[8] || (_cache[8] = [_createTextVNode(\"监控数据大屏\")])),\n    _: 1 /* STABLE */,\n    __: [8]\n  })]), _createVNode(_component_router_view)]);\n}", "map": {"version": 3, "names": ["id", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_createVNode", "_component_router_link", "to", "_cache", "_component_router_view"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\App.vue"], "sourcesContent": ["<template>\n  <div id=\"app\">\n    <nav>\n      <router-link to=\"/\">首页</router-link> |\n      <router-link to=\"/club-culture\">社团文化</router-link> |\n      <router-link to=\"/learning-resources\">学习资源</router-link> |\n      <router-link to=\"/past-activities\">往期活动回顾</router-link> |\n      <router-link to=\"/cyber-security-news\">网安资讯</router-link> |\n      <router-link to=\"/login\">登录</router-link> |\n      <router-link to=\"/admin\">管理员后台</router-link> |\n      <router-link to=\"/playground\">实操靶场</router-link> |\n      <router-link to=\"/dashboard\">监控数据大屏</router-link>\n    </nav>\n    <router-view/>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'App',\n}\n</script>\n\n<style>\n#app {\n  font-family: Avenir, Helvetica, Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  text-align: center;\n  color: #2c3e50;\n}\n\nnav {\n  padding: 30px;\n}\n\nnav a {\n  font-weight: bold;\n  color: #2c3e50;\n}\n\nnav a.router-link-exact-active {\n  color: #42b983;\n}\n</style>\n"], "mappings": ";;EACOA,EAAE,EAAC;AAAK;;;;uBAAbC,mBAAA,CAaM,OAbNC,UAaM,GAZJC,mBAAA,CAUM,cATJC,YAAA,CAAoCC,sBAAA;IAAvBC,EAAE,EAAC;EAAG;sBAAC,MAAEC,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;iDAAc,KACpC,IAAAH,YAAA,CAAkDC,sBAAA;IAArCC,EAAE,EAAC;EAAe;sBAAC,MAAIC,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;mDAAc,KAClD,IAAAH,YAAA,CAAwDC,sBAAA;IAA3CC,EAAE,EAAC;EAAqB;sBAAC,MAAIC,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;mDAAc,KACxD,IAAAH,YAAA,CAAuDC,sBAAA;IAA1CC,EAAE,EAAC;EAAkB;sBAAC,MAAMC,MAAA,QAAAA,MAAA,O,iBAAN,QAAM,E;;;mDAAc,KACvD,IAAAH,YAAA,CAAyDC,sBAAA;IAA5CC,EAAE,EAAC;EAAsB;sBAAC,MAAIC,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;mDAAc,KACzD,IAAAH,YAAA,CAAyCC,sBAAA;IAA5BC,EAAE,EAAC;EAAQ;sBAAC,MAAEC,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;mDAAc,KACzC,IAAAH,YAAA,CAA4CC,sBAAA;IAA/BC,EAAE,EAAC;EAAQ;sBAAC,MAAKC,MAAA,QAAAA,MAAA,O,iBAAL,OAAK,E;;;mDAAc,KAC5C,IAAAH,YAAA,CAAgDC,sBAAA;IAAnCC,EAAE,EAAC;EAAa;sBAAC,MAAIC,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;mDAAc,KAChD,IAAAH,YAAA,CAAiDC,sBAAA;IAApCC,EAAE,EAAC;EAAY;sBAAC,MAAMC,MAAA,QAAAA,MAAA,O,iBAAN,QAAM,E;;;QAErCH,YAAA,CAAcI,sBAAA,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}