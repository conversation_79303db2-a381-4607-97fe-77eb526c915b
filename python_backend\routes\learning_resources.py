import os
from flask import Blueprint, request, jsonify, send_from_directory, current_app
from models import db, LearningResource
from middleware.auth import token_required, authorize_roles
from utils.file_upload import upload_file

learning_resources_bp = Blueprint('learning_resources', __name__)

@learning_resources_bp.route('/', methods=['GET'])
def get_learning_resources():
    try:
        resources = LearningResource.query.order_by(LearningResource.created_at.desc()).all()
        return jsonify([{
            'id': r.id,
            'title': r.title,
            'content': r.content,
            'filePath': r.file_path,
            'createdAt': r.created_at.isoformat(),
            'updatedAt': r.updated_at.isoformat()
        } for r in resources]), 200
    except Exception as e:
        current_app.logger.error(f"获取学习资源失败: {e}")
        return jsonify({'msg': '服务器错误，获取学习资源失败'}), 500

@learning_resources_bp.route('/', methods=['POST'])
@token_required
@authorize_roles(['admin'])
def create_learning_resource():
    title = request.form.get('title')
    content = request.form.get('content')
    
    file_url = None
    if 'file' in request.files:
        file = request.files['file']
        if file.filename != '':
            file_url, error = upload_file(file, 'learning_resources', ['pdf', 'doc', 'docx', 'txt', 'zip', 'rar'], 20) # 20MB limit
            if error:
                return jsonify({'msg': f'文件上传失败: {error}'}), 400

    if not title:
        return jsonify({'msg': '标题是必填项'}), 400

    try:
        new_resource = LearningResource(title=title, content=content, file_path=file_url)
        db.session.add(new_resource)
        db.session.commit()
        return jsonify({'msg': '学习资源创建成功', 'resource': {
            'id': new_resource.id,
            'title': new_resource.title,
            'content': new_resource.content,
            'filePath': new_resource.file_path,
            'createdAt': new_resource.created_at.isoformat()
        }}), 201
    except Exception as e:
        current_app.logger.error(f"创建学习资源失败: {e}")
        return jsonify({'msg': '服务器错误，创建学习资源失败'}), 500

@learning_resources_bp.route('/<int:resource_id>', methods=['PUT'])
@token_required
@authorize_roles(['admin'])
def update_learning_resource(resource_id):
    title = request.form.get('title')
    content = request.form.get('content')
    
    try:
        resource = LearningResource.query.get(resource_id)
        if not resource:
            return jsonify({'msg': '学习资源未找到'}), 404
        
        # 处理文件更新
        file_url = resource.file_path
        if 'file' in request.files:
            file = request.files['file']
            if file.filename != '':
                # 删除旧文件
                if resource.file_path:
                    old_filename = os.path.basename(resource.file_path)
                    old_file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'learning_resources', old_filename)
                    if os.path.exists(old_file_path):
                        os.remove(old_file_path)
                
                new_file_url, error = upload_file(file, 'learning_resources', ['pdf', 'doc', 'docx', 'txt', 'zip', 'rar'], 20)
                if error:
                    return jsonify({'msg': f'文件更新失败: {error}'}), 400
                file_url = new_file_url
            else: # 如果前端发送了空文件，表示删除文件
                if resource.file_path:
                    old_filename = os.path.basename(resource.file_path)
                    old_file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'learning_resources', old_filename)
                    if os.path.exists(old_file_path):
                        os.remove(old_file_path)
                file_url = None

        resource.title = title if title is not None else resource.title
        resource.content = content if content is not None else resource.content
        resource.file_path = file_url
        db.session.commit()
        return jsonify({'msg': '学习资源更新成功', 'resource': {
            'id': resource.id,
            'title': resource.title,
            'content': resource.content,
            'filePath': resource.file_path,
            'updatedAt': resource.updated_at.isoformat()
        }}), 200
    except Exception as e:
        current_app.logger.error(f"更新学习资源失败: {e}")
        return jsonify({'msg': '服务器错误，更新学习资源失败'}), 500

@learning_resources_bp.route('/<int:resource_id>', methods=['DELETE'])
@token_required
@authorize_roles(['admin'])
def delete_learning_resource(resource_id):
    try:
        resource = LearningResource.query.get(resource_id)
        if not resource:
            return jsonify({'msg': '学习资源未找到'}), 404
        
        # 删除关联文件
        if resource.file_path:
            filename = os.path.basename(resource.file_path)
            file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'learning_resources', filename)
            if os.path.exists(file_path):
                os.remove(file_path)
            else:
                current_app.logger.warning(f"尝试删除不存在的文件: {file_path}")

        db.session.delete(resource)
        db.session.commit()
        return jsonify({'msg': '学习资源已删除'}), 200
    except Exception as e:
        current_app.logger.error(f"删除学习资源失败: {e}")
        return jsonify({'msg': '服务器错误，删除学习资源失败'}), 500

@learning_resources_bp.route('/download/<path:filename>', methods=['GET'])
def download_learning_resource(filename):
    try:
        # 确保文件在允许的下载目录内
        return send_from_directory(os.path.join(current_app.config['UPLOAD_FOLDER'], 'learning_resources'), filename, as_attachment=True)
    except Exception as e:
        current_app.logger.error(f"下载文件失败: {e}")
        return jsonify({'msg': '文件下载失败或文件不存在'}), 404
