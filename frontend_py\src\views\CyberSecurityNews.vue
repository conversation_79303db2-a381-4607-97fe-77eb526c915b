<template>
  <div class="cyber-security-news">
    <h1>网安资讯</h1>
    <p>这里发布最新的网络安全行业新闻、技术动态、漏洞信息等。</p>

    <div v-if="loading" class="loading">
      <p>加载中...</p>
    </div>

    <div v-else-if="error" class="error">
      <p>{{ error }}</p>
      <button @click="loadNews" class="btn btn-primary">重试</button>
    </div>

    <div v-else class="news-list">
      <div v-if="newsList.length === 0" class="no-data">
        <p>暂无网安资讯</p>
      </div>
      <div v-else>
        <div v-for="newsItem in newsList" :key="newsItem.id" class="news-item">
          <h3>{{ newsItem.title }}</h3>
          <div class="news-content">
            <div v-html="purifyContent(newsItem.content)"></div>
          </div>
          <div class="news-meta">
            <div v-if="newsItem.source_url" class="source-link">
              <a :href="newsItem.source_url" target="_blank" rel="noopener noreferrer">查看原文</a>
            </div>
            <div class="dates">
              <span v-if="newsItem.published_date">发布时间: {{ formatDate(newsItem.published_date) }}</span>
              <span>创建时间: {{ formatDate(newsItem.created_at) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DOMPurify from 'dompurify'
import { cyberSecurityNewsAPI } from '../services/api'

export default {
  name: 'CyberSecurityNews',
  data() {
    return {
      newsList: [],
      loading: false,
      error: null
    }
  },
  async mounted() {
    await this.loadNews()
  },
  methods: {
    async loadNews() {
      this.loading = true
      this.error = null
      try {
        const response = await cyberSecurityNewsAPI.getCyberSecurityNews()
        this.newsList = response.data
      } catch (error) {
        console.error('加载网安资讯失败:', error)
        this.error = '加载网安资讯失败，请稍后重试'
      } finally {
        this.loading = false
      }
    },

    purifyContent(content) {
      return DOMPurify.sanitize(content)
    },

    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    }
  }
}
</script>

<style scoped>
.cyber-security-news {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.loading, .error, .no-data {
  text-align: center;
  padding: 40px;
  color: #666;
}

.error {
  color: #e74c3c;
}

.news-list {
  margin-top: 20px;
}

.news-item {
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.news-item h3 {
  color: #2c3e50;
  margin-bottom: 15px;
  border-bottom: 2px solid #f39c12;
  padding-bottom: 10px;
}

.news-content {
  margin: 15px 0;
  line-height: 1.6;
}

.news-meta {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

.source-link a {
  color: #f39c12;
  text-decoration: none;
  font-weight: bold;
}

.source-link a:hover {
  text-decoration: underline;
}

.dates {
  color: #666;
  font-size: 14px;
}

.dates span {
  margin-left: 15px;
}

.dates span:first-child {
  margin-left: 0;
}

.btn {
  display: inline-block;
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  text-decoration: none;
  cursor: pointer;
  font-size: 14px;
  background-color: #f39c12;
  color: white;
}

.btn:hover {
  background-color: #e67e22;
}
</style>