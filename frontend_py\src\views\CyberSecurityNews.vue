<template>
  <div class="cyber-security-news">
    <h1>网安资讯</h1>
    <p>这里发布最新的网络安全行业新闻、技术动态、漏洞信息等。</p>
    <div class="news-list">
      <div v-for="newsItem in newsList" :key="newsItem.id" class="news-item">
        <h3>{{ newsItem.title }}</h3>
        <div v-html="purifyContent(newsItem.content)"></div>
      </div>
    </div>
  </div>
</template>

<script>
import DOMPurify from 'dompurify'; // 引入 DOMPurify

export default {
  name: 'CyberSecurityNews',
  data() {
    return {
      // 示例：网安资讯列表，实际应从后端获取
      newsList: [
        { id: 1, title: '最新漏洞预警', content: '<p>关于最新发现的**安全漏洞**的预警信息。</p><img src="invalid.png" onerror="alert(\'XSS in news!\')">' },
        { id: 2, title: '网络安全行业趋势', content: '<p>分析当前网络安全行业的发展趋势。</p>' },
      ],
    };
  },
  methods: {
    purifyContent(content) {
      return DOMPurify.sanitize(content);
    },
  },
}
</script>

<style scoped>
.cyber-security-news {
  padding: 20px;
}
</style>