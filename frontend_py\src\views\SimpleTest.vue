<template>
  <div class="simple-test">
    <h1>简单API测试</h1>
    
    <div class="test-section">
      <h2>测试后端连接</h2>
      <button @click="testBackend">测试后端</button>
      <div v-if="backendResult" class="result">
        <pre>{{ backendResult }}</pre>
      </div>
    </div>
    
    <div class="test-section">
      <h2>测试登录API</h2>
      <button @click="testLogin">测试登录</button>
      <div v-if="loginResult" class="result">
        <pre>{{ loginResult }}</pre>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SimpleTest',
  data() {
    return {
      backendResult: null,
      loginResult: null
    }
  },
  methods: {
    async testBackend() {
      try {
        const response = await fetch('http://localhost:5000/')
        const data = await response.json()
        this.backendResult = JSON.stringify(data, null, 2)
      } catch (error) {
        this.backendResult = `错误: ${error.message}`
      }
    },
    
    async testLogin() {
      try {
        const response = await fetch('http://localhost:5000/api/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({
            username: 'admin',
            password: 'Admin123!'
          })
        })
        
        const data = await response.json()
        this.loginResult = `状态: ${response.status}\n数据: ${JSON.stringify(data, null, 2)}`
      } catch (error) {
        this.loginResult = `错误: ${error.message}`
      }
    }
  }
}
</script>

<style scoped>
.simple-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

button {
  background-color: #42b983;
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background-color: #369f6e;
}

.result {
  margin-top: 15px;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
  border: 1px solid #ddd;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
