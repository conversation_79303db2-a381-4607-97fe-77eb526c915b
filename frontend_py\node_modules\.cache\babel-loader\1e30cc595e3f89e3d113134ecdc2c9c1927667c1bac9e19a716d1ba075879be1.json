{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { authAPI } from '../services/api';\nexport default {\n  name: 'Navigation',\n  data() {\n    return {\n      currentUser: null,\n      menuOpen: false\n    };\n  },\n  async mounted() {\n    await this.checkAuth();\n  },\n  methods: {\n    async checkAuth() {\n      try {\n        const response = await authAPI.checkAuth();\n        this.currentUser = response.data.user;\n      } catch (error) {\n        this.currentUser = null;\n      }\n    },\n    async logout() {\n      try {\n        await authAPI.logout();\n        this.currentUser = null;\n        this.$router.push('/');\n      } catch (error) {\n        console.error('登出失败:', error);\n      }\n    }\n  },\n  watch: {\n    $route() {\n      this.menuOpen = false;\n      this.checkAuth();\n    }\n  }\n};", "map": {"version": 3, "names": ["authAPI", "name", "data", "currentUser", "menuOpen", "mounted", "checkAuth", "methods", "response", "user", "error", "logout", "$router", "push", "console", "watch", "$route"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\components\\Navigation.vue"], "sourcesContent": ["<template>\n  <nav class=\"navigation\">\n    <div class=\"nav-container\">\n      <div class=\"nav-brand\">\n        <router-link to=\"/\" class=\"brand-link\">\n          <img src=\"@/assets/logo.png\" alt=\"NIS\" class=\"brand-logo\">\n          <span class=\"brand-text\">NIS 社团</span>\n        </router-link>\n      </div>\n      \n      <div class=\"nav-menu\" :class=\"{ active: menuOpen }\">\n        <router-link to=\"/\" class=\"nav-link\">首页</router-link>\n        <router-link to=\"/club-culture\" class=\"nav-link\">社团文化</router-link>\n        <router-link to=\"/learning-resources\" class=\"nav-link\">学习资源</router-link>\n        <router-link to=\"/past-activities\" class=\"nav-link\">过往活动</router-link>\n        <router-link to=\"/cyber-security-news\" class=\"nav-link\">安全资讯</router-link>\n        \n        <div v-if=\"currentUser\" class=\"nav-user\">\n          <router-link to=\"/dashboard\" class=\"nav-link\">仪表板</router-link>\n          <router-link to=\"/playground\" class=\"nav-link\">练习场</router-link>\n          <router-link v-if=\"currentUser.role === 'admin'\" to=\"/admin\" class=\"nav-link admin-link\">管理后台</router-link>\n          <button @click=\"logout\" class=\"nav-link logout-btn\">退出</button>\n        </div>\n        <div v-else class=\"nav-auth\">\n          <router-link to=\"/login\" class=\"nav-link\">登录</router-link>\n          <router-link to=\"/register\" class=\"nav-link register-btn\">注册</router-link>\n        </div>\n      </div>\n      \n      <button class=\"nav-toggle\" @click=\"menuOpen = !menuOpen\">\n        <span></span>\n        <span></span>\n        <span></span>\n      </button>\n    </div>\n  </nav>\n</template>\n\n<script>\nimport { authAPI } from '../services/api'\n\nexport default {\n  name: 'Navigation',\n  data() {\n    return {\n      currentUser: null,\n      menuOpen: false\n    }\n  },\n  async mounted() {\n    await this.checkAuth()\n  },\n  methods: {\n    async checkAuth() {\n      try {\n        const response = await authAPI.checkAuth()\n        this.currentUser = response.data.user\n      } catch (error) {\n        this.currentUser = null\n      }\n    },\n    \n    async logout() {\n      try {\n        await authAPI.logout()\n        this.currentUser = null\n        this.$router.push('/')\n      } catch (error) {\n        console.error('登出失败:', error)\n      }\n    }\n  },\n  watch: {\n    $route() {\n      this.menuOpen = false\n      this.checkAuth()\n    }\n  }\n}\n</script>\n\n<style scoped>\n.navigation {\n  background: white;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  position: sticky;\n  top: 0;\n  z-index: 1000;\n}\n\n.nav-container {\n  max-width: 1200px;\n  margin: 0 auto;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0 20px;\n  height: 70px;\n}\n\n.nav-brand {\n  display: flex;\n  align-items: center;\n}\n\n.brand-link {\n  display: flex;\n  align-items: center;\n  text-decoration: none;\n  color: #333;\n}\n\n.brand-logo {\n  width: 40px;\n  height: 40px;\n  margin-right: 10px;\n}\n\n.brand-text {\n  font-size: 1.5rem;\n  font-weight: bold;\n  color: #42b983;\n}\n\n.nav-menu {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n}\n\n.nav-link {\n  text-decoration: none;\n  color: #333;\n  font-weight: 500;\n  padding: 8px 16px;\n  border-radius: 4px;\n  transition: all 0.3s ease;\n  border: none;\n  background: none;\n  cursor: pointer;\n  font-size: 14px;\n}\n\n.nav-link:hover {\n  color: #42b983;\n  background-color: #f8f9fa;\n}\n\n.nav-link.router-link-active {\n  color: #42b983;\n  background-color: #e8f5e8;\n}\n\n.admin-link {\n  background-color: #ff6b6b !important;\n  color: white !important;\n}\n\n.admin-link:hover {\n  background-color: #ee5a24 !important;\n}\n\n.register-btn {\n  background-color: #42b983;\n  color: white;\n}\n\n.register-btn:hover {\n  background-color: #369f6e;\n}\n\n.logout-btn {\n  background-color: #6c757d;\n  color: white;\n}\n\n.logout-btn:hover {\n  background-color: #545b62;\n}\n\n.nav-user, .nav-auth {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.nav-toggle {\n  display: none;\n  flex-direction: column;\n  background: none;\n  border: none;\n  cursor: pointer;\n  padding: 5px;\n}\n\n.nav-toggle span {\n  width: 25px;\n  height: 3px;\n  background-color: #333;\n  margin: 3px 0;\n  transition: 0.3s;\n}\n\n@media (max-width: 768px) {\n  .nav-menu {\n    position: fixed;\n    top: 70px;\n    left: -100%;\n    width: 100%;\n    height: calc(100vh - 70px);\n    background: white;\n    flex-direction: column;\n    justify-content: flex-start;\n    align-items: center;\n    padding-top: 50px;\n    transition: left 0.3s ease;\n    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  }\n  \n  .nav-menu.active {\n    left: 0;\n  }\n  \n  .nav-link {\n    padding: 15px 30px;\n    width: 200px;\n    text-align: center;\n    margin-bottom: 10px;\n  }\n  \n  .nav-toggle {\n    display: flex;\n  }\n  \n  .nav-user, .nav-auth {\n    flex-direction: column;\n    gap: 10px;\n    margin-top: 20px;\n  }\n}\n</style>\n"], "mappings": ";AAuCA,SAASA,OAAM,QAAS,iBAAgB;AAExC,eAAe;EACbC,IAAI,EAAE,YAAY;EAClBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,WAAW,EAAE,IAAI;MACjBC,QAAQ,EAAE;IACZ;EACF,CAAC;EACD,MAAMC,OAAOA,CAAA,EAAG;IACd,MAAM,IAAI,CAACC,SAAS,CAAC;EACvB,CAAC;EACDC,OAAO,EAAE;IACP,MAAMD,SAASA,CAAA,EAAG;MAChB,IAAI;QACF,MAAME,QAAO,GAAI,MAAMR,OAAO,CAACM,SAAS,CAAC;QACzC,IAAI,CAACH,WAAU,GAAIK,QAAQ,CAACN,IAAI,CAACO,IAAG;MACtC,EAAE,OAAOC,KAAK,EAAE;QACd,IAAI,CAACP,WAAU,GAAI,IAAG;MACxB;IACF,CAAC;IAED,MAAMQ,MAAMA,CAAA,EAAG;MACb,IAAI;QACF,MAAMX,OAAO,CAACW,MAAM,CAAC;QACrB,IAAI,CAACR,WAAU,GAAI,IAAG;QACtB,IAAI,CAACS,OAAO,CAACC,IAAI,CAAC,GAAG;MACvB,EAAE,OAAOH,KAAK,EAAE;QACdI,OAAO,CAACJ,KAAK,CAAC,OAAO,EAAEA,KAAK;MAC9B;IACF;EACF,CAAC;EACDK,KAAK,EAAE;IACLC,MAAMA,CAAA,EAAG;MACP,IAAI,CAACZ,QAAO,GAAI,KAAI;MACpB,IAAI,CAACE,SAAS,CAAC;IACjB;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}