{"ast": null, "code": "import DOMPurify from 'dompurify'; // 引入 DOMPurify\n\nexport default {\n  name: 'Home',\n  data() {\n    return {\n      // 示例公告内容，实际应从后端获取\n      announcementContent: '<p>欢迎参加**网络信息安全社团**的招新活动！</p><script>alert(\"XSS Attack!\");<\\/script>'\n    };\n  },\n  computed: {\n    purifiedAnnouncementContent() {\n      // 使用 DOMPurify 清理公告内容\n      return DOMPurify.sanitize(this.announcementContent);\n    }\n  }\n};", "map": {"version": 3, "names": ["DOMPurify", "name", "data", "announcementContent", "computed", "purifiedAnnouncementContent", "sanitize"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Home.vue"], "sourcesContent": ["<template>\r\n  <div class=\"home\">\r\n    <h1>NIS 社团文化网站 - 首页</h1>\r\n    <p>欢迎来到网络信息安全社团！</p>\r\n    <h2>社团招新公告</h2>\r\n    <div class=\"announcement-content\" v-html=\"purifiedAnnouncementContent\"></div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport DOMPurify from 'dompurify'; // 引入 DOMPurify\r\n\r\nexport default {\r\n  name: 'Home',\r\n  data() {\r\n    return {\r\n      // 示例公告内容，实际应从后端获取\r\n      announcementContent: '<p>欢迎参加**网络信息安全社团**的招新活动！</p><script>alert(\"XSS Attack!\");<\\/script>',\r\n    };\r\n  },\r\n  computed: {\r\n    purifiedAnnouncementContent() {\r\n      // 使用 DOMPurify 清理公告内容\r\n      return DOMPurify.sanitize(this.announcementContent);\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.home {\r\n  padding: 20px;\r\n}\r\n</style>"], "mappings": "AAUA,OAAOA,SAAQ,MAAO,WAAW,EAAE;;AAEnC,eAAe;EACbC,IAAI,EAAE,MAAM;EACZC,IAAIA,CAAA,EAAG;IACL,OAAO;MACL;MACAC,mBAAmB,EAAE;IACvB,CAAC;EACH,CAAC;EACDC,QAAQ,EAAE;IACRC,2BAA2BA,CAAA,EAAG;MAC5B;MACA,OAAOL,SAAS,CAACM,QAAQ,CAAC,IAAI,CAACH,mBAAmB,CAAC;IACrD;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}