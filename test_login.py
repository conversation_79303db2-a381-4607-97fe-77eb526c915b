import requests
import json

try:
    # 测试登录API
    url = "http://localhost:5000/api/auth/login"
    data = {
        "username": "admin",
        "password": "Admin123!"
    }
    
    print("正在测试登录API...")
    print(f"URL: {url}")
    print(f"数据: {data}")
    
    response = requests.post(url, json=data)
    
    print(f"状态码: {response.status_code}")
    print(f"响应头: {dict(response.headers)}")
    print(f"响应内容: {response.text}")
    
    if response.status_code == 200:
        print("✅ 登录成功!")
    else:
        print("❌ 登录失败!")
        
except Exception as e:
    print(f"❌ 请求异常: {e}")
