{"ast": null, "code": "import DOMPurify from 'dompurify';\nimport { learningResourceAPI } from '../services/api';\nexport default {\n  name: 'LearningResources',\n  data() {\n    return {\n      resources: [],\n      loading: false,\n      error: null\n    };\n  },\n  async mounted() {\n    await this.loadResources();\n  },\n  methods: {\n    async loadResources() {\n      this.loading = true;\n      this.error = null;\n      try {\n        const response = await learningResourceAPI.getLearningResources();\n        this.resources = response.data;\n      } catch (error) {\n        console.error('加载学习资源失败:', error);\n        this.error = '加载学习资源失败，请稍后重试';\n      } finally {\n        this.loading = false;\n      }\n    },\n    purifyContent(content) {\n      return DOMPurify.sanitize(content);\n    },\n    formatDate(dateString) {\n      if (!dateString) return '';\n      const date = new Date(dateString);\n      return date.toLocaleDateString('zh-CN');\n    }\n  }\n};", "map": {"version": 3, "names": ["DOMPurify", "learningResourceAPI", "name", "data", "resources", "loading", "error", "mounted", "loadResources", "methods", "response", "getLearningResources", "console", "purifyContent", "content", "sanitize", "formatDate", "dateString", "date", "Date", "toLocaleDateString"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\LearningResources.vue"], "sourcesContent": ["<template>\r\n  <div class=\"learning-resources\">\r\n    <h1>学习资源</h1>\r\n    <p>这里提供与网络信息安全相关的学习资料，如组网技术、网络攻防，安全科普等。</p>\r\n\r\n    <div v-if=\"loading\" class=\"loading\">\r\n      <p>加载中...</p>\r\n    </div>\r\n\r\n    <div v-else-if=\"error\" class=\"error\">\r\n      <p>{{ error }}</p>\r\n      <button @click=\"loadResources\" class=\"btn btn-primary\">重试</button>\r\n    </div>\r\n\r\n    <div v-else class=\"resources-list\">\r\n      <div v-if=\"resources.length === 0\" class=\"no-data\">\r\n        <p>暂无学习资源</p>\r\n      </div>\r\n      <div v-else>\r\n        <div v-for=\"resource in resources\" :key=\"resource.id\" class=\"resource-item\">\r\n          <h3>{{ resource.title }}</h3>\r\n          <div v-html=\"purifyContent(resource.content)\"></div>\r\n          <div v-if=\"resource.file_path\" class=\"file-link\">\r\n            <a :href=\"resource.file_path\" target=\"_blank\" class=\"btn btn-sm btn-outline\">下载文件</a>\r\n          </div>\r\n          <div class=\"resource-meta\">\r\n            <small>创建时间: {{ formatDate(resource.created_at) }}</small>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport DOMPurify from 'dompurify'\r\nimport { learningResourceAPI } from '../services/api'\r\n\r\nexport default {\r\n  name: 'LearningResources',\r\n  data() {\r\n    return {\r\n      resources: [],\r\n      loading: false,\r\n      error: null\r\n    }\r\n  },\r\n  async mounted() {\r\n    await this.loadResources()\r\n  },\r\n  methods: {\r\n    async loadResources() {\r\n      this.loading = true\r\n      this.error = null\r\n      try {\r\n        const response = await learningResourceAPI.getLearningResources()\r\n        this.resources = response.data\r\n      } catch (error) {\r\n        console.error('加载学习资源失败:', error)\r\n        this.error = '加载学习资源失败，请稍后重试'\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    purifyContent(content) {\r\n      return DOMPurify.sanitize(content)\r\n    },\r\n\r\n    formatDate(dateString) {\r\n      if (!dateString) return ''\r\n      const date = new Date(dateString)\r\n      return date.toLocaleDateString('zh-CN')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.learning-resources {\r\n  padding: 20px;\r\n}\r\n</style>"], "mappings": "AAmCA,OAAOA,SAAQ,MAAO,WAAU;AAChC,SAASC,mBAAkB,QAAS,iBAAgB;AAEpD,eAAe;EACbC,IAAI,EAAE,mBAAmB;EACzBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,SAAS,EAAE,EAAE;MACbC,OAAO,EAAE,KAAK;MACdC,KAAK,EAAE;IACT;EACF,CAAC;EACD,MAAMC,OAAOA,CAAA,EAAG;IACd,MAAM,IAAI,CAACC,aAAa,CAAC;EAC3B,CAAC;EACDC,OAAO,EAAE;IACP,MAAMD,aAAaA,CAAA,EAAG;MACpB,IAAI,CAACH,OAAM,GAAI,IAAG;MAClB,IAAI,CAACC,KAAI,GAAI,IAAG;MAChB,IAAI;QACF,MAAMI,QAAO,GAAI,MAAMT,mBAAmB,CAACU,oBAAoB,CAAC;QAChE,IAAI,CAACP,SAAQ,GAAIM,QAAQ,CAACP,IAAG;MAC/B,EAAE,OAAOG,KAAK,EAAE;QACdM,OAAO,CAACN,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC,IAAI,CAACA,KAAI,GAAI,gBAAe;MAC9B,UAAU;QACR,IAAI,CAACD,OAAM,GAAI,KAAI;MACrB;IACF,CAAC;IAEDQ,aAAaA,CAACC,OAAO,EAAE;MACrB,OAAOd,SAAS,CAACe,QAAQ,CAACD,OAAO;IACnC,CAAC;IAEDE,UAAUA,CAACC,UAAU,EAAE;MACrB,IAAI,CAACA,UAAU,EAAE,OAAO,EAAC;MACzB,MAAMC,IAAG,GAAI,IAAIC,IAAI,CAACF,UAAU;MAChC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO;IACxC;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}