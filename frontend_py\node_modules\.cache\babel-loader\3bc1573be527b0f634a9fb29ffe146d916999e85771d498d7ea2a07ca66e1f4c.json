{"ast": null, "code": "import { createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"admin\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[0] || (_cache[0] = _createElementVNode(\"h1\", null, \"管理员后台\", -1 /* CACHED */)), _cache[1] || (_cache[1] = _createElementVNode(\"p\", null, \"这里是管理员进行用户管理、内容发布等操作的界面。\", -1 /* CACHED */)), _createCommentVNode(\" 这里将添加管理员功能，如用户导入、内容管理等 \")]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_createCommentVNode"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Admin.vue"], "sourcesContent": ["<template>\r\n  <div class=\"admin\">\r\n    <h1>管理员后台</h1>\r\n    <p>这里是管理员进行用户管理、内容发布等操作的界面。</p>\r\n    <!-- 这里将添加管理员功能，如用户导入、内容管理等 -->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'Admin',\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.admin {\r\n  padding: 20px;\r\n}\r\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAO;;uBAAlBC,mBAAA,CAIM,OAJNC,UAIM,G,0BAHJC,mBAAA,CAAc,YAAV,OAAK,qB,0BACTA,mBAAA,CAA+B,WAA5B,0BAAwB,qBAC3BC,mBAAA,4BAA+B,C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}