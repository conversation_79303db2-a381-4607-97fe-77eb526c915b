{"ast": null, "code": "import DOMPurify from 'dompurify'; // 引入 DOMPurify\n\nexport default {\n  name: 'LearningResources',\n  data() {\n    return {\n      // 示例：学习资源列表，实际应从后端获取\n      resources: [{\n        id: 1,\n        title: '网络攻防基础',\n        content: '<p>学习网络攻防的**基础知识**。</p><img src=\"invalid.png\" onerror=\"alert(\\'XSS in resource!\\')\">'\n      }, {\n        id: 2,\n        title: 'Python 安全编程',\n        content: '<p>Python 在安全领域的应用。</p>'\n      }]\n    };\n  },\n  methods: {\n    purifyContent(content) {\n      return DOMPurify.sanitize(content);\n    }\n  }\n};", "map": {"version": 3, "names": ["DOMPurify", "name", "data", "resources", "id", "title", "content", "methods", "purifyContent", "sanitize"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\LearningResources.vue"], "sourcesContent": ["<template>\r\n  <div class=\"learning-resources\">\r\n    <h1>学习资源</h1>\r\n    <p>这里提供与网络信息安全相关的学习资料，如组网技术、网络攻防，安全科普等。</p>\r\n\r\n    <div v-if=\"loading\" class=\"loading\">\r\n      <p>加载中...</p>\r\n    </div>\r\n\r\n    <div v-else-if=\"error\" class=\"error\">\r\n      <p>{{ error }}</p>\r\n      <button @click=\"loadResources\" class=\"btn btn-primary\">重试</button>\r\n    </div>\r\n\r\n    <div v-else class=\"resources-list\">\r\n      <div v-if=\"resources.length === 0\" class=\"no-data\">\r\n        <p>暂无学习资源</p>\r\n      </div>\r\n      <div v-else>\r\n        <div v-for=\"resource in resources\" :key=\"resource.id\" class=\"resource-item\">\r\n          <h3>{{ resource.title }}</h3>\r\n          <div v-html=\"purifyContent(resource.content)\"></div>\r\n          <div v-if=\"resource.file_path\" class=\"file-link\">\r\n            <a :href=\"resource.file_path\" target=\"_blank\" class=\"btn btn-sm btn-outline\">下载文件</a>\r\n          </div>\r\n          <div class=\"resource-meta\">\r\n            <small>创建时间: {{ formatDate(resource.created_at) }}</small>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport DOMPurify from 'dompurify'; // 引入 DOMPurify\r\n\r\nexport default {\r\n  name: 'LearningResources',\r\n  data() {\r\n    return {\r\n      // 示例：学习资源列表，实际应从后端获取\r\n      resources: [\r\n        { id: 1, title: '网络攻防基础', content: '<p>学习网络攻防的**基础知识**。</p><img src=\"invalid.png\" onerror=\"alert(\\'XSS in resource!\\')\">' },\r\n        { id: 2, title: 'Python 安全编程', content: '<p>Python 在安全领域的应用。</p>' },\r\n      ],\r\n    };\r\n  },\r\n  methods: {\r\n    purifyContent(content) {\r\n      return DOMPurify.sanitize(content);\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.learning-resources {\r\n  padding: 20px;\r\n}\r\n</style>"], "mappings": "AAmCA,OAAOA,SAAQ,MAAO,WAAW,EAAE;;AAEnC,eAAe;EACbC,IAAI,EAAE,mBAAmB;EACzBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACL;MACAC,SAAS,EAAE,CACT;QAAEC,EAAE,EAAE,CAAC;QAAEC,KAAK,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAuF,CAAC,EAC3H;QAAEF,EAAE,EAAE,CAAC;QAAEC,KAAK,EAAE,aAAa;QAAEC,OAAO,EAAE;MAA0B,CAAC;IAEvE,CAAC;EACH,CAAC;EACDC,OAAO,EAAE;IACPC,aAAaA,CAACF,OAAO,EAAE;MACrB,OAAON,SAAS,CAACS,QAAQ,CAACH,OAAO,CAAC;IACpC;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}