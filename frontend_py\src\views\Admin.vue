<template>
  <div class="admin">
    <div class="admin-header">
      <h1>管理员后台</h1>
      <div class="admin-actions">
        <span>欢迎，{{ currentUser?.name || currentUser?.username }}</span>
        <button @click="logout" class="btn btn-secondary">退出登录</button>
      </div>
    </div>

    <!-- 导航标签 -->
    <div class="admin-tabs">
      <button
        v-for="tab in tabs"
        :key="tab.key"
        @click="activeTab = tab.key"
        :class="['tab-btn', { active: activeTab === tab.key }]"
      >
        {{ tab.label }}
      </button>
    </div>

    <!-- 内容区域 -->
    <div class="admin-content">
      <!-- 用户管理 -->
      <div v-if="activeTab === 'users'" class="tab-content">
        <div class="content-header">
          <h2>用户管理</h2>
          <button @click="showCreateUserModal = true" class="btn btn-primary">添加用户</button>
        </div>

        <div class="table-container">
          <table class="admin-table">
            <thead>
              <tr>
                <th>ID</th>
                <th>用户名</th>
                <th>姓名</th>
                <th>学号</th>
                <th>角色</th>
                <th>创建时间</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="user in users" :key="user.id">
                <td>{{ user.id }}</td>
                <td>{{ user.username }}</td>
                <td>{{ user.name || '-' }}</td>
                <td>{{ user.student_id || '-' }}</td>
                <td>
                  <span :class="['role-badge', user.role]">
                    {{ user.role === 'admin' ? '管理员' : '普通用户' }}
                  </span>
                </td>
                <td>{{ formatDate(user.created_at) }}</td>
                <td>
                  <button @click="editUser(user)" class="btn btn-sm btn-primary">编辑</button>
                  <button @click="deleteUser(user.id)" class="btn btn-sm btn-danger" v-if="user.id !== currentUser?.id">删除</button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 公告管理 -->
      <div v-if="activeTab === 'announcements'" class="tab-content">
        <div class="content-header">
          <h2>公告管理</h2>
          <button @click="showCreateAnnouncementModal = true" class="btn btn-primary">发布公告</button>
        </div>

        <div class="table-container">
          <table class="admin-table">
            <thead>
              <tr>
                <th>ID</th>
                <th>标题</th>
                <th>内容预览</th>
                <th>创建时间</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="announcement in announcements" :key="announcement.id">
                <td>{{ announcement.id }}</td>
                <td>{{ announcement.title }}</td>
                <td>{{ truncateText(announcement.content, 50) }}</td>
                <td>{{ formatDate(announcement.created_at) }}</td>
                <td>
                  <button @click="editAnnouncement(announcement)" class="btn btn-sm btn-primary">编辑</button>
                  <button @click="deleteAnnouncement(announcement.id)" class="btn btn-sm btn-danger">删除</button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 社团文化管理 -->
      <div v-if="activeTab === 'culture'" class="tab-content">
        <div class="content-header">
          <h2>社团文化管理</h2>
          <button @click="showCreateCultureModal = true" class="btn btn-primary">添加内容</button>
        </div>

        <div class="table-container">
          <table class="admin-table">
            <thead>
              <tr>
                <th>ID</th>
                <th>标题</th>
                <th>内容预览</th>
                <th>创建时间</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="culture in clubCultures" :key="culture.id">
                <td>{{ culture.id }}</td>
                <td>{{ culture.title }}</td>
                <td>{{ truncateText(culture.content, 50) }}</td>
                <td>{{ formatDate(culture.created_at) }}</td>
                <td>
                  <button @click="editCulture(culture)" class="btn btn-sm btn-primary">编辑</button>
                  <button @click="deleteCulture(culture.id)" class="btn btn-sm btn-danger">删除</button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 学习资源管理 -->
      <div v-if="activeTab === 'resources'" class="tab-content">
        <div class="content-header">
          <h2>学习资源管理</h2>
          <button @click="showCreateResourceModal = true" class="btn btn-primary">添加资源</button>
        </div>

        <div class="table-container">
          <table class="admin-table">
            <thead>
              <tr>
                <th>ID</th>
                <th>标题</th>
                <th>类型</th>
                <th>创建时间</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="resource in learningResources" :key="resource.id">
                <td>{{ resource.id }}</td>
                <td>{{ resource.title }}</td>
                <td>{{ resource.file_path ? '文件' : '文本' }}</td>
                <td>{{ formatDate(resource.created_at) }}</td>
                <td>
                  <button @click="editResource(resource)" class="btn btn-sm btn-primary">编辑</button>
                  <button @click="deleteResource(resource.id)" class="btn btn-sm btn-danger">删除</button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 过往活动管理 -->
      <div v-if="activeTab === 'activities'" class="tab-content">
        <div class="content-header">
          <h2>过往活动管理</h2>
          <button @click="showCreateActivityModal = true" class="btn btn-primary">添加活动</button>
        </div>

        <div class="table-container">
          <table class="admin-table">
            <thead>
              <tr>
                <th>ID</th>
                <th>标题</th>
                <th>活动日期</th>
                <th>创建时间</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="activity in pastActivities" :key="activity.id">
                <td>{{ activity.id }}</td>
                <td>{{ activity.title }}</td>
                <td>{{ formatDate(activity.activity_date) }}</td>
                <td>{{ formatDate(activity.created_at) }}</td>
                <td>
                  <button @click="editActivity(activity)" class="btn btn-sm btn-primary">编辑</button>
                  <button @click="deleteActivity(activity.id)" class="btn btn-sm btn-danger">删除</button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- 模态框组件将在这里添加 -->
  </div>
</template>

<script>
import {
  authAPI,
  userAPI,
  announcementAPI,
  clubCultureAPI,
  learningResourceAPI,
  pastActivityAPI
} from '../services/api'

export default {
  name: 'Admin',
  data() {
    return {
      currentUser: null,
      activeTab: 'users',
      tabs: [
        { key: 'users', label: '用户管理' },
        { key: 'announcements', label: '公告管理' },
        { key: 'culture', label: '社团文化' },
        { key: 'resources', label: '学习资源' },
        { key: 'activities', label: '过往活动' }
      ],

      // 数据
      users: [],
      announcements: [],
      clubCultures: [],
      learningResources: [],
      pastActivities: [],

      // 模态框状态
      showCreateUserModal: false,
      showCreateAnnouncementModal: false,
      showCreateCultureModal: false,
      showCreateResourceModal: false,
      showCreateActivityModal: false,

      loading: false
    }
  },
  async mounted() {
    await this.loadCurrentUser()
    await this.loadData()
  },
  methods: {
    async loadCurrentUser() {
      try {
        const response = await authAPI.checkAuth()
        this.currentUser = response.data.user
      } catch (error) {
        console.error('获取用户信息失败:', error)
        this.$router.push('/login')
      }
    },

    async loadData() {
      this.loading = true
      try {
        await Promise.all([
          this.loadUsers(),
          this.loadAnnouncements(),
          this.loadClubCultures(),
          this.loadLearningResources(),
          this.loadPastActivities()
        ])
      } catch (error) {
        console.error('加载数据失败:', error)
      } finally {
        this.loading = false
      }
    },

    async loadUsers() {
      try {
        const response = await userAPI.getUsers()
        this.users = response.data
      } catch (error) {
        console.error('加载用户列表失败:', error)
        this.users = []
      }
    },

    async loadAnnouncements() {
      try {
        const response = await announcementAPI.getAnnouncements()
        this.announcements = response.data
      } catch (error) {
        console.error('加载公告列表失败:', error)
        this.announcements = []
      }
    },

    async loadClubCultures() {
      try {
        const response = await clubCultureAPI.getClubCultures()
        this.clubCultures = response.data
      } catch (error) {
        console.error('加载社团文化列表失败:', error)
        this.clubCultures = []
      }
    },

    async loadLearningResources() {
      try {
        const response = await learningResourceAPI.getLearningResources()
        this.learningResources = response.data
      } catch (error) {
        console.error('加载学习资源列表失败:', error)
        this.learningResources = []
      }
    },

    async loadPastActivities() {
      try {
        const response = await pastActivityAPI.getPastActivities()
        this.pastActivities = response.data
      } catch (error) {
        console.error('加载过往活动列表失败:', error)
        this.pastActivities = []
      }
    },

    async logout() {
      try {
        await authAPI.logout()
        this.$router.push('/login')
      } catch (error) {
        console.error('登出失败:', error)
      }
    },

    // 用户管理方法
    editUser(user) {
      // TODO: 实现用户编辑功能
      console.log('编辑用户:', user)
    },

    async deleteUser(userId) {
      if (confirm('确定要删除这个用户吗？')) {
        try {
          await userAPI.deleteUser(userId)
          await this.loadUsers()
        } catch (error) {
          console.error('删除用户失败:', error)
        }
      }
    },

    // 公告管理方法
    editAnnouncement(announcement) {
      // TODO: 实现公告编辑功能
      console.log('编辑公告:', announcement)
    },

    async deleteAnnouncement(announcementId) {
      if (confirm('确定要删除这个公告吗？')) {
        try {
          await announcementAPI.deleteAnnouncement(announcementId)
          await this.loadAnnouncements()
        } catch (error) {
          console.error('删除公告失败:', error)
        }
      }
    },

    // 社团文化管理方法
    editCulture(culture) {
      // TODO: 实现社团文化编辑功能
      console.log('编辑社团文化:', culture)
    },

    async deleteCulture(cultureId) {
      if (confirm('确定要删除这个内容吗？')) {
        try {
          await clubCultureAPI.deleteClubCulture(cultureId)
          await this.loadClubCultures()
        } catch (error) {
          console.error('删除社团文化失败:', error)
        }
      }
    },

    // 学习资源管理方法
    editResource(resource) {
      // TODO: 实现学习资源编辑功能
      console.log('编辑学习资源:', resource)
    },

    async deleteResource(resourceId) {
      if (confirm('确定要删除这个资源吗？')) {
        try {
          await learningResourceAPI.deleteLearningResource(resourceId)
          await this.loadLearningResources()
        } catch (error) {
          console.error('删除学习资源失败:', error)
        }
      }
    },

    // 过往活动管理方法
    editActivity(activity) {
      // TODO: 实现过往活动编辑功能
      console.log('编辑过往活动:', activity)
    },

    async deleteActivity(activityId) {
      if (confirm('确定要删除这个活动吗？')) {
        try {
          await pastActivityAPI.deletePastActivity(activityId)
          await this.loadPastActivities()
        } catch (error) {
          console.error('删除过往活动失败:', error)
        }
      }
    },

    // 工具方法
    formatDate(dateString) {
      if (!dateString) return '-'
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN')
    },

    truncateText(text, length) {
      if (!text) return '-'
      // 移除HTML标签
      const plainText = text.replace(/<[^>]*>/g, '')
      return plainText.length > length ? plainText.substring(0, length) + '...' : plainText
    }
  }
}
</script>

<style scoped>
.admin {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
}

.admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #eee;
}

.admin-header h1 {
  color: #333;
  margin: 0;
}

.admin-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.admin-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 30px;
  border-bottom: 1px solid #ddd;
}

.tab-btn {
  padding: 12px 24px;
  border: none;
  background: none;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  font-size: 16px;
  color: #666;
  transition: all 0.3s ease;
}

.tab-btn:hover {
  color: #42b983;
}

.tab-btn.active {
  color: #42b983;
  border-bottom-color: #42b983;
  font-weight: bold;
}

.admin-content {
  min-height: 500px;
}

.tab-content {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.content-header h2 {
  color: #333;
  margin: 0;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  text-decoration: none;
  display: inline-block;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: #42b983;
  color: white;
}

.btn-primary:hover {
  background-color: #369f6e;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #545b62;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-danger:hover {
  background-color: #c82333;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
  margin-right: 5px;
}

.table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.admin-table {
  width: 100%;
  border-collapse: collapse;
}

.admin-table th,
.admin-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.admin-table th {
  background-color: #f8f9fa;
  font-weight: bold;
  color: #333;
}

.admin-table tr:hover {
  background-color: #f8f9fa;
}

.role-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
}

.role-badge.admin {
  background-color: #dc3545;
  color: white;
}

.role-badge.user {
  background-color: #28a745;
  color: white;
}

@media (max-width: 768px) {
  .admin {
    padding: 10px;
  }

  .admin-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .admin-tabs {
    flex-wrap: wrap;
  }

  .content-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .table-container {
    overflow-x: auto;
  }

  .admin-table {
    min-width: 600px;
  }
}
</style>