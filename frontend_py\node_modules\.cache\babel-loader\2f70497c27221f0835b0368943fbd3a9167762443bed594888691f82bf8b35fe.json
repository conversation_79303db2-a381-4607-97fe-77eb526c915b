{"ast": null, "code": "import { createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString } from \"vue\";\nconst _hoisted_1 = {\n  class: \"learning-resources\"\n};\nconst _hoisted_2 = {\n  class: \"resources-list\"\n};\nconst _hoisted_3 = [\"innerHTML\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[0] || (_cache[0] = _createElementVNode(\"h1\", null, \"学习资源\", -1 /* CACHED */)), _cache[1] || (_cache[1] = _createElementVNode(\"p\", null, \"这里提供与网络信息安全相关的学习资料，如组网技术、网络攻防，安全科普等。\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_2, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.resources, resource => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: resource.id,\n      class: \"resource-item\"\n    }, [_createElementVNode(\"h3\", null, _toDisplayString(resource.title), 1 /* TEXT */), _createElementVNode(\"div\", {\n      innerHTML: $options.purifyContent(resource.content)\n    }, null, 8 /* PROPS */, _hoisted_3)]);\n  }), 128 /* KEYED_FRAGMENT */))])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_Fragment", "_renderList", "$data", "resources", "resource", "key", "id", "_toDisplayString", "title", "innerHTML", "$options", "purifyContent", "content"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\LearningResources.vue"], "sourcesContent": ["<template>\r\n  <div class=\"learning-resources\">\r\n    <h1>学习资源</h1>\r\n    <p>这里提供与网络信息安全相关的学习资料，如组网技术、网络攻防，安全科普等。</p>\r\n    <div class=\"resources-list\">\r\n      <div v-for=\"resource in resources\" :key=\"resource.id\" class=\"resource-item\">\r\n        <h3>{{ resource.title }}</h3>\r\n        <div v-html=\"purifyContent(resource.content)\"></div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport DOMPurify from 'dompurify'; // 引入 DOMPurify\r\n\r\nexport default {\r\n  name: 'LearningResources',\r\n  data() {\r\n    return {\r\n      // 示例：学习资源列表，实际应从后端获取\r\n      resources: [\r\n        { id: 1, title: '网络攻防基础', content: '<p>学习网络攻防的**基础知识**。</p><img src=\"invalid.png\" onerror=\"alert(\\'XSS in resource!\\')\">' },\r\n        { id: 2, title: 'Python 安全编程', content: '<p>Python 在安全领域的应用。</p>' },\r\n      ],\r\n    };\r\n  },\r\n  methods: {\r\n    purifyContent(content) {\r\n      return DOMPurify.sanitize(content);\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.learning-resources {\r\n  padding: 20px;\r\n}\r\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAoB;;EAGxBA,KAAK,EAAC;AAAgB;;;uBAH7BC,mBAAA,CASM,OATNC,UASM,G,0BARJC,mBAAA,CAAa,YAAT,MAAI,qB,0BACRA,mBAAA,CAA2C,WAAxC,sCAAoC,qBACvCA,mBAAA,CAKM,OALNC,UAKM,I,kBAJJH,mBAAA,CAGMI,SAAA,QAAAC,WAAA,CAHkBC,KAAA,CAAAC,SAAS,EAArBC,QAAQ;yBAApBR,mBAAA,CAGM;MAH8BS,GAAG,EAAED,QAAQ,CAACE,EAAE;MAAEX,KAAK,EAAC;QAC1DG,mBAAA,CAA6B,YAAAS,gBAAA,CAAtBH,QAAQ,CAACI,KAAK,kBACrBV,mBAAA,CAAoD;MAA/CW,SAAwC,EAAhCC,QAAA,CAAAC,aAAa,CAACP,QAAQ,CAACQ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}