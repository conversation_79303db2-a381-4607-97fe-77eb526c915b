登录请求: POST http://localhost:5000/api/auth/login
请求头: {'Host': 'localhost:5000', 'User-Agent': 'python-requests/2.32.4', 'Accept-Encoding': 'gzip, deflate', 'Accept': '*/*', 'Connection': 'keep-alive', 'Content-Length': '46', 'Content-Type': 'application/json'}
请求数据: b'{"username": "admin", "password": "Admin123!"}'
==================================================
登录请求: POST http://localhost:5000/api/auth/login
请求头: {'Host': 'localhost:5000', 'Connection': 'keep-alive', 'Content-Length': '43', 'Sec-Ch-Ua-Platform': '"Windows"', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'Accept': 'application/json, text/plain, */*', 'Sec-Ch-Ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"', 'Content-Type': 'application/json', 'Sec-Ch-Ua-Mobile': '?0', 'Origin': 'http://localhost:8080', 'Sec-Fetch-Site': 'same-site', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Dest': 'empty', 'Referer': 'http://localhost:8080/', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6'}
请求数据: b'{"username":"admin","password":"Admin123!"}'
==================================================
登录异常: 'Flask' object has no attribute 'env'
详细错误: Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\cs\pacong_py\python_backend\routes\auth.py", line 87, in login
    secure=current_app.env == 'production', # 生产环境使用 HTTPS
           ^^^^^^^^^^^^^^^
AttributeError: 'Flask' object has no attribute 'env'

==================================================
登录请求: POST http://localhost:5000/api/auth/login
请求头: {'Host': 'localhost:5000', 'User-Agent': 'python-requests/2.32.4', 'Accept-Encoding': 'gzip, deflate', 'Accept': '*/*', 'Connection': 'keep-alive', 'Content-Length': '46', 'Content-Type': 'application/json'}
请求数据: b'{"username": "admin", "password": "Admin123!"}'
==================================================
登录请求: POST http://localhost:5000/api/auth/login
请求头: {'Host': 'localhost:5000', 'Connection': 'keep-alive', 'Content-Length': '43', 'Sec-Ch-Ua-Platform': '"Windows"', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'Accept': 'application/json, text/plain, */*', 'Sec-Ch-Ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"', 'Content-Type': 'application/json', 'Sec-Ch-Ua-Mobile': '?0', 'Origin': 'http://localhost:8081', 'Sec-Fetch-Site': 'same-site', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Dest': 'empty', 'Referer': 'http://localhost:8081/', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6'}
请求数据: b'{"username":"admin","password":"Admin123!"}'
==================================================
登录请求: POST http://localhost:5000/api/auth/login
请求头: {'Host': 'localhost:5000', 'User-Agent': 'python-requests/2.32.4', 'Accept-Encoding': 'gzip, deflate', 'Accept': '*/*', 'Connection': 'keep-alive', 'Content-Length': '46', 'Content-Type': 'application/json'}
请求数据: b'{"username": "admin", "password": "Admin123!"}'
==================================================
