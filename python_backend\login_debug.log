登录请求: POST http://localhost:5000/api/auth/login
请求头: {'Host': 'localhost:5000', 'User-Agent': 'python-requests/2.32.4', 'Accept-Encoding': 'gzip, deflate', 'Accept': '*/*', 'Connection': 'keep-alive', 'Content-Length': '46', 'Content-Type': 'application/json'}
请求数据: b'{"username": "admin", "password": "Admin123!"}'
==================================================
登录请求: POST http://localhost:5000/api/auth/login
请求头: {'User-Agent': 'Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4652', 'Content-Type': 'application/json', 'Host': 'localhost:5000', 'Content-Length': '61'}
请求数据: b'{\r\n    "password":  "Admin123!",\r\n    "username":  "admin"\r\n}'
==================================================
登录请求: POST http://localhost:5000/api/auth/login
请求头: {'User-Agent': 'Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4652', 'Content-Type': 'application/json', 'Host': 'localhost:5000', 'Content-Length': '61'}
请求数据: b'{\r\n    "password":  "Admin123!",\r\n    "username":  "admin"\r\n}'
==================================================
