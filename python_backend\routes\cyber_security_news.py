from flask import Blueprint, request, jsonify
from models import db, CyberSecurityNews
from middleware.auth import token_required, authorize_roles
from datetime import datetime

cyber_security_news_bp = Blueprint('cyber_security_news', __name__)

@cyber_security_news_bp.route('/', methods=['GET'])
def get_cyber_security_news():
    try:
        news = CyberSecurityNews.query.order_by(CyberSecurityNews.published_date.desc()).all()
        return jsonify([{
            'id': n.id,
            'title': n.title,
            'content': n.content,
            'sourceUrl': n.source_url,
            'publishedDate': n.published_date.isoformat() if n.published_date else None,
            'createdAt': n.created_at.isoformat(),
            'updatedAt': n.updated_at.isoformat()
        } for n in news]), 200
    except Exception as e:
        return jsonify({'msg': '服务器错误，获取网安资讯失败'}), 500

@cyber_security_news_bp.route('/', methods=['POST'])
@token_required
@authorize_roles(['admin'])
def create_cyber_security_news():
    title = request.json.get('title')
    content = request.json.get('content')
    source_url = request.json.get('sourceUrl')
    published_date_str = request.json.get('publishedDate')

    if not title or not content:
        return jsonify({'msg': '标题和内容是必填项'}), 400

    published_date = None
    if published_date_str:
        try:
            published_date = datetime.strptime(published_date_str, '%Y-%m-%d').date()
        except ValueError:
            return jsonify({'msg': '发布日期格式无效，请使用 YYYY-MM-DD 格式'}), 400

    try:
        new_news = CyberSecurityNews(title=title, content=content, source_url=source_url, published_date=published_date)
        db.session.add(new_news)
        db.session.commit()
        return jsonify({'msg': '网安资讯创建成功', 'news': {
            'id': new_news.id,
            'title': new_news.title,
            'content': new_news.content,
            'sourceUrl': new_news.source_url,
            'publishedDate': new_news.published_date.isoformat() if new_news.published_date else None,
            'createdAt': new_news.created_at.isoformat()
        }}), 201
    except Exception as e:
        return jsonify({'msg': '服务器错误，创建网安资讯失败'}), 500

@cyber_security_news_bp.route('/<int:news_id>', methods=['PUT'])
@token_required
@authorize_roles(['admin'])
def update_cyber_security_news(news_id):
    title = request.json.get('title')
    content = request.json.get('content')
    source_url = request.json.get('sourceUrl')
    published_date_str = request.json.get('publishedDate')

    try:
        news = CyberSecurityNews.query.get(news_id)
        if not news:
            return jsonify({'msg': '网安资讯未找到'}), 404
        
        if published_date_str:
            try:
                news.published_date = datetime.strptime(published_date_str, '%Y-%m-%d').date()
            except ValueError:
                return jsonify({'msg': '发布日期格式无效，请使用 YYYY-MM-DD 格式'}), 400
        elif published_date_str is not None: # 允许清空日期
            news.published_date = None

        news.title = title if title is not None else news.title
        news.content = content if content is not None else news.content
        news.source_url = source_url if source_url is not None else news.source_url
        db.session.commit()
        return jsonify({'msg': '网安资讯更新成功', 'news': {
            'id': news.id,
            'title': news.title,
            'content': news.content,
            'sourceUrl': news.source_url,
            'publishedDate': news.published_date.isoformat() if news.published_date else None,
            'updatedAt': news.updated_at.isoformat()
        }}), 200
    except Exception as e:
        return jsonify({'msg': '服务器错误，更新网安资讯失败'}), 500

@cyber_security_news_bp.route('/<int:news_id>', methods=['DELETE'])
@token_required
@authorize_roles(['admin'])
def delete_cyber_security_news(news_id):
    try:
        news = CyberSecurityNews.query.get(news_id)
        if not news:
            return jsonify({'msg': '网安资讯未找到'}), 404
        
        db.session.delete(news)
        db.session.commit()
        return jsonify({'msg': '网安资讯已删除'}), 200
    except Exception as e:
        return jsonify({'msg': '服务器错误，删除网安资讯失败'}), 500
