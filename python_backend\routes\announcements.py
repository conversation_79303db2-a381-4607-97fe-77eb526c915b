from flask import Blueprint, request, jsonify
from models import db, Announcement
from middleware.auth import token_required, authorize_roles
from datetime import datetime

announcements_bp = Blueprint('announcements', __name__)

@announcements_bp.route('/', methods=['GET'])
def get_announcements():
    try:
        announcements = Announcement.query.order_by(Announcement.created_at.desc()).all()
        return jsonify([{
            'id': a.id,
            'title': a.title,
            'content': a.content,
            'createdAt': a.created_at.isoformat(),
            'updatedAt': a.updated_at.isoformat()
        } for a in announcements]), 200
    except Exception as e:
        return jsonify({'msg': '服务器错误，获取公告失败'}), 500

@announcements_bp.route('/', methods=['POST'])
@token_required
@authorize_roles(['admin'])
def create_announcement():
    title = request.json.get('title')
    content = request.json.get('content')

    if not title or not content:
        return jsonify({'msg': '标题和内容是必填项'}), 400

    try:
        new_announcement = Announcement(title=title, content=content)
        db.session.add(new_announcement)
        db.session.commit()
        return jsonify({'msg': '公告创建成功', 'announcement': {
            'id': new_announcement.id,
            'title': new_announcement.title,
            'content': new_announcement.content,
            'createdAt': new_announcement.created_at.isoformat()
        }}), 201
    except Exception as e:
        return jsonify({'msg': '服务器错误，创建公告失败'}), 500

@announcements_bp.route('/<int:announcement_id>', methods=['PUT'])
@token_required
@authorize_roles(['admin'])
def update_announcement(announcement_id):
    title = request.json.get('title')
    content = request.json.get('content')

    try:
        announcement = Announcement.query.get(announcement_id)
        if not announcement:
            return jsonify({'msg': '公告未找到'}), 404
        
        announcement.title = title if title is not None else announcement.title
        announcement.content = content if content is not None else announcement.content
        announcement.updated_at = datetime.utcnow() # 手动更新时间戳
        db.session.commit()
        return jsonify({'msg': '公告更新成功', 'announcement': {
            'id': announcement.id,
            'title': announcement.title,
            'content': announcement.content,
            'updatedAt': announcement.updated_at.isoformat()
        }}), 200
    except Exception as e:
        return jsonify({'msg': '服务器错误，更新公告失败'}), 500

@announcements_bp.route('/<int:announcement_id>', methods=['DELETE'])
@token_required
@authorize_roles(['admin'])
def delete_announcement(announcement_id):
    try:
        announcement = Announcement.query.get(announcement_id)
        if not announcement:
            return jsonify({'msg': '公告未找到'}), 404
        
        db.session.delete(announcement)
        db.session.commit()
        return jsonify({'msg': '公告已删除'}), 200
    except Exception as e:
        return jsonify({'msg': '服务器错误，删除公告失败'}), 500
