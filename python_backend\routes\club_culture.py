import os
from flask import Blueprint, request, jsonify, current_app
from models import db, ClubCulture
from middleware.auth import token_required, authorize_roles
from utils.file_upload import upload_file
import json

club_culture_bp = Blueprint('club_culture', __name__)

@club_culture_bp.route('/', methods=['GET'])
def get_club_cultures():
    try:
        club_cultures = ClubCulture.query.all()
        result = []
        for culture in club_cultures:
            result.append({
                'id': culture.id,
                'title': culture.title,
                'content': culture.content,
                'images': culture.get_images(),
                'created_at': culture.created_at.isoformat() if culture.created_at else None,
                'updated_at': culture.updated_at.isoformat() if culture.updated_at else None
            })

        return jsonify(result), 200
    except Exception as e:
        current_app.logger.error(f"获取社团文化列表失败: {e}")
        return jsonify({'msg': '服务器错误，获取社团文化列表失败'}), 500

@club_culture_bp.route('/<int:id>', methods=['GET'])
def get_club_culture(id):
    try:
        club_culture = ClubCulture.query.get(id)
        if not club_culture:
            return jsonify({'msg': '社团文化内容未找到'}), 404

        return jsonify({
            'id': club_culture.id,
            'title': club_culture.title,
            'content': club_culture.content,
            'images': club_culture.get_images(),
            'created_at': club_culture.created_at.isoformat() if club_culture.created_at else None,
            'updated_at': club_culture.updated_at.isoformat() if club_culture.updated_at else None
        }), 200
    except Exception as e:
        current_app.logger.error(f"获取社团文化失败: {e}")
        return jsonify({'msg': '服务器错误，获取社团文化失败'}), 500

@club_culture_bp.route('/', methods=['POST'])
@token_required
@authorize_roles(['admin'])
def create_or_update_club_culture():
    title = request.form.get('title')
    content = request.form.get('content')
    
    # 处理图片上传
    image_urls = []
    if 'images' in request.files:
        files = request.files.getlist('images')
        for file in files:
            if file.filename != '':
                file_url, error = upload_file(file, 'club_culture', ['jpeg', 'jpg', 'png', 'gif'], 5)
                if error:
                    return jsonify({'msg': f'图片上传失败: {error}'}), 400
                image_urls.append({'url': file_url, 'alt': file.filename})

    try:
        club_culture = ClubCulture.query.first()

        if club_culture:
            club_culture.title = title if title is not None else club_culture.title
            club_culture.content = content if content is not None else club_culture.content
            if image_urls:
                existing_images = club_culture.get_images()
                club_culture.set_images(existing_images + image_urls)
            db.session.commit()
            msg = '社团文化内容更新成功'
            status_code = 200
        else:
            new_club_culture = ClubCulture(title=title, content=content)
            new_club_culture.set_images(image_urls)
            db.session.add(new_club_culture)
            db.session.commit()
            club_culture = new_club_culture # 将新创建的对象赋值给 club_culture
            msg = '社团文化内容创建成功'
            status_code = 201
        
        return jsonify({'msg': msg, 'clubCulture': {
            'id': club_culture.id,
            'title': club_culture.title,
            'content': club_culture.content,
            'images': club_culture.get_images(),
            'createdAt': club_culture.created_at.isoformat(),
            'updatedAt': club_culture.updated_at.isoformat() if club_culture.updated_at else club_culture.created_at.isoformat()
        }}), status_code

    except Exception as e:
        current_app.logger.error(f"创建/更新社团文化失败: {e}")
        return jsonify({'msg': '服务器错误，创建/更新社团文化失败'}), 500

@club_culture_bp.route('/image/<int:index>', methods=['DELETE'])
@token_required
@authorize_roles(['admin'])
def delete_club_culture_image(index):
    try:
        club_culture = ClubCulture.query.first()
        if not club_culture:
            return jsonify({'msg': '社团文化内容未找到'}), 404

        images = club_culture.get_images()
        if index < 0 or index >= len(images):
            return jsonify({'msg': '图片索引无效'}), 404
        
        image_url = images[index]['url']
        
        # 从文件系统删除图片
        # 确保只删除 uploads/club_culture 目录下的文件
        filename = os.path.basename(image_url)
        image_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'club_culture', filename)
        
        if os.path.exists(image_path):
            os.remove(image_path)
        else:
            current_app.logger.warning(f"尝试删除不存在的文件: {image_path}")

        # 从数据库中移除图片信息
        images.pop(index)
        club_culture.set_images(images)
        db.session.commit()

        return jsonify({'msg': '图片已删除'}), 200

    except Exception as e:
        current_app.logger.error(f"删除社团文化图片失败: {e}")
        return jsonify({'msg': '服务器错误，删除图片失败'}), 500
