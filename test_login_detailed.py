import requests
import json
import sys

def test_login():
    try:
        # 测试登录API
        url = "http://localhost:5000/api/auth/login"
        data = {
            "username": "admin",
            "password": "Admin123!"
        }
        
        print("=" * 50)
        print("正在测试登录API...")
        print(f"URL: {url}")
        print(f"数据: {json.dumps(data, indent=2)}")
        
        # 设置请求头
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
        
        response = requests.post(url, json=data, headers=headers, timeout=10)
        
        print(f"\n状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            print("\n✅ 登录成功!")
            return True
        else:
            print(f"\n❌ 登录失败! 状态码: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接错误: 无法连接到服务器，请确保后端服务正在运行")
        return False
    except requests.exceptions.Timeout:
        print("❌ 请求超时: 服务器响应超时")
        return False
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_health_check():
    """测试服务器健康检查"""
    try:
        url = "http://localhost:5000/"
        print("\n" + "=" * 50)
        print("正在测试服务器健康检查...")
        print(f"URL: {url}")
        
        response = requests.get(url, timeout=5)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"健康检查失败: {e}")
        return False

if __name__ == "__main__":
    print("开始测试登录系统...")
    
    # 先测试健康检查
    if not test_health_check():
        print("服务器未正常运行，请先启动后端服务")
        sys.exit(1)
    
    # 测试登录
    success = test_login()
    
    if success:
        print("\n🎉 所有测试通过!")
    else:
        print("\n💥 测试失败，请检查日志")
