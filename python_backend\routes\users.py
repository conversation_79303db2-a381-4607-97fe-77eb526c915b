from flask import Blueprint, request, jsonify, current_app
from models import db, User
from middleware.auth import token_required, authorize_roles
from utils.file_upload import upload_file
from utils.password_utils import is_weak_password
import openpyxl
import os

users_bp = Blueprint('users', __name__)

@users_bp.route('/import', methods=['POST'])
@token_required
@authorize_roles(['admin'])
def import_users():
    if 'file' not in request.files:
        return jsonify({'msg': '请上传 Excel 文件'}), 400

    file = request.files['file']
    if file.filename == '':
        return jsonify({'msg': '没有选择文件'}), 400

    # 使用文件上传工具函数进行验证和保存
    file_url, error = upload_file(file, 'excel_imports', ['xlsx', 'xls'], 5) # 5MB limit
    if error:
        return jsonify({'msg': error}), 400

    try:
        # openpyxl 只能处理 .xlsx 文件，对于 .xls 文件需要额外的库或转换
        if not file_url.lower().endswith('.xlsx'):
            return jsonify({'msg': '目前只支持导入 .xlsx 格式的 Excel 文件。'}), 400

        # 临时保存文件以便 openpyxl 读取
        temp_file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'excel_imports', os.path.basename(file_url))
        
        workbook = openpyxl.load_workbook(temp_file_path)
        sheet = workbook.active
        
        users_data = []
        header = [cell.value for cell in sheet[1]] # 获取第一行作为表头
        for row in sheet.iter_rows(min_row=2, values_only=True): # 从第二行开始读取数据
            user_data = dict(zip(header, row))
            users_data.append(user_data)

        imported_users = []
        errors = []

        for userData in users_data:
            username = userData.get('username')
            password = userData.get('password')
            student_id = userData.get('studentId')
            name = userData.get('name')

            validation_errors = []
            if not username or not isinstance(username, str) or username.strip() == '':
                validation_errors.append('用户名不能为空且必须是字符串')
            if not password or not isinstance(password, str) or password.strip() == '':
                validation_errors.append('密码不能为空且必须是字符串')
            if student_id and not isinstance(student_id, str):
                validation_errors.append('学号必须是字符串')
            if name and not isinstance(name, str):
                validation_errors.append('姓名必须是字符串')

            if validation_errors:
                errors.append({'user': username or '未知用户', 'issues': validation_errors})
                continue

            if is_weak_password(password):
                errors.append({'user': username, 'issues': ['密码过于简单，请设置更复杂的密码。']})
                continue

            existing_user = User.query.filter_by(username=username).first()
            if existing_user:
                errors.append({'user': username, 'issues': ['用户已存在，跳过导入。']})
                continue

            new_user = User(username=username, student_id=student_id, name=name, role='user')
            new_user.set_password(password)
            db.session.add(new_user)
            imported_users.append(new_user)
        
        db.session.commit() # 批量提交

        # 删除临时文件
        os.remove(temp_file_path)

        return jsonify({
            'msg': '用户导入完成',
            'importedCount': len(imported_users),
            'importedUsers': [{'username': u.username, 'studentId': u.student_id, 'name': u.name} for u in imported_users],
            'errors': errors
        }), 200

    except Exception as e:
        current_app.logger.error(f"用户导入失败: {e}")
        # 尝试删除临时文件，即使导入失败
        if os.path.exists(temp_file_path):
            os.remove(temp_file_path)
        return jsonify({'msg': '服务器错误，用户导入失败'}), 500

@users_bp.route('/', methods=['GET'])
@token_required
@authorize_roles(['admin'])
def get_all_users():
    try:
        users = User.query.filter_by(role='user').all()
        return jsonify([{'id': u.id, 'username': u.username, 'studentId': u.student_id, 'name': u.name, 'role': u.role} for u in users]), 200
    except Exception as e:
        current_app.logger.error(f"获取用户列表失败: {e}")
        return jsonify({'msg': '服务器错误，获取用户列表失败'}), 500

@users_bp.route('/<int:user_id>', methods=['DELETE'])
@token_required
@authorize_roles(['admin'])
def delete_user(user_id):
    try:
        user = User.query.get(user_id)
        if not user:
            return jsonify({'msg': '用户未找到'}), 404
        
        db.session.delete(user)
        db.session.commit()
        return jsonify({'msg': '用户已删除'}), 200
    except Exception as e:
        current_app.logger.error(f"删除用户失败: {e}")
        return jsonify({'msg': '服务器错误，删除用户失败'}), 500
