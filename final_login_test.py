#!/usr/bin/env python3
"""
NIS社团文化网站登录系统完整测试脚本
"""

import os
import sys
import json
import requests
import time

def test_backend_health():
    """测试后端服务健康状态"""
    try:
        response = requests.get("http://localhost:5000/", timeout=5)
        if response.status_code == 200:
            print("✅ 后端服务运行正常")
            return True
        else:
            print(f"❌ 后端服务异常: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到后端服务，请确保服务已启动")
        return False
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
        return False

def test_login_api():
    """测试登录API"""
    try:
        login_data = {
            "username": "admin",
            "password": "Admin123!"
        }
        
        response = requests.post(
            "http://localhost:5000/api/auth/login",
            json=login_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"登录响应状态码: {response.status_code}")
        print(f"登录响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✅ 登录API测试通过")
            return True
        else:
            print(f"❌ 登录API测试失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 登录测试异常: {e}")
        return False

def test_auth_check():
    """测试认证检查API"""
    try:
        # 先登录获取token
        login_response = requests.post(
            "http://localhost:5000/api/auth/login",
            json={"username": "admin", "password": "Admin123!"},
            timeout=10
        )
        
        if login_response.status_code != 200:
            print("❌ 需要先成功登录才能测试认证检查")
            return False
            
        # 测试认证检查
        response = requests.get(
            "http://localhost:5000/api/auth/check-auth",
            timeout=10
        )
        
        print(f"认证检查状态码: {response.status_code}")
        if response.status_code == 200:
            user_data = response.json()
            print(f"✅ 认证检查通过: {user_data}")
            return True
        else:
            print(f"❌ 认证检查失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 认证检查异常: {e}")
        return False

def main():
    """主测试流程"""
    print("=" * 60)
    print("NIS社团文化网站登录系统测试")
    print("=" * 60)
    
    # 步骤1: 检查后端服务
    print("\n1. 检查后端服务状态...")
    if not test_backend_health():
        print("\n💡 提示: 请先启动后端服务")
        print("   运行: python python_backend/app.py")
        return False
    
    # 步骤2: 测试登录API
    print("\n2. 测试登录API...")
    if not test_login_api():
        print("\n💡 提示: 检查管理员用户是否存在")
        return False
    
    # 步骤3: 测试认证检查
    print("\n3. 测试认证检查...")
    test_auth_check()
    
    print("\n" + "=" * 60)
    print("🎉 测试完成！")
    print("=" * 60)
    print("使用说明:")
    print("1. 后端服务: http://localhost:5000")
    print("2. 前端服务: http://localhost:8080/login")
    print("3. 管理员账户: admin / Admin123!")
    print("=" * 60)

if __name__ == "__main__":
    main()
