{"ast": null, "code": "import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, renderList as _renderList, Fragment as _Fragment } from \"vue\";\nconst _hoisted_1 = {\n  class: \"learning-resources\"\n};\nconst _hoisted_2 = {\n  key: 0,\n  class: \"loading\"\n};\nconst _hoisted_3 = {\n  key: 1,\n  class: \"error\"\n};\nconst _hoisted_4 = {\n  key: 2,\n  class: \"resources-list\"\n};\nconst _hoisted_5 = {\n  key: 0,\n  class: \"no-data\"\n};\nconst _hoisted_6 = {\n  key: 1\n};\nconst _hoisted_7 = [\"innerHTML\"];\nconst _hoisted_8 = {\n  key: 0,\n  class: \"file-link\"\n};\nconst _hoisted_9 = [\"href\"];\nconst _hoisted_10 = {\n  class: \"resource-meta\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[3] || (_cache[3] = _createElementVNode(\"h1\", null, \"学习资源\", -1 /* CACHED */)), _cache[4] || (_cache[4] = _createElementVNode(\"p\", null, \"这里提供与网络信息安全相关的学习资料，如组网技术、网络攻防，安全科普等。\", -1 /* CACHED */)), $data.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, _cache[1] || (_cache[1] = [_createElementVNode(\"p\", null, \"加载中...\", -1 /* CACHED */)]))) : $data.error ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, [_createElementVNode(\"p\", null, _toDisplayString($data.error), 1 /* TEXT */), _createElementVNode(\"button\", {\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.loadResources && $options.loadResources(...args)),\n    class: \"btn btn-primary\"\n  }, \"重试\")])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [$data.resources.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, _cache[2] || (_cache[2] = [_createElementVNode(\"p\", null, \"暂无学习资源\", -1 /* CACHED */)]))) : (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.resources, resource => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: resource.id,\n      class: \"resource-item\"\n    }, [_createElementVNode(\"h3\", null, _toDisplayString(resource.title), 1 /* TEXT */), _createElementVNode(\"div\", {\n      innerHTML: $options.purifyContent(resource.content)\n    }, null, 8 /* PROPS */, _hoisted_7), resource.file_path ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, [_createElementVNode(\"a\", {\n      href: resource.file_path,\n      target: \"_blank\",\n      class: \"btn btn-sm btn-outline\"\n    }, \"下载文件\", 8 /* PROPS */, _hoisted_9)])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"small\", null, \"创建时间: \" + _toDisplayString($options.formatDate(resource.created_at)), 1 /* TEXT */)])]);\n  }), 128 /* KEYED_FRAGMENT */))]))]))]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "$data", "loading", "_hoisted_2", "_cache", "error", "_hoisted_3", "_toDisplayString", "onClick", "args", "$options", "loadResources", "_hoisted_4", "resources", "length", "_hoisted_5", "_hoisted_6", "_Fragment", "_renderList", "resource", "key", "id", "title", "innerHTML", "purifyContent", "content", "file_path", "_hoisted_8", "href", "target", "_hoisted_9", "_hoisted_10", "formatDate", "created_at"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\LearningResources.vue"], "sourcesContent": ["<template>\r\n  <div class=\"learning-resources\">\r\n    <h1>学习资源</h1>\r\n    <p>这里提供与网络信息安全相关的学习资料，如组网技术、网络攻防，安全科普等。</p>\r\n\r\n    <div v-if=\"loading\" class=\"loading\">\r\n      <p>加载中...</p>\r\n    </div>\r\n\r\n    <div v-else-if=\"error\" class=\"error\">\r\n      <p>{{ error }}</p>\r\n      <button @click=\"loadResources\" class=\"btn btn-primary\">重试</button>\r\n    </div>\r\n\r\n    <div v-else class=\"resources-list\">\r\n      <div v-if=\"resources.length === 0\" class=\"no-data\">\r\n        <p>暂无学习资源</p>\r\n      </div>\r\n      <div v-else>\r\n        <div v-for=\"resource in resources\" :key=\"resource.id\" class=\"resource-item\">\r\n          <h3>{{ resource.title }}</h3>\r\n          <div v-html=\"purifyContent(resource.content)\"></div>\r\n          <div v-if=\"resource.file_path\" class=\"file-link\">\r\n            <a :href=\"resource.file_path\" target=\"_blank\" class=\"btn btn-sm btn-outline\">下载文件</a>\r\n          </div>\r\n          <div class=\"resource-meta\">\r\n            <small>创建时间: {{ formatDate(resource.created_at) }}</small>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport DOMPurify from 'dompurify'\r\nimport { learningResourceAPI } from '../services/api'\r\n\r\nexport default {\r\n  name: 'LearningResources',\r\n  data() {\r\n    return {\r\n      resources: [],\r\n      loading: false,\r\n      error: null\r\n    }\r\n  },\r\n  async mounted() {\r\n    await this.loadResources()\r\n  },\r\n  methods: {\r\n    async loadResources() {\r\n      this.loading = true\r\n      this.error = null\r\n      try {\r\n        const response = await learningResourceAPI.getLearningResources()\r\n        this.resources = response.data\r\n      } catch (error) {\r\n        console.error('加载学习资源失败:', error)\r\n        this.error = '加载学习资源失败，请稍后重试'\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    purifyContent(content) {\r\n      return DOMPurify.sanitize(content)\r\n    },\r\n\r\n    formatDate(dateString) {\r\n      if (!dateString) return ''\r\n      const date = new Date(dateString)\r\n      return date.toLocaleDateString('zh-CN')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.learning-resources {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 20px;\r\n}\r\n\r\n.loading, .error, .no-data {\r\n  text-align: center;\r\n  padding: 40px;\r\n  color: #666;\r\n}\r\n\r\n.error {\r\n  color: #e74c3c;\r\n}\r\n\r\n.resources-list {\r\n  margin-top: 20px;\r\n}\r\n\r\n.resource-item {\r\n  background: white;\r\n  border: 1px solid #ddd;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  margin-bottom: 20px;\r\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\r\n}\r\n\r\n.resource-item h3 {\r\n  color: #2c3e50;\r\n  margin-bottom: 15px;\r\n  border-bottom: 2px solid #3498db;\r\n  padding-bottom: 10px;\r\n}\r\n\r\n.file-link {\r\n  margin: 15px 0;\r\n}\r\n\r\n.resource-meta {\r\n  margin-top: 15px;\r\n  padding-top: 15px;\r\n  border-top: 1px solid #eee;\r\n  color: #666;\r\n}\r\n\r\n.btn {\r\n  display: inline-block;\r\n  padding: 8px 16px;\r\n  border: none;\r\n  border-radius: 4px;\r\n  text-decoration: none;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n}\r\n\r\n.btn-primary {\r\n  background-color: #3498db;\r\n  color: white;\r\n}\r\n\r\n.btn-primary:hover {\r\n  background-color: #2980b9;\r\n}\r\n\r\n.btn-sm {\r\n  padding: 4px 8px;\r\n  font-size: 12px;\r\n}\r\n\r\n.btn-outline {\r\n  background-color: transparent;\r\n  color: #3498db;\r\n  border: 1px solid #3498db;\r\n}\r\n\r\n.btn-outline:hover {\r\n  background-color: #3498db;\r\n  color: white;\r\n}\r\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAoB;;;EAITA,KAAK,EAAC;;;;EAIHA,KAAK,EAAC;;;;EAKjBA,KAAK,EAAC;;;;EACmBA,KAAK,EAAC;;;;;;;;EAONA,KAAK,EAAC;;;;EAGhCA,KAAK,EAAC;AAAe;;uBAxBlCC,mBAAA,CA8BM,OA9BNC,UA8BM,G,0BA7BJC,mBAAA,CAAa,YAAT,MAAI,qB,0BACRA,mBAAA,CAA2C,WAAxC,sCAAoC,qBAE5BC,KAAA,CAAAC,OAAO,I,cAAlBJ,mBAAA,CAEM,OAFNK,UAEM,EAAAC,MAAA,QAAAA,MAAA,OADJJ,mBAAA,CAAa,WAAV,QAAM,mB,MAGKC,KAAA,CAAAI,KAAK,I,cAArBP,mBAAA,CAGM,OAHNQ,UAGM,GAFJN,mBAAA,CAAkB,WAAAO,gBAAA,CAAZN,KAAA,CAAAI,KAAK,kBACXL,mBAAA,CAAkE;IAAzDQ,OAAK,EAAAJ,MAAA,QAAAA,MAAA,UAAAK,IAAA,KAAEC,QAAA,CAAAC,aAAA,IAAAD,QAAA,CAAAC,aAAA,IAAAF,IAAA,CAAa;IAAEZ,KAAK,EAAC;KAAkB,IAAE,E,oBAG3DC,mBAAA,CAgBM,OAhBNc,UAgBM,GAfOX,KAAA,CAAAY,SAAS,CAACC,MAAM,U,cAA3BhB,mBAAA,CAEM,OAFNiB,UAEM,EAAAX,MAAA,QAAAA,MAAA,OADJJ,mBAAA,CAAa,WAAV,QAAM,mB,qBAEXF,mBAAA,CAWM,OAAAkB,UAAA,I,kBAVJlB,mBAAA,CASMmB,SAAA,QAAAC,WAAA,CATkBjB,KAAA,CAAAY,SAAS,EAArBM,QAAQ;yBAApBrB,mBAAA,CASM;MAT8BsB,GAAG,EAAED,QAAQ,CAACE,EAAE;MAAExB,KAAK,EAAC;QAC1DG,mBAAA,CAA6B,YAAAO,gBAAA,CAAtBY,QAAQ,CAACG,KAAK,kBACrBtB,mBAAA,CAAoD;MAA/CuB,SAAwC,EAAhCb,QAAA,CAAAc,aAAa,CAACL,QAAQ,CAACM,OAAO;yCAChCN,QAAQ,CAACO,SAAS,I,cAA7B5B,mBAAA,CAEM,OAFN6B,UAEM,GADJ3B,mBAAA,CAAqF;MAAjF4B,IAAI,EAAET,QAAQ,CAACO,SAAS;MAAEG,MAAM,EAAC,QAAQ;MAAChC,KAAK,EAAC;OAAyB,MAAI,iBAAAiC,UAAA,E,wCAEnF9B,mBAAA,CAEM,OAFN+B,WAEM,GADJ/B,mBAAA,CAA0D,eAAnD,QAAM,GAAAO,gBAAA,CAAGG,QAAA,CAAAsB,UAAU,CAACb,QAAQ,CAACc,UAAU,kB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}