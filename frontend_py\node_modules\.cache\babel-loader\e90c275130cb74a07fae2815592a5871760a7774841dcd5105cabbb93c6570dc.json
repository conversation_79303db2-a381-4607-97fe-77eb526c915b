{"ast": null, "code": "import DOMPurify from 'dompurify';\nimport { clubCultureAPI } from '../services/api';\nexport default {\n  name: 'ClubCulture',\n  data() {\n    return {\n      cultures: [],\n      loading: false\n    };\n  },\n  async mounted() {\n    await this.loadCultures();\n  },\n  methods: {\n    async loadCultures() {\n      this.loading = true;\n      try {\n        const response = await clubCultureAPI.getClubCultures();\n        this.cultures = response.data;\n      } catch (error) {\n        console.error('加载社团文化失败:', error);\n        // 如果API调用失败，显示示例数据\n        this.cultures = [{\n          id: 1,\n          title: '社团成立历程',\n          content: '<p>网络信息安全社团成立于2020年，致力于培养学生的网络安全意识和技能。</p>',\n          images: [],\n          created_at: new Date().toISOString()\n        }];\n      } finally {\n        this.loading = false;\n      }\n    },\n    purifyContent(content) {\n      return DOMPurify.sanitize(content);\n    },\n    formatDate(dateString) {\n      if (!dateString) return '';\n      const date = new Date(dateString);\n      return date.toLocaleDateString('zh-CN');\n    }\n  }\n};", "map": {"version": 3, "names": ["DOMPurify", "clubCultureAPI", "name", "data", "cultures", "loading", "mounted", "loadCultures", "methods", "response", "getClubCultures", "error", "console", "id", "title", "content", "images", "created_at", "Date", "toISOString", "purifyContent", "sanitize", "formatDate", "dateString", "date", "toLocaleDateString"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\ClubCulture.vue"], "sourcesContent": ["<template>\r\n  <div class=\"club-culture\">\r\n    <div class=\"hero-section\">\r\n      <h1>社团文化</h1>\r\n      <p class=\"hero-subtitle\">传承网络安全精神，培养技术人才</p>\r\n    </div>\r\n\r\n    <!-- 社团介绍 -->\r\n    <div class=\"section\">\r\n      <div class=\"intro-card\">\r\n        <div class=\"intro-content\">\r\n          <h2>网络信息安全社团</h2>\r\n          <div class=\"intro-details\">\r\n            <p><strong>学校：</strong>成都工业职业技术学院（金堂校区）</p>\r\n            <p><strong>社团群号：</strong>242050951</p>\r\n            <p><strong>抖音：</strong>21647629167</p>\r\n            <p><strong>主要活动：</strong>组网技术，网络攻防，安全科普</p>\r\n          </div>\r\n        </div>\r\n        <div class=\"intro-image\">\r\n          <img src=\"@/assets/logo.png\" alt=\"NIS 社团标志\" />\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 社团文化内容 -->\r\n    <div class=\"section\">\r\n      <h2>社团文化内容</h2>\r\n      <div v-if=\"loading\" class=\"loading\">加载中...</div>\r\n      <div v-else-if=\"cultures.length === 0\" class=\"no-data\">暂无文化内容</div>\r\n      <div v-else class=\"culture-grid\">\r\n        <div v-for=\"culture in cultures\" :key=\"culture.id\" class=\"culture-card\">\r\n          <h3>{{ culture.title }}</h3>\r\n          <div class=\"culture-content\" v-html=\"purifyContent(culture.content)\"></div>\r\n          <div v-if=\"culture.images && culture.images.length > 0\" class=\"culture-images\">\r\n            <img v-for=\"(image, index) in culture.images\" :key=\"index\" :src=\"image\" :alt=\"culture.title\" />\r\n          </div>\r\n          <p class=\"culture-date\">{{ formatDate(culture.created_at) }}</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 社团价值观 -->\r\n    <div class=\"section values-section\">\r\n      <h2>社团价值观</h2>\r\n      <div class=\"values-grid\">\r\n        <div class=\"value-card\">\r\n          <div class=\"value-icon\">🛡️</div>\r\n          <h3>安全第一</h3>\r\n          <p>始终将网络安全放在首位，保护数字世界的安全</p>\r\n        </div>\r\n        <div class=\"value-card\">\r\n          <div class=\"value-icon\">🤝</div>\r\n          <h3>团队协作</h3>\r\n          <p>相互学习，共同进步，打造强大的技术团队</p>\r\n        </div>\r\n        <div class=\"value-card\">\r\n          <div class=\"value-icon\">💡</div>\r\n          <h3>创新思维</h3>\r\n          <p>勇于探索新技术，创新解决方案</p>\r\n        </div>\r\n        <div class=\"value-card\">\r\n          <div class=\"value-icon\">📚</div>\r\n          <h3>持续学习</h3>\r\n          <p>保持学习热情，跟上技术发展步伐</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 加入我们 -->\r\n    <div class=\"section join-section\">\r\n      <h2>加入我们</h2>\r\n      <div class=\"join-content\">\r\n        <p>如果你对网络安全感兴趣，想要学习相关技术，欢迎加入我们的大家庭！</p>\r\n        <div class=\"join-actions\">\r\n          <router-link to=\"/register\" class=\"btn btn-primary\">立即注册</router-link>\r\n          <a href=\"https://qm.qq.com/cgi-bin/qm/qr?k=242050951\" target=\"_blank\" class=\"btn btn-secondary\">加入QQ群</a>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport DOMPurify from 'dompurify'\r\nimport { clubCultureAPI } from '../services/api'\r\n\r\nexport default {\r\n  name: 'ClubCulture',\r\n  data() {\r\n    return {\r\n      cultures: [],\r\n      loading: false\r\n    }\r\n  },\r\n  async mounted() {\r\n    await this.loadCultures()\r\n  },\r\n  methods: {\r\n    async loadCultures() {\r\n      this.loading = true\r\n      try {\r\n        const response = await clubCultureAPI.getClubCultures()\r\n        this.cultures = response.data\r\n      } catch (error) {\r\n        console.error('加载社团文化失败:', error)\r\n        // 如果API调用失败，显示示例数据\r\n        this.cultures = [\r\n          {\r\n            id: 1,\r\n            title: '社团成立历程',\r\n            content: '<p>网络信息安全社团成立于2020年，致力于培养学生的网络安全意识和技能。</p>',\r\n            images: [],\r\n            created_at: new Date().toISOString()\r\n          }\r\n        ]\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    purifyContent(content) {\r\n      return DOMPurify.sanitize(content)\r\n    },\r\n\r\n    formatDate(dateString) {\r\n      if (!dateString) return ''\r\n      const date = new Date(dateString)\r\n      return date.toLocaleDateString('zh-CN')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.club-culture {\r\n  padding: 20px;\r\n}\r\nimg {\r\n  display: block;\r\n}\r\n</style>"], "mappings": "AAoFA,OAAOA,SAAQ,MAAO,WAAU;AAChC,SAASC,cAAa,QAAS,iBAAgB;AAE/C,eAAe;EACbC,IAAI,EAAE,aAAa;EACnBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,QAAQ,EAAE,EAAE;MACZC,OAAO,EAAE;IACX;EACF,CAAC;EACD,MAAMC,OAAOA,CAAA,EAAG;IACd,MAAM,IAAI,CAACC,YAAY,CAAC;EAC1B,CAAC;EACDC,OAAO,EAAE;IACP,MAAMD,YAAYA,CAAA,EAAG;MACnB,IAAI,CAACF,OAAM,GAAI,IAAG;MAClB,IAAI;QACF,MAAMI,QAAO,GAAI,MAAMR,cAAc,CAACS,eAAe,CAAC;QACtD,IAAI,CAACN,QAAO,GAAIK,QAAQ,CAACN,IAAG;MAC9B,EAAE,OAAOQ,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC;QACA,IAAI,CAACP,QAAO,GAAI,CACd;UACES,EAAE,EAAE,CAAC;UACLC,KAAK,EAAE,QAAQ;UACfC,OAAO,EAAE,4CAA4C;UACrDC,MAAM,EAAE,EAAE;UACVC,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QACrC,EACF;MACF,UAAU;QACR,IAAI,CAACd,OAAM,GAAI,KAAI;MACrB;IACF,CAAC;IAEDe,aAAaA,CAACL,OAAO,EAAE;MACrB,OAAOf,SAAS,CAACqB,QAAQ,CAACN,OAAO;IACnC,CAAC;IAEDO,UAAUA,CAACC,UAAU,EAAE;MACrB,IAAI,CAACA,UAAU,EAAE,OAAO,EAAC;MACzB,MAAMC,IAAG,GAAI,IAAIN,IAAI,CAACK,UAAU;MAChC,OAAOC,IAAI,CAACC,kBAAkB,CAAC,OAAO;IACxC;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}