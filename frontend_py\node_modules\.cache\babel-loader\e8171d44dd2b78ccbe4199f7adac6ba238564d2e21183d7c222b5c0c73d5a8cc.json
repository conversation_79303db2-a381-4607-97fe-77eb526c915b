{"ast": null, "code": "import DOMPurify from 'dompurify'; // 引入 DOMPurify\n\nexport default {\n  name: 'PastActivities',\n  data() {\n    return {\n      // 示例：往期活动列表，实际应从后端获取\n      activities: [{\n        id: 1,\n        title: '2023 年网络安全周',\n        description: '<p>回顾 2023 年网络安全周的**精彩瞬间**。</p><img src=\"invalid.png\" onerror=\"alert(\\'XSS in activity!\\')\">'\n      }, {\n        id: 2,\n        title: 'CTF 比赛回顾',\n        description: '<p>社团成员在 CTF 比赛中的表现。</p>'\n      }]\n    };\n  },\n  methods: {\n    purifyContent(content) {\n      return DOMPurify.sanitize(content);\n    }\n  }\n};", "map": {"version": 3, "names": ["DOMPurify", "name", "data", "activities", "id", "title", "description", "methods", "purifyContent", "content", "sanitize"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\PastActivities.vue"], "sourcesContent": ["<template>\r\n  <div class=\"past-activities\">\r\n    <h1>往期活动回顾</h1>\r\n    <p>这里展示社团过往活动的图片、视频、文字记录。</p>\r\n    <div class=\"activities-list\">\r\n      <div v-for=\"activity in activities\" :key=\"activity.id\" class=\"activity-item\">\r\n        <h3>{{ activity.title }}</h3>\r\n        <div v-html=\"purifyContent(activity.description)\"></div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport DOMPurify from 'dompurify'; // 引入 DOMPurify\r\n\r\nexport default {\r\n  name: 'PastActivities',\r\n  data() {\r\n    return {\r\n      // 示例：往期活动列表，实际应从后端获取\r\n      activities: [\r\n        { id: 1, title: '2023 年网络安全周', description: '<p>回顾 2023 年网络安全周的**精彩瞬间**。</p><img src=\"invalid.png\" onerror=\"alert(\\'XSS in activity!\\')\">' },\r\n        { id: 2, title: 'CTF 比赛回顾', description: '<p>社团成员在 CTF 比赛中的表现。</p>' },\r\n      ],\r\n    };\r\n  },\r\n  methods: {\r\n    purifyContent(content) {\r\n      return DOMPurify.sanitize(content);\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.past-activities {\r\n  padding: 20px;\r\n}\r\n</style>"], "mappings": "AAcA,OAAOA,SAAQ,MAAO,WAAW,EAAE;;AAEnC,eAAe;EACbC,IAAI,EAAE,gBAAgB;EACtBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACL;MACAC,UAAU,EAAE,CACV;QAAEC,EAAE,EAAE,CAAC;QAAEC,KAAK,EAAE,aAAa;QAAEC,WAAW,EAAE;MAA+F,CAAC,EAC5I;QAAEF,EAAE,EAAE,CAAC;QAAEC,KAAK,EAAE,UAAU;QAAEC,WAAW,EAAE;MAA2B,CAAC;IAEzE,CAAC;EACH,CAAC;EACDC,OAAO,EAAE;IACPC,aAAaA,CAACC,OAAO,EAAE;MACrB,OAAOT,SAAS,CAACU,QAAQ,CAACD,OAAO,CAAC;IACpC;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}