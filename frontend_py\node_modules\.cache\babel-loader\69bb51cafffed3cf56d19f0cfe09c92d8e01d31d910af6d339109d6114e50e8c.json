{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport axios from 'axios';\nexport default {\n  name: 'Login',\n  data() {\n    return {\n      username: '',\n      password: '',\n      error: null\n    };\n  },\n  methods: {\n    async login() {\n      this.error = null; // 清除之前的错误信息\n\n      // 前端输入验证\n      if (!this.username) {\n        this.error = '用户名不能为空。';\n        return;\n      }\n      if (!this.password) {\n        this.error = '密码不能为空。';\n        return;\n      }\n      try {\n        const res = await axios.post('http://localhost:5000/api/auth/login', {\n          username: this.username,\n          password: this.password\n        });\n        // 登录成功后，token 会通过 HttpOnly cookie 自动设置，前端无需手动处理\n        this.$router.push('/'); // 示例：跳转到首页\n      } catch (err) {\n        this.error = err.response.data.msg || '登录失败，请检查用户名和密码。';\n        console.error(err);\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["axios", "name", "data", "username", "password", "error", "methods", "login", "res", "post", "$router", "push", "err", "response", "msg", "console"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Login.vue"], "sourcesContent": ["<template>\r\n  <div class=\"login\">\r\n    <h1>用户登录</h1>\r\n    <form @submit.prevent=\"login\">\r\n      <div class=\"form-group\">\r\n        <label for=\"username\">用户名:</label>\r\n        <input type=\"text\" id=\"username\" v-model=\"username\" required>\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"password\">密码:</label>\r\n        <input type=\"password\" id=\"password\" v-model=\"password\" required>\r\n      </div>\r\n      <button type=\"submit\">登录</button>\r\n    </form>\r\n    <p v-if=\"error\" class=\"error-message\">{{ error }}</p>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport axios from 'axios';\r\n\r\nexport default {\r\n  name: 'Login',\r\n  data() {\r\n    return {\r\n      username: '',\r\n      password: '',\r\n      error: null,\r\n    };\r\n  },\r\n  methods: {\r\n    async login() {\r\n      this.error = null; // 清除之前的错误信息\r\n\r\n      // 前端输入验证\r\n      if (!this.username) {\r\n        this.error = '用户名不能为空。';\r\n        return;\r\n      }\r\n      if (!this.password) {\r\n        this.error = '密码不能为空。';\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const res = await axios.post('http://localhost:5000/api/auth/login', {\r\n          username: this.username,\r\n          password: this.password,\r\n        });\r\n        // 登录成功后，token 会通过 HttpOnly cookie 自动设置，前端无需手动处理\r\n        this.$router.push('/'); // 示例：跳转到首页\r\n      } catch (err) {\r\n        this.error = err.response.data.msg || '登录失败，请检查用户名和密码。';\r\n        console.error(err);\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.login {\r\n  padding: 20px;\r\n  max-width: 400px;\r\n  margin: 50px auto;\r\n  border: 1px solid #ccc;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.form-group {\r\n  margin-bottom: 15px;\r\n  text-align: left;\r\n}\r\n\r\n.form-group label {\r\n  display: block;\r\n  margin-bottom: 5px;\r\n  font-weight: bold;\r\n}\r\n\r\n.form-group input[type=\"text\"],\r\n.form-group input[type=\"password\"] {\r\n  width: 100%;\r\n  padding: 10px;\r\n  border: 1px solid #ddd;\r\n  border-radius: 4px;\r\n  box-sizing: border-box; /* Ensures padding doesn't increase overall width */\r\n}\r\n\r\nbutton {\r\n  background-color: #42b983;\r\n  color: white;\r\n  padding: 10px 15px;\r\n  border: none;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  font-size: 16px;\r\n}\r\n\r\nbutton:hover {\r\n  background-color: #369f6e;\r\n}\r\n\r\n.error-message {\r\n  color: red;\r\n  margin-top: 10px;\r\n}\r\n</style>"], "mappings": ";AAmBA,OAAOA,KAAI,MAAO,OAAO;AAEzB,eAAe;EACbC,IAAI,EAAE,OAAO;EACbC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE;IACT,CAAC;EACH,CAAC;EACDC,OAAO,EAAE;IACP,MAAMC,KAAKA,CAAA,EAAG;MACZ,IAAI,CAACF,KAAI,GAAI,IAAI,EAAE;;MAEnB;MACA,IAAI,CAAC,IAAI,CAACF,QAAQ,EAAE;QAClB,IAAI,CAACE,KAAI,GAAI,UAAU;QACvB;MACF;MACA,IAAI,CAAC,IAAI,CAACD,QAAQ,EAAE;QAClB,IAAI,CAACC,KAAI,GAAI,SAAS;QACtB;MACF;MAEA,IAAI;QACF,MAAMG,GAAE,GAAI,MAAMR,KAAK,CAACS,IAAI,CAAC,sCAAsC,EAAE;UACnEN,QAAQ,EAAE,IAAI,CAACA,QAAQ;UACvBC,QAAQ,EAAE,IAAI,CAACA;QACjB,CAAC,CAAC;QACF;QACA,IAAI,CAACM,OAAO,CAACC,IAAI,CAAC,GAAG,CAAC,EAAE;MAC1B,EAAE,OAAOC,GAAG,EAAE;QACZ,IAAI,CAACP,KAAI,GAAIO,GAAG,CAACC,QAAQ,CAACX,IAAI,CAACY,GAAE,IAAK,iBAAiB;QACvDC,OAAO,CAACV,KAAK,CAACO,GAAG,CAAC;MACpB;IACF;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}