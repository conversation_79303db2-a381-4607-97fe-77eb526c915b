{"ast": null, "code": "import { createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"simple-test\"\n};\nconst _hoisted_2 = {\n  class: \"test-section\"\n};\nconst _hoisted_3 = {\n  key: 0,\n  class: \"result\"\n};\nconst _hoisted_4 = {\n  class: \"test-section\"\n};\nconst _hoisted_5 = {\n  key: 0,\n  class: \"result\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[4] || (_cache[4] = _createElementVNode(\"h1\", null, \"简单API测试\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_2, [_cache[2] || (_cache[2] = _createElementVNode(\"h2\", null, \"测试后端连接\", -1 /* CACHED */)), _createElementVNode(\"button\", {\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.testBackend && $options.testBackend(...args))\n  }, \"测试后端\"), $data.backendResult ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, [_createElementVNode(\"pre\", null, _toDisplayString($data.backendResult), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_4, [_cache[3] || (_cache[3] = _createElementVNode(\"h2\", null, \"测试登录API\", -1 /* CACHED */)), _createElementVNode(\"button\", {\n    onClick: _cache[1] || (_cache[1] = (...args) => $options.testLogin && $options.testLogin(...args))\n  }, \"测试登录\"), $data.loginResult ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [_createElementVNode(\"pre\", null, _toDisplayString($data.loginResult), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "onClick", "_cache", "args", "$options", "testBackend", "$data", "backendResult", "_hoisted_3", "_toDisplayString", "_hoisted_4", "testLogin", "loginResult", "_hoisted_5"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\SimpleTest.vue"], "sourcesContent": ["<template>\n  <div class=\"simple-test\">\n    <h1>简单API测试</h1>\n    \n    <div class=\"test-section\">\n      <h2>测试后端连接</h2>\n      <button @click=\"testBackend\">测试后端</button>\n      <div v-if=\"backendResult\" class=\"result\">\n        <pre>{{ backendResult }}</pre>\n      </div>\n    </div>\n    \n    <div class=\"test-section\">\n      <h2>测试登录API</h2>\n      <button @click=\"testLogin\">测试登录</button>\n      <div v-if=\"loginResult\" class=\"result\">\n        <pre>{{ loginResult }}</pre>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'SimpleTest',\n  data() {\n    return {\n      backendResult: null,\n      loginResult: null\n    }\n  },\n  methods: {\n    async testBackend() {\n      try {\n        const response = await fetch('http://localhost:5000/')\n        const data = await response.json()\n        this.backendResult = JSON.stringify(data, null, 2)\n      } catch (error) {\n        this.backendResult = `错误: ${error.message}`\n      }\n    },\n    \n    async testLogin() {\n      try {\n        const response = await fetch('http://localhost:5000/api/auth/login', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          credentials: 'include',\n          body: JSON.stringify({\n            username: 'admin',\n            password: 'Admin123!'\n          })\n        })\n        \n        const data = await response.json()\n        this.loginResult = `状态: ${response.status}\\n数据: ${JSON.stringify(data, null, 2)}`\n      } catch (error) {\n        this.loginResult = `错误: ${error.message}`\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.simple-test {\n  padding: 20px;\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.test-section {\n  margin-bottom: 30px;\n  padding: 20px;\n  border: 1px solid #ddd;\n  border-radius: 8px;\n}\n\nbutton {\n  background-color: #42b983;\n  color: white;\n  padding: 10px 20px;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n}\n\nbutton:hover {\n  background-color: #369f6e;\n}\n\n.result {\n  margin-top: 15px;\n  padding: 10px;\n  background-color: #f5f5f5;\n  border-radius: 4px;\n  border: 1px solid #ddd;\n}\n\npre {\n  white-space: pre-wrap;\n  word-wrap: break-word;\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAa;;EAGjBA,KAAK,EAAC;AAAc;;;EAGGA,KAAK,EAAC;;;EAK7BA,KAAK,EAAC;AAAc;;;EAGCA,KAAK,EAAC;;;uBAdlCC,mBAAA,CAkBM,OAlBNC,UAkBM,G,0BAjBJC,mBAAA,CAAgB,YAAZ,SAAO,qBAEXA,mBAAA,CAMM,OANNC,UAMM,G,0BALJD,mBAAA,CAAe,YAAX,QAAM,qBACVA,mBAAA,CAA0C;IAAjCE,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAC,WAAA,IAAAD,QAAA,CAAAC,WAAA,IAAAF,IAAA,CAAW;KAAE,MAAI,GACtBG,KAAA,CAAAC,aAAa,I,cAAxBV,mBAAA,CAEM,OAFNW,UAEM,GADJT,mBAAA,CAA8B,aAAAU,gBAAA,CAAtBH,KAAA,CAAAC,aAAa,iB,0CAIzBR,mBAAA,CAMM,OANNW,UAMM,G,0BALJX,mBAAA,CAAgB,YAAZ,SAAO,qBACXA,mBAAA,CAAwC;IAA/BE,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAO,SAAA,IAAAP,QAAA,CAAAO,SAAA,IAAAR,IAAA,CAAS;KAAE,MAAI,GACpBG,KAAA,CAAAM,WAAW,I,cAAtBf,mBAAA,CAEM,OAFNgB,UAEM,GADJd,mBAAA,CAA4B,aAAAU,gBAAA,CAApBH,KAAA,CAAAM,WAAW,iB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}