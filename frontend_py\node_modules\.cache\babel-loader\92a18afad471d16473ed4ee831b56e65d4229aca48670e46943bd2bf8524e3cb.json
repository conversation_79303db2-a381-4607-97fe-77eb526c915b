{"ast": null, "code": "import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, renderList as _renderList, Fragment as _Fragment } from \"vue\";\nconst _hoisted_1 = {\n  class: \"cyber-security-news\"\n};\nconst _hoisted_2 = {\n  key: 0,\n  class: \"loading\"\n};\nconst _hoisted_3 = {\n  key: 1,\n  class: \"error\"\n};\nconst _hoisted_4 = {\n  key: 2,\n  class: \"news-list\"\n};\nconst _hoisted_5 = {\n  key: 0,\n  class: \"no-data\"\n};\nconst _hoisted_6 = {\n  key: 1\n};\nconst _hoisted_7 = {\n  class: \"news-content\"\n};\nconst _hoisted_8 = [\"innerHTML\"];\nconst _hoisted_9 = {\n  class: \"news-meta\"\n};\nconst _hoisted_10 = {\n  key: 0,\n  class: \"source-link\"\n};\nconst _hoisted_11 = [\"href\"];\nconst _hoisted_12 = {\n  class: \"dates\"\n};\nconst _hoisted_13 = {\n  key: 0\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[3] || (_cache[3] = _createElementVNode(\"h1\", null, \"网安资讯\", -1 /* CACHED */)), _cache[4] || (_cache[4] = _createElementVNode(\"p\", null, \"这里发布最新的网络安全行业新闻、技术动态、漏洞信息等。\", -1 /* CACHED */)), $data.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, _cache[1] || (_cache[1] = [_createElementVNode(\"p\", null, \"加载中...\", -1 /* CACHED */)]))) : $data.error ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, [_createElementVNode(\"p\", null, _toDisplayString($data.error), 1 /* TEXT */), _createElementVNode(\"button\", {\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.loadNews && $options.loadNews(...args)),\n    class: \"btn btn-primary\"\n  }, \"重试\")])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [$data.newsList.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, _cache[2] || (_cache[2] = [_createElementVNode(\"p\", null, \"暂无网安资讯\", -1 /* CACHED */)]))) : (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.newsList, newsItem => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: newsItem.id,\n      class: \"news-item\"\n    }, [_createElementVNode(\"h3\", null, _toDisplayString(newsItem.title), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", {\n      innerHTML: $options.purifyContent(newsItem.content)\n    }, null, 8 /* PROPS */, _hoisted_8)]), _createElementVNode(\"div\", _hoisted_9, [newsItem.source_url ? (_openBlock(), _createElementBlock(\"div\", _hoisted_10, [_createElementVNode(\"a\", {\n      href: newsItem.source_url,\n      target: \"_blank\",\n      rel: \"noopener noreferrer\"\n    }, \"查看原文\", 8 /* PROPS */, _hoisted_11)])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_12, [newsItem.published_date ? (_openBlock(), _createElementBlock(\"span\", _hoisted_13, \"发布时间: \" + _toDisplayString($options.formatDate(newsItem.published_date)), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"span\", null, \"创建时间: \" + _toDisplayString($options.formatDate(newsItem.created_at)), 1 /* TEXT */)])])]);\n  }), 128 /* KEYED_FRAGMENT */))]))]))]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "$data", "loading", "_hoisted_2", "_cache", "error", "_hoisted_3", "_toDisplayString", "onClick", "args", "$options", "loadNews", "_hoisted_4", "newsList", "length", "_hoisted_5", "_hoisted_6", "_Fragment", "_renderList", "newsItem", "key", "id", "title", "_hoisted_7", "innerHTML", "purifyContent", "content", "_hoisted_9", "source_url", "_hoisted_10", "href", "target", "rel", "_hoisted_11", "_hoisted_12", "published_date", "_hoisted_13", "formatDate", "created_at"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\CyberSecurityNews.vue"], "sourcesContent": ["<template>\r\n  <div class=\"cyber-security-news\">\r\n    <h1>网安资讯</h1>\r\n    <p>这里发布最新的网络安全行业新闻、技术动态、漏洞信息等。</p>\r\n\r\n    <div v-if=\"loading\" class=\"loading\">\r\n      <p>加载中...</p>\r\n    </div>\r\n\r\n    <div v-else-if=\"error\" class=\"error\">\r\n      <p>{{ error }}</p>\r\n      <button @click=\"loadNews\" class=\"btn btn-primary\">重试</button>\r\n    </div>\r\n\r\n    <div v-else class=\"news-list\">\r\n      <div v-if=\"newsList.length === 0\" class=\"no-data\">\r\n        <p>暂无网安资讯</p>\r\n      </div>\r\n      <div v-else>\r\n        <div v-for=\"newsItem in newsList\" :key=\"newsItem.id\" class=\"news-item\">\r\n          <h3>{{ newsItem.title }}</h3>\r\n          <div class=\"news-content\">\r\n            <div v-html=\"purifyContent(newsItem.content)\"></div>\r\n          </div>\r\n          <div class=\"news-meta\">\r\n            <div v-if=\"newsItem.source_url\" class=\"source-link\">\r\n              <a :href=\"newsItem.source_url\" target=\"_blank\" rel=\"noopener noreferrer\">查看原文</a>\r\n            </div>\r\n            <div class=\"dates\">\r\n              <span v-if=\"newsItem.published_date\">发布时间: {{ formatDate(newsItem.published_date) }}</span>\r\n              <span>创建时间: {{ formatDate(newsItem.created_at) }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport DOMPurify from 'dompurify'\r\nimport { cyberSecurityNewsAPI } from '../services/api'\r\n\r\nexport default {\r\n  name: 'CyberSecurityNews',\r\n  data() {\r\n    return {\r\n      newsList: [],\r\n      loading: false,\r\n      error: null\r\n    }\r\n  },\r\n  async mounted() {\r\n    await this.loadNews()\r\n  },\r\n  methods: {\r\n    async loadNews() {\r\n      this.loading = true\r\n      this.error = null\r\n      try {\r\n        const response = await cyberSecurityNewsAPI.getCyberSecurityNews()\r\n        this.newsList = response.data\r\n      } catch (error) {\r\n        console.error('加载网安资讯失败:', error)\r\n        this.error = '加载网安资讯失败，请稍后重试'\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    purifyContent(content) {\r\n      return DOMPurify.sanitize(content)\r\n    },\r\n\r\n    formatDate(dateString) {\r\n      if (!dateString) return ''\r\n      const date = new Date(dateString)\r\n      return date.toLocaleDateString('zh-CN')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.cyber-security-news {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 20px;\r\n}\r\n\r\n.loading, .error, .no-data {\r\n  text-align: center;\r\n  padding: 40px;\r\n  color: #666;\r\n}\r\n\r\n.error {\r\n  color: #e74c3c;\r\n}\r\n\r\n.news-list {\r\n  margin-top: 20px;\r\n}\r\n\r\n.news-item {\r\n  background: white;\r\n  border: 1px solid #ddd;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  margin-bottom: 20px;\r\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\r\n}\r\n\r\n.news-item h3 {\r\n  color: #2c3e50;\r\n  margin-bottom: 15px;\r\n  border-bottom: 2px solid #f39c12;\r\n  padding-bottom: 10px;\r\n}\r\n\r\n.news-content {\r\n  margin: 15px 0;\r\n  line-height: 1.6;\r\n}\r\n\r\n.news-meta {\r\n  margin-top: 15px;\r\n  padding-top: 15px;\r\n  border-top: 1px solid #eee;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.source-link a {\r\n  color: #f39c12;\r\n  text-decoration: none;\r\n  font-weight: bold;\r\n}\r\n\r\n.source-link a:hover {\r\n  text-decoration: underline;\r\n}\r\n\r\n.dates {\r\n  color: #666;\r\n  font-size: 14px;\r\n}\r\n\r\n.dates span {\r\n  margin-left: 15px;\r\n}\r\n\r\n.dates span:first-child {\r\n  margin-left: 0;\r\n}\r\n\r\n.btn {\r\n  display: inline-block;\r\n  padding: 8px 16px;\r\n  border: none;\r\n  border-radius: 4px;\r\n  text-decoration: none;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  background-color: #f39c12;\r\n  color: white;\r\n}\r\n\r\n.btn:hover {\r\n  background-color: #e67e22;\r\n}\r\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAqB;;;EAIVA,KAAK,EAAC;;;;EAIHA,KAAK,EAAC;;;;EAKjBA,KAAK,EAAC;;;;EACkBA,KAAK,EAAC;;;;;;EAM/BA,KAAK,EAAC;AAAc;;;EAGpBA,KAAK,EAAC;AAAW;;;EACYA,KAAK,EAAC;;;;EAGjCA,KAAK,EAAC;AAAO;;;;;uBA3B5BC,mBAAA,CAmCM,OAnCNC,UAmCM,G,0BAlCJC,mBAAA,CAAa,YAAT,MAAI,qB,0BACRA,mBAAA,CAAkC,WAA/B,6BAA2B,qBAEnBC,KAAA,CAAAC,OAAO,I,cAAlBJ,mBAAA,CAEM,OAFNK,UAEM,EAAAC,MAAA,QAAAA,MAAA,OADJJ,mBAAA,CAAa,WAAV,QAAM,mB,MAGKC,KAAA,CAAAI,KAAK,I,cAArBP,mBAAA,CAGM,OAHNQ,UAGM,GAFJN,mBAAA,CAAkB,WAAAO,gBAAA,CAAZN,KAAA,CAAAI,KAAK,kBACXL,mBAAA,CAA6D;IAApDQ,OAAK,EAAAJ,MAAA,QAAAA,MAAA,UAAAK,IAAA,KAAEC,QAAA,CAAAC,QAAA,IAAAD,QAAA,CAAAC,QAAA,IAAAF,IAAA,CAAQ;IAAEZ,KAAK,EAAC;KAAkB,IAAE,E,oBAGtDC,mBAAA,CAqBM,OArBNc,UAqBM,GApBOX,KAAA,CAAAY,QAAQ,CAACC,MAAM,U,cAA1BhB,mBAAA,CAEM,OAFNiB,UAEM,EAAAX,MAAA,QAAAA,MAAA,OADJJ,mBAAA,CAAa,WAAV,QAAM,mB,qBAEXF,mBAAA,CAgBM,OAAAkB,UAAA,I,kBAfJlB,mBAAA,CAcMmB,SAAA,QAAAC,WAAA,CAdkBjB,KAAA,CAAAY,QAAQ,EAApBM,QAAQ;yBAApBrB,mBAAA,CAcM;MAd6BsB,GAAG,EAAED,QAAQ,CAACE,EAAE;MAAExB,KAAK,EAAC;QACzDG,mBAAA,CAA6B,YAAAO,gBAAA,CAAtBY,QAAQ,CAACG,KAAK,kBACrBtB,mBAAA,CAEM,OAFNuB,UAEM,GADJvB,mBAAA,CAAoD;MAA/CwB,SAAwC,EAAhCd,QAAA,CAAAe,aAAa,CAACN,QAAQ,CAACO,OAAO;2CAE7C1B,mBAAA,CAQM,OARN2B,UAQM,GAPOR,QAAQ,CAACS,UAAU,I,cAA9B9B,mBAAA,CAEM,OAFN+B,WAEM,GADJ7B,mBAAA,CAAiF;MAA7E8B,IAAI,EAAEX,QAAQ,CAACS,UAAU;MAAEG,MAAM,EAAC,QAAQ;MAACC,GAAG,EAAC;OAAsB,MAAI,iBAAAC,WAAA,E,wCAE/EjC,mBAAA,CAGM,OAHNkC,WAGM,GAFQf,QAAQ,CAACgB,cAAc,I,cAAnCrC,mBAAA,CAA2F,QAAAsC,WAAA,EAAtD,QAAM,GAAA7B,gBAAA,CAAGG,QAAA,CAAA2B,UAAU,CAAClB,QAAQ,CAACgB,cAAc,qB,mCAChFnC,mBAAA,CAAwD,cAAlD,QAAM,GAAAO,gBAAA,CAAGG,QAAA,CAAA2B,UAAU,CAAClB,QAAQ,CAACmB,UAAU,kB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}