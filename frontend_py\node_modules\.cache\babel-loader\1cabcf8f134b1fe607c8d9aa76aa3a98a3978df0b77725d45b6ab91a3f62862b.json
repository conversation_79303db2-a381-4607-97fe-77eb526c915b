{"ast": null, "code": "import { createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString } from \"vue\";\nconst _hoisted_1 = {\n  class: \"cyber-security-news\"\n};\nconst _hoisted_2 = {\n  class: \"news-list\"\n};\nconst _hoisted_3 = [\"innerHTML\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[0] || (_cache[0] = _createElementVNode(\"h1\", null, \"网安资讯\", -1 /* CACHED */)), _cache[1] || (_cache[1] = _createElementVNode(\"p\", null, \"这里发布最新的网络安全行业新闻、技术动态、漏洞信息等。\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_2, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.newsList, newsItem => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: newsItem.id,\n      class: \"news-item\"\n    }, [_createElementVNode(\"h3\", null, _toDisplayString(newsItem.title), 1 /* TEXT */), _createElementVNode(\"div\", {\n      innerHTML: $options.purifyContent(newsItem.content)\n    }, null, 8 /* PROPS */, _hoisted_3)]);\n  }), 128 /* KEYED_FRAGMENT */))])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_Fragment", "_renderList", "$data", "newsList", "newsItem", "key", "id", "_toDisplayString", "title", "innerHTML", "$options", "purifyContent", "content"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\CyberSecurityNews.vue"], "sourcesContent": ["<template>\r\n  <div class=\"cyber-security-news\">\r\n    <h1>网安资讯</h1>\r\n    <p>这里发布最新的网络安全行业新闻、技术动态、漏洞信息等。</p>\r\n    <div class=\"news-list\">\r\n      <div v-for=\"newsItem in newsList\" :key=\"newsItem.id\" class=\"news-item\">\r\n        <h3>{{ newsItem.title }}</h3>\r\n        <div v-html=\"purifyContent(newsItem.content)\"></div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport DOMPurify from 'dompurify'; // 引入 DOMPurify\r\n\r\nexport default {\r\n  name: 'CyberSecurityNews',\r\n  data() {\r\n    return {\r\n      // 示例：网安资讯列表，实际应从后端获取\r\n      newsList: [\r\n        { id: 1, title: '最新漏洞预警', content: '<p>关于最新发现的**安全漏洞**的预警信息。</p><img src=\"invalid.png\" onerror=\"alert(\\'XSS in news!\\')\">' },\r\n        { id: 2, title: '网络安全行业趋势', content: '<p>分析当前网络安全行业的发展趋势。</p>' },\r\n      ],\r\n    };\r\n  },\r\n  methods: {\r\n    purifyContent(content) {\r\n      return DOMPurify.sanitize(content);\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.cyber-security-news {\r\n  padding: 20px;\r\n}\r\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAqB;;EAGzBA,KAAK,EAAC;AAAW;;;uBAHxBC,mBAAA,CASM,OATNC,UASM,G,0BARJC,mBAAA,CAAa,YAAT,MAAI,qB,0BACRA,mBAAA,CAAkC,WAA/B,6BAA2B,qBAC9BA,mBAAA,CAKM,OALNC,UAKM,I,kBAJJH,mBAAA,CAGMI,SAAA,QAAAC,WAAA,CAHkBC,KAAA,CAAAC,QAAQ,EAApBC,QAAQ;yBAApBR,mBAAA,CAGM;MAH6BS,GAAG,EAAED,QAAQ,CAACE,EAAE;MAAEX,KAAK,EAAC;QACzDG,mBAAA,CAA6B,YAAAS,gBAAA,CAAtBH,QAAQ,CAACI,KAAK,kBACrBV,mBAAA,CAAoD;MAA/CW,SAAwC,EAAhCC,QAAA,CAAAC,aAAa,CAACP,QAAQ,CAACQ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}