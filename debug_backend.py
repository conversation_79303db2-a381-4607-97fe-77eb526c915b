import sys
import os
sys.path.insert(0, 'python_backend')

# 设置环境变量
os.environ['DATABASE_URL'] = 'sqlite:///nis_club.db'
os.environ['JWT_SECRET_KEY'] = 'supersecretjwtkey'
os.environ['SECRET_KEY'] = 'supersecretflaskkey'

try:
    from app import app
    
    print("✅ 应用导入成功")
    
    # 测试数据库连接
    with app.app_context():
        from models import db, User
        print("✅ 数据库模型导入成功")
        
        # 创建所有表
        db.create_all()
        print("✅ 数据库表创建成功")
        
        # 检查管理员用户
        admin = User.query.filter_by(username='admin').first()
        if admin:
            print(f"✅ 管理员用户已存在: {admin.username}")
            print(f"   密码哈希: {admin.password_hash[:20]}...")
        else:
            print("❌ 管理员用户不存在")
            
        # 测试密码验证
        if admin:
            test_password = "Admin123!"
            is_valid = admin.check_password(test_password)
            print(f"✅ 密码验证测试: {'通过' if is_valid else '失败'}")
        
except ImportError as e:
    print(f"❌ 导入错误: {e}")
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()
