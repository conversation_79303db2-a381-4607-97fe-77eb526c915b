{"ast": null, "code": "import DOMPurify from 'dompurify'; // 引入 DOMPurify\n\nexport default {\n  name: 'ClubCulture',\n  data() {\n    return {\n      // 示例：社团文化介绍，实际应从后端获取\n      cultureContent: '<p>这里是NIS社团的**详细介绍**、宗旨、历史、吉祥物等信息。</p><p>我们致力于网络安全技术的学习与交流。</p><img src=\"invalid-image.jpg\" onerror=\"alert(\\'XSS in image!\\')\">'\n    };\n  },\n  computed: {\n    purifiedCultureContent() {\n      return DOMPurify.sanitize(this.cultureContent);\n    }\n  }\n};", "map": {"version": 3, "names": ["DOMPurify", "name", "data", "cultureContent", "computed", "purifiedCultureContent", "sanitize"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\ClubCulture.vue"], "sourcesContent": ["<template>\r\n  <div class=\"club-culture\">\r\n    <div class=\"hero-section\">\r\n      <h1>社团文化</h1>\r\n      <p class=\"hero-subtitle\">传承网络安全精神，培养技术人才</p>\r\n    </div>\r\n\r\n    <!-- 社团介绍 -->\r\n    <div class=\"section\">\r\n      <div class=\"intro-card\">\r\n        <div class=\"intro-content\">\r\n          <h2>网络信息安全社团</h2>\r\n          <div class=\"intro-details\">\r\n            <p><strong>学校：</strong>成都工业职业技术学院（金堂校区）</p>\r\n            <p><strong>社团群号：</strong>242050951</p>\r\n            <p><strong>抖音：</strong>21647629167</p>\r\n            <p><strong>主要活动：</strong>组网技术，网络攻防，安全科普</p>\r\n          </div>\r\n        </div>\r\n        <div class=\"intro-image\">\r\n          <img src=\"@/assets/logo.png\" alt=\"NIS 社团标志\" />\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 社团文化内容 -->\r\n    <div class=\"section\">\r\n      <h2>社团文化内容</h2>\r\n      <div v-if=\"loading\" class=\"loading\">加载中...</div>\r\n      <div v-else-if=\"cultures.length === 0\" class=\"no-data\">暂无文化内容</div>\r\n      <div v-else class=\"culture-grid\">\r\n        <div v-for=\"culture in cultures\" :key=\"culture.id\" class=\"culture-card\">\r\n          <h3>{{ culture.title }}</h3>\r\n          <div class=\"culture-content\" v-html=\"purifyContent(culture.content)\"></div>\r\n          <div v-if=\"culture.images && culture.images.length > 0\" class=\"culture-images\">\r\n            <img v-for=\"(image, index) in culture.images\" :key=\"index\" :src=\"image\" :alt=\"culture.title\" />\r\n          </div>\r\n          <p class=\"culture-date\">{{ formatDate(culture.created_at) }}</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 社团价值观 -->\r\n    <div class=\"section values-section\">\r\n      <h2>社团价值观</h2>\r\n      <div class=\"values-grid\">\r\n        <div class=\"value-card\">\r\n          <div class=\"value-icon\">🛡️</div>\r\n          <h3>安全第一</h3>\r\n          <p>始终将网络安全放在首位，保护数字世界的安全</p>\r\n        </div>\r\n        <div class=\"value-card\">\r\n          <div class=\"value-icon\">🤝</div>\r\n          <h3>团队协作</h3>\r\n          <p>相互学习，共同进步，打造强大的技术团队</p>\r\n        </div>\r\n        <div class=\"value-card\">\r\n          <div class=\"value-icon\">💡</div>\r\n          <h3>创新思维</h3>\r\n          <p>勇于探索新技术，创新解决方案</p>\r\n        </div>\r\n        <div class=\"value-card\">\r\n          <div class=\"value-icon\">📚</div>\r\n          <h3>持续学习</h3>\r\n          <p>保持学习热情，跟上技术发展步伐</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 加入我们 -->\r\n    <div class=\"section join-section\">\r\n      <h2>加入我们</h2>\r\n      <div class=\"join-content\">\r\n        <p>如果你对网络安全感兴趣，想要学习相关技术，欢迎加入我们的大家庭！</p>\r\n        <div class=\"join-actions\">\r\n          <router-link to=\"/register\" class=\"btn btn-primary\">立即注册</router-link>\r\n          <a href=\"https://qm.qq.com/cgi-bin/qm/qr?k=242050951\" target=\"_blank\" class=\"btn btn-secondary\">加入QQ群</a>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport DOMPurify from 'dompurify'; // 引入 DOMPurify\r\n\r\nexport default {\r\n  name: 'ClubCulture',\r\n  data() {\r\n    return {\r\n      // 示例：社团文化介绍，实际应从后端获取\r\n      cultureContent: '<p>这里是NIS社团的**详细介绍**、宗旨、历史、吉祥物等信息。</p><p>我们致力于网络安全技术的学习与交流。</p><img src=\"invalid-image.jpg\" onerror=\"alert(\\'XSS in image!\\')\">',\r\n    };\r\n  },\r\n  computed: {\r\n    purifiedCultureContent() {\r\n      return DOMPurify.sanitize(this.cultureContent);\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.club-culture {\r\n  padding: 20px;\r\n}\r\nimg {\r\n  display: block;\r\n}\r\n</style>"], "mappings": "AAoFA,OAAOA,SAAQ,MAAO,WAAW,EAAE;;AAEnC,eAAe;EACbC,IAAI,EAAE,aAAa;EACnBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACL;MACAC,cAAc,EAAE;IAClB,CAAC;EACH,CAAC;EACDC,QAAQ,EAAE;IACRC,sBAAsBA,CAAA,EAAG;MACvB,OAAOL,SAAS,CAACM,QAAQ,CAAC,IAAI,CAACH,cAAc,CAAC;IAChD;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}