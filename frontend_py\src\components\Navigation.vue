<template>
  <nav class="navigation">
    <div class="nav-container">
      <div class="nav-brand">
        <router-link to="/" class="brand-link">
          <img src="@/assets/logo.png" alt="NIS" class="brand-logo">
          <span class="brand-text">NIS 社团</span>
        </router-link>
      </div>
      
      <div class="nav-menu" :class="{ active: menuOpen }">
        <router-link to="/" class="nav-link">首页</router-link>
        <router-link to="/club-culture" class="nav-link">社团文化</router-link>
        <router-link to="/learning-resources" class="nav-link">学习资源</router-link>
        <router-link to="/past-activities" class="nav-link">过往活动</router-link>
        <router-link to="/cyber-security-news" class="nav-link">安全资讯</router-link>
        
        <div v-if="currentUser" class="nav-user">
          <router-link to="/dashboard" class="nav-link">仪表板</router-link>
          <router-link to="/playground" class="nav-link">练习场</router-link>
          <router-link v-if="currentUser.role === 'admin'" to="/admin" class="nav-link admin-link">管理后台</router-link>
          <button @click="logout" class="nav-link logout-btn">退出</button>
        </div>
        <div v-else class="nav-auth">
          <router-link to="/login" class="nav-link">登录</router-link>
          <router-link to="/register" class="nav-link register-btn">注册</router-link>
        </div>
      </div>
      
      <button class="nav-toggle" @click="menuOpen = !menuOpen">
        <span></span>
        <span></span>
        <span></span>
      </button>
    </div>
  </nav>
</template>

<script>
import { authAPI } from '../services/api'

export default {
  name: 'Navigation',
  data() {
    return {
      currentUser: null,
      menuOpen: false
    }
  },
  async mounted() {
    await this.checkAuth()
  },
  methods: {
    async checkAuth() {
      try {
        const response = await authAPI.checkAuth()
        this.currentUser = response.data.user
      } catch (error) {
        this.currentUser = null
      }
    },
    
    async logout() {
      try {
        await authAPI.logout()
        this.currentUser = null
        this.$router.push('/')
      } catch (error) {
        console.error('登出失败:', error)
      }
    }
  },
  watch: {
    $route() {
      this.menuOpen = false
      this.checkAuth()
    }
  }
}
</script>

<style scoped>
.navigation {
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 70px;
}

.nav-brand {
  display: flex;
  align-items: center;
}

.brand-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #333;
}

.brand-logo {
  width: 40px;
  height: 40px;
  margin-right: 10px;
}

.brand-text {
  font-size: 1.5rem;
  font-weight: bold;
  color: #42b983;
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: 20px;
}

.nav-link {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 4px;
  transition: all 0.3s ease;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 14px;
}

.nav-link:hover {
  color: #42b983;
  background-color: #f8f9fa;
}

.nav-link.router-link-active {
  color: #42b983;
  background-color: #e8f5e8;
}

.admin-link {
  background-color: #ff6b6b !important;
  color: white !important;
}

.admin-link:hover {
  background-color: #ee5a24 !important;
}

.register-btn {
  background-color: #42b983;
  color: white;
}

.register-btn:hover {
  background-color: #369f6e;
}

.logout-btn {
  background-color: #6c757d;
  color: white;
}

.logout-btn:hover {
  background-color: #545b62;
}

.nav-user, .nav-auth {
  display: flex;
  align-items: center;
  gap: 10px;
}

.nav-toggle {
  display: none;
  flex-direction: column;
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
}

.nav-toggle span {
  width: 25px;
  height: 3px;
  background-color: #333;
  margin: 3px 0;
  transition: 0.3s;
}

@media (max-width: 768px) {
  .nav-menu {
    position: fixed;
    top: 70px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 70px);
    background: white;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding-top: 50px;
    transition: left 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .nav-menu.active {
    left: 0;
  }
  
  .nav-link {
    padding: 15px 30px;
    width: 200px;
    text-align: center;
    margin-bottom: 10px;
  }
  
  .nav-toggle {
    display: flex;
  }
  
  .nav-user, .nav-auth {
    flex-direction: column;
    gap: 10px;
    margin-top: 20px;
  }
}
</style>
