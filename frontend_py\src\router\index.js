import { createRouter, createWebHistory } from 'vue-router'
import { authAPI } from '../services/api'
import Home from '../views/Home.vue'
import ClubCulture from '../views/ClubCulture.vue'
import LearningResources from '../views/LearningResources.vue'
import PastActivities from '../views/PastActivities.vue'
import CyberSecurityNews from '../views/CyberSecurityNews.vue'
import Login from '../views/Login.vue'
import Register from '../views/Register.vue'
import Admin from '../views/Admin.vue'
import Playground from '../views/Playground.vue'
import Dashboard from '../views/Dashboard.vue'
import Test from '../views/Test.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
  },
  {
    path: '/club-culture',
    name: 'ClubCulture',
    component: ClubCulture,
  },
  {
    path: '/learning-resources',
    name: 'LearningResources',
    component: LearningResources,
  },
  {
    path: '/past-activities',
    name: 'PastActivities',
    component: PastActivities,
  },
  {
    path: '/cyber-security-news',
    name: 'CyberSecurityNews',
    component: CyberSecurityNews,
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
  },
  {
    path: '/register',
    name: 'Register',
    component: Register,
  },
  {
    path: '/admin',
    name: 'Admin',
    component: Admin,
    meta: { requiresAuth: true, authorize: ['admin'] }, // 需要认证且是管理员
  },
  {
    path: '/playground',
    name: 'Playground',
    component: Playground,
    meta: { requiresAuth: true }, // 需要认证
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    meta: { requiresAuth: true }, // 需要认证
  },
  {
    path: '/test',
    name: 'Test',
    component: Test,
  },
];

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes,
});

router.beforeEach(async (to, from, next) => {
  const publicPages = ['/login', '/register', '/', '/club-culture', '/learning-resources', '/past-activities', '/cyber-security-news', '/test']
  const authRequired = !publicPages.includes(to.path)

  if (authRequired) {
    try {
      // 使用 API 服务检查认证状态
      const response = await authAPI.checkAuth()

      if (response.status === 200) {
        // 认证成功，获取用户角色并检查权限
        const userRole = response.data.user.role

        if (to.meta.authorize && !to.meta.authorize.includes(userRole)) {
          return next('/') // 无权限，重定向到首页
        }
      }
    } catch (error) {
      if (error.response) {
        if (error.response.status === 401) {
          // 未认证，重定向到登录页
          return next('/login')
        } else if (error.response.status === 403) {
          // 未授权，重定向到首页
          return next('/')
        }
      } else {
        console.error('认证检查失败:', error)
        // 网络错误或后端不可达，也重定向到登录页
        return next('/login')
      }
    }
  }

  next() // 继续导航
})

export default router