import { createRouter, createWebHistory } from 'vue-router';
import Home from '../views/Home.vue';
import ClubCulture from '../views/ClubCulture.vue';
import LearningResources from '../views/LearningResources.vue';
import PastActivities from '../views/PastActivities.vue';
import CyberSecurityNews from '../views/CyberSecurityNews.vue';
import Login from '../views/Login.vue';
import Admin from '../views/Admin.vue';
import Playground from '../views/Playground.vue';
import Dashboard from '../views/Dashboard.vue';

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
  },
  {
    path: '/club-culture',
    name: 'ClubCulture',
    component: ClubCulture,
  },
  {
    path: '/learning-resources',
    name: 'LearningResources',
    component: LearningResources,
  },
  {
    path: '/past-activities',
    name: 'PastActivities',
    component: PastActivities,
  },
  {
    path: '/cyber-security-news',
    name: 'CyberSecurityNews',
    component: CyberSecurityNews,
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
  },
  {
    path: '/admin',
    name: 'Admin',
    component: Admin,
    meta: { requiresAuth: true, authorize: ['admin'] }, // 需要认证且是管理员
  },
  {
    path: '/playground',
    name: 'Playground',
    component: Playground,
    meta: { requiresAuth: true }, // 需要认证
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    meta: { requiresAuth: true }, // 需要认证
  },
];

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes,
});

router.beforeEach(async (to, from, next) => {
  const publicPages = ['/login', '/', '/club-culture', '/learning-resources', '/past-activities', '/cyber-security-news'];
  const authRequired = !publicPages.includes(to.path);

  if (authRequired) {
    try {
      // 尝试访问一个受保护的后端路由，让后端通过 cookie 验证
      // 例如，可以请求一个简单的 /api/auth/check-auth 路由
      // 如果后端返回 401/403，则表示未认证/未授权
      const response = await fetch('http://localhost:5000/api/auth/check-auth', {
        credentials: 'include' // 确保发送 cookie
      });

      if (response.status === 401) {
        // 未认证，重定向到登录页
        return next('/login');
      } else if (response.status === 403) {
        // 未授权，重定向到首页
        return next('/');
      } else if (response.ok) {
        // 认证成功，获取用户角色并检查权限
        const data = await response.json();
        const userRole = data.user.role; // 假设后端返回用户角色

        if (to.meta.authorize && !to.meta.authorize.includes(userRole)) {
          return next('/'); // 无权限，重定向到首页
        }
      }
    } catch (error) {
      console.error('认证检查失败:', error);
      // 网络错误或后端不可达，也重定向到登录页
      return next('/login');
    }
  }

  next(); // 继续导航
});

// 添加一个简单的后端路由用于前端检查认证状态
// 在 Python 后端 (app.py 或 auth.py) 中需要添加对应的路由
// 例如:
// @auth_bp.route('/check-auth', methods=['GET'])
// @token_required
// def check_auth():
//     return jsonify({'msg': '认证成功', 'user': {'id': request.user.id, 'role': request.user.role}}), 200

export default router;