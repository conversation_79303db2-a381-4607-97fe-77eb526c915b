{"ast": null, "code": "'use strict';\n\nimport \"core-js/modules/es.array.push.js\";\nimport CanceledError from './CanceledError.js';\n\n/**\n * A `CancelToken` is an object that can be used to request cancellation of an operation.\n *\n * @param {Function} executor The executor function.\n *\n * @returns {CancelToken}\n */\nclass CancelToken {\n  constructor(executor) {\n    if (typeof executor !== 'function') {\n      throw new TypeError('executor must be a function.');\n    }\n    let resolvePromise;\n    this.promise = new Promise(function promiseExecutor(resolve) {\n      resolvePromise = resolve;\n    });\n    const token = this;\n\n    // eslint-disable-next-line func-names\n    this.promise.then(cancel => {\n      if (!token._listeners) return;\n      let i = token._listeners.length;\n      while (i-- > 0) {\n        token._listeners[i](cancel);\n      }\n      token._listeners = null;\n    });\n\n    // eslint-disable-next-line func-names\n    this.promise.then = onfulfilled => {\n      let _resolve;\n      // eslint-disable-next-line func-names\n      const promise = new Promise(resolve => {\n        token.subscribe(resolve);\n        _resolve = resolve;\n      }).then(onfulfilled);\n      promise.cancel = function reject() {\n        token.unsubscribe(_resolve);\n      };\n      return promise;\n    };\n    executor(function cancel(message, config, request) {\n      if (token.reason) {\n        // Cancellation has already been requested\n        return;\n      }\n      token.reason = new CanceledError(message, config, request);\n      resolvePromise(token.reason);\n    });\n  }\n\n  /**\n   * Throws a `CanceledError` if cancellation has been requested.\n   */\n  throwIfRequested() {\n    if (this.reason) {\n      throw this.reason;\n    }\n  }\n\n  /**\n   * Subscribe to the cancel signal\n   */\n\n  subscribe(listener) {\n    if (this.reason) {\n      listener(this.reason);\n      return;\n    }\n    if (this._listeners) {\n      this._listeners.push(listener);\n    } else {\n      this._listeners = [listener];\n    }\n  }\n\n  /**\n   * Unsubscribe from the cancel signal\n   */\n\n  unsubscribe(listener) {\n    if (!this._listeners) {\n      return;\n    }\n    const index = this._listeners.indexOf(listener);\n    if (index !== -1) {\n      this._listeners.splice(index, 1);\n    }\n  }\n  toAbortSignal() {\n    const controller = new AbortController();\n    const abort = err => {\n      controller.abort(err);\n    };\n    this.subscribe(abort);\n    controller.signal.unsubscribe = () => this.unsubscribe(abort);\n    return controller.signal;\n  }\n\n  /**\n   * Returns an object that contains a new `CancelToken` and a function that, when called,\n   * cancels the `CancelToken`.\n   */\n  static source() {\n    let cancel;\n    const token = new CancelToken(function executor(c) {\n      cancel = c;\n    });\n    return {\n      token,\n      cancel\n    };\n  }\n}\nexport default CancelToken;", "map": {"version": 3, "names": ["CanceledError", "CancelToken", "constructor", "executor", "TypeError", "resolvePromise", "promise", "Promise", "promiseExecutor", "resolve", "token", "then", "cancel", "_listeners", "i", "length", "onfulfilled", "_resolve", "subscribe", "reject", "unsubscribe", "message", "config", "request", "reason", "throwIfRequested", "listener", "push", "index", "indexOf", "splice", "toAbortSignal", "controller", "AbortController", "abort", "err", "signal", "source", "c"], "sources": ["C:/Users/<USER>/Desktop/cs/pacong_py/frontend_py/node_modules/axios/lib/cancel/CancelToken.js"], "sourcesContent": ["'use strict';\n\nimport CanceledError from './CanceledError.js';\n\n/**\n * A `CancelToken` is an object that can be used to request cancellation of an operation.\n *\n * @param {Function} executor The executor function.\n *\n * @returns {CancelToken}\n */\nclass CancelToken {\n  constructor(executor) {\n    if (typeof executor !== 'function') {\n      throw new TypeError('executor must be a function.');\n    }\n\n    let resolvePromise;\n\n    this.promise = new Promise(function promiseExecutor(resolve) {\n      resolvePromise = resolve;\n    });\n\n    const token = this;\n\n    // eslint-disable-next-line func-names\n    this.promise.then(cancel => {\n      if (!token._listeners) return;\n\n      let i = token._listeners.length;\n\n      while (i-- > 0) {\n        token._listeners[i](cancel);\n      }\n      token._listeners = null;\n    });\n\n    // eslint-disable-next-line func-names\n    this.promise.then = onfulfilled => {\n      let _resolve;\n      // eslint-disable-next-line func-names\n      const promise = new Promise(resolve => {\n        token.subscribe(resolve);\n        _resolve = resolve;\n      }).then(onfulfilled);\n\n      promise.cancel = function reject() {\n        token.unsubscribe(_resolve);\n      };\n\n      return promise;\n    };\n\n    executor(function cancel(message, config, request) {\n      if (token.reason) {\n        // Cancellation has already been requested\n        return;\n      }\n\n      token.reason = new CanceledError(message, config, request);\n      resolvePromise(token.reason);\n    });\n  }\n\n  /**\n   * Throws a `CanceledError` if cancellation has been requested.\n   */\n  throwIfRequested() {\n    if (this.reason) {\n      throw this.reason;\n    }\n  }\n\n  /**\n   * Subscribe to the cancel signal\n   */\n\n  subscribe(listener) {\n    if (this.reason) {\n      listener(this.reason);\n      return;\n    }\n\n    if (this._listeners) {\n      this._listeners.push(listener);\n    } else {\n      this._listeners = [listener];\n    }\n  }\n\n  /**\n   * Unsubscribe from the cancel signal\n   */\n\n  unsubscribe(listener) {\n    if (!this._listeners) {\n      return;\n    }\n    const index = this._listeners.indexOf(listener);\n    if (index !== -1) {\n      this._listeners.splice(index, 1);\n    }\n  }\n\n  toAbortSignal() {\n    const controller = new AbortController();\n\n    const abort = (err) => {\n      controller.abort(err);\n    };\n\n    this.subscribe(abort);\n\n    controller.signal.unsubscribe = () => this.unsubscribe(abort);\n\n    return controller.signal;\n  }\n\n  /**\n   * Returns an object that contains a new `CancelToken` and a function that, when called,\n   * cancels the `CancelToken`.\n   */\n  static source() {\n    let cancel;\n    const token = new CancelToken(function executor(c) {\n      cancel = c;\n    });\n    return {\n      token,\n      cancel\n    };\n  }\n}\n\nexport default CancelToken;\n"], "mappings": "AAAA,YAAY;;AAAC;AAEb,OAAOA,aAAa,MAAM,oBAAoB;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,CAAC;EAChBC,WAAWA,CAACC,QAAQ,EAAE;IACpB,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE;MAClC,MAAM,IAAIC,SAAS,CAAC,8BAA8B,CAAC;IACrD;IAEA,IAAIC,cAAc;IAElB,IAAI,CAACC,OAAO,GAAG,IAAIC,OAAO,CAAC,SAASC,eAAeA,CAACC,OAAO,EAAE;MAC3DJ,cAAc,GAAGI,OAAO;IAC1B,CAAC,CAAC;IAEF,MAAMC,KAAK,GAAG,IAAI;;IAElB;IACA,IAAI,CAACJ,OAAO,CAACK,IAAI,CAACC,MAAM,IAAI;MAC1B,IAAI,CAACF,KAAK,CAACG,UAAU,EAAE;MAEvB,IAAIC,CAAC,GAAGJ,KAAK,CAACG,UAAU,CAACE,MAAM;MAE/B,OAAOD,CAAC,EAAE,GAAG,CAAC,EAAE;QACdJ,KAAK,CAACG,UAAU,CAACC,CAAC,CAAC,CAACF,MAAM,CAAC;MAC7B;MACAF,KAAK,CAACG,UAAU,GAAG,IAAI;IACzB,CAAC,CAAC;;IAEF;IACA,IAAI,CAACP,OAAO,CAACK,IAAI,GAAGK,WAAW,IAAI;MACjC,IAAIC,QAAQ;MACZ;MACA,MAAMX,OAAO,GAAG,IAAIC,OAAO,CAACE,OAAO,IAAI;QACrCC,KAAK,CAACQ,SAAS,CAACT,OAAO,CAAC;QACxBQ,QAAQ,GAAGR,OAAO;MACpB,CAAC,CAAC,CAACE,IAAI,CAACK,WAAW,CAAC;MAEpBV,OAAO,CAACM,MAAM,GAAG,SAASO,MAAMA,CAAA,EAAG;QACjCT,KAAK,CAACU,WAAW,CAACH,QAAQ,CAAC;MAC7B,CAAC;MAED,OAAOX,OAAO;IAChB,CAAC;IAEDH,QAAQ,CAAC,SAASS,MAAMA,CAACS,OAAO,EAAEC,MAAM,EAAEC,OAAO,EAAE;MACjD,IAAIb,KAAK,CAACc,MAAM,EAAE;QAChB;QACA;MACF;MAEAd,KAAK,CAACc,MAAM,GAAG,IAAIxB,aAAa,CAACqB,OAAO,EAAEC,MAAM,EAAEC,OAAO,CAAC;MAC1DlB,cAAc,CAACK,KAAK,CAACc,MAAM,CAAC;IAC9B,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;EACEC,gBAAgBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACD,MAAM,EAAE;MACf,MAAM,IAAI,CAACA,MAAM;IACnB;EACF;;EAEA;AACF;AACA;;EAEEN,SAASA,CAACQ,QAAQ,EAAE;IAClB,IAAI,IAAI,CAACF,MAAM,EAAE;MACfE,QAAQ,CAAC,IAAI,CAACF,MAAM,CAAC;MACrB;IACF;IAEA,IAAI,IAAI,CAACX,UAAU,EAAE;MACnB,IAAI,CAACA,UAAU,CAACc,IAAI,CAACD,QAAQ,CAAC;IAChC,CAAC,MAAM;MACL,IAAI,CAACb,UAAU,GAAG,CAACa,QAAQ,CAAC;IAC9B;EACF;;EAEA;AACF;AACA;;EAEEN,WAAWA,CAACM,QAAQ,EAAE;IACpB,IAAI,CAAC,IAAI,CAACb,UAAU,EAAE;MACpB;IACF;IACA,MAAMe,KAAK,GAAG,IAAI,CAACf,UAAU,CAACgB,OAAO,CAACH,QAAQ,CAAC;IAC/C,IAAIE,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,IAAI,CAACf,UAAU,CAACiB,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;IAClC;EACF;EAEAG,aAAaA,CAAA,EAAG;IACd,MAAMC,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;IAExC,MAAMC,KAAK,GAAIC,GAAG,IAAK;MACrBH,UAAU,CAACE,KAAK,CAACC,GAAG,CAAC;IACvB,CAAC;IAED,IAAI,CAACjB,SAAS,CAACgB,KAAK,CAAC;IAErBF,UAAU,CAACI,MAAM,CAAChB,WAAW,GAAG,MAAM,IAAI,CAACA,WAAW,CAACc,KAAK,CAAC;IAE7D,OAAOF,UAAU,CAACI,MAAM;EAC1B;;EAEA;AACF;AACA;AACA;EACE,OAAOC,MAAMA,CAAA,EAAG;IACd,IAAIzB,MAAM;IACV,MAAMF,KAAK,GAAG,IAAIT,WAAW,CAAC,SAASE,QAAQA,CAACmC,CAAC,EAAE;MACjD1B,MAAM,GAAG0B,CAAC;IACZ,CAAC,CAAC;IACF,OAAO;MACL5B,KAAK;MACLE;IACF,CAAC;EACH;AACF;AAEA,eAAeX,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}