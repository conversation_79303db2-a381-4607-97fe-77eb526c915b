{"ast": null, "code": "import DOMPurify from 'dompurify'; // 引入 DOMPurify\n\nexport default {\n  name: 'CyberSecurityNews',\n  data() {\n    return {\n      // 示例：网安资讯列表，实际应从后端获取\n      newsList: [{\n        id: 1,\n        title: '最新漏洞预警',\n        content: '<p>关于最新发现的**安全漏洞**的预警信息。</p><img src=\"invalid.png\" onerror=\"alert(\\'XSS in news!\\')\">'\n      }, {\n        id: 2,\n        title: '网络安全行业趋势',\n        content: '<p>分析当前网络安全行业的发展趋势。</p>'\n      }]\n    };\n  },\n  methods: {\n    purifyContent(content) {\n      return DOMPurify.sanitize(content);\n    }\n  }\n};", "map": {"version": 3, "names": ["DOMPurify", "name", "data", "newsList", "id", "title", "content", "methods", "purifyContent", "sanitize"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\CyberSecurityNews.vue"], "sourcesContent": ["<template>\r\n  <div class=\"cyber-security-news\">\r\n    <h1>网安资讯</h1>\r\n    <p>这里发布最新的网络安全行业新闻、技术动态、漏洞信息等。</p>\r\n    <div class=\"news-list\">\r\n      <div v-for=\"newsItem in newsList\" :key=\"newsItem.id\" class=\"news-item\">\r\n        <h3>{{ newsItem.title }}</h3>\r\n        <div v-html=\"purifyContent(newsItem.content)\"></div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport DOMPurify from 'dompurify'; // 引入 DOMPurify\r\n\r\nexport default {\r\n  name: 'CyberSecurityNews',\r\n  data() {\r\n    return {\r\n      // 示例：网安资讯列表，实际应从后端获取\r\n      newsList: [\r\n        { id: 1, title: '最新漏洞预警', content: '<p>关于最新发现的**安全漏洞**的预警信息。</p><img src=\"invalid.png\" onerror=\"alert(\\'XSS in news!\\')\">' },\r\n        { id: 2, title: '网络安全行业趋势', content: '<p>分析当前网络安全行业的发展趋势。</p>' },\r\n      ],\r\n    };\r\n  },\r\n  methods: {\r\n    purifyContent(content) {\r\n      return DOMPurify.sanitize(content);\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.cyber-security-news {\r\n  padding: 20px;\r\n}\r\n</style>"], "mappings": "AAcA,OAAOA,SAAQ,MAAO,WAAW,EAAE;;AAEnC,eAAe;EACbC,IAAI,EAAE,mBAAmB;EACzBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACL;MACAC,QAAQ,EAAE,CACR;QAAEC,EAAE,EAAE,CAAC;QAAEC,KAAK,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAwF,CAAC,EAC5H;QAAEF,EAAE,EAAE,CAAC;QAAEC,KAAK,EAAE,UAAU;QAAEC,OAAO,EAAE;MAA0B,CAAC;IAEpE,CAAC;EACH,CAAC;EACDC,OAAO,EAAE;IACPC,aAAaA,CAACF,OAAO,EAAE;MACrB,OAAON,SAAS,CAACS,QAAQ,CAACH,OAAO,CAAC;IACpC;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}