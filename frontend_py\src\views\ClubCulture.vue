<template>
  <div class="club-culture">
    <div class="hero-section">
      <h1>社团文化</h1>
      <p class="hero-subtitle">传承网络安全精神，培养技术人才</p>
    </div>

    <!-- 社团介绍 -->
    <div class="section">
      <div class="intro-card">
        <div class="intro-content">
          <h2>网络信息安全社团</h2>
          <div class="intro-details">
            <p><strong>学校：</strong>成都工业职业技术学院（金堂校区）</p>
            <p><strong>社团群号：</strong>242050951</p>
            <p><strong>抖音：</strong>21647629167</p>
            <p><strong>主要活动：</strong>组网技术，网络攻防，安全科普</p>
          </div>
        </div>
        <div class="intro-image">
          <img src="@/assets/logo.png" alt="NIS 社团标志" />
        </div>
      </div>
    </div>

    <!-- 社团文化内容 -->
    <div class="section">
      <h2>社团文化内容</h2>
      <div v-if="loading" class="loading">加载中...</div>
      <div v-else-if="cultures.length === 0" class="no-data">暂无文化内容</div>
      <div v-else class="culture-grid">
        <div v-for="culture in cultures" :key="culture.id" class="culture-card">
          <h3>{{ culture.title }}</h3>
          <div class="culture-content" v-html="purifyContent(culture.content)"></div>
          <div v-if="culture.images && culture.images.length > 0" class="culture-images">
            <img v-for="(image, index) in culture.images" :key="index" :src="image" :alt="culture.title" />
          </div>
          <p class="culture-date">{{ formatDate(culture.created_at) }}</p>
        </div>
      </div>
    </div>

    <!-- 社团价值观 -->
    <div class="section values-section">
      <h2>社团价值观</h2>
      <div class="values-grid">
        <div class="value-card">
          <div class="value-icon">🛡️</div>
          <h3>安全第一</h3>
          <p>始终将网络安全放在首位，保护数字世界的安全</p>
        </div>
        <div class="value-card">
          <div class="value-icon">🤝</div>
          <h3>团队协作</h3>
          <p>相互学习，共同进步，打造强大的技术团队</p>
        </div>
        <div class="value-card">
          <div class="value-icon">💡</div>
          <h3>创新思维</h3>
          <p>勇于探索新技术，创新解决方案</p>
        </div>
        <div class="value-card">
          <div class="value-icon">📚</div>
          <h3>持续学习</h3>
          <p>保持学习热情，跟上技术发展步伐</p>
        </div>
      </div>
    </div>

    <!-- 加入我们 -->
    <div class="section join-section">
      <h2>加入我们</h2>
      <div class="join-content">
        <p>如果你对网络安全感兴趣，想要学习相关技术，欢迎加入我们的大家庭！</p>
        <div class="join-actions">
          <router-link to="/register" class="btn btn-primary">立即注册</router-link>
          <a href="https://qm.qq.com/cgi-bin/qm/qr?k=242050951" target="_blank" class="btn btn-secondary">加入QQ群</a>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DOMPurify from 'dompurify'
import { clubCultureAPI } from '../services/api'

export default {
  name: 'ClubCulture',
  data() {
    return {
      cultures: [],
      loading: false
    }
  },
  async mounted() {
    await this.loadCultures()
  },
  methods: {
    async loadCultures() {
      this.loading = true
      try {
        const response = await clubCultureAPI.getClubCultures()
        this.cultures = response.data
      } catch (error) {
        console.error('加载社团文化失败:', error)
        // 如果API调用失败，显示示例数据
        this.cultures = [
          {
            id: 1,
            title: '社团成立历程',
            content: '<p>网络信息安全社团成立于2020年，致力于培养学生的网络安全意识和技能。</p>',
            images: [],
            created_at: new Date().toISOString()
          }
        ]
      } finally {
        this.loading = false
      }
    },

    purifyContent(content) {
      return DOMPurify.sanitize(content)
    },

    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    }
  }
}
</script>

<style scoped>
.club-culture {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.hero-section {
  text-align: center;
  padding: 60px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  margin-bottom: 40px;
}

.hero-section h1 {
  font-size: 3rem;
  margin-bottom: 10px;
  font-weight: bold;
}

.hero-subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
}

.section {
  margin-bottom: 40px;
}

.section h2 {
  font-size: 2rem;
  margin-bottom: 20px;
  color: #333;
  border-bottom: 3px solid #42b983;
  padding-bottom: 10px;
}

.intro-card {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 40px;
  align-items: center;
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.intro-content h2 {
  border: none;
  margin-bottom: 20px;
  color: #42b983;
}

.intro-details p {
  margin-bottom: 10px;
  font-size: 1.1rem;
  line-height: 1.6;
}

.intro-image img {
  width: 100%;
  max-width: 300px;
  height: auto;
  border-radius: 8px;
  display: block;
}

.culture-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 30px;
}

.culture-card {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #42b983;
}

.culture-card h3 {
  color: #333;
  margin-bottom: 15px;
  font-size: 1.5rem;
}

.culture-content {
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
}

.culture-date {
  color: #999;
  font-size: 0.9rem;
  text-align: right;
  margin: 0;
}

.values-section {
  background: #f8f9fa;
  padding: 40px;
  border-radius: 12px;
}

.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  margin-top: 20px;
}

.value-card {
  background: white;
  padding: 30px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.value-card:hover {
  transform: translateY(-5px);
}

.value-icon {
  font-size: 3rem;
  margin-bottom: 15px;
}

.value-card h3 {
  color: #333;
  margin-bottom: 15px;
}

.value-card p {
  color: #666;
  line-height: 1.6;
}

.join-section {
  background: linear-gradient(135deg, #42b983 0%, #369f6e 100%);
  color: white;
  padding: 40px;
  border-radius: 12px;
  text-align: center;
}

.join-section h2 {
  color: white;
  border-color: white;
}

.join-content p {
  font-size: 1.2rem;
  margin-bottom: 30px;
  opacity: 0.9;
}

.join-actions {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.btn {
  padding: 12px 24px;
  border-radius: 6px;
  text-decoration: none;
  font-weight: bold;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: white;
  color: #42b983;
}

.btn-primary:hover {
  background-color: #f8f9fa;
  transform: translateY(-2px);
}

.btn-secondary {
  background-color: transparent;
  color: white;
  border: 2px solid white;
}

.btn-secondary:hover {
  background-color: white;
  color: #42b983;
}

.loading, .no-data {
  text-align: center;
  padding: 40px;
  color: #666;
}

@media (max-width: 768px) {
  .hero-section h1 {
    font-size: 2rem;
  }

  .intro-card {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .culture-grid {
    grid-template-columns: 1fr;
  }

  .values-grid {
    grid-template-columns: 1fr;
  }

  .join-actions {
    flex-direction: column;
    align-items: center;
  }
}
</style>