<template>
  <div class="club-culture">
    <h1>社团文化</h1>
    <div class="culture-description" v-html="purifiedCultureContent"></div>
    <!-- 社团吉祥物图片 -->
    <img src="@/assets/nis_mascot.png" alt="NIS 社团吉祥物" style="max-width: 300px; margin: 20px auto;">
    <p>学校：成都工业职业技术学院（金堂校区）</p>
    <p>社团群号：242050951</p>
    <p>抖音：21647629167</p>
    <p>主要活动内容：组网技术，网络攻防，安全科普</p>
    <!-- 这里可以添加更多社团文化内容，考虑折叠规置信息 -->
  </div>
</template>

<script>
import DOMPurify from 'dompurify'; // 引入 DOMPurify

export default {
  name: 'ClubCulture',
  data() {
    return {
      // 示例：社团文化介绍，实际应从后端获取
      cultureContent: '<p>这里是NIS社团的**详细介绍**、宗旨、历史、吉祥物等信息。</p><p>我们致力于网络安全技术的学习与交流。</p><img src="invalid-image.jpg" onerror="alert(\'XSS in image!\')">',
    };
  },
  computed: {
    purifiedCultureContent() {
      return DOMPurify.sanitize(this.cultureContent);
    },
  },
}
</script>

<style scoped>
.club-culture {
  padding: 20px;
}
img {
  display: block;
}
</style>