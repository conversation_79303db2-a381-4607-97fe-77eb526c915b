{"ast": null, "code": "import DOMPurify from 'dompurify'; // 引入 DOMPurify\n\nexport default {\n  name: 'ClubCulture',\n  data() {\n    return {\n      // 示例：社团文化介绍，实际应从后端获取\n      cultureContent: '<p>这里是NIS社团的**详细介绍**、宗旨、历史、吉祥物等信息。</p><p>我们致力于网络安全技术的学习与交流。</p><img src=\"invalid-image.jpg\" onerror=\"alert(\\'XSS in image!\\')\">'\n    };\n  },\n  computed: {\n    purifiedCultureContent() {\n      return DOMPurify.sanitize(this.cultureContent);\n    }\n  }\n};", "map": {"version": 3, "names": ["DOMPurify", "name", "data", "cultureContent", "computed", "purifiedCultureContent", "sanitize"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\ClubCulture.vue"], "sourcesContent": ["<template>\r\n  <div class=\"club-culture\">\r\n    <h1>社团文化</h1>\r\n    <div class=\"culture-description\" v-html=\"purifiedCultureContent\"></div>\r\n    <!-- 社团吉祥物图片 -->\r\n    <img src=\"@/assets/logo.png\" alt=\"NIS 社团吉祥物\" style=\"max-width: 300px; margin: 20px auto;\">\r\n    <p>学校：成都工业职业技术学院（金堂校区）</p>\r\n    <p>社团群号：242050951</p>\r\n    <p>抖音：21647629167</p>\r\n    <p>主要活动内容：组网技术，网络攻防，安全科普</p>\r\n    <!-- 这里可以添加更多社团文化内容，考虑折叠规置信息 -->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport DOMPurify from 'dompurify'; // 引入 DOMPurify\r\n\r\nexport default {\r\n  name: 'ClubCulture',\r\n  data() {\r\n    return {\r\n      // 示例：社团文化介绍，实际应从后端获取\r\n      cultureContent: '<p>这里是NIS社团的**详细介绍**、宗旨、历史、吉祥物等信息。</p><p>我们致力于网络安全技术的学习与交流。</p><img src=\"invalid-image.jpg\" onerror=\"alert(\\'XSS in image!\\')\">',\r\n    };\r\n  },\r\n  computed: {\r\n    purifiedCultureContent() {\r\n      return DOMPurify.sanitize(this.cultureContent);\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.club-culture {\r\n  padding: 20px;\r\n}\r\nimg {\r\n  display: block;\r\n}\r\n</style>"], "mappings": "AAeA,OAAOA,SAAQ,MAAO,WAAW,EAAE;;AAEnC,eAAe;EACbC,IAAI,EAAE,aAAa;EACnBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACL;MACAC,cAAc,EAAE;IAClB,CAAC;EACH,CAAC;EACDC,QAAQ,EAAE;IACRC,sBAAsBA,CAAA,EAAG;MACvB,OAAOL,SAAS,CAACM,QAAQ,CAAC,IAAI,CAACH,cAAc,CAAC;IAChD;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}