import os
import sys
import json

# 设置环境变量
os.environ['DATABASE_URL'] = 'sqlite:///nis_club.db'
os.environ['JWT_SECRET_KEY'] = 'supersecretjwtkey'
os.environ['SECRET_KEY'] = 'supersecretflaskkey'

# 切换到python_backend目录
os.chdir('python_backend')
sys.path.insert(0, '.')

try:
    from app import app
    from models import db, User
    
    print("=" * 60)
    print("调试登录功能...")
    print("=" * 60)
    
    with app.app_context():
        print("1. 检查数据库连接...")
        try:
            db.create_all()
            print("   ✅ 数据库连接成功")
        except Exception as e:
            print(f"   ❌ 数据库连接失败: {e}")
            raise
        
        print("2. 检查管理员用户...")
        admin = User.query.filter_by(username='admin').first()
        if admin:
            print(f"   ✅ 找到管理员用户: {admin.username}")
            print(f"   ✅ 用户ID: {admin.id}")
            print(f"   ✅ 用户角色: {admin.role}")
            
            # 测试密码验证
            test_password = "Admin123!"
            is_valid = admin.check_password(test_password)
            print(f"   ✅ 密码验证: {'通过' if is_valid else '失败'}")
            
            if is_valid:
                print("3. 测试JWT生成...")
                import jwt
                from datetime import datetime, timedelta
                
                payload = {
                    'user': {
                        'id': admin.id,
                        'role': admin.role
                    },
                    'exp': datetime.utcnow() + timedelta(hours=1)
                }
                
                try:
                    token = jwt.encode(payload, app.config['JWT_SECRET_KEY'], algorithm='HS256')
                    print(f"   ✅ JWT生成成功: {token[:50]}...")
                    
                    # 测试JWT解码
                    decoded = jwt.decode(token, app.config['JWT_SECRET_KEY'], algorithms=['HS256'])
                    print(f"   ✅ JWT解码成功: 用户ID={decoded['user']['id']}")
                    
                except Exception as e:
                    print(f"   ❌ JWT生成失败: {e}")
                    raise
            else:
                print("   ❌ 密码验证失败")
        else:
            print("   ❌ 管理员用户不存在")
            
            # 创建管理员用户
            print("   创建管理员用户...")
            admin = User(
                username='admin',
                student_id='ADMIN001',
                name='系统管理员',
                role='admin'
            )
            admin.set_password('Admin123!')
            db.session.add(admin)
            db.session.commit()
            print("   ✅ 管理员用户创建成功")
    
    print("\n🎉 所有测试通过！登录功能正常")
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    import traceback
    traceback.print_exc()
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()
