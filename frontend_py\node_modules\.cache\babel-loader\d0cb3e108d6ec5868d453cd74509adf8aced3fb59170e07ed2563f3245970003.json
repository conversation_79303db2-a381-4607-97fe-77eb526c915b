{"ast": null, "code": "export default {\n  name: 'Dashboard'\n};", "map": {"version": 3, "names": ["name"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Dashboard.vue"], "sourcesContent": ["<template>\r\n  <div class=\"dashboard\">\r\n    <h1>监控数据大屏</h1>\r\n    <p>这里是监控数据大屏的预留区域，用于记录靶场漏洞告警信息，功能正在开发中，敬请期待！</p>\r\n    <!-- 监控数据大屏框架内容 -->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'Dashboard',\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.dashboard {\r\n  padding: 20px;\r\n}\r\n</style>"], "mappings": "AASA,eAAe;EACbA,IAAI,EAAE;AACR", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}