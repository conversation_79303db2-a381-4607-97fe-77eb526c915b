{"ast": null, "code": "export default {\n  name: 'SimpleTest',\n  data() {\n    return {\n      backendResult: null,\n      loginResult: null\n    };\n  },\n  methods: {\n    async testBackend() {\n      try {\n        const response = await fetch('http://localhost:5000/');\n        const data = await response.json();\n        this.backendResult = JSON.stringify(data, null, 2);\n      } catch (error) {\n        this.backendResult = `错误: ${error.message}`;\n      }\n    },\n    async testLogin() {\n      try {\n        const response = await fetch('http://localhost:5000/api/auth/login', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          credentials: 'include',\n          body: JSON.stringify({\n            username: 'admin',\n            password: 'Admin123!'\n          })\n        });\n        const data = await response.json();\n        this.loginResult = `状态: ${response.status}\\n数据: ${JSON.stringify(data, null, 2)}`;\n      } catch (error) {\n        this.loginResult = `错误: ${error.message}`;\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "backendResult", "loginResult", "methods", "testBackend", "response", "fetch", "json", "JSON", "stringify", "error", "message", "testLogin", "method", "headers", "credentials", "body", "username", "password", "status"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\SimpleTest.vue"], "sourcesContent": ["<template>\n  <div class=\"simple-test\">\n    <h1>简单API测试</h1>\n    \n    <div class=\"test-section\">\n      <h2>测试后端连接</h2>\n      <button @click=\"testBackend\">测试后端</button>\n      <div v-if=\"backendResult\" class=\"result\">\n        <pre>{{ backendResult }}</pre>\n      </div>\n    </div>\n    \n    <div class=\"test-section\">\n      <h2>测试登录API</h2>\n      <button @click=\"testLogin\">测试登录</button>\n      <div v-if=\"loginResult\" class=\"result\">\n        <pre>{{ loginResult }}</pre>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'SimpleTest',\n  data() {\n    return {\n      backendResult: null,\n      loginResult: null\n    }\n  },\n  methods: {\n    async testBackend() {\n      try {\n        const response = await fetch('http://localhost:5000/')\n        const data = await response.json()\n        this.backendResult = JSON.stringify(data, null, 2)\n      } catch (error) {\n        this.backendResult = `错误: ${error.message}`\n      }\n    },\n    \n    async testLogin() {\n      try {\n        const response = await fetch('http://localhost:5000/api/auth/login', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          credentials: 'include',\n          body: JSON.stringify({\n            username: 'admin',\n            password: 'Admin123!'\n          })\n        })\n        \n        const data = await response.json()\n        this.loginResult = `状态: ${response.status}\\n数据: ${JSON.stringify(data, null, 2)}`\n      } catch (error) {\n        this.loginResult = `错误: ${error.message}`\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.simple-test {\n  padding: 20px;\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.test-section {\n  margin-bottom: 30px;\n  padding: 20px;\n  border: 1px solid #ddd;\n  border-radius: 8px;\n}\n\nbutton {\n  background-color: #42b983;\n  color: white;\n  padding: 10px 20px;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n}\n\nbutton:hover {\n  background-color: #369f6e;\n}\n\n.result {\n  margin-top: 15px;\n  padding: 10px;\n  background-color: #f5f5f5;\n  border-radius: 4px;\n  border: 1px solid #ddd;\n}\n\npre {\n  white-space: pre-wrap;\n  word-wrap: break-word;\n}\n</style>\n"], "mappings": "AAuBA,eAAe;EACbA,IAAI,EAAE,YAAY;EAClBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,aAAa,EAAE,IAAI;MACnBC,WAAW,EAAE;IACf;EACF,CAAC;EACDC,OAAO,EAAE;IACP,MAAMC,WAAWA,CAAA,EAAG;MAClB,IAAI;QACF,MAAMC,QAAO,GAAI,MAAMC,KAAK,CAAC,wBAAwB;QACrD,MAAMN,IAAG,GAAI,MAAMK,QAAQ,CAACE,IAAI,CAAC;QACjC,IAAI,CAACN,aAAY,GAAIO,IAAI,CAACC,SAAS,CAACT,IAAI,EAAE,IAAI,EAAE,CAAC;MACnD,EAAE,OAAOU,KAAK,EAAE;QACd,IAAI,CAACT,aAAY,GAAI,OAAOS,KAAK,CAACC,OAAO,EAAC;MAC5C;IACF,CAAC;IAED,MAAMC,SAASA,CAAA,EAAG;MAChB,IAAI;QACF,MAAMP,QAAO,GAAI,MAAMC,KAAK,CAAC,sCAAsC,EAAE;UACnEO,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDC,WAAW,EAAE,SAAS;UACtBC,IAAI,EAAER,IAAI,CAACC,SAAS,CAAC;YACnBQ,QAAQ,EAAE,OAAO;YACjBC,QAAQ,EAAE;UACZ,CAAC;QACH,CAAC;QAED,MAAMlB,IAAG,GAAI,MAAMK,QAAQ,CAACE,IAAI,CAAC;QACjC,IAAI,CAACL,WAAU,GAAI,OAAOG,QAAQ,CAACc,MAAM,SAASX,IAAI,CAACC,SAAS,CAACT,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,EAAC;MAClF,EAAE,OAAOU,KAAK,EAAE;QACd,IAAI,CAACR,WAAU,GAAI,OAAOQ,KAAK,CAACC,OAAO,EAAC;MAC1C;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}