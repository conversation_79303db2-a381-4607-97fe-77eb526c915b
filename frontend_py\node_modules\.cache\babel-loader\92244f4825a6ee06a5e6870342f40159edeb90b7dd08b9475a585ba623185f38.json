{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, openBlock as _openBlock, createBlock as _createBlock, createElementBlock as _createElementBlock, renderList as _renderList, Fragment as _Fragment } from \"vue\";\nconst _hoisted_1 = {\n  class: \"dashboard\"\n};\nconst _hoisted_2 = {\n  class: \"dashboard-header\"\n};\nconst _hoisted_3 = {\n  class: \"user-welcome\"\n};\nconst _hoisted_4 = {\n  class: \"user-role\"\n};\nconst _hoisted_5 = {\n  class: \"header-actions\"\n};\nconst _hoisted_6 = {\n  class: \"quick-nav\"\n};\nconst _hoisted_7 = {\n  class: \"nav-grid\"\n};\nconst _hoisted_8 = {\n  class: \"dashboard-section\"\n};\nconst _hoisted_9 = {\n  class: \"announcements-container\"\n};\nconst _hoisted_10 = {\n  key: 0,\n  class: \"loading\"\n};\nconst _hoisted_11 = {\n  key: 1,\n  class: \"no-data\"\n};\nconst _hoisted_12 = {\n  key: 2,\n  class: \"announcements-list\"\n};\nconst _hoisted_13 = [\"innerHTML\"];\nconst _hoisted_14 = {\n  class: \"announcement-date\"\n};\nconst _hoisted_15 = {\n  class: \"dashboard-section\"\n};\nconst _hoisted_16 = {\n  class: \"monitoring-preview\"\n};\nconst _hoisted_17 = {\n  class: \"monitoring-card\"\n};\nconst _hoisted_18 = {\n  class: \"metric-value\"\n};\nconst _hoisted_19 = {\n  class: \"monitoring-card\"\n};\nconst _hoisted_20 = {\n  class: \"metric-value\"\n};\nconst _hoisted_21 = {\n  class: \"monitoring-card\"\n};\nconst _hoisted_22 = {\n  class: \"metric-value\"\n};\nconst _hoisted_23 = {\n  class: \"dashboard-section\"\n};\nconst _hoisted_24 = {\n  class: \"user-info-card\"\n};\nconst _hoisted_25 = {\n  class: \"info-row\"\n};\nconst _hoisted_26 = {\n  class: \"info-value\"\n};\nconst _hoisted_27 = {\n  class: \"info-row\"\n};\nconst _hoisted_28 = {\n  class: \"info-value\"\n};\nconst _hoisted_29 = {\n  class: \"info-row\"\n};\nconst _hoisted_30 = {\n  class: \"info-value\"\n};\nconst _hoisted_31 = {\n  class: \"info-row\"\n};\nconst _hoisted_32 = {\n  class: \"info-value\"\n};\nconst _hoisted_33 = {\n  class: \"info-row\"\n};\nconst _hoisted_34 = {\n  class: \"info-value\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_router_link = _resolveComponent(\"router-link\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 用户信息头部 \"), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"h1\", null, \"欢迎回来，\" + _toDisplayString($data.currentUser?.name || $data.currentUser?.username) + \"！\", 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_4, _toDisplayString($data.currentUser?.role === 'admin' ? '管理员' : '社团成员'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"button\", {\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.logout && $options.logout(...args)),\n    class: \"btn btn-secondary\"\n  }, \"退出登录\")])]), _createCommentVNode(\" 快速导航 \"), _createElementVNode(\"div\", _hoisted_6, [_cache[7] || (_cache[7] = _createElementVNode(\"h2\", null, \"快速导航\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_router_link, {\n    to: \"/club-culture\",\n    class: \"nav-card\"\n  }, {\n    default: _withCtx(() => _cache[1] || (_cache[1] = [_createElementVNode(\"div\", {\n      class: \"nav-icon\"\n    }, \"🏛️\", -1 /* CACHED */), _createElementVNode(\"h3\", null, \"社团文化\", -1 /* CACHED */), _createElementVNode(\"p\", null, \"了解社团历史与文化\", -1 /* CACHED */)])),\n    _: 1 /* STABLE */,\n    __: [1]\n  }), _createVNode(_component_router_link, {\n    to: \"/learning-resources\",\n    class: \"nav-card\"\n  }, {\n    default: _withCtx(() => _cache[2] || (_cache[2] = [_createElementVNode(\"div\", {\n      class: \"nav-icon\"\n    }, \"📚\", -1 /* CACHED */), _createElementVNode(\"h3\", null, \"学习资源\", -1 /* CACHED */), _createElementVNode(\"p\", null, \"获取学习材料和资源\", -1 /* CACHED */)])),\n    _: 1 /* STABLE */,\n    __: [2]\n  }), _createVNode(_component_router_link, {\n    to: \"/past-activities\",\n    class: \"nav-card\"\n  }, {\n    default: _withCtx(() => _cache[3] || (_cache[3] = [_createElementVNode(\"div\", {\n      class: \"nav-icon\"\n    }, \"🎯\", -1 /* CACHED */), _createElementVNode(\"h3\", null, \"过往活动\", -1 /* CACHED */), _createElementVNode(\"p\", null, \"查看历史活动记录\", -1 /* CACHED */)])),\n    _: 1 /* STABLE */,\n    __: [3]\n  }), _createVNode(_component_router_link, {\n    to: \"/cyber-security-news\",\n    class: \"nav-card\"\n  }, {\n    default: _withCtx(() => _cache[4] || (_cache[4] = [_createElementVNode(\"div\", {\n      class: \"nav-icon\"\n    }, \"🔒\", -1 /* CACHED */), _createElementVNode(\"h3\", null, \"安全资讯\", -1 /* CACHED */), _createElementVNode(\"p\", null, \"最新网络安全新闻\", -1 /* CACHED */)])),\n    _: 1 /* STABLE */,\n    __: [4]\n  }), _createVNode(_component_router_link, {\n    to: \"/playground\",\n    class: \"nav-card\"\n  }, {\n    default: _withCtx(() => _cache[5] || (_cache[5] = [_createElementVNode(\"div\", {\n      class: \"nav-icon\"\n    }, \"🎮\", -1 /* CACHED */), _createElementVNode(\"h3\", null, \"练习场\", -1 /* CACHED */), _createElementVNode(\"p\", null, \"技能练习与挑战\", -1 /* CACHED */)])),\n    _: 1 /* STABLE */,\n    __: [5]\n  }), $data.currentUser?.role === 'admin' ? (_openBlock(), _createBlock(_component_router_link, {\n    key: 0,\n    to: \"/admin\",\n    class: \"nav-card admin-card\"\n  }, {\n    default: _withCtx(() => _cache[6] || (_cache[6] = [_createElementVNode(\"div\", {\n      class: \"nav-icon\"\n    }, \"⚙️\", -1 /* CACHED */), _createElementVNode(\"h3\", null, \"管理后台\", -1 /* CACHED */), _createElementVNode(\"p\", null, \"系统管理与配置\", -1 /* CACHED */)])),\n    _: 1 /* STABLE */,\n    __: [6]\n  })) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" 最新公告 \"), _createElementVNode(\"div\", _hoisted_8, [_cache[8] || (_cache[8] = _createElementVNode(\"h2\", null, \"最新公告\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_9, [$data.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_10, \"加载中...\")) : $data.announcements.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_11, \"暂无公告\")) : (_openBlock(), _createElementBlock(\"div\", _hoisted_12, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.announcements.slice(0, 3), announcement => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: announcement.id,\n      class: \"announcement-item\"\n    }, [_createElementVNode(\"h3\", null, _toDisplayString(announcement.title), 1 /* TEXT */), _createElementVNode(\"p\", {\n      class: \"announcement-content\",\n      innerHTML: $options.purifyContent(announcement.content)\n    }, null, 8 /* PROPS */, _hoisted_13), _createElementVNode(\"p\", _hoisted_14, _toDisplayString($options.formatDate(announcement.created_at)), 1 /* TEXT */)]);\n  }), 128 /* KEYED_FRAGMENT */))]))])]), _createCommentVNode(\" 监控数据大屏预览 \"), _createElementVNode(\"div\", _hoisted_15, [_cache[16] || (_cache[16] = _createElementVNode(\"h2\", null, \"监控数据大屏\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"div\", _hoisted_17, [_cache[9] || (_cache[9] = _createElementVNode(\"h3\", null, \"🚨 安全告警\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_18, _toDisplayString($data.securityAlerts), 1 /* TEXT */), _cache[10] || (_cache[10] = _createElementVNode(\"p\", null, \"本月检测到的安全事件\", -1 /* CACHED */))]), _createElementVNode(\"div\", _hoisted_19, [_cache[11] || (_cache[11] = _createElementVNode(\"h3\", null, \"🔍 漏洞扫描\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_20, _toDisplayString($data.vulnerabilities), 1 /* TEXT */), _cache[12] || (_cache[12] = _createElementVNode(\"p\", null, \"发现的潜在漏洞\", -1 /* CACHED */))]), _createElementVNode(\"div\", _hoisted_21, [_cache[13] || (_cache[13] = _createElementVNode(\"h3\", null, \"👥 在线用户\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_22, _toDisplayString($data.onlineUsers), 1 /* TEXT */), _cache[14] || (_cache[14] = _createElementVNode(\"p\", null, \"当前活跃用户数\", -1 /* CACHED */))]), _cache[15] || (_cache[15] = _createElementVNode(\"div\", {\n    class: \"monitoring-card\"\n  }, [_createElementVNode(\"h3\", null, \"📊 系统状态\"), _createElementVNode(\"div\", {\n    class: \"metric-value status-good\"\n  }, \"正常\"), _createElementVNode(\"p\", null, \"系统运行状态\")], -1 /* CACHED */))]), _cache[17] || (_cache[17] = _createElementVNode(\"div\", {\n    class: \"monitoring-note\"\n  }, [_createElementVNode(\"p\", null, \"💡 完整的监控数据大屏功能正在开发中，将用于记录靶场漏洞告警信息，敬请期待！\")], -1 /* CACHED */))]), _createCommentVNode(\" 个人信息 \"), _createElementVNode(\"div\", _hoisted_23, [_cache[23] || (_cache[23] = _createElementVNode(\"h2\", null, \"个人信息\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_24, [_createElementVNode(\"div\", _hoisted_25, [_cache[18] || (_cache[18] = _createElementVNode(\"span\", {\n    class: \"info-label\"\n  }, \"用户名：\", -1 /* CACHED */)), _createElementVNode(\"span\", _hoisted_26, _toDisplayString($data.currentUser?.username), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_27, [_cache[19] || (_cache[19] = _createElementVNode(\"span\", {\n    class: \"info-label\"\n  }, \"姓名：\", -1 /* CACHED */)), _createElementVNode(\"span\", _hoisted_28, _toDisplayString($data.currentUser?.name || '未设置'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_29, [_cache[20] || (_cache[20] = _createElementVNode(\"span\", {\n    class: \"info-label\"\n  }, \"学号：\", -1 /* CACHED */)), _createElementVNode(\"span\", _hoisted_30, _toDisplayString($data.currentUser?.student_id || '未设置'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_31, [_cache[21] || (_cache[21] = _createElementVNode(\"span\", {\n    class: \"info-label\"\n  }, \"角色：\", -1 /* CACHED */)), _createElementVNode(\"span\", _hoisted_32, _toDisplayString($data.currentUser?.role === 'admin' ? '管理员' : '普通用户'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_33, [_cache[22] || (_cache[22] = _createElementVNode(\"span\", {\n    class: \"info-label\"\n  }, \"加入时间：\", -1 /* CACHED */)), _createElementVNode(\"span\", _hoisted_34, _toDisplayString($options.formatDate($data.currentUser?.created_at)), 1 /* TEXT */)])])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_toDisplayString", "$data", "currentUser", "name", "username", "_hoisted_4", "role", "_hoisted_5", "onClick", "_cache", "args", "$options", "logout", "_hoisted_6", "_hoisted_7", "_createVNode", "_component_router_link", "to", "_createBlock", "_hoisted_8", "_hoisted_9", "loading", "_hoisted_10", "announcements", "length", "_hoisted_11", "_hoisted_12", "_Fragment", "_renderList", "slice", "announcement", "key", "id", "title", "innerHTML", "purifyContent", "content", "_hoisted_14", "formatDate", "created_at", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "securityAlerts", "_hoisted_19", "_hoisted_20", "vulnerabilities", "_hoisted_21", "_hoisted_22", "onlineUsers", "_hoisted_23", "_hoisted_24", "_hoisted_25", "_hoisted_26", "_hoisted_27", "_hoisted_28", "_hoisted_29", "_hoisted_30", "student_id", "_hoisted_31", "_hoisted_32", "_hoisted_33", "_hoisted_34"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Dashboard.vue"], "sourcesContent": ["<template>\r\n  <div class=\"dashboard\">\r\n    <!-- 用户信息头部 -->\r\n    <div class=\"dashboard-header\">\r\n      <div class=\"user-welcome\">\r\n        <h1>欢迎回来，{{ currentUser?.name || currentUser?.username }}！</h1>\r\n        <p class=\"user-role\">{{ currentUser?.role === 'admin' ? '管理员' : '社团成员' }}</p>\r\n      </div>\r\n      <div class=\"header-actions\">\r\n        <button @click=\"logout\" class=\"btn btn-secondary\">退出登录</button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 快速导航 -->\r\n    <div class=\"quick-nav\">\r\n      <h2>快速导航</h2>\r\n      <div class=\"nav-grid\">\r\n        <router-link to=\"/club-culture\" class=\"nav-card\">\r\n          <div class=\"nav-icon\">🏛️</div>\r\n          <h3>社团文化</h3>\r\n          <p>了解社团历史与文化</p>\r\n        </router-link>\r\n        <router-link to=\"/learning-resources\" class=\"nav-card\">\r\n          <div class=\"nav-icon\">📚</div>\r\n          <h3>学习资源</h3>\r\n          <p>获取学习材料和资源</p>\r\n        </router-link>\r\n        <router-link to=\"/past-activities\" class=\"nav-card\">\r\n          <div class=\"nav-icon\">🎯</div>\r\n          <h3>过往活动</h3>\r\n          <p>查看历史活动记录</p>\r\n        </router-link>\r\n        <router-link to=\"/cyber-security-news\" class=\"nav-card\">\r\n          <div class=\"nav-icon\">🔒</div>\r\n          <h3>安全资讯</h3>\r\n          <p>最新网络安全新闻</p>\r\n        </router-link>\r\n        <router-link to=\"/playground\" class=\"nav-card\">\r\n          <div class=\"nav-icon\">🎮</div>\r\n          <h3>练习场</h3>\r\n          <p>技能练习与挑战</p>\r\n        </router-link>\r\n        <router-link v-if=\"currentUser?.role === 'admin'\" to=\"/admin\" class=\"nav-card admin-card\">\r\n          <div class=\"nav-icon\">⚙️</div>\r\n          <h3>管理后台</h3>\r\n          <p>系统管理与配置</p>\r\n        </router-link>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 最新公告 -->\r\n    <div class=\"dashboard-section\">\r\n      <h2>最新公告</h2>\r\n      <div class=\"announcements-container\">\r\n        <div v-if=\"loading\" class=\"loading\">加载中...</div>\r\n        <div v-else-if=\"announcements.length === 0\" class=\"no-data\">暂无公告</div>\r\n        <div v-else class=\"announcements-list\">\r\n          <div v-for=\"announcement in announcements.slice(0, 3)\" :key=\"announcement.id\" class=\"announcement-item\">\r\n            <h3>{{ announcement.title }}</h3>\r\n            <p class=\"announcement-content\" v-html=\"purifyContent(announcement.content)\"></p>\r\n            <p class=\"announcement-date\">{{ formatDate(announcement.created_at) }}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 监控数据大屏预览 -->\r\n    <div class=\"dashboard-section\">\r\n      <h2>监控数据大屏</h2>\r\n      <div class=\"monitoring-preview\">\r\n        <div class=\"monitoring-card\">\r\n          <h3>🚨 安全告警</h3>\r\n          <div class=\"metric-value\">{{ securityAlerts }}</div>\r\n          <p>本月检测到的安全事件</p>\r\n        </div>\r\n        <div class=\"monitoring-card\">\r\n          <h3>🔍 漏洞扫描</h3>\r\n          <div class=\"metric-value\">{{ vulnerabilities }}</div>\r\n          <p>发现的潜在漏洞</p>\r\n        </div>\r\n        <div class=\"monitoring-card\">\r\n          <h3>👥 在线用户</h3>\r\n          <div class=\"metric-value\">{{ onlineUsers }}</div>\r\n          <p>当前活跃用户数</p>\r\n        </div>\r\n        <div class=\"monitoring-card\">\r\n          <h3>📊 系统状态</h3>\r\n          <div class=\"metric-value status-good\">正常</div>\r\n          <p>系统运行状态</p>\r\n        </div>\r\n      </div>\r\n      <div class=\"monitoring-note\">\r\n        <p>💡 完整的监控数据大屏功能正在开发中，将用于记录靶场漏洞告警信息，敬请期待！</p>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 个人信息 -->\r\n    <div class=\"dashboard-section\">\r\n      <h2>个人信息</h2>\r\n      <div class=\"user-info-card\">\r\n        <div class=\"info-row\">\r\n          <span class=\"info-label\">用户名：</span>\r\n          <span class=\"info-value\">{{ currentUser?.username }}</span>\r\n        </div>\r\n        <div class=\"info-row\">\r\n          <span class=\"info-label\">姓名：</span>\r\n          <span class=\"info-value\">{{ currentUser?.name || '未设置' }}</span>\r\n        </div>\r\n        <div class=\"info-row\">\r\n          <span class=\"info-label\">学号：</span>\r\n          <span class=\"info-value\">{{ currentUser?.student_id || '未设置' }}</span>\r\n        </div>\r\n        <div class=\"info-row\">\r\n          <span class=\"info-label\">角色：</span>\r\n          <span class=\"info-value\">{{ currentUser?.role === 'admin' ? '管理员' : '普通用户' }}</span>\r\n        </div>\r\n        <div class=\"info-row\">\r\n          <span class=\"info-label\">加入时间：</span>\r\n          <span class=\"info-value\">{{ formatDate(currentUser?.created_at) }}</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport DOMPurify from 'dompurify'\r\nimport { authAPI, announcementAPI } from '../services/api'\r\n\r\nexport default {\r\n  name: 'Dashboard',\r\n  data() {\r\n    return {\r\n      currentUser: null,\r\n      announcements: [],\r\n      loading: false,\r\n\r\n      // 模拟监控数据\r\n      securityAlerts: 12,\r\n      vulnerabilities: 5,\r\n      onlineUsers: 23,\r\n    }\r\n  },\r\n  async mounted() {\r\n    await this.loadCurrentUser()\r\n    await this.loadAnnouncements()\r\n    this.startMonitoringSimulation()\r\n  },\r\n  methods: {\r\n    async loadCurrentUser() {\r\n      try {\r\n        const response = await authAPI.checkAuth()\r\n        this.currentUser = response.data.user\r\n      } catch (error) {\r\n        console.error('获取用户信息失败:', error)\r\n        this.$router.push('/login')\r\n      }\r\n    },\r\n\r\n    async loadAnnouncements() {\r\n      this.loading = true\r\n      try {\r\n        const response = await announcementAPI.getAnnouncements()\r\n        this.announcements = response.data\r\n      } catch (error) {\r\n        console.error('加载公告失败:', error)\r\n        // 如果API调用失败，显示示例数据\r\n        this.announcements = [\r\n          {\r\n            id: 1,\r\n            title: '社团招新公告',\r\n            content: '<p>欢迎参加网络信息安全社团的招新活动！</p>',\r\n            created_at: new Date().toISOString()\r\n          }\r\n        ]\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    async logout() {\r\n      try {\r\n        await authAPI.logout()\r\n        this.$router.push('/login')\r\n      } catch (error) {\r\n        console.error('登出失败:', error)\r\n      }\r\n    },\r\n\r\n    purifyContent(content) {\r\n      return DOMPurify.sanitize(content)\r\n    },\r\n\r\n    formatDate(dateString) {\r\n      if (!dateString) return '未知'\r\n      const date = new Date(dateString)\r\n      return date.toLocaleDateString('zh-CN')\r\n    },\r\n\r\n    startMonitoringSimulation() {\r\n      // 模拟监控数据变化\r\n      setInterval(() => {\r\n        this.securityAlerts = Math.floor(Math.random() * 20) + 10\r\n        this.vulnerabilities = Math.floor(Math.random() * 10) + 3\r\n        this.onlineUsers = Math.floor(Math.random() * 50) + 15\r\n      }, 30000) // 每30秒更新一次\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.dashboard {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 20px;\r\n}\r\n\r\n.dashboard-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 30px;\r\n  padding: 20px;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  border-radius: 12px;\r\n}\r\n\r\n.user-welcome h1 {\r\n  margin: 0 0 5px 0;\r\n  font-size: 2rem;\r\n}\r\n\r\n.user-role {\r\n  margin: 0;\r\n  opacity: 0.9;\r\n  font-size: 1.1rem;\r\n}\r\n\r\n.header-actions .btn {\r\n  padding: 10px 20px;\r\n  border: 2px solid white;\r\n  background: transparent;\r\n  color: white;\r\n  border-radius: 6px;\r\n  text-decoration: none;\r\n  font-weight: bold;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.header-actions .btn:hover {\r\n  background: white;\r\n  color: #667eea;\r\n}\r\n\r\n.dashboard-section {\r\n  margin-bottom: 40px;\r\n}\r\n\r\n.dashboard-section h2 {\r\n  font-size: 1.8rem;\r\n  margin-bottom: 20px;\r\n  color: #333;\r\n  border-bottom: 3px solid #42b983;\r\n  padding-bottom: 10px;\r\n}\r\n\r\n.quick-nav .nav-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\r\n  gap: 20px;\r\n  margin-top: 20px;\r\n}\r\n\r\n.nav-card {\r\n  background: white;\r\n  padding: 25px;\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\r\n  text-decoration: none;\r\n  color: inherit;\r\n  transition: all 0.3s ease;\r\n  border: 2px solid transparent;\r\n}\r\n\r\n.nav-card:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);\r\n  border-color: #42b983;\r\n}\r\n\r\n.nav-card.admin-card {\r\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\r\n  color: white;\r\n}\r\n\r\n.nav-card.admin-card:hover {\r\n  border-color: #ff6b6b;\r\n}\r\n\r\n.nav-icon {\r\n  font-size: 2.5rem;\r\n  margin-bottom: 15px;\r\n  text-align: center;\r\n}\r\n\r\n.nav-card h3 {\r\n  margin: 0 0 10px 0;\r\n  font-size: 1.3rem;\r\n  text-align: center;\r\n}\r\n\r\n.nav-card p {\r\n  margin: 0;\r\n  text-align: center;\r\n  opacity: 0.8;\r\n}\r\n\r\n.announcements-container {\r\n  background: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n  padding: 20px;\r\n}\r\n\r\n.announcements-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20px;\r\n}\r\n\r\n.announcement-item {\r\n  padding: 15px;\r\n  border-left: 4px solid #42b983;\r\n  background: #f8f9fa;\r\n  border-radius: 0 8px 8px 0;\r\n}\r\n\r\n.announcement-item h3 {\r\n  margin: 0 0 10px 0;\r\n  color: #333;\r\n}\r\n\r\n.announcement-content {\r\n  color: #666;\r\n  line-height: 1.6;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.announcement-date {\r\n  color: #999;\r\n  font-size: 0.9rem;\r\n  text-align: right;\r\n  margin: 0;\r\n}\r\n\r\n.monitoring-preview {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\r\n  gap: 20px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.monitoring-card {\r\n  background: white;\r\n  padding: 20px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n  text-align: center;\r\n}\r\n\r\n.monitoring-card h3 {\r\n  margin: 0 0 15px 0;\r\n  color: #333;\r\n  font-size: 1.1rem;\r\n}\r\n\r\n.metric-value {\r\n  font-size: 2.5rem;\r\n  font-weight: bold;\r\n  color: #42b983;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.metric-value.status-good {\r\n  color: #28a745;\r\n  font-size: 1.5rem;\r\n}\r\n\r\n.monitoring-card p {\r\n  margin: 0;\r\n  color: #666;\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.monitoring-note {\r\n  background: #e3f2fd;\r\n  padding: 15px;\r\n  border-radius: 8px;\r\n  border-left: 4px solid #2196f3;\r\n}\r\n\r\n.monitoring-note p {\r\n  margin: 0;\r\n  color: #1976d2;\r\n}\r\n\r\n.user-info-card {\r\n  background: white;\r\n  padding: 25px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.info-row {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 10px 0;\r\n  border-bottom: 1px solid #eee;\r\n}\r\n\r\n.info-row:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.info-label {\r\n  font-weight: bold;\r\n  color: #333;\r\n}\r\n\r\n.info-value {\r\n  color: #666;\r\n}\r\n\r\n.loading, .no-data {\r\n  text-align: center;\r\n  padding: 40px;\r\n  color: #666;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .dashboard {\r\n    padding: 10px;\r\n  }\r\n\r\n  .dashboard-header {\r\n    flex-direction: column;\r\n    gap: 15px;\r\n    text-align: center;\r\n  }\r\n\r\n  .dashboard-header h1 {\r\n    font-size: 1.5rem;\r\n  }\r\n\r\n  .nav-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n\r\n  .monitoring-preview {\r\n    grid-template-columns: repeat(2, 1fr);\r\n  }\r\n\r\n  .info-row {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 5px;\r\n  }\r\n}\r\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAc;;EAEpBA,KAAK,EAAC;AAAW;;EAEjBA,KAAK,EAAC;AAAgB;;EAMxBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAU;;EAmClBA,KAAK,EAAC;AAAmB;;EAEvBA,KAAK,EAAC;AAAyB;;;EACdA,KAAK,EAAC;;;;EACkBA,KAAK,EAAC;;;;EACtCA,KAAK,EAAC;;;;EAIXA,KAAK,EAAC;AAAmB;;EAO/BA,KAAK,EAAC;AAAmB;;EAEvBA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAc;;EAGtBA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAc;;EAGtBA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAc;;EAe1BA,KAAK,EAAC;AAAmB;;EAEvBA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAU;;EAEbA,KAAK,EAAC;AAAY;;EAErBA,KAAK,EAAC;AAAU;;EAEbA,KAAK,EAAC;AAAY;;EAErBA,KAAK,EAAC;AAAU;;EAEbA,KAAK,EAAC;AAAY;;EAErBA,KAAK,EAAC;AAAU;;EAEbA,KAAK,EAAC;AAAY;;EAErBA,KAAK,EAAC;AAAU;;EAEbA,KAAK,EAAC;AAAY;;;uBArHhCC,mBAAA,CAyHM,OAzHNC,UAyHM,GAxHJC,mBAAA,YAAe,EACfC,mBAAA,CAQM,OARNC,UAQM,GAPJD,mBAAA,CAGM,OAHNE,UAGM,GAFJF,mBAAA,CAA+D,YAA3D,OAAK,GAAAG,gBAAA,CAAGC,KAAA,CAAAC,WAAW,EAAEC,IAAI,IAAIF,KAAA,CAAAC,WAAW,EAAEE,QAAQ,IAAG,GAAC,iBAC1DP,mBAAA,CAA6E,KAA7EQ,UAA6E,EAAAL,gBAAA,CAArDC,KAAA,CAAAC,WAAW,EAAEI,IAAI,8C,GAE3CT,mBAAA,CAEM,OAFNU,UAEM,GADJV,mBAAA,CAA+D;IAAtDW,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAC,MAAA,IAAAD,QAAA,CAAAC,MAAA,IAAAF,IAAA,CAAM;IAAEjB,KAAK,EAAC;KAAoB,MAAI,E,KAI1DG,mBAAA,UAAa,EACbC,mBAAA,CAkCM,OAlCNgB,UAkCM,G,0BAjCJhB,mBAAA,CAAa,YAAT,MAAI,qBACRA,mBAAA,CA+BM,OA/BNiB,UA+BM,GA9BJC,YAAA,CAIcC,sBAAA;IAJDC,EAAE,EAAC,eAAe;IAACxB,KAAK,EAAC;;sBACpC,MAA+BgB,MAAA,QAAAA,MAAA,OAA/BZ,mBAAA,CAA+B;MAA1BJ,KAAK,EAAC;IAAU,GAAC,KAAG,oBACzBI,mBAAA,CAAa,YAAT,MAAI,oBACRA,mBAAA,CAAgB,WAAb,WAAS,mB;;;MAEdkB,YAAA,CAIcC,sBAAA;IAJDC,EAAE,EAAC,qBAAqB;IAACxB,KAAK,EAAC;;sBAC1C,MAA8BgB,MAAA,QAAAA,MAAA,OAA9BZ,mBAAA,CAA8B;MAAzBJ,KAAK,EAAC;IAAU,GAAC,IAAE,oBACxBI,mBAAA,CAAa,YAAT,MAAI,oBACRA,mBAAA,CAAgB,WAAb,WAAS,mB;;;MAEdkB,YAAA,CAIcC,sBAAA;IAJDC,EAAE,EAAC,kBAAkB;IAACxB,KAAK,EAAC;;sBACvC,MAA8BgB,MAAA,QAAAA,MAAA,OAA9BZ,mBAAA,CAA8B;MAAzBJ,KAAK,EAAC;IAAU,GAAC,IAAE,oBACxBI,mBAAA,CAAa,YAAT,MAAI,oBACRA,mBAAA,CAAe,WAAZ,UAAQ,mB;;;MAEbkB,YAAA,CAIcC,sBAAA;IAJDC,EAAE,EAAC,sBAAsB;IAACxB,KAAK,EAAC;;sBAC3C,MAA8BgB,MAAA,QAAAA,MAAA,OAA9BZ,mBAAA,CAA8B;MAAzBJ,KAAK,EAAC;IAAU,GAAC,IAAE,oBACxBI,mBAAA,CAAa,YAAT,MAAI,oBACRA,mBAAA,CAAe,WAAZ,UAAQ,mB;;;MAEbkB,YAAA,CAIcC,sBAAA;IAJDC,EAAE,EAAC,aAAa;IAACxB,KAAK,EAAC;;sBAClC,MAA8BgB,MAAA,QAAAA,MAAA,OAA9BZ,mBAAA,CAA8B;MAAzBJ,KAAK,EAAC;IAAU,GAAC,IAAE,oBACxBI,mBAAA,CAAY,YAAR,KAAG,oBACPA,mBAAA,CAAc,WAAX,SAAO,mB;;;MAEOI,KAAA,CAAAC,WAAW,EAAEI,IAAI,gB,cAApCY,YAAA,CAIcF,sBAAA;;IAJoCC,EAAE,EAAC,QAAQ;IAACxB,KAAK,EAAC;;sBAClE,MAA8BgB,MAAA,QAAAA,MAAA,OAA9BZ,mBAAA,CAA8B;MAAzBJ,KAAK,EAAC;IAAU,GAAC,IAAE,oBACxBI,mBAAA,CAAa,YAAT,MAAI,oBACRA,mBAAA,CAAc,WAAX,SAAO,mB;;;+CAKhBD,mBAAA,UAAa,EACbC,mBAAA,CAaM,OAbNsB,UAaM,G,0BAZJtB,mBAAA,CAAa,YAAT,MAAI,qBACRA,mBAAA,CAUM,OAVNuB,UAUM,GATOnB,KAAA,CAAAoB,OAAO,I,cAAlB3B,mBAAA,CAAgD,OAAhD4B,WAAgD,EAAZ,QAAM,KAC1BrB,KAAA,CAAAsB,aAAa,CAACC,MAAM,U,cAApC9B,mBAAA,CAAsE,OAAtE+B,WAAsE,EAAV,MAAI,M,cAChE/B,mBAAA,CAMM,OANNgC,WAMM,I,kBALJhC,mBAAA,CAIMiC,SAAA,QAAAC,WAAA,CAJsB3B,KAAA,CAAAsB,aAAa,CAACM,KAAK,QAAnCC,YAAY;yBAAxBpC,mBAAA,CAIM;MAJkDqC,GAAG,EAAED,YAAY,CAACE,EAAE;MAAEvC,KAAK,EAAC;QAClFI,mBAAA,CAAiC,YAAAG,gBAAA,CAA1B8B,YAAY,CAACG,KAAK,kBACzBpC,mBAAA,CAAiF;MAA9EJ,KAAK,EAAC,sBAAsB;MAACyC,SAA4C,EAApCvB,QAAA,CAAAwB,aAAa,CAACL,YAAY,CAACM,OAAO;0CAC1EvC,mBAAA,CAA0E,KAA1EwC,WAA0E,EAAArC,gBAAA,CAA1CW,QAAA,CAAA2B,UAAU,CAACR,YAAY,CAACS,UAAU,kB;yCAM1E3C,mBAAA,cAAiB,EACjBC,mBAAA,CA2BM,OA3BN2C,WA2BM,G,4BA1BJ3C,mBAAA,CAAe,YAAX,QAAM,qBACVA,mBAAA,CAqBM,OArBN4C,WAqBM,GApBJ5C,mBAAA,CAIM,OAJN6C,WAIM,G,0BAHJ7C,mBAAA,CAAgB,YAAZ,SAAO,qBACXA,mBAAA,CAAoD,OAApD8C,WAAoD,EAAA3C,gBAAA,CAAvBC,KAAA,CAAA2C,cAAc,kB,4BAC3C/C,mBAAA,CAAiB,WAAd,YAAU,oB,GAEfA,mBAAA,CAIM,OAJNgD,WAIM,G,4BAHJhD,mBAAA,CAAgB,YAAZ,SAAO,qBACXA,mBAAA,CAAqD,OAArDiD,WAAqD,EAAA9C,gBAAA,CAAxBC,KAAA,CAAA8C,eAAe,kB,4BAC5ClD,mBAAA,CAAc,WAAX,SAAO,oB,GAEZA,mBAAA,CAIM,OAJNmD,WAIM,G,4BAHJnD,mBAAA,CAAgB,YAAZ,SAAO,qBACXA,mBAAA,CAAiD,OAAjDoD,WAAiD,EAAAjD,gBAAA,CAApBC,KAAA,CAAAiD,WAAW,kB,4BACxCrD,mBAAA,CAAc,WAAX,SAAO,oB,+BAEZA,mBAAA,CAIM;IAJDJ,KAAK,EAAC;EAAiB,IAC1BI,mBAAA,CAAgB,YAAZ,SAAO,GACXA,mBAAA,CAA8C;IAAzCJ,KAAK,EAAC;EAA0B,GAAC,IAAE,GACxCI,mBAAA,CAAa,WAAV,QAAM,E,mDAGbA,mBAAA,CAEM;IAFDJ,KAAK,EAAC;EAAiB,IAC1BI,mBAAA,CAA8C,WAA3C,yCAAuC,E,uBAI9CD,mBAAA,UAAa,EACbC,mBAAA,CAwBM,OAxBNsD,WAwBM,G,4BAvBJtD,mBAAA,CAAa,YAAT,MAAI,qBACRA,mBAAA,CAqBM,OArBNuD,WAqBM,GApBJvD,mBAAA,CAGM,OAHNwD,WAGM,G,4BAFJxD,mBAAA,CAAoC;IAA9BJ,KAAK,EAAC;EAAY,GAAC,MAAI,qBAC7BI,mBAAA,CAA2D,QAA3DyD,WAA2D,EAAAtD,gBAAA,CAA/BC,KAAA,CAAAC,WAAW,EAAEE,QAAQ,iB,GAEnDP,mBAAA,CAGM,OAHN0D,WAGM,G,4BAFJ1D,mBAAA,CAAmC;IAA7BJ,KAAK,EAAC;EAAY,GAAC,KAAG,qBAC5BI,mBAAA,CAAgE,QAAhE2D,WAAgE,EAAAxD,gBAAA,CAApCC,KAAA,CAAAC,WAAW,EAAEC,IAAI,0B,GAE/CN,mBAAA,CAGM,OAHN4D,WAGM,G,4BAFJ5D,mBAAA,CAAmC;IAA7BJ,KAAK,EAAC;EAAY,GAAC,KAAG,qBAC5BI,mBAAA,CAAsE,QAAtE6D,WAAsE,EAAA1D,gBAAA,CAA1CC,KAAA,CAAAC,WAAW,EAAEyD,UAAU,0B,GAErD9D,mBAAA,CAGM,OAHN+D,WAGM,G,4BAFJ/D,mBAAA,CAAmC;IAA7BJ,KAAK,EAAC;EAAY,GAAC,KAAG,qBAC5BI,mBAAA,CAAoF,QAApFgE,WAAoF,EAAA7D,gBAAA,CAAxDC,KAAA,CAAAC,WAAW,EAAEI,IAAI,8C,GAE/CT,mBAAA,CAGM,OAHNiE,WAGM,G,4BAFJjE,mBAAA,CAAqC;IAA/BJ,KAAK,EAAC;EAAY,GAAC,OAAK,qBAC9BI,mBAAA,CAAyE,QAAzEkE,WAAyE,EAAA/D,gBAAA,CAA7CW,QAAA,CAAA2B,UAAU,CAACrC,KAAA,CAAAC,WAAW,EAAEqC,UAAU,kB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}