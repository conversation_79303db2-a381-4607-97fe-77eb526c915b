{"ast": null, "code": "import { createElementVNode as _createElementVNode, vModelText as _vModelText, withDirectives as _withDirectives, toDisplayString as _toDisplayString, withModifiers as _withModifiers, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"register\"\n};\nconst _hoisted_2 = {\n  class: \"form-group\"\n};\nconst _hoisted_3 = {\n  class: \"form-group\"\n};\nconst _hoisted_4 = {\n  class: \"form-group\"\n};\nconst _hoisted_5 = {\n  class: \"form-group\"\n};\nconst _hoisted_6 = {\n  class: \"form-group\"\n};\nconst _hoisted_7 = [\"disabled\"];\nconst _hoisted_8 = {\n  key: 0,\n  class: \"error-message\"\n};\nconst _hoisted_9 = {\n  key: 1,\n  class: \"success-message\"\n};\nconst _hoisted_10 = {\n  class: \"login-link\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_router_link = _resolveComponent(\"router-link\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[14] || (_cache[14] = _createElementVNode(\"h1\", null, \"用户注册\", -1 /* CACHED */)), _createElementVNode(\"form\", {\n    onSubmit: _cache[5] || (_cache[5] = _withModifiers((...args) => $options.register && $options.register(...args), [\"prevent\"]))\n  }, [_createElementVNode(\"div\", _hoisted_2, [_cache[6] || (_cache[6] = _createElementVNode(\"label\", {\n    for: \"username\"\n  }, \"用户名:\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    id: \"username\",\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $data.form.username = $event),\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.form.username]])]), _createElementVNode(\"div\", _hoisted_3, [_cache[7] || (_cache[7] = _createElementVNode(\"label\", {\n    for: \"password\"\n  }, \"密码:\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"password\",\n    id: \"password\",\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $data.form.password = $event),\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.form.password]]), _cache[8] || (_cache[8] = _createElementVNode(\"small\", null, \"密码需包含大小写字母、数字和特殊字符，至少8位\", -1 /* CACHED */))]), _createElementVNode(\"div\", _hoisted_4, [_cache[9] || (_cache[9] = _createElementVNode(\"label\", {\n    for: \"confirmPassword\"\n  }, \"确认密码:\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"password\",\n    id: \"confirmPassword\",\n    \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $data.confirmPassword = $event),\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.confirmPassword]])]), _createElementVNode(\"div\", _hoisted_5, [_cache[10] || (_cache[10] = _createElementVNode(\"label\", {\n    for: \"student_id\"\n  }, \"学号:\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    id: \"student_id\",\n    \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $data.form.student_id = $event)\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.form.student_id]])]), _createElementVNode(\"div\", _hoisted_6, [_cache[11] || (_cache[11] = _createElementVNode(\"label\", {\n    for: \"name\"\n  }, \"姓名:\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    id: \"name\",\n    \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $data.form.name = $event)\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.form.name]])]), _createElementVNode(\"button\", {\n    type: \"submit\",\n    disabled: $data.loading\n  }, _toDisplayString($data.loading ? '注册中...' : '注册'), 9 /* TEXT, PROPS */, _hoisted_7)], 32 /* NEED_HYDRATION */), $data.error ? (_openBlock(), _createElementBlock(\"p\", _hoisted_8, _toDisplayString($data.error), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), $data.success ? (_openBlock(), _createElementBlock(\"p\", _hoisted_9, _toDisplayString($data.success), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"p\", null, [_cache[13] || (_cache[13] = _createTextVNode(\"已有账户？\")), _createVNode(_component_router_link, {\n    to: \"/login\"\n  }, {\n    default: _withCtx(() => _cache[12] || (_cache[12] = [_createTextVNode(\"立即登录\")])),\n    _: 1 /* STABLE */,\n    __: [12]\n  })])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "onSubmit", "_cache", "_withModifiers", "args", "$options", "register", "_hoisted_2", "for", "type", "id", "$data", "form", "username", "$event", "required", "_hoisted_3", "password", "_hoisted_4", "confirmPassword", "_hoisted_5", "student_id", "_hoisted_6", "name", "disabled", "loading", "_hoisted_7", "error", "_hoisted_8", "_toDisplayString", "success", "_hoisted_9", "_hoisted_10", "_createVNode", "_component_router_link", "to"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Register.vue"], "sourcesContent": ["<template>\n  <div class=\"register\">\n    <h1>用户注册</h1>\n    <form @submit.prevent=\"register\">\n      <div class=\"form-group\">\n        <label for=\"username\">用户名:</label>\n        <input type=\"text\" id=\"username\" v-model=\"form.username\" required>\n      </div>\n      <div class=\"form-group\">\n        <label for=\"password\">密码:</label>\n        <input type=\"password\" id=\"password\" v-model=\"form.password\" required>\n        <small>密码需包含大小写字母、数字和特殊字符，至少8位</small>\n      </div>\n      <div class=\"form-group\">\n        <label for=\"confirmPassword\">确认密码:</label>\n        <input type=\"password\" id=\"confirmPassword\" v-model=\"confirmPassword\" required>\n      </div>\n      <div class=\"form-group\">\n        <label for=\"student_id\">学号:</label>\n        <input type=\"text\" id=\"student_id\" v-model=\"form.student_id\">\n      </div>\n      <div class=\"form-group\">\n        <label for=\"name\">姓名:</label>\n        <input type=\"text\" id=\"name\" v-model=\"form.name\">\n      </div>\n      <button type=\"submit\" :disabled=\"loading\">\n        {{ loading ? '注册中...' : '注册' }}\n      </button>\n    </form>\n    <p v-if=\"error\" class=\"error-message\">{{ error }}</p>\n    <p v-if=\"success\" class=\"success-message\">{{ success }}</p>\n    \n    <div class=\"login-link\">\n      <p>已有账户？<router-link to=\"/login\">立即登录</router-link></p>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { authAPI } from '../services/api'\n\nexport default {\n  name: 'Register',\n  data() {\n    return {\n      form: {\n        username: '',\n        password: '',\n        student_id: '',\n        name: ''\n      },\n      confirmPassword: '',\n      error: null,\n      success: null,\n      loading: false\n    }\n  },\n  methods: {\n    async register() {\n      this.error = null\n      this.success = null\n      this.loading = true\n\n      // 前端验证\n      if (!this.form.username) {\n        this.error = '用户名不能为空'\n        this.loading = false\n        return\n      }\n      if (!this.form.password) {\n        this.error = '密码不能为空'\n        this.loading = false\n        return\n      }\n      if (this.form.password !== this.confirmPassword) {\n        this.error = '两次输入的密码不一致'\n        this.loading = false\n        return\n      }\n\n      try {\n        await authAPI.register(this.form)\n        this.success = '注册成功！请登录'\n        \n        // 3秒后跳转到登录页\n        setTimeout(() => {\n          this.$router.push('/login')\n        }, 3000)\n      } catch (err) {\n        this.error = err.response?.data?.msg || '注册失败，请稍后重试'\n        console.error(err)\n      } finally {\n        this.loading = false\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.register {\n  padding: 20px;\n  max-width: 400px;\n  margin: 50px auto;\n  border: 1px solid #ccc;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.form-group {\n  margin-bottom: 15px;\n  text-align: left;\n}\n\n.form-group label {\n  display: block;\n  margin-bottom: 5px;\n  font-weight: bold;\n}\n\n.form-group input {\n  width: 100%;\n  padding: 10px;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  box-sizing: border-box;\n}\n\n.form-group small {\n  color: #666;\n  font-size: 12px;\n  margin-top: 5px;\n  display: block;\n}\n\nbutton {\n  background-color: #42b983;\n  color: white;\n  padding: 10px 15px;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 16px;\n  width: 100%;\n}\n\nbutton:hover:not(:disabled) {\n  background-color: #369f6e;\n}\n\nbutton:disabled {\n  background-color: #ccc;\n  cursor: not-allowed;\n}\n\n.error-message {\n  color: red;\n  margin-top: 10px;\n}\n\n.success-message {\n  color: green;\n  margin-top: 10px;\n}\n\n.login-link {\n  margin-top: 20px;\n  text-align: center;\n}\n\n.login-link a {\n  color: #42b983;\n  text-decoration: none;\n}\n\n.login-link a:hover {\n  text-decoration: underline;\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAU;;EAGZA,KAAK,EAAC;AAAY;;EAIlBA,KAAK,EAAC;AAAY;;EAKlBA,KAAK,EAAC;AAAY;;EAIlBA,KAAK,EAAC;AAAY;;EAIlBA,KAAK,EAAC;AAAY;;;;EAQTA,KAAK,EAAC;;;;EACJA,KAAK,EAAC;;;EAEnBA,KAAK,EAAC;AAAY;;;uBA/BzBC,mBAAA,CAkCM,OAlCNC,UAkCM,G,4BAjCJC,mBAAA,CAAa,YAAT,MAAI,qBACRA,mBAAA,CAyBO;IAzBAC,QAAM,EAAAC,MAAA,QAAAA,MAAA,MAAAC,cAAA,KAAAC,IAAA,KAAUC,QAAA,CAAAC,QAAA,IAAAD,QAAA,CAAAC,QAAA,IAAAF,IAAA,CAAQ;MAC7BJ,mBAAA,CAGM,OAHNO,UAGM,G,0BAFJP,mBAAA,CAAkC;IAA3BQ,GAAG,EAAC;EAAU,GAAC,MAAI,qB,gBAC1BR,mBAAA,CAAkE;IAA3DS,IAAI,EAAC,MAAM;IAACC,EAAE,EAAC,UAAU;+DAAUC,KAAA,CAAAC,IAAI,CAACC,QAAQ,GAAAC,MAAA;IAAEC,QAAQ,EAAR;iDAAfJ,KAAA,CAAAC,IAAI,CAACC,QAAQ,E,KAEzDb,mBAAA,CAIM,OAJNgB,UAIM,G,0BAHJhB,mBAAA,CAAiC;IAA1BQ,GAAG,EAAC;EAAU,GAAC,KAAG,qB,gBACzBR,mBAAA,CAAsE;IAA/DS,IAAI,EAAC,UAAU;IAACC,EAAE,EAAC,UAAU;+DAAUC,KAAA,CAAAC,IAAI,CAACK,QAAQ,GAAAH,MAAA;IAAEC,QAAQ,EAAR;iDAAfJ,KAAA,CAAAC,IAAI,CAACK,QAAQ,E,6BAC3DjB,mBAAA,CAAsC,eAA/B,yBAAuB,oB,GAEhCA,mBAAA,CAGM,OAHNkB,UAGM,G,0BAFJlB,mBAAA,CAA0C;IAAnCQ,GAAG,EAAC;EAAiB,GAAC,OAAK,qB,gBAClCR,mBAAA,CAA+E;IAAxES,IAAI,EAAC,UAAU;IAACC,EAAE,EAAC,iBAAiB;+DAAUC,KAAA,CAAAQ,eAAe,GAAAL,MAAA;IAAEC,QAAQ,EAAR;iDAAjBJ,KAAA,CAAAQ,eAAe,E,KAEtEnB,mBAAA,CAGM,OAHNoB,UAGM,G,4BAFJpB,mBAAA,CAAmC;IAA5BQ,GAAG,EAAC;EAAY,GAAC,KAAG,qB,gBAC3BR,mBAAA,CAA6D;IAAtDS,IAAI,EAAC,MAAM;IAACC,EAAE,EAAC,YAAY;+DAAUC,KAAA,CAAAC,IAAI,CAACS,UAAU,GAAAP,MAAA;iDAAfH,KAAA,CAAAC,IAAI,CAACS,UAAU,E,KAE7DrB,mBAAA,CAGM,OAHNsB,UAGM,G,4BAFJtB,mBAAA,CAA6B;IAAtBQ,GAAG,EAAC;EAAM,GAAC,KAAG,qB,gBACrBR,mBAAA,CAAiD;IAA1CS,IAAI,EAAC,MAAM;IAACC,EAAE,EAAC,MAAM;+DAAUC,KAAA,CAAAC,IAAI,CAACW,IAAI,GAAAT,MAAA;iDAATH,KAAA,CAAAC,IAAI,CAACW,IAAI,E,KAEjDvB,mBAAA,CAES;IAFDS,IAAI,EAAC,QAAQ;IAAEe,QAAQ,EAAEb,KAAA,CAAAc;sBAC5Bd,KAAA,CAAAc,OAAO,0CAAAC,UAAA,E,4BAGLf,KAAA,CAAAgB,KAAK,I,cAAd7B,mBAAA,CAAqD,KAArD8B,UAAqD,EAAAC,gBAAA,CAAZlB,KAAA,CAAAgB,KAAK,oB,mCACrChB,KAAA,CAAAmB,OAAO,I,cAAhBhC,mBAAA,CAA2D,KAA3DiC,UAA2D,EAAAF,gBAAA,CAAdlB,KAAA,CAAAmB,OAAO,oB,mCAEpD9B,mBAAA,CAEM,OAFNgC,WAEM,GADJhC,mBAAA,CAAuD,Y,6CAApD,OAAK,IAAAiC,YAAA,CAA2CC,sBAAA;IAA9BC,EAAE,EAAC;EAAQ;sBAAC,MAAIjC,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}