{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport DOMPurify from 'dompurify';\nimport { authAPI, announcementAPI } from '../services/api';\nexport default {\n  name: 'Dashboard',\n  data() {\n    return {\n      currentUser: null,\n      announcements: [],\n      loading: false,\n      // 模拟监控数据\n      securityAlerts: 12,\n      vulnerabilities: 5,\n      onlineUsers: 23\n    };\n  },\n  async mounted() {\n    await this.loadCurrentUser();\n    await this.loadAnnouncements();\n    this.startMonitoringSimulation();\n  },\n  methods: {\n    async loadCurrentUser() {\n      try {\n        const response = await authAPI.checkAuth();\n        this.currentUser = response.data.user;\n      } catch (error) {\n        console.error('获取用户信息失败:', error);\n        this.$router.push('/login');\n      }\n    },\n    async loadAnnouncements() {\n      this.loading = true;\n      try {\n        const response = await announcementAPI.getAnnouncements();\n        this.announcements = response.data;\n      } catch (error) {\n        console.error('加载公告失败:', error);\n        // 如果API调用失败，显示示例数据\n        this.announcements = [{\n          id: 1,\n          title: '社团招新公告',\n          content: '<p>欢迎参加网络信息安全社团的招新活动！</p>',\n          created_at: new Date().toISOString()\n        }];\n      } finally {\n        this.loading = false;\n      }\n    },\n    async logout() {\n      try {\n        await authAPI.logout();\n        this.$router.push('/login');\n      } catch (error) {\n        console.error('登出失败:', error);\n      }\n    },\n    purifyContent(content) {\n      return DOMPurify.sanitize(content);\n    },\n    formatDate(dateString) {\n      if (!dateString) return '未知';\n      const date = new Date(dateString);\n      return date.toLocaleDateString('zh-CN');\n    },\n    startMonitoringSimulation() {\n      // 模拟监控数据变化\n      setInterval(() => {\n        this.securityAlerts = Math.floor(Math.random() * 20) + 10;\n        this.vulnerabilities = Math.floor(Math.random() * 10) + 3;\n        this.onlineUsers = Math.floor(Math.random() * 50) + 15;\n      }, 30000); // 每30秒更新一次\n    }\n  }\n};", "map": {"version": 3, "names": ["DOMPurify", "authAPI", "announcementAPI", "name", "data", "currentUser", "announcements", "loading", "securityAlerts", "vulnerabilities", "onlineUsers", "mounted", "loadCurrentUser", "loadAnnouncements", "startMonitoringSimulation", "methods", "response", "checkAuth", "user", "error", "console", "$router", "push", "getAnnouncements", "id", "title", "content", "created_at", "Date", "toISOString", "logout", "purifyContent", "sanitize", "formatDate", "dateString", "date", "toLocaleDateString", "setInterval", "Math", "floor", "random"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Dashboard.vue"], "sourcesContent": ["<template>\r\n  <div class=\"dashboard\">\r\n    <!-- 用户信息头部 -->\r\n    <div class=\"dashboard-header\">\r\n      <div class=\"user-welcome\">\r\n        <h1>欢迎回来，{{ currentUser?.name || currentUser?.username }}！</h1>\r\n        <p class=\"user-role\">{{ currentUser?.role === 'admin' ? '管理员' : '社团成员' }}</p>\r\n      </div>\r\n      <div class=\"header-actions\">\r\n        <button @click=\"logout\" class=\"btn btn-secondary\">退出登录</button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 快速导航 -->\r\n    <div class=\"quick-nav\">\r\n      <h2>快速导航</h2>\r\n      <div class=\"nav-grid\">\r\n        <router-link to=\"/club-culture\" class=\"nav-card\">\r\n          <div class=\"nav-icon\">🏛️</div>\r\n          <h3>社团文化</h3>\r\n          <p>了解社团历史与文化</p>\r\n        </router-link>\r\n        <router-link to=\"/learning-resources\" class=\"nav-card\">\r\n          <div class=\"nav-icon\">📚</div>\r\n          <h3>学习资源</h3>\r\n          <p>获取学习材料和资源</p>\r\n        </router-link>\r\n        <router-link to=\"/past-activities\" class=\"nav-card\">\r\n          <div class=\"nav-icon\">🎯</div>\r\n          <h3>过往活动</h3>\r\n          <p>查看历史活动记录</p>\r\n        </router-link>\r\n        <router-link to=\"/cyber-security-news\" class=\"nav-card\">\r\n          <div class=\"nav-icon\">🔒</div>\r\n          <h3>安全资讯</h3>\r\n          <p>最新网络安全新闻</p>\r\n        </router-link>\r\n        <router-link to=\"/playground\" class=\"nav-card\">\r\n          <div class=\"nav-icon\">🎮</div>\r\n          <h3>练习场</h3>\r\n          <p>技能练习与挑战</p>\r\n        </router-link>\r\n        <router-link v-if=\"currentUser?.role === 'admin'\" to=\"/admin\" class=\"nav-card admin-card\">\r\n          <div class=\"nav-icon\">⚙️</div>\r\n          <h3>管理后台</h3>\r\n          <p>系统管理与配置</p>\r\n        </router-link>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 最新公告 -->\r\n    <div class=\"dashboard-section\">\r\n      <h2>最新公告</h2>\r\n      <div class=\"announcements-container\">\r\n        <div v-if=\"loading\" class=\"loading\">加载中...</div>\r\n        <div v-else-if=\"announcements.length === 0\" class=\"no-data\">暂无公告</div>\r\n        <div v-else class=\"announcements-list\">\r\n          <div v-for=\"announcement in announcements.slice(0, 3)\" :key=\"announcement.id\" class=\"announcement-item\">\r\n            <h3>{{ announcement.title }}</h3>\r\n            <p class=\"announcement-content\" v-html=\"purifyContent(announcement.content)\"></p>\r\n            <p class=\"announcement-date\">{{ formatDate(announcement.created_at) }}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 监控数据大屏预览 -->\r\n    <div class=\"dashboard-section\">\r\n      <h2>监控数据大屏</h2>\r\n      <div class=\"monitoring-preview\">\r\n        <div class=\"monitoring-card\">\r\n          <h3>🚨 安全告警</h3>\r\n          <div class=\"metric-value\">{{ securityAlerts }}</div>\r\n          <p>本月检测到的安全事件</p>\r\n        </div>\r\n        <div class=\"monitoring-card\">\r\n          <h3>🔍 漏洞扫描</h3>\r\n          <div class=\"metric-value\">{{ vulnerabilities }}</div>\r\n          <p>发现的潜在漏洞</p>\r\n        </div>\r\n        <div class=\"monitoring-card\">\r\n          <h3>👥 在线用户</h3>\r\n          <div class=\"metric-value\">{{ onlineUsers }}</div>\r\n          <p>当前活跃用户数</p>\r\n        </div>\r\n        <div class=\"monitoring-card\">\r\n          <h3>📊 系统状态</h3>\r\n          <div class=\"metric-value status-good\">正常</div>\r\n          <p>系统运行状态</p>\r\n        </div>\r\n      </div>\r\n      <div class=\"monitoring-note\">\r\n        <p>💡 完整的监控数据大屏功能正在开发中，将用于记录靶场漏洞告警信息，敬请期待！</p>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 个人信息 -->\r\n    <div class=\"dashboard-section\">\r\n      <h2>个人信息</h2>\r\n      <div class=\"user-info-card\">\r\n        <div class=\"info-row\">\r\n          <span class=\"info-label\">用户名：</span>\r\n          <span class=\"info-value\">{{ currentUser?.username }}</span>\r\n        </div>\r\n        <div class=\"info-row\">\r\n          <span class=\"info-label\">姓名：</span>\r\n          <span class=\"info-value\">{{ currentUser?.name || '未设置' }}</span>\r\n        </div>\r\n        <div class=\"info-row\">\r\n          <span class=\"info-label\">学号：</span>\r\n          <span class=\"info-value\">{{ currentUser?.student_id || '未设置' }}</span>\r\n        </div>\r\n        <div class=\"info-row\">\r\n          <span class=\"info-label\">角色：</span>\r\n          <span class=\"info-value\">{{ currentUser?.role === 'admin' ? '管理员' : '普通用户' }}</span>\r\n        </div>\r\n        <div class=\"info-row\">\r\n          <span class=\"info-label\">加入时间：</span>\r\n          <span class=\"info-value\">{{ formatDate(currentUser?.created_at) }}</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport DOMPurify from 'dompurify'\r\nimport { authAPI, announcementAPI } from '../services/api'\r\n\r\nexport default {\r\n  name: 'Dashboard',\r\n  data() {\r\n    return {\r\n      currentUser: null,\r\n      announcements: [],\r\n      loading: false,\r\n\r\n      // 模拟监控数据\r\n      securityAlerts: 12,\r\n      vulnerabilities: 5,\r\n      onlineUsers: 23,\r\n    }\r\n  },\r\n  async mounted() {\r\n    await this.loadCurrentUser()\r\n    await this.loadAnnouncements()\r\n    this.startMonitoringSimulation()\r\n  },\r\n  methods: {\r\n    async loadCurrentUser() {\r\n      try {\r\n        const response = await authAPI.checkAuth()\r\n        this.currentUser = response.data.user\r\n      } catch (error) {\r\n        console.error('获取用户信息失败:', error)\r\n        this.$router.push('/login')\r\n      }\r\n    },\r\n\r\n    async loadAnnouncements() {\r\n      this.loading = true\r\n      try {\r\n        const response = await announcementAPI.getAnnouncements()\r\n        this.announcements = response.data\r\n      } catch (error) {\r\n        console.error('加载公告失败:', error)\r\n        // 如果API调用失败，显示示例数据\r\n        this.announcements = [\r\n          {\r\n            id: 1,\r\n            title: '社团招新公告',\r\n            content: '<p>欢迎参加网络信息安全社团的招新活动！</p>',\r\n            created_at: new Date().toISOString()\r\n          }\r\n        ]\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    async logout() {\r\n      try {\r\n        await authAPI.logout()\r\n        this.$router.push('/login')\r\n      } catch (error) {\r\n        console.error('登出失败:', error)\r\n      }\r\n    },\r\n\r\n    purifyContent(content) {\r\n      return DOMPurify.sanitize(content)\r\n    },\r\n\r\n    formatDate(dateString) {\r\n      if (!dateString) return '未知'\r\n      const date = new Date(dateString)\r\n      return date.toLocaleDateString('zh-CN')\r\n    },\r\n\r\n    startMonitoringSimulation() {\r\n      // 模拟监控数据变化\r\n      setInterval(() => {\r\n        this.securityAlerts = Math.floor(Math.random() * 20) + 10\r\n        this.vulnerabilities = Math.floor(Math.random() * 10) + 3\r\n        this.onlineUsers = Math.floor(Math.random() * 50) + 15\r\n      }, 30000) // 每30秒更新一次\r\n    }\r\n  }\r\n}\r\n</script>"], "mappings": ";AA8HA,OAAOA,SAAQ,MAAO,WAAU;AAChC,SAASC,OAAO,EAAEC,eAAc,QAAS,iBAAgB;AAEzD,eAAe;EACbC,IAAI,EAAE,WAAW;EACjBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,WAAW,EAAE,IAAI;MACjBC,aAAa,EAAE,EAAE;MACjBC,OAAO,EAAE,KAAK;MAEd;MACAC,cAAc,EAAE,EAAE;MAClBC,eAAe,EAAE,CAAC;MAClBC,WAAW,EAAE;IACf;EACF,CAAC;EACD,MAAMC,OAAOA,CAAA,EAAG;IACd,MAAM,IAAI,CAACC,eAAe,CAAC;IAC3B,MAAM,IAAI,CAACC,iBAAiB,CAAC;IAC7B,IAAI,CAACC,yBAAyB,CAAC;EACjC,CAAC;EACDC,OAAO,EAAE;IACP,MAAMH,eAAeA,CAAA,EAAG;MACtB,IAAI;QACF,MAAMI,QAAO,GAAI,MAAMf,OAAO,CAACgB,SAAS,CAAC;QACzC,IAAI,CAACZ,WAAU,GAAIW,QAAQ,CAACZ,IAAI,CAACc,IAAG;MACtC,EAAE,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC,IAAI,CAACE,OAAO,CAACC,IAAI,CAAC,QAAQ;MAC5B;IACF,CAAC;IAED,MAAMT,iBAAiBA,CAAA,EAAG;MACxB,IAAI,CAACN,OAAM,GAAI,IAAG;MAClB,IAAI;QACF,MAAMS,QAAO,GAAI,MAAMd,eAAe,CAACqB,gBAAgB,CAAC;QACxD,IAAI,CAACjB,aAAY,GAAIU,QAAQ,CAACZ,IAAG;MACnC,EAAE,OAAOe,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;QAC9B;QACA,IAAI,CAACb,aAAY,GAAI,CACnB;UACEkB,EAAE,EAAE,CAAC;UACLC,KAAK,EAAE,QAAQ;UACfC,OAAO,EAAE,2BAA2B;UACpCC,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QACrC,EACF;MACF,UAAU;QACR,IAAI,CAACtB,OAAM,GAAI,KAAI;MACrB;IACF,CAAC;IAED,MAAMuB,MAAMA,CAAA,EAAG;MACb,IAAI;QACF,MAAM7B,OAAO,CAAC6B,MAAM,CAAC;QACrB,IAAI,CAACT,OAAO,CAACC,IAAI,CAAC,QAAQ;MAC5B,EAAE,OAAOH,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK;MAC9B;IACF,CAAC;IAEDY,aAAaA,CAACL,OAAO,EAAE;MACrB,OAAO1B,SAAS,CAACgC,QAAQ,CAACN,OAAO;IACnC,CAAC;IAEDO,UAAUA,CAACC,UAAU,EAAE;MACrB,IAAI,CAACA,UAAU,EAAE,OAAO,IAAG;MAC3B,MAAMC,IAAG,GAAI,IAAIP,IAAI,CAACM,UAAU;MAChC,OAAOC,IAAI,CAACC,kBAAkB,CAAC,OAAO;IACxC,CAAC;IAEDtB,yBAAyBA,CAAA,EAAG;MAC1B;MACAuB,WAAW,CAAC,MAAM;QAChB,IAAI,CAAC7B,cAAa,GAAI8B,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,IAAI,EAAE,IAAI,EAAC;QACxD,IAAI,CAAC/B,eAAc,GAAI6B,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,IAAI,EAAE,IAAI;QACxD,IAAI,CAAC9B,WAAU,GAAI4B,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,IAAI,EAAE,IAAI,EAAC;MACvD,CAAC,EAAE,KAAK,GAAE;IACZ;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}