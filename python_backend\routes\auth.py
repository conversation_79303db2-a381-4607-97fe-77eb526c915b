from flask import Blueprint, request, jsonify, current_app
from models import db, User
from utils.password_utils import check_password, is_weak_password
import jwt
from datetime import datetime, timedelta

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/login', methods=['POST'])
def login():
    username = request.json.get('username')
    password = request.json.get('password')

    if not username or not password:
        return jsonify({'msg': '用户名和密码是必填项'}), 400

    user = User.query.filter_by(username=username).first()

    if not user or not user.check_password(password):
        return jsonify({'msg': '无效的凭据'}), 400

    if is_weak_password(password):
        return jsonify({'msg': '您的密码过于简单，请修改密码。'}), 403

    payload = {
        'user': {
            'id': user.id,
            'role': user.role
        },
        'exp': datetime.utcnow() + timedelta(hours=1) # Token 有效期 1 小时
    }
    token = jwt.encode(payload, current_app.config['JWT_SECRET_KEY'], algorithm='HS256')

    # 将 token 设置为 HttpOnly cookie
    response = jsonify({'msg': '登录成功'})
    response.set_cookie(
        'token',
        token,
        httponly=True,
        secure=current_app.env == 'production', # 生产环境使用 HTTPS
        max_age=3600, # 1小时有效期 (秒)
        samesite='Lax' # CSRF 防护
    )
    return response
