from flask import Blueprint, request, jsonify, current_app
from models import db, User
from utils.password_utils import is_weak_password
from middleware.auth import token_required
import jwt
from datetime import datetime, timedelta

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/login', methods=['POST'])
def login():
    try:
        print(f"收到登录请求: {request.method} {request.url}")
        print(f"请求头: {dict(request.headers)}")
        print(f"请求数据: {request.get_data()}")

        if not request.json:
            print("错误: 没有JSON数据")
            return jsonify({'msg': '请求格式错误'}), 400

        username = request.json.get('username')
        password = request.json.get('password')

        print(f"用户名: {username}, 密码长度: {len(password) if password else 0}")

        if not username or not password:
            print("错误: 用户名或密码为空")
            return jsonify({'msg': '用户名和密码是必填项'}), 400

        user = User.query.filter_by(username=username).first()
        print(f"查找用户结果: {user}")

        if not user:
            print("错误: 用户不存在")
            return jsonify({'msg': '无效的凭据'}), 400

        if not user.check_password(password):
            print("错误: 密码不正确")
            return jsonify({'msg': '无效的凭据'}), 400

        print("登录验证成功")
    except Exception as e:
        print(f"登录过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'msg': '服务器内部错误'}), 500

    payload = {
        'user': {
            'id': user.id,
            'role': user.role
        },
        'exp': datetime.utcnow() + timedelta(hours=1) # Token 有效期 1 小时
    }
    token = jwt.encode(payload, current_app.config['JWT_SECRET_KEY'], algorithm='HS256')

    # 将 token 设置为 HttpOnly cookie
    response = jsonify({'msg': '登录成功'})
    response.set_cookie(
        'token',
        token,
        httponly=True,
        secure=current_app.env == 'production', # 生产环境使用 HTTPS
        max_age=3600, # 1小时有效期 (秒)
        samesite='Lax' # CSRF 防护
    )
    return response

@auth_bp.route('/check-auth', methods=['GET'])
@token_required
def check_auth():
    """检查用户认证状态"""
    return jsonify({
        'msg': '认证成功',
        'user': {
            'id': request.user.id,
            'username': request.user.username,
            'role': request.user.role,
            'name': request.user.name
        }
    }), 200

@auth_bp.route('/logout', methods=['POST'])
def logout():
    """用户登出"""
    response = jsonify({'msg': '登出成功'})
    response.set_cookie('token', '', expires=0)  # 清除 cookie
    return response

@auth_bp.route('/register', methods=['POST'])
def register():
    """用户注册"""
    data = request.get_json()

    username = data.get('username')
    password = data.get('password')
    student_id = data.get('student_id')
    name = data.get('name')

    if not username or not password:
        return jsonify({'msg': '用户名和密码是必填项'}), 400

    # 检查用户名是否已存在
    if User.query.filter_by(username=username).first():
        return jsonify({'msg': '用户名已存在'}), 400

    # 检查学号是否已存在
    if student_id and User.query.filter_by(student_id=student_id).first():
        return jsonify({'msg': '学号已存在'}), 400

    # 检查密码强度
    if is_weak_password(password):
        return jsonify({'msg': '密码过于简单，请使用包含大小写字母、数字和特殊字符的8位以上密码'}), 400

    # 创建新用户
    user = User(
        username=username,
        student_id=student_id,
        name=name,
        role='user'  # 默认角色为普通用户
    )
    user.set_password(password)

    try:
        db.session.add(user)
        db.session.commit()
        return jsonify({'msg': '注册成功'}), 201
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'注册失败: {e}')
        return jsonify({'msg': '注册失败，请稍后重试'}), 500
