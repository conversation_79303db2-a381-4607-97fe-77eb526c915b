from flask import Blueprint, request, jsonify, current_app
from models import db, User
from utils.password_utils import check_password, is_weak_password
from middleware.auth import token_required
import jwt
from datetime import datetime, timedelta

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/login', methods=['POST'])
def login():
    username = request.json.get('username')
    password = request.json.get('password')

    if not username or not password:
        return jsonify({'msg': '用户名和密码是必填项'}), 400

    user = User.query.filter_by(username=username).first()

    if not user or not user.check_password(password):
        return jsonify({'msg': '无效的凭据'}), 400

    if is_weak_password(password):
        return jsonify({'msg': '您的密码过于简单，请修改密码。'}), 403

    payload = {
        'user': {
            'id': user.id,
            'role': user.role
        },
        'exp': datetime.utcnow() + timedelta(hours=1) # Token 有效期 1 小时
    }
    token = jwt.encode(payload, current_app.config['JWT_SECRET_KEY'], algorithm='HS256')

    # 将 token 设置为 HttpOnly cookie
    response = jsonify({'msg': '登录成功'})
    response.set_cookie(
        'token',
        token,
        httponly=True,
        secure=current_app.env == 'production', # 生产环境使用 HTTPS
        max_age=3600, # 1小时有效期 (秒)
        samesite='Lax' # CSRF 防护
    )
    return response

@auth_bp.route('/check-auth', methods=['GET'])
@token_required
def check_auth():
    """检查用户认证状态"""
    return jsonify({
        'msg': '认证成功',
        'user': {
            'id': request.user.id,
            'username': request.user.username,
            'role': request.user.role,
            'name': request.user.name
        }
    }), 200

@auth_bp.route('/logout', methods=['POST'])
def logout():
    """用户登出"""
    response = jsonify({'msg': '登出成功'})
    response.set_cookie('token', '', expires=0)  # 清除 cookie
    return response

@auth_bp.route('/register', methods=['POST'])
def register():
    """用户注册"""
    data = request.get_json()

    username = data.get('username')
    password = data.get('password')
    student_id = data.get('student_id')
    name = data.get('name')

    if not username or not password:
        return jsonify({'msg': '用户名和密码是必填项'}), 400

    # 检查用户名是否已存在
    if User.query.filter_by(username=username).first():
        return jsonify({'msg': '用户名已存在'}), 400

    # 检查学号是否已存在
    if student_id and User.query.filter_by(student_id=student_id).first():
        return jsonify({'msg': '学号已存在'}), 400

    # 检查密码强度
    if is_weak_password(password):
        return jsonify({'msg': '密码过于简单，请使用包含大小写字母、数字和特殊字符的8位以上密码'}), 400

    # 创建新用户
    user = User(
        username=username,
        student_id=student_id,
        name=name,
        role='user'  # 默认角色为普通用户
    )
    user.set_password(password)

    try:
        db.session.add(user)
        db.session.commit()
        return jsonify({'msg': '注册成功'}), 201
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'注册失败: {e}')
        return jsonify({'msg': '注册失败，请稍后重试'}), 500
