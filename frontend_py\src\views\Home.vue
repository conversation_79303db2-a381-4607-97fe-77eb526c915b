<template>
  <div class="home">
    <h1>NIS 社团文化网站 - 首页</h1>
    <p>欢迎来到网络信息安全社团！</p>
    <h2>社团招新公告</h2>
    <div class="announcement-content" v-html="purifiedAnnouncementContent"></div>
  </div>
</template>

<script>
import DOMPurify from 'dompurify'; // 引入 DOMPurify

export default {
  name: 'Home',
  data() {
    return {
      // 示例公告内容，实际应从后端获取
      announcementContent: '<p>欢迎参加**网络信息安全社团**的招新活动！</p><script>alert("XSS Attack!");<\/script>',
    };
  },
  computed: {
    purifiedAnnouncementContent() {
      // 使用 DOMPurify 清理公告内容
      return DOMPurify.sanitize(this.announcementContent);
    },
  },
}
</script>

<style scoped>
.home {
  padding: 20px;
}
</style>