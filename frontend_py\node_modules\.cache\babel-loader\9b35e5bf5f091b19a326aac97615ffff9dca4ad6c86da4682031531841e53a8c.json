{"ast": null, "code": "import axios from 'axios';\n\n// 创建 axios 实例\nconst api = axios.create({\n  baseURL: process.env.NODE_ENV === 'production' ? '/api' : 'http://localhost:5000/api',\n  withCredentials: true,\n  // 发送 cookies\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// 请求拦截器\napi.interceptors.request.use(config => {\n  // 可以在这里添加 loading 状态\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// 响应拦截器\napi.interceptors.response.use(response => {\n  return response;\n}, error => {\n  if (error.response) {\n    // 处理不同的错误状态码\n    switch (error.response.status) {\n      case 401:\n        // 未认证，跳转到登录页\n        if (window.location.pathname !== '/login') {\n          window.location.href = '/login';\n        }\n        break;\n      case 403:\n        // 无权限\n        console.error('无权限访问');\n        break;\n      case 500:\n        console.error('服务器内部错误');\n        break;\n      default:\n        console.error('请求失败:', error.response.data.msg || '未知错误');\n    }\n  } else if (error.request) {\n    console.error('网络错误，请检查网络连接');\n  } else {\n    console.error('请求配置错误:', error.message);\n  }\n  return Promise.reject(error);\n});\n\n// 认证相关 API\nexport const authAPI = {\n  // 登录\n  login: credentials => api.post('/auth/login', credentials),\n  // 注册\n  register: userData => api.post('/auth/register', userData),\n  // 检查认证状态\n  checkAuth: () => api.get('/auth/check-auth'),\n  // 登出\n  logout: () => api.post('/auth/logout')\n};\n\n// 用户相关 API\nexport const userAPI = {\n  // 获取用户列表\n  getUsers: () => api.get('/users'),\n  // 获取用户详情\n  getUser: id => api.get(`/users/${id}`),\n  // 创建用户\n  createUser: userData => api.post('/users', userData),\n  // 更新用户\n  updateUser: (id, userData) => api.put(`/users/${id}`, userData),\n  // 删除用户\n  deleteUser: id => api.delete(`/users/${id}`)\n};\n\n// 公告相关 API\nexport const announcementAPI = {\n  getAnnouncements: () => api.get('/announcements'),\n  getAnnouncement: id => api.get(`/announcements/${id}`),\n  createAnnouncement: data => api.post('/announcements', data),\n  updateAnnouncement: (id, data) => api.put(`/announcements/${id}`, data),\n  deleteAnnouncement: id => api.delete(`/announcements/${id}`)\n};\n\n// 社团文化相关 API\nexport const clubCultureAPI = {\n  getClubCultures: () => api.get('/club-culture'),\n  getClubCulture: id => api.get(`/club-culture/${id}`),\n  createClubCulture: data => api.post('/club-culture', data),\n  updateClubCulture: (id, data) => api.put(`/club-culture/${id}`, data),\n  deleteClubCulture: id => api.delete(`/club-culture/${id}`)\n};\n\n// 学习资源相关 API\nexport const learningResourceAPI = {\n  getLearningResources: () => api.get('/learning-resources'),\n  getLearningResource: id => api.get(`/learning-resources/${id}`),\n  createLearningResource: data => api.post('/learning-resources', data),\n  updateLearningResource: (id, data) => api.put(`/learning-resources/${id}`, data),\n  deleteLearningResource: id => api.delete(`/learning-resources/${id}`)\n};\n\n// 过往活动相关 API\nexport const pastActivityAPI = {\n  getPastActivities: () => api.get('/past-activities'),\n  getPastActivity: id => api.get(`/past-activities/${id}`),\n  createPastActivity: data => api.post('/past-activities', data),\n  updatePastActivity: (id, data) => api.put(`/past-activities/${id}`, data),\n  deletePastActivity: id => api.delete(`/past-activities/${id}`)\n};\n\n// 网络安全新闻相关 API\nexport const cyberSecurityNewsAPI = {\n  getCyberSecurityNews: () => api.get('/cyber-security-news'),\n  getCyberSecurityNewsItem: id => api.get(`/cyber-security-news/${id}`),\n  createCyberSecurityNews: data => api.post('/cyber-security-news', data),\n  updateCyberSecurityNews: (id, data) => api.put(`/cyber-security-news/${id}`, data),\n  deleteCyberSecurityNews: id => api.delete(`/cyber-security-news/${id}`)\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "api", "create", "baseURL", "process", "env", "NODE_ENV", "withCredentials", "timeout", "headers", "interceptors", "request", "use", "config", "error", "Promise", "reject", "response", "status", "window", "location", "pathname", "href", "console", "data", "msg", "message", "authAPI", "login", "credentials", "post", "register", "userData", "checkAuth", "get", "logout", "userAPI", "getUsers", "getUser", "id", "createUser", "updateUser", "put", "deleteUser", "delete", "announcementAPI", "getAnnouncements", "getAnnouncement", "createAnnouncement", "updateAnnouncement", "deleteAnnouncement", "clubCultureAPI", "getClubCultures", "getClubCulture", "createClubCulture", "updateClubCulture", "deleteClubCulture", "learningResourceAPI", "getLearningResources", "getLearningResource", "createLearningResource", "updateLearningResource", "deleteLearningResource", "pastActivityAPI", "getPastActivities", "getPastActivity", "createPastActivity", "updatePastActivity", "deletePastActivity", "cyberSecurityNewsAPI", "getCyberSecurityNews", "getCyberSecurityNewsItem", "createCyberSecurityNews", "updateCyberSecurityNews", "deleteCyberSecurityNews"], "sources": ["C:/Users/<USER>/Desktop/cs/pacong_py/frontend_py/src/services/api.js"], "sourcesContent": ["import axios from 'axios'\n\n// 创建 axios 实例\nconst api = axios.create({\n  baseURL: process.env.NODE_ENV === 'production' ? '/api' : 'http://localhost:5000/api',\n  withCredentials: true, // 发送 cookies\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n})\n\n// 请求拦截器\napi.interceptors.request.use(\n  config => {\n    // 可以在这里添加 loading 状态\n    return config\n  },\n  error => {\n    return Promise.reject(error)\n  }\n)\n\n// 响应拦截器\napi.interceptors.response.use(\n  response => {\n    return response\n  },\n  error => {\n    if (error.response) {\n      // 处理不同的错误状态码\n      switch (error.response.status) {\n        case 401:\n          // 未认证，跳转到登录页\n          if (window.location.pathname !== '/login') {\n            window.location.href = '/login'\n          }\n          break\n        case 403:\n          // 无权限\n          console.error('无权限访问')\n          break\n        case 500:\n          console.error('服务器内部错误')\n          break\n        default:\n          console.error('请求失败:', error.response.data.msg || '未知错误')\n      }\n    } else if (error.request) {\n      console.error('网络错误，请检查网络连接')\n    } else {\n      console.error('请求配置错误:', error.message)\n    }\n    return Promise.reject(error)\n  }\n)\n\n// 认证相关 API\nexport const authAPI = {\n  // 登录\n  login: (credentials) => api.post('/auth/login', credentials),\n\n  // 注册\n  register: (userData) => api.post('/auth/register', userData),\n\n  // 检查认证状态\n  checkAuth: () => api.get('/auth/check-auth'),\n\n  // 登出\n  logout: () => api.post('/auth/logout')\n}\n\n// 用户相关 API\nexport const userAPI = {\n  // 获取用户列表\n  getUsers: () => api.get('/users'),\n  \n  // 获取用户详情\n  getUser: (id) => api.get(`/users/${id}`),\n  \n  // 创建用户\n  createUser: (userData) => api.post('/users', userData),\n  \n  // 更新用户\n  updateUser: (id, userData) => api.put(`/users/${id}`, userData),\n  \n  // 删除用户\n  deleteUser: (id) => api.delete(`/users/${id}`)\n}\n\n// 公告相关 API\nexport const announcementAPI = {\n  getAnnouncements: () => api.get('/announcements'),\n  getAnnouncement: (id) => api.get(`/announcements/${id}`),\n  createAnnouncement: (data) => api.post('/announcements', data),\n  updateAnnouncement: (id, data) => api.put(`/announcements/${id}`, data),\n  deleteAnnouncement: (id) => api.delete(`/announcements/${id}`)\n}\n\n// 社团文化相关 API\nexport const clubCultureAPI = {\n  getClubCultures: () => api.get('/club-culture'),\n  getClubCulture: (id) => api.get(`/club-culture/${id}`),\n  createClubCulture: (data) => api.post('/club-culture', data),\n  updateClubCulture: (id, data) => api.put(`/club-culture/${id}`, data),\n  deleteClubCulture: (id) => api.delete(`/club-culture/${id}`)\n}\n\n// 学习资源相关 API\nexport const learningResourceAPI = {\n  getLearningResources: () => api.get('/learning-resources'),\n  getLearningResource: (id) => api.get(`/learning-resources/${id}`),\n  createLearningResource: (data) => api.post('/learning-resources', data),\n  updateLearningResource: (id, data) => api.put(`/learning-resources/${id}`, data),\n  deleteLearningResource: (id) => api.delete(`/learning-resources/${id}`)\n}\n\n// 过往活动相关 API\nexport const pastActivityAPI = {\n  getPastActivities: () => api.get('/past-activities'),\n  getPastActivity: (id) => api.get(`/past-activities/${id}`),\n  createPastActivity: (data) => api.post('/past-activities', data),\n  updatePastActivity: (id, data) => api.put(`/past-activities/${id}`, data),\n  deletePastActivity: (id) => api.delete(`/past-activities/${id}`)\n}\n\n// 网络安全新闻相关 API\nexport const cyberSecurityNewsAPI = {\n  getCyberSecurityNews: () => api.get('/cyber-security-news'),\n  getCyberSecurityNewsItem: (id) => api.get(`/cyber-security-news/${id}`),\n  createCyberSecurityNews: (data) => api.post('/cyber-security-news', data),\n  updateCyberSecurityNews: (id, data) => api.put(`/cyber-security-news/${id}`, data),\n  deleteCyberSecurityNews: (id) => api.delete(`/cyber-security-news/${id}`)\n}\n\nexport default api\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,GAAG,GAAGD,KAAK,CAACE,MAAM,CAAC;EACvBC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG,MAAM,GAAG,2BAA2B;EACrFC,eAAe,EAAE,IAAI;EAAE;EACvBC,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAR,GAAG,CAACS,YAAY,CAACC,OAAO,CAACC,GAAG,CAC1BC,MAAM,IAAI;EACR;EACA,OAAOA,MAAM;AACf,CAAC,EACDC,KAAK,IAAI;EACP,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAb,GAAG,CAACS,YAAY,CAACO,QAAQ,CAACL,GAAG,CAC3BK,QAAQ,IAAI;EACV,OAAOA,QAAQ;AACjB,CAAC,EACDH,KAAK,IAAI;EACP,IAAIA,KAAK,CAACG,QAAQ,EAAE;IAClB;IACA,QAAQH,KAAK,CAACG,QAAQ,CAACC,MAAM;MAC3B,KAAK,GAAG;QACN;QACA,IAAIC,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,QAAQ,EAAE;UACzCF,MAAM,CAACC,QAAQ,CAACE,IAAI,GAAG,QAAQ;QACjC;QACA;MACF,KAAK,GAAG;QACN;QACAC,OAAO,CAACT,KAAK,CAAC,OAAO,CAAC;QACtB;MACF,KAAK,GAAG;QACNS,OAAO,CAACT,KAAK,CAAC,SAAS,CAAC;QACxB;MACF;QACES,OAAO,CAACT,KAAK,CAAC,OAAO,EAAEA,KAAK,CAACG,QAAQ,CAACO,IAAI,CAACC,GAAG,IAAI,MAAM,CAAC;IAC7D;EACF,CAAC,MAAM,IAAIX,KAAK,CAACH,OAAO,EAAE;IACxBY,OAAO,CAACT,KAAK,CAAC,cAAc,CAAC;EAC/B,CAAC,MAAM;IACLS,OAAO,CAACT,KAAK,CAAC,SAAS,EAAEA,KAAK,CAACY,OAAO,CAAC;EACzC;EACA,OAAOX,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,OAAO,MAAMa,OAAO,GAAG;EACrB;EACAC,KAAK,EAAGC,WAAW,IAAK5B,GAAG,CAAC6B,IAAI,CAAC,aAAa,EAAED,WAAW,CAAC;EAE5D;EACAE,QAAQ,EAAGC,QAAQ,IAAK/B,GAAG,CAAC6B,IAAI,CAAC,gBAAgB,EAAEE,QAAQ,CAAC;EAE5D;EACAC,SAAS,EAAEA,CAAA,KAAMhC,GAAG,CAACiC,GAAG,CAAC,kBAAkB,CAAC;EAE5C;EACAC,MAAM,EAAEA,CAAA,KAAMlC,GAAG,CAAC6B,IAAI,CAAC,cAAc;AACvC,CAAC;;AAED;AACA,OAAO,MAAMM,OAAO,GAAG;EACrB;EACAC,QAAQ,EAAEA,CAAA,KAAMpC,GAAG,CAACiC,GAAG,CAAC,QAAQ,CAAC;EAEjC;EACAI,OAAO,EAAGC,EAAE,IAAKtC,GAAG,CAACiC,GAAG,CAAC,UAAUK,EAAE,EAAE,CAAC;EAExC;EACAC,UAAU,EAAGR,QAAQ,IAAK/B,GAAG,CAAC6B,IAAI,CAAC,QAAQ,EAAEE,QAAQ,CAAC;EAEtD;EACAS,UAAU,EAAEA,CAACF,EAAE,EAAEP,QAAQ,KAAK/B,GAAG,CAACyC,GAAG,CAAC,UAAUH,EAAE,EAAE,EAAEP,QAAQ,CAAC;EAE/D;EACAW,UAAU,EAAGJ,EAAE,IAAKtC,GAAG,CAAC2C,MAAM,CAAC,UAAUL,EAAE,EAAE;AAC/C,CAAC;;AAED;AACA,OAAO,MAAMM,eAAe,GAAG;EAC7BC,gBAAgB,EAAEA,CAAA,KAAM7C,GAAG,CAACiC,GAAG,CAAC,gBAAgB,CAAC;EACjDa,eAAe,EAAGR,EAAE,IAAKtC,GAAG,CAACiC,GAAG,CAAC,kBAAkBK,EAAE,EAAE,CAAC;EACxDS,kBAAkB,EAAGxB,IAAI,IAAKvB,GAAG,CAAC6B,IAAI,CAAC,gBAAgB,EAAEN,IAAI,CAAC;EAC9DyB,kBAAkB,EAAEA,CAACV,EAAE,EAAEf,IAAI,KAAKvB,GAAG,CAACyC,GAAG,CAAC,kBAAkBH,EAAE,EAAE,EAAEf,IAAI,CAAC;EACvE0B,kBAAkB,EAAGX,EAAE,IAAKtC,GAAG,CAAC2C,MAAM,CAAC,kBAAkBL,EAAE,EAAE;AAC/D,CAAC;;AAED;AACA,OAAO,MAAMY,cAAc,GAAG;EAC5BC,eAAe,EAAEA,CAAA,KAAMnD,GAAG,CAACiC,GAAG,CAAC,eAAe,CAAC;EAC/CmB,cAAc,EAAGd,EAAE,IAAKtC,GAAG,CAACiC,GAAG,CAAC,iBAAiBK,EAAE,EAAE,CAAC;EACtDe,iBAAiB,EAAG9B,IAAI,IAAKvB,GAAG,CAAC6B,IAAI,CAAC,eAAe,EAAEN,IAAI,CAAC;EAC5D+B,iBAAiB,EAAEA,CAAChB,EAAE,EAAEf,IAAI,KAAKvB,GAAG,CAACyC,GAAG,CAAC,iBAAiBH,EAAE,EAAE,EAAEf,IAAI,CAAC;EACrEgC,iBAAiB,EAAGjB,EAAE,IAAKtC,GAAG,CAAC2C,MAAM,CAAC,iBAAiBL,EAAE,EAAE;AAC7D,CAAC;;AAED;AACA,OAAO,MAAMkB,mBAAmB,GAAG;EACjCC,oBAAoB,EAAEA,CAAA,KAAMzD,GAAG,CAACiC,GAAG,CAAC,qBAAqB,CAAC;EAC1DyB,mBAAmB,EAAGpB,EAAE,IAAKtC,GAAG,CAACiC,GAAG,CAAC,uBAAuBK,EAAE,EAAE,CAAC;EACjEqB,sBAAsB,EAAGpC,IAAI,IAAKvB,GAAG,CAAC6B,IAAI,CAAC,qBAAqB,EAAEN,IAAI,CAAC;EACvEqC,sBAAsB,EAAEA,CAACtB,EAAE,EAAEf,IAAI,KAAKvB,GAAG,CAACyC,GAAG,CAAC,uBAAuBH,EAAE,EAAE,EAAEf,IAAI,CAAC;EAChFsC,sBAAsB,EAAGvB,EAAE,IAAKtC,GAAG,CAAC2C,MAAM,CAAC,uBAAuBL,EAAE,EAAE;AACxE,CAAC;;AAED;AACA,OAAO,MAAMwB,eAAe,GAAG;EAC7BC,iBAAiB,EAAEA,CAAA,KAAM/D,GAAG,CAACiC,GAAG,CAAC,kBAAkB,CAAC;EACpD+B,eAAe,EAAG1B,EAAE,IAAKtC,GAAG,CAACiC,GAAG,CAAC,oBAAoBK,EAAE,EAAE,CAAC;EAC1D2B,kBAAkB,EAAG1C,IAAI,IAAKvB,GAAG,CAAC6B,IAAI,CAAC,kBAAkB,EAAEN,IAAI,CAAC;EAChE2C,kBAAkB,EAAEA,CAAC5B,EAAE,EAAEf,IAAI,KAAKvB,GAAG,CAACyC,GAAG,CAAC,oBAAoBH,EAAE,EAAE,EAAEf,IAAI,CAAC;EACzE4C,kBAAkB,EAAG7B,EAAE,IAAKtC,GAAG,CAAC2C,MAAM,CAAC,oBAAoBL,EAAE,EAAE;AACjE,CAAC;;AAED;AACA,OAAO,MAAM8B,oBAAoB,GAAG;EAClCC,oBAAoB,EAAEA,CAAA,KAAMrE,GAAG,CAACiC,GAAG,CAAC,sBAAsB,CAAC;EAC3DqC,wBAAwB,EAAGhC,EAAE,IAAKtC,GAAG,CAACiC,GAAG,CAAC,wBAAwBK,EAAE,EAAE,CAAC;EACvEiC,uBAAuB,EAAGhD,IAAI,IAAKvB,GAAG,CAAC6B,IAAI,CAAC,sBAAsB,EAAEN,IAAI,CAAC;EACzEiD,uBAAuB,EAAEA,CAAClC,EAAE,EAAEf,IAAI,KAAKvB,GAAG,CAACyC,GAAG,CAAC,wBAAwBH,EAAE,EAAE,EAAEf,IAAI,CAAC;EAClFkD,uBAAuB,EAAGnC,EAAE,IAAKtC,GAAG,CAAC2C,MAAM,CAAC,wBAAwBL,EAAE,EAAE;AAC1E,CAAC;AAED,eAAetC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}