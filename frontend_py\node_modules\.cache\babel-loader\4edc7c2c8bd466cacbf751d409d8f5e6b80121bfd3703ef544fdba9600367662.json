{"ast": null, "code": "import Navigation from './components/Navigation.vue';\nexport default {\n  name: 'App',\n  components: {\n    Navigation\n  }\n};", "map": {"version": 3, "names": ["Navigation", "name", "components"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\App.vue"], "sourcesContent": ["<template>\n  <div id=\"app\">\n    <Navigation />\n    <main class=\"main-content\">\n      <router-view/>\n    </main>\n  </div>\n</template>\n\n<script>\nimport Navigation from './components/Navigation.vue'\n\nexport default {\n  name: 'App',\n  components: {\n    Navigation\n  }\n}\n</script>\n\n<style>\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\n#app {\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  color: #2c3e50;\n  min-height: 100vh;\n  background-color: #f8f9fa;\n}\n\n.main-content {\n  min-height: calc(100vh - 70px);\n}\n\nbody {\n  margin: 0;\n  padding: 0;\n}\n</style>\n"], "mappings": "AAUA,OAAOA,UAAS,MAAO,6BAA4B;AAEnD,eAAe;EACbC,IAAI,EAAE,KAAK;EACXC,UAAU,EAAE;IACVF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}