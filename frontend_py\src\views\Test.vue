<template>
  <div class="test-page">
    <h1>前后端通信测试</h1>
    
    <!-- 测试后端连接 -->
    <div class="test-section">
      <h2>1. 测试后端连接</h2>
      <button @click="testBackendConnection" :disabled="loading">
        {{ loading ? '测试中...' : '测试后端连接' }}
      </button>
      <div v-if="connectionResult" class="result">
        <p :class="connectionResult.success ? 'success' : 'error'">
          {{ connectionResult.message }}
        </p>
      </div>
    </div>

    <!-- 测试用户注册 -->
    <div class="test-section">
      <h2>2. 测试用户注册</h2>
      <form @submit.prevent="testRegister">
        <div class="form-group">
          <label>用户名:</label>
          <input v-model="registerForm.username" type="text" required>
        </div>
        <div class="form-group">
          <label>密码:</label>
          <input v-model="registerForm.password" type="password" required>
        </div>
        <div class="form-group">
          <label>学号:</label>
          <input v-model="registerForm.student_id" type="text">
        </div>
        <div class="form-group">
          <label>姓名:</label>
          <input v-model="registerForm.name" type="text">
        </div>
        <button type="submit" :disabled="loading">
          {{ loading ? '注册中...' : '测试注册' }}
        </button>
      </form>
      <div v-if="registerResult" class="result">
        <p :class="registerResult.success ? 'success' : 'error'">
          {{ registerResult.message }}
        </p>
      </div>
    </div>

    <!-- 测试用户登录 -->
    <div class="test-section">
      <h2>3. 测试用户登录</h2>
      <form @submit.prevent="testLogin">
        <div class="form-group">
          <label>用户名:</label>
          <input v-model="loginForm.username" type="text" required>
        </div>
        <div class="form-group">
          <label>密码:</label>
          <input v-model="loginForm.password" type="password" required>
        </div>
        <button type="submit" :disabled="loading">
          {{ loading ? '登录中...' : '测试登录' }}
        </button>
      </form>
      <div v-if="loginResult" class="result">
        <p :class="loginResult.success ? 'success' : 'error'">
          {{ loginResult.message }}
        </p>
      </div>
    </div>

    <!-- 测试认证状态 -->
    <div class="test-section">
      <h2>4. 测试认证状态</h2>
      <button @click="testAuth" :disabled="loading">
        {{ loading ? '检查中...' : '检查认证状态' }}
      </button>
      <div v-if="authResult" class="result">
        <p :class="authResult.success ? 'success' : 'error'">
          {{ authResult.message }}
        </p>
        <div v-if="authResult.user" class="user-info">
          <h4>用户信息:</h4>
          <p>ID: {{ authResult.user.id }}</p>
          <p>用户名: {{ authResult.user.username }}</p>
          <p>角色: {{ authResult.user.role }}</p>
          <p>姓名: {{ authResult.user.name || '未设置' }}</p>
        </div>
      </div>
    </div>

    <!-- 测试登出 -->
    <div class="test-section">
      <h2>5. 测试登出</h2>
      <button @click="testLogout" :disabled="loading">
        {{ loading ? '登出中...' : '测试登出' }}
      </button>
      <div v-if="logoutResult" class="result">
        <p :class="logoutResult.success ? 'success' : 'error'">
          {{ logoutResult.message }}
        </p>
      </div>
    </div>
  </div>
</template>

<script>
import { authAPI } from '../services/api'

export default {
  name: 'Test',
  data() {
    return {
      loading: false,
      connectionResult: null,
      registerResult: null,
      loginResult: null,
      authResult: null,
      logoutResult: null,
      registerForm: {
        username: 'testuser' + Date.now(),
        password: 'TestPass123!',
        student_id: '2024001',
        name: '测试用户'
      },
      loginForm: {
        username: '',
        password: ''
      }
    }
  },
  methods: {
    async testBackendConnection() {
      this.loading = true
      this.connectionResult = null
      
      try {
        const response = await fetch('http://localhost:5000/')
        const data = await response.json()
        
        this.connectionResult = {
          success: true,
          message: `后端连接成功: ${data.msg}`
        }
      } catch (error) {
        this.connectionResult = {
          success: false,
          message: `后端连接失败: ${error.message}`
        }
      } finally {
        this.loading = false
      }
    },

    async testRegister() {
      this.loading = true
      this.registerResult = null
      
      try {
        const response = await authAPI.register(this.registerForm)
        this.registerResult = {
          success: true,
          message: response.data.msg
        }
        
        // 注册成功后，更新登录表单
        this.loginForm.username = this.registerForm.username
        this.loginForm.password = this.registerForm.password
      } catch (error) {
        this.registerResult = {
          success: false,
          message: error.response?.data?.msg || error.message
        }
      } finally {
        this.loading = false
      }
    },

    async testLogin() {
      this.loading = true
      this.loginResult = null
      
      try {
        const response = await authAPI.login(this.loginForm)
        this.loginResult = {
          success: true,
          message: response.data.msg
        }
      } catch (error) {
        this.loginResult = {
          success: false,
          message: error.response?.data?.msg || error.message
        }
      } finally {
        this.loading = false
      }
    },

    async testAuth() {
      this.loading = true
      this.authResult = null
      
      try {
        const response = await authAPI.checkAuth()
        this.authResult = {
          success: true,
          message: response.data.msg,
          user: response.data.user
        }
      } catch (error) {
        this.authResult = {
          success: false,
          message: error.response?.data?.msg || error.message
        }
      } finally {
        this.loading = false
      }
    },

    async testLogout() {
      this.loading = true
      this.logoutResult = null
      
      try {
        const response = await authAPI.logout()
        this.logoutResult = {
          success: true,
          message: response.data.msg
        }
      } catch (error) {
        this.logoutResult = {
          success: false,
          message: error.response?.data?.msg || error.message
        }
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.test-page {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-sizing: border-box;
}

button {
  background-color: #007bff;
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

button:hover:not(:disabled) {
  background-color: #0056b3;
}

.result {
  margin-top: 15px;
  padding: 10px;
  border-radius: 4px;
}

.success {
  color: #155724;
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
}

.error {
  color: #721c24;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
}

.user-info {
  margin-top: 10px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.user-info h4 {
  margin-top: 0;
}

.user-info p {
  margin: 5px 0;
}
</style>
