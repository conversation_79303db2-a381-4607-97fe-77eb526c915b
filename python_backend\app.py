import os
from flask import Flask, jsonify
from flask_cors import CORS
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

app = Flask(__name__)

# 配置数据库
app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL', 'mysql+pymysql://root:password@localhost/nis_club')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# 导入 db 实例并初始化
from models import db
db.init_app(app)

# 配置 CORS
CORS(app, supports_credentials=True, resources={r"/api/*": {"origins": "http://localhost:8080"}})

# 导入模型，确保在 db 对象创建后导入
from models import User, Announcement, ClubCulture, LearningResource, PastActivity, CyberSecurityNews

# 导入并注册蓝图 (路由)
from routes.auth import auth_bp
from routes.users import users_bp
from routes.announcements import announcements_bp
from routes.club_culture import club_culture_bp
from routes.learning_resources import learning_resources_bp
from routes.past_activities import past_activities_bp
from routes.cyber_security_news import cyber_security_news_bp

app.register_blueprint(auth_bp, url_prefix='/api/auth')
app.register_blueprint(users_bp, url_prefix='/api/users')
app.register_blueprint(announcements_bp, url_prefix='/api/announcements')
app.register_blueprint(club_culture_bp, url_prefix='/api/club-culture')
app.register_blueprint(learning_resources_bp, url_prefix='/api/learning-resources')
app.register_blueprint(past_activities_bp, url_prefix='/api/past-activities')
app.register_blueprint(cyber_security_news_bp, url_prefix='/api/cyber-security-news')

# 根路由
@app.route('/')
def home():
    return jsonify(msg='NIS 社团文化网站后端 API 运行中！')

# 全局错误处理
@app.errorhandler(Exception)
def handle_exception(e):
    # 记录错误
    app.logger.error('服务器内部错误', exc_info=e)
    return jsonify(msg='服务器内部错误！'), 500

if __name__ == '__main__':
    with app.app_context():
        db.create_all() # 创建所有数据库表
    app.run(debug=True, port=5000)
