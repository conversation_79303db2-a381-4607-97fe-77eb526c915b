import os
from flask import Flask, jsonify, request
from flask_cors import CORS
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

app = Flask(__name__)

# 配置数据库
app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL', 'mysql+pymysql://root:password@localhost/nis_club')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# 配置 JWT 和其他设置
app.config['JWT_SECRET_KEY'] = os.getenv('JWT_SECRET_KEY', 'supersecretjwtkey')
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'supersecretflaskkey')
app.config['UPLOAD_FOLDER'] = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')

# 导入 db 实例并初始化
from models import db
db.init_app(app)

# 配置 CORS - 开发环境使用宽松配置
CORS(app, supports_credentials=True, origins=["http://localhost:8080", "http://127.0.0.1:8080"])

# 添加请求日志
@app.before_request
def log_request():
    print(f"收到请求: {request.method} {request.url}")
    print(f"请求头: {dict(request.headers)}")
    if request.is_json:
        print(f"请求数据: {request.get_json()}")
    else:
        print(f"请求数据: {request.get_data()}")

# 导入模型，确保在 db 对象创建后导入
from models import User, Announcement, ClubCulture, LearningResource, PastActivity, CyberSecurityNews

# 导入并注册蓝图 (路由)
from routes.auth import auth_bp
from routes.users import users_bp
from routes.announcements import announcements_bp
from routes.club_culture import club_culture_bp
from routes.learning_resources import learning_resources_bp
from routes.past_activities import past_activities_bp
from routes.cyber_security_news import cyber_security_news_bp

app.register_blueprint(auth_bp, url_prefix='/api/auth')
app.register_blueprint(users_bp, url_prefix='/api/users')
app.register_blueprint(announcements_bp, url_prefix='/api/announcements')
app.register_blueprint(club_culture_bp, url_prefix='/api/club-culture')
app.register_blueprint(learning_resources_bp, url_prefix='/api/learning-resources')
app.register_blueprint(past_activities_bp, url_prefix='/api/past-activities')
app.register_blueprint(cyber_security_news_bp, url_prefix='/api/cyber-security-news')

# 根路由
@app.route('/')
def home():
    return jsonify(msg='NIS 社团文化网站后端 API 运行中！')

# 全局错误处理
@app.errorhandler(Exception)
def handle_exception(e):
    # 记录错误
    app.logger.error('服务器内部错误', exc_info=e)
    return jsonify(msg='服务器内部错误！'), 500

def init_admin_user():
    """初始化管理员账户"""
    admin_user = User.query.filter_by(username='admin').first()
    if not admin_user:
        admin_user = User(
            username='admin',
            student_id='ADMIN001',
            name='系统管理员',
            role='admin'
        )
        admin_user.set_password('Admin123!')
        db.session.add(admin_user)
        db.session.commit()
        print('管理员账户已创建: admin / Admin123!')
    else:
        print('管理员账户已存在')

if __name__ == '__main__':
    with app.app_context():
        db.create_all() # 创建所有数据库表
        init_admin_user() # 初始化管理员账户
    app.run(debug=True, port=5000)
