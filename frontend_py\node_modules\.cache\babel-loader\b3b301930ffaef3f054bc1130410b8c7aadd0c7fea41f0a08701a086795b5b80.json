{"ast": null, "code": "import { authAPI } from '../services/api';\nexport default {\n  name: 'Test',\n  data() {\n    return {\n      loading: false,\n      connectionResult: null,\n      registerResult: null,\n      loginResult: null,\n      authResult: null,\n      logoutResult: null,\n      registerForm: {\n        username: 'testuser' + Date.now(),\n        password: 'TestPass123!',\n        student_id: '2024001',\n        name: '测试用户'\n      },\n      loginForm: {\n        username: '',\n        password: ''\n      }\n    };\n  },\n  methods: {\n    async testBackendConnection() {\n      this.loading = true;\n      this.connectionResult = null;\n      try {\n        const response = await fetch('http://localhost:5000/');\n        const data = await response.json();\n        this.connectionResult = {\n          success: true,\n          message: `后端连接成功: ${data.msg}`\n        };\n      } catch (error) {\n        this.connectionResult = {\n          success: false,\n          message: `后端连接失败: ${error.message}`\n        };\n      } finally {\n        this.loading = false;\n      }\n    },\n    async testRegister() {\n      this.loading = true;\n      this.registerResult = null;\n      try {\n        const response = await authAPI.register(this.registerForm);\n        this.registerResult = {\n          success: true,\n          message: response.data.msg\n        };\n\n        // 注册成功后，更新登录表单\n        this.loginForm.username = this.registerForm.username;\n        this.loginForm.password = this.registerForm.password;\n      } catch (error) {\n        this.registerResult = {\n          success: false,\n          message: error.response?.data?.msg || error.message\n        };\n      } finally {\n        this.loading = false;\n      }\n    },\n    async testLogin() {\n      this.loading = true;\n      this.loginResult = null;\n      try {\n        const response = await authAPI.login(this.loginForm);\n        this.loginResult = {\n          success: true,\n          message: response.data.msg\n        };\n      } catch (error) {\n        this.loginResult = {\n          success: false,\n          message: error.response?.data?.msg || error.message\n        };\n      } finally {\n        this.loading = false;\n      }\n    },\n    async testAuth() {\n      this.loading = true;\n      this.authResult = null;\n      try {\n        const response = await authAPI.checkAuth();\n        this.authResult = {\n          success: true,\n          message: response.data.msg,\n          user: response.data.user\n        };\n      } catch (error) {\n        this.authResult = {\n          success: false,\n          message: error.response?.data?.msg || error.message\n        };\n      } finally {\n        this.loading = false;\n      }\n    },\n    async testLogout() {\n      this.loading = true;\n      this.logoutResult = null;\n      try {\n        const response = await authAPI.logout();\n        this.logoutResult = {\n          success: true,\n          message: response.data.msg\n        };\n      } catch (error) {\n        this.logoutResult = {\n          success: false,\n          message: error.response?.data?.msg || error.message\n        };\n      } finally {\n        this.loading = false;\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["authAPI", "name", "data", "loading", "connectionResult", "registerResult", "loginResult", "authResult", "logoutResult", "registerForm", "username", "Date", "now", "password", "student_id", "loginForm", "methods", "testBackendConnection", "response", "fetch", "json", "success", "message", "msg", "error", "testRegister", "register", "testLogin", "login", "testAuth", "checkAuth", "user", "testLogout", "logout"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Test.vue"], "sourcesContent": ["<template>\n  <div class=\"test-page\">\n    <h1>前后端通信测试</h1>\n    \n    <!-- 测试后端连接 -->\n    <div class=\"test-section\">\n      <h2>1. 测试后端连接</h2>\n      <button @click=\"testBackendConnection\" :disabled=\"loading\">\n        {{ loading ? '测试中...' : '测试后端连接' }}\n      </button>\n      <div v-if=\"connectionResult\" class=\"result\">\n        <p :class=\"connectionResult.success ? 'success' : 'error'\">\n          {{ connectionResult.message }}\n        </p>\n      </div>\n    </div>\n\n    <!-- 测试用户注册 -->\n    <div class=\"test-section\">\n      <h2>2. 测试用户注册</h2>\n      <form @submit.prevent=\"testRegister\">\n        <div class=\"form-group\">\n          <label>用户名:</label>\n          <input v-model=\"registerForm.username\" type=\"text\" required>\n        </div>\n        <div class=\"form-group\">\n          <label>密码:</label>\n          <input v-model=\"registerForm.password\" type=\"password\" required>\n        </div>\n        <div class=\"form-group\">\n          <label>学号:</label>\n          <input v-model=\"registerForm.student_id\" type=\"text\">\n        </div>\n        <div class=\"form-group\">\n          <label>姓名:</label>\n          <input v-model=\"registerForm.name\" type=\"text\">\n        </div>\n        <button type=\"submit\" :disabled=\"loading\">\n          {{ loading ? '注册中...' : '测试注册' }}\n        </button>\n      </form>\n      <div v-if=\"registerResult\" class=\"result\">\n        <p :class=\"registerResult.success ? 'success' : 'error'\">\n          {{ registerResult.message }}\n        </p>\n      </div>\n    </div>\n\n    <!-- 测试用户登录 -->\n    <div class=\"test-section\">\n      <h2>3. 测试用户登录</h2>\n      <form @submit.prevent=\"testLogin\">\n        <div class=\"form-group\">\n          <label>用户名:</label>\n          <input v-model=\"loginForm.username\" type=\"text\" required>\n        </div>\n        <div class=\"form-group\">\n          <label>密码:</label>\n          <input v-model=\"loginForm.password\" type=\"password\" required>\n        </div>\n        <button type=\"submit\" :disabled=\"loading\">\n          {{ loading ? '登录中...' : '测试登录' }}\n        </button>\n      </form>\n      <div v-if=\"loginResult\" class=\"result\">\n        <p :class=\"loginResult.success ? 'success' : 'error'\">\n          {{ loginResult.message }}\n        </p>\n      </div>\n    </div>\n\n    <!-- 测试认证状态 -->\n    <div class=\"test-section\">\n      <h2>4. 测试认证状态</h2>\n      <button @click=\"testAuth\" :disabled=\"loading\">\n        {{ loading ? '检查中...' : '检查认证状态' }}\n      </button>\n      <div v-if=\"authResult\" class=\"result\">\n        <p :class=\"authResult.success ? 'success' : 'error'\">\n          {{ authResult.message }}\n        </p>\n        <div v-if=\"authResult.user\" class=\"user-info\">\n          <h4>用户信息:</h4>\n          <p>ID: {{ authResult.user.id }}</p>\n          <p>用户名: {{ authResult.user.username }}</p>\n          <p>角色: {{ authResult.user.role }}</p>\n          <p>姓名: {{ authResult.user.name || '未设置' }}</p>\n        </div>\n      </div>\n    </div>\n\n    <!-- 测试登出 -->\n    <div class=\"test-section\">\n      <h2>5. 测试登出</h2>\n      <button @click=\"testLogout\" :disabled=\"loading\">\n        {{ loading ? '登出中...' : '测试登出' }}\n      </button>\n      <div v-if=\"logoutResult\" class=\"result\">\n        <p :class=\"logoutResult.success ? 'success' : 'error'\">\n          {{ logoutResult.message }}\n        </p>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { authAPI } from '../services/api'\n\nexport default {\n  name: 'Test',\n  data() {\n    return {\n      loading: false,\n      connectionResult: null,\n      registerResult: null,\n      loginResult: null,\n      authResult: null,\n      logoutResult: null,\n      registerForm: {\n        username: 'testuser' + Date.now(),\n        password: 'TestPass123!',\n        student_id: '2024001',\n        name: '测试用户'\n      },\n      loginForm: {\n        username: '',\n        password: ''\n      }\n    }\n  },\n  methods: {\n    async testBackendConnection() {\n      this.loading = true\n      this.connectionResult = null\n      \n      try {\n        const response = await fetch('http://localhost:5000/')\n        const data = await response.json()\n        \n        this.connectionResult = {\n          success: true,\n          message: `后端连接成功: ${data.msg}`\n        }\n      } catch (error) {\n        this.connectionResult = {\n          success: false,\n          message: `后端连接失败: ${error.message}`\n        }\n      } finally {\n        this.loading = false\n      }\n    },\n\n    async testRegister() {\n      this.loading = true\n      this.registerResult = null\n      \n      try {\n        const response = await authAPI.register(this.registerForm)\n        this.registerResult = {\n          success: true,\n          message: response.data.msg\n        }\n        \n        // 注册成功后，更新登录表单\n        this.loginForm.username = this.registerForm.username\n        this.loginForm.password = this.registerForm.password\n      } catch (error) {\n        this.registerResult = {\n          success: false,\n          message: error.response?.data?.msg || error.message\n        }\n      } finally {\n        this.loading = false\n      }\n    },\n\n    async testLogin() {\n      this.loading = true\n      this.loginResult = null\n      \n      try {\n        const response = await authAPI.login(this.loginForm)\n        this.loginResult = {\n          success: true,\n          message: response.data.msg\n        }\n      } catch (error) {\n        this.loginResult = {\n          success: false,\n          message: error.response?.data?.msg || error.message\n        }\n      } finally {\n        this.loading = false\n      }\n    },\n\n    async testAuth() {\n      this.loading = true\n      this.authResult = null\n      \n      try {\n        const response = await authAPI.checkAuth()\n        this.authResult = {\n          success: true,\n          message: response.data.msg,\n          user: response.data.user\n        }\n      } catch (error) {\n        this.authResult = {\n          success: false,\n          message: error.response?.data?.msg || error.message\n        }\n      } finally {\n        this.loading = false\n      }\n    },\n\n    async testLogout() {\n      this.loading = true\n      this.logoutResult = null\n      \n      try {\n        const response = await authAPI.logout()\n        this.logoutResult = {\n          success: true,\n          message: response.data.msg\n        }\n      } catch (error) {\n        this.logoutResult = {\n          success: false,\n          message: error.response?.data?.msg || error.message\n        }\n      } finally {\n        this.loading = false\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.test-page {\n  max-width: 800px;\n  margin: 0 auto;\n  padding: 20px;\n}\n\n.test-section {\n  margin-bottom: 30px;\n  padding: 20px;\n  border: 1px solid #ddd;\n  border-radius: 8px;\n}\n\n.form-group {\n  margin-bottom: 15px;\n}\n\n.form-group label {\n  display: block;\n  margin-bottom: 5px;\n  font-weight: bold;\n}\n\n.form-group input {\n  width: 100%;\n  padding: 8px;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  box-sizing: border-box;\n}\n\nbutton {\n  background-color: #007bff;\n  color: white;\n  padding: 10px 20px;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n}\n\nbutton:disabled {\n  background-color: #6c757d;\n  cursor: not-allowed;\n}\n\nbutton:hover:not(:disabled) {\n  background-color: #0056b3;\n}\n\n.result {\n  margin-top: 15px;\n  padding: 10px;\n  border-radius: 4px;\n}\n\n.success {\n  color: #155724;\n  background-color: #d4edda;\n  border: 1px solid #c3e6cb;\n}\n\n.error {\n  color: #721c24;\n  background-color: #f8d7da;\n  border: 1px solid #f5c6cb;\n}\n\n.user-info {\n  margin-top: 10px;\n  padding: 10px;\n  background-color: #f8f9fa;\n  border-radius: 4px;\n}\n\n.user-info h4 {\n  margin-top: 0;\n}\n\n.user-info p {\n  margin: 5px 0;\n}\n</style>\n"], "mappings": "AA2GA,SAASA,OAAM,QAAS,iBAAgB;AAExC,eAAe;EACbC,IAAI,EAAE,MAAM;EACZC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,OAAO,EAAE,KAAK;MACdC,gBAAgB,EAAE,IAAI;MACtBC,cAAc,EAAE,IAAI;MACpBC,WAAW,EAAE,IAAI;MACjBC,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,IAAI;MAClBC,YAAY,EAAE;QACZC,QAAQ,EAAE,UAAS,GAAIC,IAAI,CAACC,GAAG,CAAC,CAAC;QACjCC,QAAQ,EAAE,cAAc;QACxBC,UAAU,EAAE,SAAS;QACrBb,IAAI,EAAE;MACR,CAAC;MACDc,SAAS,EAAE;QACTL,QAAQ,EAAE,EAAE;QACZG,QAAQ,EAAE;MACZ;IACF;EACF,CAAC;EACDG,OAAO,EAAE;IACP,MAAMC,qBAAqBA,CAAA,EAAG;MAC5B,IAAI,CAACd,OAAM,GAAI,IAAG;MAClB,IAAI,CAACC,gBAAe,GAAI,IAAG;MAE3B,IAAI;QACF,MAAMc,QAAO,GAAI,MAAMC,KAAK,CAAC,wBAAwB;QACrD,MAAMjB,IAAG,GAAI,MAAMgB,QAAQ,CAACE,IAAI,CAAC;QAEjC,IAAI,CAAChB,gBAAe,GAAI;UACtBiB,OAAO,EAAE,IAAI;UACbC,OAAO,EAAE,WAAWpB,IAAI,CAACqB,GAAG;QAC9B;MACF,EAAE,OAAOC,KAAK,EAAE;QACd,IAAI,CAACpB,gBAAe,GAAI;UACtBiB,OAAO,EAAE,KAAK;UACdC,OAAO,EAAE,WAAWE,KAAK,CAACF,OAAO;QACnC;MACF,UAAU;QACR,IAAI,CAACnB,OAAM,GAAI,KAAI;MACrB;IACF,CAAC;IAED,MAAMsB,YAAYA,CAAA,EAAG;MACnB,IAAI,CAACtB,OAAM,GAAI,IAAG;MAClB,IAAI,CAACE,cAAa,GAAI,IAAG;MAEzB,IAAI;QACF,MAAMa,QAAO,GAAI,MAAMlB,OAAO,CAAC0B,QAAQ,CAAC,IAAI,CAACjB,YAAY;QACzD,IAAI,CAACJ,cAAa,GAAI;UACpBgB,OAAO,EAAE,IAAI;UACbC,OAAO,EAAEJ,QAAQ,CAAChB,IAAI,CAACqB;QACzB;;QAEA;QACA,IAAI,CAACR,SAAS,CAACL,QAAO,GAAI,IAAI,CAACD,YAAY,CAACC,QAAO;QACnD,IAAI,CAACK,SAAS,CAACF,QAAO,GAAI,IAAI,CAACJ,YAAY,CAACI,QAAO;MACrD,EAAE,OAAOW,KAAK,EAAE;QACd,IAAI,CAACnB,cAAa,GAAI;UACpBgB,OAAO,EAAE,KAAK;UACdC,OAAO,EAAEE,KAAK,CAACN,QAAQ,EAAEhB,IAAI,EAAEqB,GAAE,IAAKC,KAAK,CAACF;QAC9C;MACF,UAAU;QACR,IAAI,CAACnB,OAAM,GAAI,KAAI;MACrB;IACF,CAAC;IAED,MAAMwB,SAASA,CAAA,EAAG;MAChB,IAAI,CAACxB,OAAM,GAAI,IAAG;MAClB,IAAI,CAACG,WAAU,GAAI,IAAG;MAEtB,IAAI;QACF,MAAMY,QAAO,GAAI,MAAMlB,OAAO,CAAC4B,KAAK,CAAC,IAAI,CAACb,SAAS;QACnD,IAAI,CAACT,WAAU,GAAI;UACjBe,OAAO,EAAE,IAAI;UACbC,OAAO,EAAEJ,QAAQ,CAAChB,IAAI,CAACqB;QACzB;MACF,EAAE,OAAOC,KAAK,EAAE;QACd,IAAI,CAAClB,WAAU,GAAI;UACjBe,OAAO,EAAE,KAAK;UACdC,OAAO,EAAEE,KAAK,CAACN,QAAQ,EAAEhB,IAAI,EAAEqB,GAAE,IAAKC,KAAK,CAACF;QAC9C;MACF,UAAU;QACR,IAAI,CAACnB,OAAM,GAAI,KAAI;MACrB;IACF,CAAC;IAED,MAAM0B,QAAQA,CAAA,EAAG;MACf,IAAI,CAAC1B,OAAM,GAAI,IAAG;MAClB,IAAI,CAACI,UAAS,GAAI,IAAG;MAErB,IAAI;QACF,MAAMW,QAAO,GAAI,MAAMlB,OAAO,CAAC8B,SAAS,CAAC;QACzC,IAAI,CAACvB,UAAS,GAAI;UAChBc,OAAO,EAAE,IAAI;UACbC,OAAO,EAAEJ,QAAQ,CAAChB,IAAI,CAACqB,GAAG;UAC1BQ,IAAI,EAAEb,QAAQ,CAAChB,IAAI,CAAC6B;QACtB;MACF,EAAE,OAAOP,KAAK,EAAE;QACd,IAAI,CAACjB,UAAS,GAAI;UAChBc,OAAO,EAAE,KAAK;UACdC,OAAO,EAAEE,KAAK,CAACN,QAAQ,EAAEhB,IAAI,EAAEqB,GAAE,IAAKC,KAAK,CAACF;QAC9C;MACF,UAAU;QACR,IAAI,CAACnB,OAAM,GAAI,KAAI;MACrB;IACF,CAAC;IAED,MAAM6B,UAAUA,CAAA,EAAG;MACjB,IAAI,CAAC7B,OAAM,GAAI,IAAG;MAClB,IAAI,CAACK,YAAW,GAAI,IAAG;MAEvB,IAAI;QACF,MAAMU,QAAO,GAAI,MAAMlB,OAAO,CAACiC,MAAM,CAAC;QACtC,IAAI,CAACzB,YAAW,GAAI;UAClBa,OAAO,EAAE,IAAI;UACbC,OAAO,EAAEJ,QAAQ,CAAChB,IAAI,CAACqB;QACzB;MACF,EAAE,OAAOC,KAAK,EAAE;QACd,IAAI,CAAChB,YAAW,GAAI;UAClBa,OAAO,EAAE,KAAK;UACdC,OAAO,EAAEE,KAAK,CAACN,QAAQ,EAAEhB,IAAI,EAAEqB,GAAE,IAAKC,KAAK,CAACF;QAC9C;MACF,UAAU;QACR,IAAI,CAACnB,OAAM,GAAI,KAAI;MACrB;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}