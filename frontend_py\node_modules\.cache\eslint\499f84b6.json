[{"C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\main.js": "1", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\App.vue": "2", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\router\\index.js": "3", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\ClubCulture.vue": "4", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\PastActivities.vue": "5", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\LearningResources.vue": "6", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Home.vue": "7", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\CyberSecurityNews.vue": "8", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Dashboard.vue": "9", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Login.vue": "10", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Admin.vue": "11", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Playground.vue": "12", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Test.vue": "13", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\services\\api.js": "14", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Register.vue": "15", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\components\\Navigation.vue": "16", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\SimpleTest.vue": "17"}, {"size": 153, "mtime": 1752858518037, "results": "18", "hashOfConfig": "19"}, {"size": 662, "mtime": 1752859786081, "results": "20", "hashOfConfig": "19"}, {"size": 3340, "mtime": 1752860439481, "results": "21", "hashOfConfig": "19"}, {"size": 8232, "mtime": 1752859689559, "results": "22", "hashOfConfig": "19"}, {"size": 3317, "mtime": 1752871879975, "results": "23", "hashOfConfig": "19"}, {"size": 3476, "mtime": 1752871828752, "results": "24", "hashOfConfig": "19"}, {"size": 6089, "mtime": 1752859360267, "results": "25", "hashOfConfig": "19"}, {"size": 3770, "mtime": 1752871920443, "results": "26", "hashOfConfig": "19"}, {"size": 11563, "mtime": 1752859555950, "results": "27", "hashOfConfig": "19"}, {"size": 3943, "mtime": 1752860324646, "results": "28", "hashOfConfig": "19"}, {"size": 17089, "mtime": 1752859466798, "results": "29", "hashOfConfig": "19"}, {"size": 4271, "mtime": 1752859853170, "results": "30", "hashOfConfig": "19"}, {"size": 7889, "mtime": 1752858661970, "results": "31", "hashOfConfig": "19"}, {"size": 4150, "mtime": 1752858620916, "results": "32", "hashOfConfig": "19"}, {"size": 3882, "mtime": 1752859242639, "results": "33", "hashOfConfig": "19"}, {"size": 4821, "mtime": 1752859756390, "results": "34", "hashOfConfig": "19"}, {"size": 2217, "mtime": 1752860414327, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "38"}, "1l8h3ax", {"filePath": "39", "messages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "41"}, {"filePath": "42", "messages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "38"}, {"filePath": "44", "messages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "41"}, {"filePath": "46", "messages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "41"}, {"filePath": "52", "messages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "41"}, {"filePath": "56", "messages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "41"}, {"filePath": "58", "messages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "41"}, {"filePath": "60", "messages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "41"}, {"filePath": "62", "messages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "41"}, {"filePath": "64", "messages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "38"}, {"filePath": "66", "messages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "41"}, {"filePath": "68", "messages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "41"}, {"filePath": "70", "messages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "41"}, "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\main.js", [], [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\App.vue", [], [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\router\\index.js", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\ClubCulture.vue", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\PastActivities.vue", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\LearningResources.vue", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Home.vue", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\CyberSecurityNews.vue", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Dashboard.vue", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Login.vue", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Admin.vue", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Playground.vue", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Test.vue", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\services\\api.js", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Register.vue", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\components\\Navigation.vue", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\SimpleTest.vue", []]