[{"C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\main.js": "1", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\App.vue": "2", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\router\\index.js": "3", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\ClubCulture.vue": "4", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\PastActivities.vue": "5", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\LearningResources.vue": "6", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Home.vue": "7", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\CyberSecurityNews.vue": "8", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Dashboard.vue": "9", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Login.vue": "10", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Admin.vue": "11", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Playground.vue": "12", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Test.vue": "13", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\services\\api.js": "14", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Register.vue": "15", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\components\\Navigation.vue": "16"}, {"size": 153, "mtime": 1752858518037, "results": "17", "hashOfConfig": "18"}, {"size": 662, "mtime": 1752859786081, "results": "19", "hashOfConfig": "18"}, {"size": 3183, "mtime": 1752859268158, "results": "20", "hashOfConfig": "18"}, {"size": 8232, "mtime": 1752859689559, "results": "21", "hashOfConfig": "18"}, {"size": 1167, "mtime": 1752834532539, "results": "22", "hashOfConfig": "18"}, {"size": 1176, "mtime": 1752834434777, "results": "23", "hashOfConfig": "18"}, {"size": 6089, "mtime": 1752859360267, "results": "24", "hashOfConfig": "18"}, {"size": 1170, "mtime": 1752834552200, "results": "25", "hashOfConfig": "18"}, {"size": 11563, "mtime": 1752859555950, "results": "26", "hashOfConfig": "18"}, {"size": 3387, "mtime": 1752859217443, "results": "27", "hashOfConfig": "18"}, {"size": 17089, "mtime": 1752859466798, "results": "28", "hashOfConfig": "18"}, {"size": 4271, "mtime": 1752859853170, "results": "29", "hashOfConfig": "18"}, {"size": 7889, "mtime": 1752858661970, "results": "30", "hashOfConfig": "18"}, {"size": 4150, "mtime": 1752858620916, "results": "31", "hashOfConfig": "18"}, {"size": 3882, "mtime": 1752859242639, "results": "32", "hashOfConfig": "18"}, {"size": 4821, "mtime": 1752859756390, "results": "33", "hashOfConfig": "18"}, {"filePath": "34", "messages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1l8h3ax", {"filePath": "36", "messages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\main.js", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\App.vue", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\router\\index.js", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\ClubCulture.vue", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\PastActivities.vue", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\LearningResources.vue", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Home.vue", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\CyberSecurityNews.vue", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Dashboard.vue", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Login.vue", ["66"], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Admin.vue", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Playground.vue", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Test.vue", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\services\\api.js", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Register.vue", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\components\\Navigation.vue", [], {"ruleId": "67", "severity": 1, "message": "68", "line": 57, "column": 15, "nodeType": "69", "messageId": "70", "endLine": 57, "endColumn": 23}, "no-unused-vars", "'response' is assigned a value but never used.", "Identifier", "unusedVar"]