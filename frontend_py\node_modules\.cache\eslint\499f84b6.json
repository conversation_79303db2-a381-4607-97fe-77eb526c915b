[{"C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\main.js": "1", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\App.vue": "2", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\router\\index.js": "3", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\ClubCulture.vue": "4", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\PastActivities.vue": "5", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\LearningResources.vue": "6", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Home.vue": "7", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\CyberSecurityNews.vue": "8", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Dashboard.vue": "9", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Login.vue": "10", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Admin.vue": "11", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Playground.vue": "12", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Test.vue": "13", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\services\\api.js": "14"}, {"size": 153, "mtime": 1752858518037, "results": "15", "hashOfConfig": "16"}, {"size": 1059, "mtime": 1752806841845, "results": "17", "hashOfConfig": "16"}, {"size": 3040, "mtime": 1752858687967, "results": "18", "hashOfConfig": "16"}, {"size": 1289, "mtime": 1752858786905, "results": "19", "hashOfConfig": "16"}, {"size": 1167, "mtime": 1752834532539, "results": "20", "hashOfConfig": "16"}, {"size": 1176, "mtime": 1752834434777, "results": "21", "hashOfConfig": "16"}, {"size": 850, "mtime": 1752834313718, "results": "22", "hashOfConfig": "16"}, {"size": 1170, "mtime": 1752834552200, "results": "23", "hashOfConfig": "16"}, {"size": 403, "mtime": 1752809387822, "results": "24", "hashOfConfig": "16"}, {"size": 2477, "mtime": 1752833728952, "results": "25", "hashOfConfig": "16"}, {"size": 373, "mtime": 1752809324778, "results": "26", "hashOfConfig": "16"}, {"size": 343, "mtime": 1752809375981, "results": "27", "hashOfConfig": "16"}, {"size": 7889, "mtime": 1752858661970, "results": "28", "hashOfConfig": "16"}, {"size": 4150, "mtime": 1752858620916, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1l8h3ax", {"filePath": "32", "messages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "50", "messages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\main.js", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\App.vue", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\router\\index.js", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\ClubCulture.vue", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\PastActivities.vue", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\LearningResources.vue", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Home.vue", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\CyberSecurityNews.vue", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Dashboard.vue", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Login.vue", ["58"], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Admin.vue", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Playground.vue", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Test.vue", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\services\\api.js", [], {"ruleId": "59", "severity": 1, "message": "60", "line": 46, "column": 15, "nodeType": "61", "messageId": "62", "endLine": 46, "endColumn": 18}, "no-unused-vars", "'res' is assigned a value but never used.", "Identifier", "unusedVar"]