{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { authAPI, userAPI, announcementAPI, clubCultureAPI, learningResourceAPI, pastActivityAPI } from '../services/api';\nexport default {\n  name: 'Admin',\n  data() {\n    return {\n      currentUser: null,\n      activeTab: 'users',\n      tabs: [{\n        key: 'users',\n        label: '用户管理'\n      }, {\n        key: 'announcements',\n        label: '公告管理'\n      }, {\n        key: 'culture',\n        label: '社团文化'\n      }, {\n        key: 'resources',\n        label: '学习资源'\n      }, {\n        key: 'activities',\n        label: '过往活动'\n      }],\n      // 数据\n      users: [],\n      announcements: [],\n      clubCultures: [],\n      learningResources: [],\n      pastActivities: [],\n      // 模态框状态\n      showCreateUserModal: false,\n      showCreateAnnouncementModal: false,\n      showCreateCultureModal: false,\n      showCreateResourceModal: false,\n      showCreateActivityModal: false,\n      loading: false\n    };\n  },\n  async mounted() {\n    await this.loadCurrentUser();\n    await this.loadData();\n  },\n  methods: {\n    async loadCurrentUser() {\n      try {\n        const response = await authAPI.checkAuth();\n        this.currentUser = response.data.user;\n      } catch (error) {\n        console.error('获取用户信息失败:', error);\n        this.$router.push('/login');\n      }\n    },\n    async loadData() {\n      this.loading = true;\n      try {\n        await Promise.all([this.loadUsers(), this.loadAnnouncements(), this.loadClubCultures(), this.loadLearningResources(), this.loadPastActivities()]);\n      } catch (error) {\n        console.error('加载数据失败:', error);\n      } finally {\n        this.loading = false;\n      }\n    },\n    async loadUsers() {\n      try {\n        const response = await userAPI.getUsers();\n        this.users = response.data;\n      } catch (error) {\n        console.error('加载用户列表失败:', error);\n        this.users = [];\n      }\n    },\n    async loadAnnouncements() {\n      try {\n        const response = await announcementAPI.getAnnouncements();\n        this.announcements = response.data;\n      } catch (error) {\n        console.error('加载公告列表失败:', error);\n        this.announcements = [];\n      }\n    },\n    async loadClubCultures() {\n      try {\n        const response = await clubCultureAPI.getClubCultures();\n        this.clubCultures = response.data;\n      } catch (error) {\n        console.error('加载社团文化列表失败:', error);\n        this.clubCultures = [];\n      }\n    },\n    async loadLearningResources() {\n      try {\n        const response = await learningResourceAPI.getLearningResources();\n        this.learningResources = response.data;\n      } catch (error) {\n        console.error('加载学习资源列表失败:', error);\n        this.learningResources = [];\n      }\n    },\n    async loadPastActivities() {\n      try {\n        const response = await pastActivityAPI.getPastActivities();\n        this.pastActivities = response.data;\n      } catch (error) {\n        console.error('加载过往活动列表失败:', error);\n        this.pastActivities = [];\n      }\n    },\n    async logout() {\n      try {\n        await authAPI.logout();\n        this.$router.push('/login');\n      } catch (error) {\n        console.error('登出失败:', error);\n      }\n    },\n    // 用户管理方法\n    editUser(user) {\n      // TODO: 实现用户编辑功能\n      console.log('编辑用户:', user);\n    },\n    async deleteUser(userId) {\n      if (confirm('确定要删除这个用户吗？')) {\n        try {\n          await userAPI.deleteUser(userId);\n          await this.loadUsers();\n        } catch (error) {\n          console.error('删除用户失败:', error);\n        }\n      }\n    },\n    // 公告管理方法\n    editAnnouncement(announcement) {\n      // TODO: 实现公告编辑功能\n      console.log('编辑公告:', announcement);\n    },\n    async deleteAnnouncement(announcementId) {\n      if (confirm('确定要删除这个公告吗？')) {\n        try {\n          await announcementAPI.deleteAnnouncement(announcementId);\n          await this.loadAnnouncements();\n        } catch (error) {\n          console.error('删除公告失败:', error);\n        }\n      }\n    },\n    // 社团文化管理方法\n    editCulture(culture) {\n      // TODO: 实现社团文化编辑功能\n      console.log('编辑社团文化:', culture);\n    },\n    async deleteCulture(cultureId) {\n      if (confirm('确定要删除这个内容吗？')) {\n        try {\n          await clubCultureAPI.deleteClubCulture(cultureId);\n          await this.loadClubCultures();\n        } catch (error) {\n          console.error('删除社团文化失败:', error);\n        }\n      }\n    },\n    // 学习资源管理方法\n    editResource(resource) {\n      // TODO: 实现学习资源编辑功能\n      console.log('编辑学习资源:', resource);\n    },\n    async deleteResource(resourceId) {\n      if (confirm('确定要删除这个资源吗？')) {\n        try {\n          await learningResourceAPI.deleteLearningResource(resourceId);\n          await this.loadLearningResources();\n        } catch (error) {\n          console.error('删除学习资源失败:', error);\n        }\n      }\n    },\n    // 过往活动管理方法\n    editActivity(activity) {\n      // TODO: 实现过往活动编辑功能\n      console.log('编辑过往活动:', activity);\n    },\n    async deleteActivity(activityId) {\n      if (confirm('确定要删除这个活动吗？')) {\n        try {\n          await pastActivityAPI.deletePastActivity(activityId);\n          await this.loadPastActivities();\n        } catch (error) {\n          console.error('删除过往活动失败:', error);\n        }\n      }\n    },\n    // 工具方法\n    formatDate(dateString) {\n      if (!dateString) return '-';\n      const date = new Date(dateString);\n      return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN');\n    },\n    truncateText(text, length) {\n      if (!text) return '-';\n      // 移除HTML标签\n      const plainText = text.replace(/<[^>]*>/g, '');\n      return plainText.length > length ? plainText.substring(0, length) + '...' : plainText;\n    }\n  }\n};", "map": {"version": 3, "names": ["authAPI", "userAPI", "announcementAPI", "clubCultureAPI", "learningResourceAPI", "pastActivityAPI", "name", "data", "currentUser", "activeTab", "tabs", "key", "label", "users", "announcements", "clubCultures", "learningResources", "pastActivities", "showCreateUserModal", "showCreateAnnouncementModal", "showCreateCultureModal", "showCreateResourceModal", "showCreateActivityModal", "loading", "mounted", "loadCurrentUser", "loadData", "methods", "response", "checkAuth", "user", "error", "console", "$router", "push", "Promise", "all", "loadUsers", "loadAnnouncements", "loadClubCultures", "loadLearningResources", "loadPastActivities", "getUsers", "getAnnouncements", "getClubCultures", "getLearningResources", "getPastActivities", "logout", "editUser", "log", "deleteUser", "userId", "confirm", "editAnnouncement", "announcement", "deleteAnnouncement", "announcementId", "editCulture", "culture", "deleteCulture", "cultureId", "deleteClubCulture", "editResource", "resource", "deleteResource", "resourceId", "deleteLearningResource", "editActivity", "activity", "deleteActivity", "activityId", "deletePastActivity", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "toLocaleTimeString", "truncateText", "text", "length", "plainText", "replace", "substring"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Admin.vue"], "sourcesContent": ["<template>\r\n  <div class=\"admin\">\r\n    <div class=\"admin-header\">\r\n      <h1>管理员后台</h1>\r\n      <div class=\"admin-actions\">\r\n        <span>欢迎，{{ currentUser?.name || currentUser?.username }}</span>\r\n        <button @click=\"logout\" class=\"btn btn-secondary\">退出登录</button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 导航标签 -->\r\n    <div class=\"admin-tabs\">\r\n      <button\r\n        v-for=\"tab in tabs\"\r\n        :key=\"tab.key\"\r\n        @click=\"activeTab = tab.key\"\r\n        :class=\"['tab-btn', { active: activeTab === tab.key }]\"\r\n      >\r\n        {{ tab.label }}\r\n      </button>\r\n    </div>\r\n\r\n    <!-- 内容区域 -->\r\n    <div class=\"admin-content\">\r\n      <!-- 用户管理 -->\r\n      <div v-if=\"activeTab === 'users'\" class=\"tab-content\">\r\n        <div class=\"content-header\">\r\n          <h2>用户管理</h2>\r\n          <button @click=\"showCreateUserModal = true\" class=\"btn btn-primary\">添加用户</button>\r\n        </div>\r\n\r\n        <div class=\"table-container\">\r\n          <table class=\"admin-table\">\r\n            <thead>\r\n              <tr>\r\n                <th>ID</th>\r\n                <th>用户名</th>\r\n                <th>姓名</th>\r\n                <th>学号</th>\r\n                <th>角色</th>\r\n                <th>创建时间</th>\r\n                <th>操作</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              <tr v-for=\"user in users\" :key=\"user.id\">\r\n                <td>{{ user.id }}</td>\r\n                <td>{{ user.username }}</td>\r\n                <td>{{ user.name || '-' }}</td>\r\n                <td>{{ user.student_id || '-' }}</td>\r\n                <td>\r\n                  <span :class=\"['role-badge', user.role]\">\r\n                    {{ user.role === 'admin' ? '管理员' : '普通用户' }}\r\n                  </span>\r\n                </td>\r\n                <td>{{ formatDate(user.created_at) }}</td>\r\n                <td>\r\n                  <button @click=\"editUser(user)\" class=\"btn btn-sm btn-primary\">编辑</button>\r\n                  <button @click=\"deleteUser(user.id)\" class=\"btn btn-sm btn-danger\" v-if=\"user.id !== currentUser?.id\">删除</button>\r\n                </td>\r\n              </tr>\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 公告管理 -->\r\n      <div v-if=\"activeTab === 'announcements'\" class=\"tab-content\">\r\n        <div class=\"content-header\">\r\n          <h2>公告管理</h2>\r\n          <button @click=\"showCreateAnnouncementModal = true\" class=\"btn btn-primary\">发布公告</button>\r\n        </div>\r\n\r\n        <div class=\"table-container\">\r\n          <table class=\"admin-table\">\r\n            <thead>\r\n              <tr>\r\n                <th>ID</th>\r\n                <th>标题</th>\r\n                <th>内容预览</th>\r\n                <th>创建时间</th>\r\n                <th>操作</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              <tr v-for=\"announcement in announcements\" :key=\"announcement.id\">\r\n                <td>{{ announcement.id }}</td>\r\n                <td>{{ announcement.title }}</td>\r\n                <td>{{ truncateText(announcement.content, 50) }}</td>\r\n                <td>{{ formatDate(announcement.created_at) }}</td>\r\n                <td>\r\n                  <button @click=\"editAnnouncement(announcement)\" class=\"btn btn-sm btn-primary\">编辑</button>\r\n                  <button @click=\"deleteAnnouncement(announcement.id)\" class=\"btn btn-sm btn-danger\">删除</button>\r\n                </td>\r\n              </tr>\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 社团文化管理 -->\r\n      <div v-if=\"activeTab === 'culture'\" class=\"tab-content\">\r\n        <div class=\"content-header\">\r\n          <h2>社团文化管理</h2>\r\n          <button @click=\"showCreateCultureModal = true\" class=\"btn btn-primary\">添加内容</button>\r\n        </div>\r\n\r\n        <div class=\"table-container\">\r\n          <table class=\"admin-table\">\r\n            <thead>\r\n              <tr>\r\n                <th>ID</th>\r\n                <th>标题</th>\r\n                <th>内容预览</th>\r\n                <th>创建时间</th>\r\n                <th>操作</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              <tr v-for=\"culture in clubCultures\" :key=\"culture.id\">\r\n                <td>{{ culture.id }}</td>\r\n                <td>{{ culture.title }}</td>\r\n                <td>{{ truncateText(culture.content, 50) }}</td>\r\n                <td>{{ formatDate(culture.created_at) }}</td>\r\n                <td>\r\n                  <button @click=\"editCulture(culture)\" class=\"btn btn-sm btn-primary\">编辑</button>\r\n                  <button @click=\"deleteCulture(culture.id)\" class=\"btn btn-sm btn-danger\">删除</button>\r\n                </td>\r\n              </tr>\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 学习资源管理 -->\r\n      <div v-if=\"activeTab === 'resources'\" class=\"tab-content\">\r\n        <div class=\"content-header\">\r\n          <h2>学习资源管理</h2>\r\n          <button @click=\"showCreateResourceModal = true\" class=\"btn btn-primary\">添加资源</button>\r\n        </div>\r\n\r\n        <div class=\"table-container\">\r\n          <table class=\"admin-table\">\r\n            <thead>\r\n              <tr>\r\n                <th>ID</th>\r\n                <th>标题</th>\r\n                <th>类型</th>\r\n                <th>创建时间</th>\r\n                <th>操作</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              <tr v-for=\"resource in learningResources\" :key=\"resource.id\">\r\n                <td>{{ resource.id }}</td>\r\n                <td>{{ resource.title }}</td>\r\n                <td>{{ resource.file_path ? '文件' : '文本' }}</td>\r\n                <td>{{ formatDate(resource.created_at) }}</td>\r\n                <td>\r\n                  <button @click=\"editResource(resource)\" class=\"btn btn-sm btn-primary\">编辑</button>\r\n                  <button @click=\"deleteResource(resource.id)\" class=\"btn btn-sm btn-danger\">删除</button>\r\n                </td>\r\n              </tr>\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 过往活动管理 -->\r\n      <div v-if=\"activeTab === 'activities'\" class=\"tab-content\">\r\n        <div class=\"content-header\">\r\n          <h2>过往活动管理</h2>\r\n          <button @click=\"showCreateActivityModal = true\" class=\"btn btn-primary\">添加活动</button>\r\n        </div>\r\n\r\n        <div class=\"table-container\">\r\n          <table class=\"admin-table\">\r\n            <thead>\r\n              <tr>\r\n                <th>ID</th>\r\n                <th>标题</th>\r\n                <th>活动日期</th>\r\n                <th>创建时间</th>\r\n                <th>操作</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              <tr v-for=\"activity in pastActivities\" :key=\"activity.id\">\r\n                <td>{{ activity.id }}</td>\r\n                <td>{{ activity.title }}</td>\r\n                <td>{{ formatDate(activity.activity_date) }}</td>\r\n                <td>{{ formatDate(activity.created_at) }}</td>\r\n                <td>\r\n                  <button @click=\"editActivity(activity)\" class=\"btn btn-sm btn-primary\">编辑</button>\r\n                  <button @click=\"deleteActivity(activity.id)\" class=\"btn btn-sm btn-danger\">删除</button>\r\n                </td>\r\n              </tr>\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 模态框组件将在这里添加 -->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  authAPI,\r\n  userAPI,\r\n  announcementAPI,\r\n  clubCultureAPI,\r\n  learningResourceAPI,\r\n  pastActivityAPI\r\n} from '../services/api'\r\n\r\nexport default {\r\n  name: 'Admin',\r\n  data() {\r\n    return {\r\n      currentUser: null,\r\n      activeTab: 'users',\r\n      tabs: [\r\n        { key: 'users', label: '用户管理' },\r\n        { key: 'announcements', label: '公告管理' },\r\n        { key: 'culture', label: '社团文化' },\r\n        { key: 'resources', label: '学习资源' },\r\n        { key: 'activities', label: '过往活动' }\r\n      ],\r\n\r\n      // 数据\r\n      users: [],\r\n      announcements: [],\r\n      clubCultures: [],\r\n      learningResources: [],\r\n      pastActivities: [],\r\n\r\n      // 模态框状态\r\n      showCreateUserModal: false,\r\n      showCreateAnnouncementModal: false,\r\n      showCreateCultureModal: false,\r\n      showCreateResourceModal: false,\r\n      showCreateActivityModal: false,\r\n\r\n      loading: false\r\n    }\r\n  },\r\n  async mounted() {\r\n    await this.loadCurrentUser()\r\n    await this.loadData()\r\n  },\r\n  methods: {\r\n    async loadCurrentUser() {\r\n      try {\r\n        const response = await authAPI.checkAuth()\r\n        this.currentUser = response.data.user\r\n      } catch (error) {\r\n        console.error('获取用户信息失败:', error)\r\n        this.$router.push('/login')\r\n      }\r\n    },\r\n\r\n    async loadData() {\r\n      this.loading = true\r\n      try {\r\n        await Promise.all([\r\n          this.loadUsers(),\r\n          this.loadAnnouncements(),\r\n          this.loadClubCultures(),\r\n          this.loadLearningResources(),\r\n          this.loadPastActivities()\r\n        ])\r\n      } catch (error) {\r\n        console.error('加载数据失败:', error)\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    async loadUsers() {\r\n      try {\r\n        const response = await userAPI.getUsers()\r\n        this.users = response.data\r\n      } catch (error) {\r\n        console.error('加载用户列表失败:', error)\r\n        this.users = []\r\n      }\r\n    },\r\n\r\n    async loadAnnouncements() {\r\n      try {\r\n        const response = await announcementAPI.getAnnouncements()\r\n        this.announcements = response.data\r\n      } catch (error) {\r\n        console.error('加载公告列表失败:', error)\r\n        this.announcements = []\r\n      }\r\n    },\r\n\r\n    async loadClubCultures() {\r\n      try {\r\n        const response = await clubCultureAPI.getClubCultures()\r\n        this.clubCultures = response.data\r\n      } catch (error) {\r\n        console.error('加载社团文化列表失败:', error)\r\n        this.clubCultures = []\r\n      }\r\n    },\r\n\r\n    async loadLearningResources() {\r\n      try {\r\n        const response = await learningResourceAPI.getLearningResources()\r\n        this.learningResources = response.data\r\n      } catch (error) {\r\n        console.error('加载学习资源列表失败:', error)\r\n        this.learningResources = []\r\n      }\r\n    },\r\n\r\n    async loadPastActivities() {\r\n      try {\r\n        const response = await pastActivityAPI.getPastActivities()\r\n        this.pastActivities = response.data\r\n      } catch (error) {\r\n        console.error('加载过往活动列表失败:', error)\r\n        this.pastActivities = []\r\n      }\r\n    },\r\n\r\n    async logout() {\r\n      try {\r\n        await authAPI.logout()\r\n        this.$router.push('/login')\r\n      } catch (error) {\r\n        console.error('登出失败:', error)\r\n      }\r\n    },\r\n\r\n    // 用户管理方法\r\n    editUser(user) {\r\n      // TODO: 实现用户编辑功能\r\n      console.log('编辑用户:', user)\r\n    },\r\n\r\n    async deleteUser(userId) {\r\n      if (confirm('确定要删除这个用户吗？')) {\r\n        try {\r\n          await userAPI.deleteUser(userId)\r\n          await this.loadUsers()\r\n        } catch (error) {\r\n          console.error('删除用户失败:', error)\r\n        }\r\n      }\r\n    },\r\n\r\n    // 公告管理方法\r\n    editAnnouncement(announcement) {\r\n      // TODO: 实现公告编辑功能\r\n      console.log('编辑公告:', announcement)\r\n    },\r\n\r\n    async deleteAnnouncement(announcementId) {\r\n      if (confirm('确定要删除这个公告吗？')) {\r\n        try {\r\n          await announcementAPI.deleteAnnouncement(announcementId)\r\n          await this.loadAnnouncements()\r\n        } catch (error) {\r\n          console.error('删除公告失败:', error)\r\n        }\r\n      }\r\n    },\r\n\r\n    // 社团文化管理方法\r\n    editCulture(culture) {\r\n      // TODO: 实现社团文化编辑功能\r\n      console.log('编辑社团文化:', culture)\r\n    },\r\n\r\n    async deleteCulture(cultureId) {\r\n      if (confirm('确定要删除这个内容吗？')) {\r\n        try {\r\n          await clubCultureAPI.deleteClubCulture(cultureId)\r\n          await this.loadClubCultures()\r\n        } catch (error) {\r\n          console.error('删除社团文化失败:', error)\r\n        }\r\n      }\r\n    },\r\n\r\n    // 学习资源管理方法\r\n    editResource(resource) {\r\n      // TODO: 实现学习资源编辑功能\r\n      console.log('编辑学习资源:', resource)\r\n    },\r\n\r\n    async deleteResource(resourceId) {\r\n      if (confirm('确定要删除这个资源吗？')) {\r\n        try {\r\n          await learningResourceAPI.deleteLearningResource(resourceId)\r\n          await this.loadLearningResources()\r\n        } catch (error) {\r\n          console.error('删除学习资源失败:', error)\r\n        }\r\n      }\r\n    },\r\n\r\n    // 过往活动管理方法\r\n    editActivity(activity) {\r\n      // TODO: 实现过往活动编辑功能\r\n      console.log('编辑过往活动:', activity)\r\n    },\r\n\r\n    async deleteActivity(activityId) {\r\n      if (confirm('确定要删除这个活动吗？')) {\r\n        try {\r\n          await pastActivityAPI.deletePastActivity(activityId)\r\n          await this.loadPastActivities()\r\n        } catch (error) {\r\n          console.error('删除过往活动失败:', error)\r\n        }\r\n      }\r\n    },\r\n\r\n    // 工具方法\r\n    formatDate(dateString) {\r\n      if (!dateString) return '-'\r\n      const date = new Date(dateString)\r\n      return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN')\r\n    },\r\n\r\n    truncateText(text, length) {\r\n      if (!text) return '-'\r\n      // 移除HTML标签\r\n      const plainText = text.replace(/<[^>]*>/g, '')\r\n      return plainText.length > length ? plainText.substring(0, length) + '...' : plainText\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.admin {\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n  padding: 20px;\r\n}\r\n\r\n.admin-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 30px;\r\n  padding-bottom: 20px;\r\n  border-bottom: 2px solid #eee;\r\n}\r\n\r\n.admin-header h1 {\r\n  color: #333;\r\n  margin: 0;\r\n}\r\n\r\n.admin-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n}\r\n\r\n.admin-tabs {\r\n  display: flex;\r\n  gap: 10px;\r\n  margin-bottom: 30px;\r\n  border-bottom: 1px solid #ddd;\r\n}\r\n\r\n.tab-btn {\r\n  padding: 12px 24px;\r\n  border: none;\r\n  background: none;\r\n  cursor: pointer;\r\n  border-bottom: 3px solid transparent;\r\n  font-size: 16px;\r\n  color: #666;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.tab-btn:hover {\r\n  color: #42b983;\r\n}\r\n\r\n.tab-btn.active {\r\n  color: #42b983;\r\n  border-bottom-color: #42b983;\r\n  font-weight: bold;\r\n}\r\n\r\n.admin-content {\r\n  min-height: 500px;\r\n}\r\n\r\n.tab-content {\r\n  animation: fadeIn 0.3s ease;\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from { opacity: 0; transform: translateY(10px); }\r\n  to { opacity: 1; transform: translateY(0); }\r\n}\r\n\r\n.content-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.content-header h2 {\r\n  color: #333;\r\n  margin: 0;\r\n}\r\n\r\n.btn {\r\n  padding: 8px 16px;\r\n  border: none;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  text-decoration: none;\r\n  display: inline-block;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.btn-primary {\r\n  background-color: #42b983;\r\n  color: white;\r\n}\r\n\r\n.btn-primary:hover {\r\n  background-color: #369f6e;\r\n}\r\n\r\n.btn-secondary {\r\n  background-color: #6c757d;\r\n  color: white;\r\n}\r\n\r\n.btn-secondary:hover {\r\n  background-color: #545b62;\r\n}\r\n\r\n.btn-danger {\r\n  background-color: #dc3545;\r\n  color: white;\r\n}\r\n\r\n.btn-danger:hover {\r\n  background-color: #c82333;\r\n}\r\n\r\n.btn-sm {\r\n  padding: 4px 8px;\r\n  font-size: 12px;\r\n  margin-right: 5px;\r\n}\r\n\r\n.table-container {\r\n  background: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.admin-table {\r\n  width: 100%;\r\n  border-collapse: collapse;\r\n}\r\n\r\n.admin-table th,\r\n.admin-table td {\r\n  padding: 12px;\r\n  text-align: left;\r\n  border-bottom: 1px solid #eee;\r\n}\r\n\r\n.admin-table th {\r\n  background-color: #f8f9fa;\r\n  font-weight: bold;\r\n  color: #333;\r\n}\r\n\r\n.admin-table tr:hover {\r\n  background-color: #f8f9fa;\r\n}\r\n\r\n.role-badge {\r\n  padding: 4px 8px;\r\n  border-radius: 12px;\r\n  font-size: 12px;\r\n  font-weight: bold;\r\n}\r\n\r\n.role-badge.admin {\r\n  background-color: #dc3545;\r\n  color: white;\r\n}\r\n\r\n.role-badge.user {\r\n  background-color: #28a745;\r\n  color: white;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .admin {\r\n    padding: 10px;\r\n  }\r\n\r\n  .admin-header {\r\n    flex-direction: column;\r\n    gap: 15px;\r\n    text-align: center;\r\n  }\r\n\r\n  .admin-tabs {\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  .content-header {\r\n    flex-direction: column;\r\n    gap: 15px;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .table-container {\r\n    overflow-x: auto;\r\n  }\r\n\r\n  .admin-table {\r\n    min-width: 600px;\r\n  }\r\n}\r\n</style>"], "mappings": ";AAgNA,SACEA,OAAO,EACPC,OAAO,EACPC,eAAe,EACfC,cAAc,EACdC,mBAAmB,EACnBC,eAAc,QACT,iBAAgB;AAEvB,eAAe;EACbC,IAAI,EAAE,OAAO;EACbC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,WAAW,EAAE,IAAI;MACjBC,SAAS,EAAE,OAAO;MAClBC,IAAI,EAAE,CACJ;QAAEC,GAAG,EAAE,OAAO;QAAEC,KAAK,EAAE;MAAO,CAAC,EAC/B;QAAED,GAAG,EAAE,eAAe;QAAEC,KAAK,EAAE;MAAO,CAAC,EACvC;QAAED,GAAG,EAAE,SAAS;QAAEC,KAAK,EAAE;MAAO,CAAC,EACjC;QAAED,GAAG,EAAE,WAAW;QAAEC,KAAK,EAAE;MAAO,CAAC,EACnC;QAAED,GAAG,EAAE,YAAY;QAAEC,KAAK,EAAE;MAAO,EACpC;MAED;MACAC,KAAK,EAAE,EAAE;MACTC,aAAa,EAAE,EAAE;MACjBC,YAAY,EAAE,EAAE;MAChBC,iBAAiB,EAAE,EAAE;MACrBC,cAAc,EAAE,EAAE;MAElB;MACAC,mBAAmB,EAAE,KAAK;MAC1BC,2BAA2B,EAAE,KAAK;MAClCC,sBAAsB,EAAE,KAAK;MAC7BC,uBAAuB,EAAE,KAAK;MAC9BC,uBAAuB,EAAE,KAAK;MAE9BC,OAAO,EAAE;IACX;EACF,CAAC;EACD,MAAMC,OAAOA,CAAA,EAAG;IACd,MAAM,IAAI,CAACC,eAAe,CAAC;IAC3B,MAAM,IAAI,CAACC,QAAQ,CAAC;EACtB,CAAC;EACDC,OAAO,EAAE;IACP,MAAMF,eAAeA,CAAA,EAAG;MACtB,IAAI;QACF,MAAMG,QAAO,GAAI,MAAM5B,OAAO,CAAC6B,SAAS,CAAC;QACzC,IAAI,CAACrB,WAAU,GAAIoB,QAAQ,CAACrB,IAAI,CAACuB,IAAG;MACtC,EAAE,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC,IAAI,CAACE,OAAO,CAACC,IAAI,CAAC,QAAQ;MAC5B;IACF,CAAC;IAED,MAAMR,QAAQA,CAAA,EAAG;MACf,IAAI,CAACH,OAAM,GAAI,IAAG;MAClB,IAAI;QACF,MAAMY,OAAO,CAACC,GAAG,CAAC,CAChB,IAAI,CAACC,SAAS,CAAC,CAAC,EAChB,IAAI,CAACC,iBAAiB,CAAC,CAAC,EACxB,IAAI,CAACC,gBAAgB,CAAC,CAAC,EACvB,IAAI,CAACC,qBAAqB,CAAC,CAAC,EAC5B,IAAI,CAACC,kBAAkB,CAAC,EACzB;MACH,EAAE,OAAOV,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;MAChC,UAAU;QACR,IAAI,CAACR,OAAM,GAAI,KAAI;MACrB;IACF,CAAC;IAED,MAAMc,SAASA,CAAA,EAAG;MAChB,IAAI;QACF,MAAMT,QAAO,GAAI,MAAM3B,OAAO,CAACyC,QAAQ,CAAC;QACxC,IAAI,CAAC7B,KAAI,GAAIe,QAAQ,CAACrB,IAAG;MAC3B,EAAE,OAAOwB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC,IAAI,CAAClB,KAAI,GAAI,EAAC;MAChB;IACF,CAAC;IAED,MAAMyB,iBAAiBA,CAAA,EAAG;MACxB,IAAI;QACF,MAAMV,QAAO,GAAI,MAAM1B,eAAe,CAACyC,gBAAgB,CAAC;QACxD,IAAI,CAAC7B,aAAY,GAAIc,QAAQ,CAACrB,IAAG;MACnC,EAAE,OAAOwB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC,IAAI,CAACjB,aAAY,GAAI,EAAC;MACxB;IACF,CAAC;IAED,MAAMyB,gBAAgBA,CAAA,EAAG;MACvB,IAAI;QACF,MAAMX,QAAO,GAAI,MAAMzB,cAAc,CAACyC,eAAe,CAAC;QACtD,IAAI,CAAC7B,YAAW,GAAIa,QAAQ,CAACrB,IAAG;MAClC,EAAE,OAAOwB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK;QAClC,IAAI,CAAChB,YAAW,GAAI,EAAC;MACvB;IACF,CAAC;IAED,MAAMyB,qBAAqBA,CAAA,EAAG;MAC5B,IAAI;QACF,MAAMZ,QAAO,GAAI,MAAMxB,mBAAmB,CAACyC,oBAAoB,CAAC;QAChE,IAAI,CAAC7B,iBAAgB,GAAIY,QAAQ,CAACrB,IAAG;MACvC,EAAE,OAAOwB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK;QAClC,IAAI,CAACf,iBAAgB,GAAI,EAAC;MAC5B;IACF,CAAC;IAED,MAAMyB,kBAAkBA,CAAA,EAAG;MACzB,IAAI;QACF,MAAMb,QAAO,GAAI,MAAMvB,eAAe,CAACyC,iBAAiB,CAAC;QACzD,IAAI,CAAC7B,cAAa,GAAIW,QAAQ,CAACrB,IAAG;MACpC,EAAE,OAAOwB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK;QAClC,IAAI,CAACd,cAAa,GAAI,EAAC;MACzB;IACF,CAAC;IAED,MAAM8B,MAAMA,CAAA,EAAG;MACb,IAAI;QACF,MAAM/C,OAAO,CAAC+C,MAAM,CAAC;QACrB,IAAI,CAACd,OAAO,CAACC,IAAI,CAAC,QAAQ;MAC5B,EAAE,OAAOH,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK;MAC9B;IACF,CAAC;IAED;IACAiB,QAAQA,CAAClB,IAAI,EAAE;MACb;MACAE,OAAO,CAACiB,GAAG,CAAC,OAAO,EAAEnB,IAAI;IAC3B,CAAC;IAED,MAAMoB,UAAUA,CAACC,MAAM,EAAE;MACvB,IAAIC,OAAO,CAAC,aAAa,CAAC,EAAE;QAC1B,IAAI;UACF,MAAMnD,OAAO,CAACiD,UAAU,CAACC,MAAM;UAC/B,MAAM,IAAI,CAACd,SAAS,CAAC;QACvB,EAAE,OAAON,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;QAChC;MACF;IACF,CAAC;IAED;IACAsB,gBAAgBA,CAACC,YAAY,EAAE;MAC7B;MACAtB,OAAO,CAACiB,GAAG,CAAC,OAAO,EAAEK,YAAY;IACnC,CAAC;IAED,MAAMC,kBAAkBA,CAACC,cAAc,EAAE;MACvC,IAAIJ,OAAO,CAAC,aAAa,CAAC,EAAE;QAC1B,IAAI;UACF,MAAMlD,eAAe,CAACqD,kBAAkB,CAACC,cAAc;UACvD,MAAM,IAAI,CAAClB,iBAAiB,CAAC;QAC/B,EAAE,OAAOP,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;QAChC;MACF;IACF,CAAC;IAED;IACA0B,WAAWA,CAACC,OAAO,EAAE;MACnB;MACA1B,OAAO,CAACiB,GAAG,CAAC,SAAS,EAAES,OAAO;IAChC,CAAC;IAED,MAAMC,aAAaA,CAACC,SAAS,EAAE;MAC7B,IAAIR,OAAO,CAAC,aAAa,CAAC,EAAE;QAC1B,IAAI;UACF,MAAMjD,cAAc,CAAC0D,iBAAiB,CAACD,SAAS;UAChD,MAAM,IAAI,CAACrB,gBAAgB,CAAC;QAC9B,EAAE,OAAOR,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAClC;MACF;IACF,CAAC;IAED;IACA+B,YAAYA,CAACC,QAAQ,EAAE;MACrB;MACA/B,OAAO,CAACiB,GAAG,CAAC,SAAS,EAAEc,QAAQ;IACjC,CAAC;IAED,MAAMC,cAAcA,CAACC,UAAU,EAAE;MAC/B,IAAIb,OAAO,CAAC,aAAa,CAAC,EAAE;QAC1B,IAAI;UACF,MAAMhD,mBAAmB,CAAC8D,sBAAsB,CAACD,UAAU;UAC3D,MAAM,IAAI,CAACzB,qBAAqB,CAAC;QACnC,EAAE,OAAOT,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAClC;MACF;IACF,CAAC;IAED;IACAoC,YAAYA,CAACC,QAAQ,EAAE;MACrB;MACApC,OAAO,CAACiB,GAAG,CAAC,SAAS,EAAEmB,QAAQ;IACjC,CAAC;IAED,MAAMC,cAAcA,CAACC,UAAU,EAAE;MAC/B,IAAIlB,OAAO,CAAC,aAAa,CAAC,EAAE;QAC1B,IAAI;UACF,MAAM/C,eAAe,CAACkE,kBAAkB,CAACD,UAAU;UACnD,MAAM,IAAI,CAAC7B,kBAAkB,CAAC;QAChC,EAAE,OAAOV,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAClC;MACF;IACF,CAAC;IAED;IACAyC,UAAUA,CAACC,UAAU,EAAE;MACrB,IAAI,CAACA,UAAU,EAAE,OAAO,GAAE;MAC1B,MAAMC,IAAG,GAAI,IAAIC,IAAI,CAACF,UAAU;MAChC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,IAAI,GAAE,GAAIF,IAAI,CAACG,kBAAkB,CAAC,OAAO;IACjF,CAAC;IAEDC,YAAYA,CAACC,IAAI,EAAEC,MAAM,EAAE;MACzB,IAAI,CAACD,IAAI,EAAE,OAAO,GAAE;MACpB;MACA,MAAME,SAAQ,GAAIF,IAAI,CAACG,OAAO,CAAC,UAAU,EAAE,EAAE;MAC7C,OAAOD,SAAS,CAACD,MAAK,GAAIA,MAAK,GAAIC,SAAS,CAACE,SAAS,CAAC,CAAC,EAAEH,MAAM,IAAI,KAAI,GAAIC,SAAQ;IACtF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}