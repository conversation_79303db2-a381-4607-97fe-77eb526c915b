import os
import sys
import json
import jwt
from datetime import datetime, timedelta

# 设置环境变量
os.environ['DATABASE_URL'] = 'sqlite:///python_backend/nis_club.db'
os.environ['JWT_SECRET_KEY'] = 'supersecretjwtkey'
os.environ['SECRET_KEY'] = 'supersecretflaskkey'

# 切换到python_backend目录
os.chdir('python_backend')
sys.path.insert(0, '.')

print("=" * 60)
print("直接测试登录逻辑...")
print("=" * 60)

try:
    # 导入必要的模块
    from models import db, User
    from flask import Flask
    
    # 创建Flask应用
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///nis_club.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['JWT_SECRET_KEY'] = 'supersecretjwtkey'
    
    db.init_app(app)
    
    with app.app_context():
        print("1. 初始化数据库...")
        db.create_all()
        
        print("2. 检查管理员用户...")
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            print("   创建管理员用户...")
            admin = User(
                username='admin',
                student_id='ADMIN001',
                name='系统管理员',
                role='admin'
            )
            admin.set_password('Admin123!')
            db.session.add(admin)
            db.session.commit()
            print("   ✅ 管理员用户创建成功")
        else:
            print("   ✅ 管理员用户已存在")
        
        print("3. 测试登录验证...")
        username = "admin"
        password = "Admin123!"
        
        user = User.query.filter_by(username=username).first()
        if user:
            print(f"   找到用户: {user.username}")
            is_valid = user.check_password(password)
            print(f"   密码验证: {'通过' if is_valid else '失败'}")
            
            if is_valid:
                print("4. 测试JWT生成...")
                payload = {
                    'user': {
                        'id': user.id,
                        'role': user.role
                    },
                    'exp': datetime.utcnow() + timedelta(hours=1)
                }
                
                token = jwt.encode(payload, app.config['JWT_SECRET_KEY'], algorithm='HS256')
                print(f"   ✅ JWT生成成功: {token[:50]}...")
                
                # 测试JWT解码
                decoded = jwt.decode(token, app.config['JWT_SECRET_KEY'], algorithms=['HS256'])
                print(f"   ✅ JWT解码成功: 用户ID={decoded['user']['id']}")
                
                print("\n🎉 所有测试通过！登录功能完全正常")
            else:
                print("   ❌ 密码验证失败")
        else:
            print("   ❌ 用户不存在")
            
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()

if __name__ == "__main__":
    test_login()
