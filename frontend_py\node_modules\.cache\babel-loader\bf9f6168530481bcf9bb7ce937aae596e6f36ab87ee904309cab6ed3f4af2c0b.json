{"ast": null, "code": "import { createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nimport _imports_0 from '@/assets/nis_mascot.png';\nconst _hoisted_1 = {\n  class: \"club-culture\"\n};\nconst _hoisted_2 = [\"innerHTML\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[0] || (_cache[0] = _createElementVNode(\"h1\", null, \"社团文化\", -1 /* CACHED */)), _createElementVNode(\"div\", {\n    class: \"culture-description\",\n    innerHTML: $options.purifiedCultureContent\n  }, null, 8 /* PROPS */, _hoisted_2), _createCommentVNode(\" 社团吉祥物图片 \"), _cache[1] || (_cache[1] = _createElementVNode(\"img\", {\n    src: _imports_0,\n    alt: \"NIS 社团吉祥物\",\n    style: {\n      \"max-width\": \"300px\",\n      \"margin\": \"20px auto\"\n    }\n  }, null, -1 /* CACHED */)), _cache[2] || (_cache[2] = _createElementVNode(\"p\", null, \"学校：成都工业职业技术学院（金堂校区）\", -1 /* CACHED */)), _cache[3] || (_cache[3] = _createElementVNode(\"p\", null, \"社团群号：242050951\", -1 /* CACHED */)), _cache[4] || (_cache[4] = _createElementVNode(\"p\", null, \"抖音：21647629167\", -1 /* CACHED */)), _cache[5] || (_cache[5] = _createElementVNode(\"p\", null, \"主要活动内容：组网技术，网络攻防，安全科普\", -1 /* CACHED */)), _createCommentVNode(\" 这里可以添加更多社团文化内容，考虑折叠规置信息 \")]);\n}", "map": {"version": 3, "names": ["_imports_0", "class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "innerHTML", "$options", "purifiedCultureContent", "_createCommentVNode", "src", "alt", "style"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\ClubCulture.vue"], "sourcesContent": ["<template>\r\n  <div class=\"club-culture\">\r\n    <h1>社团文化</h1>\r\n    <div class=\"culture-description\" v-html=\"purifiedCultureContent\"></div>\r\n    <!-- 社团吉祥物图片 -->\r\n    <img src=\"@/assets/nis_mascot.png\" alt=\"NIS 社团吉祥物\" style=\"max-width: 300px; margin: 20px auto;\">\r\n    <p>学校：成都工业职业技术学院（金堂校区）</p>\r\n    <p>社团群号：242050951</p>\r\n    <p>抖音：21647629167</p>\r\n    <p>主要活动内容：组网技术，网络攻防，安全科普</p>\r\n    <!-- 这里可以添加更多社团文化内容，考虑折叠规置信息 -->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport DOMPurify from 'dompurify'; // 引入 DOMPurify\r\n\r\nexport default {\r\n  name: 'ClubCulture',\r\n  data() {\r\n    return {\r\n      // 示例：社团文化介绍，实际应从后端获取\r\n      cultureContent: '<p>这里是NIS社团的**详细介绍**、宗旨、历史、吉祥物等信息。</p><p>我们致力于网络安全技术的学习与交流。</p><img src=\"invalid-image.jpg\" onerror=\"alert(\\'XSS in image!\\')\">',\r\n    };\r\n  },\r\n  computed: {\r\n    purifiedCultureContent() {\r\n      return DOMPurify.sanitize(this.cultureContent);\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.club-culture {\r\n  padding: 20px;\r\n}\r\nimg {\r\n  display: block;\r\n}\r\n</style>"], "mappings": ";OAKSA,UAA6B;;EAJ/BC,KAAK,EAAC;AAAc;;;uBAAzBC,mBAAA,CAUM,OAVNC,UAUM,G,0BATJC,mBAAA,CAAa,YAAT,MAAI,qBACRA,mBAAA,CAAuE;IAAlEH,KAAK,EAAC,qBAAqB;IAACI,SAA+B,EAAvBC,QAAA,CAAAC;uCACzCC,mBAAA,aAAgB,E,0BAChBJ,mBAAA,CAAgG;IAA3FK,GAA6B,EAA7BT,UAA6B;IAACU,GAAG,EAAC,WAAW;IAACC,KAA4C,EAA5C;MAAA;MAAA;IAAA;wDACnDP,mBAAA,CAA0B,WAAvB,qBAAmB,qB,0BACtBA,mBAAA,CAAqB,WAAlB,gBAAc,qB,0BACjBA,mBAAA,CAAqB,WAAlB,gBAAc,qB,0BACjBA,mBAAA,CAA4B,WAAzB,uBAAqB,qBACxBI,mBAAA,6BAAgC,C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}