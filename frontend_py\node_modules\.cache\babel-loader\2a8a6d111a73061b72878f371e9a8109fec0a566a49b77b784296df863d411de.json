{"ast": null, "code": "import DOMPurify from 'dompurify';\nimport { cyberSecurityNewsAPI } from '../services/api';\nexport default {\n  name: 'CyberSecurityNews',\n  data() {\n    return {\n      newsList: [],\n      loading: false,\n      error: null\n    };\n  },\n  async mounted() {\n    await this.loadNews();\n  },\n  methods: {\n    async loadNews() {\n      this.loading = true;\n      this.error = null;\n      try {\n        const response = await cyberSecurityNewsAPI.getCyberSecurityNews();\n        this.newsList = response.data;\n      } catch (error) {\n        console.error('加载网安资讯失败:', error);\n        this.error = '加载网安资讯失败，请稍后重试';\n      } finally {\n        this.loading = false;\n      }\n    },\n    purifyContent(content) {\n      return DOMPurify.sanitize(content);\n    },\n    formatDate(dateString) {\n      if (!dateString) return '';\n      const date = new Date(dateString);\n      return date.toLocaleDateString('zh-CN');\n    }\n  }\n};", "map": {"version": 3, "names": ["DOMPurify", "cyberSecurityNewsAPI", "name", "data", "newsList", "loading", "error", "mounted", "loadNews", "methods", "response", "getCyberSecurityNews", "console", "purifyContent", "content", "sanitize", "formatDate", "dateString", "date", "Date", "toLocaleDateString"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\CyberSecurityNews.vue"], "sourcesContent": ["<template>\r\n  <div class=\"cyber-security-news\">\r\n    <h1>网安资讯</h1>\r\n    <p>这里发布最新的网络安全行业新闻、技术动态、漏洞信息等。</p>\r\n\r\n    <div v-if=\"loading\" class=\"loading\">\r\n      <p>加载中...</p>\r\n    </div>\r\n\r\n    <div v-else-if=\"error\" class=\"error\">\r\n      <p>{{ error }}</p>\r\n      <button @click=\"loadNews\" class=\"btn btn-primary\">重试</button>\r\n    </div>\r\n\r\n    <div v-else class=\"news-list\">\r\n      <div v-if=\"newsList.length === 0\" class=\"no-data\">\r\n        <p>暂无网安资讯</p>\r\n      </div>\r\n      <div v-else>\r\n        <div v-for=\"newsItem in newsList\" :key=\"newsItem.id\" class=\"news-item\">\r\n          <h3>{{ newsItem.title }}</h3>\r\n          <div class=\"news-content\">\r\n            <div v-html=\"purifyContent(newsItem.content)\"></div>\r\n          </div>\r\n          <div class=\"news-meta\">\r\n            <div v-if=\"newsItem.source_url\" class=\"source-link\">\r\n              <a :href=\"newsItem.source_url\" target=\"_blank\" rel=\"noopener noreferrer\">查看原文</a>\r\n            </div>\r\n            <div class=\"dates\">\r\n              <span v-if=\"newsItem.published_date\">发布时间: {{ formatDate(newsItem.published_date) }}</span>\r\n              <span>创建时间: {{ formatDate(newsItem.created_at) }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport DOMPurify from 'dompurify'\r\nimport { cyberSecurityNewsAPI } from '../services/api'\r\n\r\nexport default {\r\n  name: 'CyberSecurityNews',\r\n  data() {\r\n    return {\r\n      newsList: [],\r\n      loading: false,\r\n      error: null\r\n    }\r\n  },\r\n  async mounted() {\r\n    await this.loadNews()\r\n  },\r\n  methods: {\r\n    async loadNews() {\r\n      this.loading = true\r\n      this.error = null\r\n      try {\r\n        const response = await cyberSecurityNewsAPI.getCyberSecurityNews()\r\n        this.newsList = response.data\r\n      } catch (error) {\r\n        console.error('加载网安资讯失败:', error)\r\n        this.error = '加载网安资讯失败，请稍后重试'\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    purifyContent(content) {\r\n      return DOMPurify.sanitize(content)\r\n    },\r\n\r\n    formatDate(dateString) {\r\n      if (!dateString) return ''\r\n      const date = new Date(dateString)\r\n      return date.toLocaleDateString('zh-CN')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.cyber-security-news {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 20px;\r\n}\r\n\r\n.loading, .error, .no-data {\r\n  text-align: center;\r\n  padding: 40px;\r\n  color: #666;\r\n}\r\n\r\n.error {\r\n  color: #e74c3c;\r\n}\r\n\r\n.news-list {\r\n  margin-top: 20px;\r\n}\r\n\r\n.news-item {\r\n  background: white;\r\n  border: 1px solid #ddd;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  margin-bottom: 20px;\r\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\r\n}\r\n\r\n.news-item h3 {\r\n  color: #2c3e50;\r\n  margin-bottom: 15px;\r\n  border-bottom: 2px solid #f39c12;\r\n  padding-bottom: 10px;\r\n}\r\n\r\n.news-content {\r\n  margin: 15px 0;\r\n  line-height: 1.6;\r\n}\r\n\r\n.news-meta {\r\n  margin-top: 15px;\r\n  padding-top: 15px;\r\n  border-top: 1px solid #eee;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.source-link a {\r\n  color: #f39c12;\r\n  text-decoration: none;\r\n  font-weight: bold;\r\n}\r\n\r\n.source-link a:hover {\r\n  text-decoration: underline;\r\n}\r\n\r\n.dates {\r\n  color: #666;\r\n  font-size: 14px;\r\n}\r\n\r\n.dates span {\r\n  margin-left: 15px;\r\n}\r\n\r\n.dates span:first-child {\r\n  margin-left: 0;\r\n}\r\n\r\n.btn {\r\n  display: inline-block;\r\n  padding: 8px 16px;\r\n  border: none;\r\n  border-radius: 4px;\r\n  text-decoration: none;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  background-color: #f39c12;\r\n  color: white;\r\n}\r\n\r\n.btn:hover {\r\n  background-color: #e67e22;\r\n}\r\n</style>"], "mappings": "AAwCA,OAAOA,SAAQ,MAAO,WAAU;AAChC,SAASC,oBAAmB,QAAS,iBAAgB;AAErD,eAAe;EACbC,IAAI,EAAE,mBAAmB;EACzBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,QAAQ,EAAE,EAAE;MACZC,OAAO,EAAE,KAAK;MACdC,KAAK,EAAE;IACT;EACF,CAAC;EACD,MAAMC,OAAOA,CAAA,EAAG;IACd,MAAM,IAAI,CAACC,QAAQ,CAAC;EACtB,CAAC;EACDC,OAAO,EAAE;IACP,MAAMD,QAAQA,CAAA,EAAG;MACf,IAAI,CAACH,OAAM,GAAI,IAAG;MAClB,IAAI,CAACC,KAAI,GAAI,IAAG;MAChB,IAAI;QACF,MAAMI,QAAO,GAAI,MAAMT,oBAAoB,CAACU,oBAAoB,CAAC;QACjE,IAAI,CAACP,QAAO,GAAIM,QAAQ,CAACP,IAAG;MAC9B,EAAE,OAAOG,KAAK,EAAE;QACdM,OAAO,CAACN,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC,IAAI,CAACA,KAAI,GAAI,gBAAe;MAC9B,UAAU;QACR,IAAI,CAACD,OAAM,GAAI,KAAI;MACrB;IACF,CAAC;IAEDQ,aAAaA,CAACC,OAAO,EAAE;MACrB,OAAOd,SAAS,CAACe,QAAQ,CAACD,OAAO;IACnC,CAAC;IAEDE,UAAUA,CAACC,UAAU,EAAE;MACrB,IAAI,CAACA,UAAU,EAAE,OAAO,EAAC;MACzB,MAAMC,IAAG,GAAI,IAAIC,IAAI,CAACF,UAAU;MAChC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO;IACxC;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}