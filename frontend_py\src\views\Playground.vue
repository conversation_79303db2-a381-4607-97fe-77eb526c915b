<template>
  <div class="playground">
    <div class="hero-section">
      <h1>🎮 实操靶场</h1>
      <p class="hero-subtitle">提升网络安全技能的实战平台</p>
    </div>

    <div class="content-container">
      <!-- 功能介绍 -->
      <div class="section">
        <h2>靶场功能</h2>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">🔍</div>
            <h3>漏洞扫描</h3>
            <p>学习使用各种扫描工具发现系统漏洞</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🛡️</div>
            <h3>渗透测试</h3>
            <p>在安全环境中练习渗透测试技术</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🔐</div>
            <h3>密码破解</h3>
            <p>学习密码安全和破解防护技术</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🌐</div>
            <h3>Web安全</h3>
            <p>掌握Web应用安全测试方法</p>
          </div>
        </div>
      </div>

      <!-- 开发状态 -->
      <div class="section status-section">
        <h2>开发状态</h2>
        <div class="status-card">
          <div class="status-icon">🚧</div>
          <h3>功能开发中</h3>
          <p>实操靶场功能正在紧张开发中，将为大家提供：</p>
          <ul class="status-list">
            <li>✅ 多种难度级别的挑战</li>
            <li>✅ 实时的技能评估系统</li>
            <li>✅ 详细的学习指导</li>
            <li>✅ 团队协作练习环境</li>
            <li>✅ 成就系统和排行榜</li>
          </ul>
          <p class="status-note">敬请期待正式上线！</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Playground'
}
</script>

<style scoped>
.playground {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.hero-section {
  text-align: center;
  padding: 80px 20px;
  color: white;
}

.hero-section h1 {
  font-size: 3.5rem;
  margin-bottom: 20px;
  font-weight: bold;
}

.hero-subtitle {
  font-size: 1.3rem;
  opacity: 0.9;
}

.content-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
  background: white;
  border-radius: 20px 20px 0 0;
}

.section {
  margin-bottom: 50px;
}

.section h2 {
  font-size: 2.2rem;
  margin-bottom: 30px;
  color: #333;
  text-align: center;
  border-bottom: 3px solid #42b983;
  padding-bottom: 15px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  margin-top: 30px;
}

.feature-card {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s ease;
  border: 2px solid transparent;
}

.feature-card:hover {
  transform: translateY(-10px);
  border-color: #42b983;
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 20px;
}

.feature-card h3 {
  color: #333;
  margin-bottom: 15px;
  font-size: 1.4rem;
}

.feature-card p {
  color: #666;
  line-height: 1.6;
}

.status-section {
  background: #f8f9fa;
  padding: 40px;
  border-radius: 15px;
}

.status-card {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.status-icon {
  font-size: 4rem;
  margin-bottom: 20px;
}

.status-card h3 {
  color: #333;
  margin-bottom: 20px;
  font-size: 1.8rem;
}

.status-card p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.status-list {
  text-align: left;
  max-width: 400px;
  margin: 20px auto;
  color: #666;
}

.status-list li {
  margin-bottom: 10px;
  padding-left: 10px;
}

.status-note {
  font-weight: bold;
  color: #42b983;
  font-size: 1.1rem;
}

@media (max-width: 768px) {
  .hero-section h1 {
    font-size: 2.5rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .content-container {
    padding: 20px 15px;
  }
}
</style>