{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { authAPI } from '../services/api';\nexport default {\n  name: 'Register',\n  data() {\n    return {\n      form: {\n        username: '',\n        password: '',\n        student_id: '',\n        name: ''\n      },\n      confirmPassword: '',\n      error: null,\n      success: null,\n      loading: false\n    };\n  },\n  methods: {\n    async register() {\n      this.error = null;\n      this.success = null;\n      this.loading = true;\n\n      // 前端验证\n      if (!this.form.username) {\n        this.error = '用户名不能为空';\n        this.loading = false;\n        return;\n      }\n      if (!this.form.password) {\n        this.error = '密码不能为空';\n        this.loading = false;\n        return;\n      }\n      if (this.form.password !== this.confirmPassword) {\n        this.error = '两次输入的密码不一致';\n        this.loading = false;\n        return;\n      }\n      try {\n        await authAPI.register(this.form);\n        this.success = '注册成功！请登录';\n\n        // 3秒后跳转到登录页\n        setTimeout(() => {\n          this.$router.push('/login');\n        }, 3000);\n      } catch (err) {\n        this.error = err.response?.data?.msg || '注册失败，请稍后重试';\n        console.error(err);\n      } finally {\n        this.loading = false;\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["authAPI", "name", "data", "form", "username", "password", "student_id", "confirmPassword", "error", "success", "loading", "methods", "register", "setTimeout", "$router", "push", "err", "response", "msg", "console"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Register.vue"], "sourcesContent": ["<template>\n  <div class=\"register\">\n    <h1>用户注册</h1>\n    <form @submit.prevent=\"register\">\n      <div class=\"form-group\">\n        <label for=\"username\">用户名:</label>\n        <input type=\"text\" id=\"username\" v-model=\"form.username\" required>\n      </div>\n      <div class=\"form-group\">\n        <label for=\"password\">密码:</label>\n        <input type=\"password\" id=\"password\" v-model=\"form.password\" required>\n        <small>密码需包含大小写字母、数字和特殊字符，至少8位</small>\n      </div>\n      <div class=\"form-group\">\n        <label for=\"confirmPassword\">确认密码:</label>\n        <input type=\"password\" id=\"confirmPassword\" v-model=\"confirmPassword\" required>\n      </div>\n      <div class=\"form-group\">\n        <label for=\"student_id\">学号:</label>\n        <input type=\"text\" id=\"student_id\" v-model=\"form.student_id\">\n      </div>\n      <div class=\"form-group\">\n        <label for=\"name\">姓名:</label>\n        <input type=\"text\" id=\"name\" v-model=\"form.name\">\n      </div>\n      <button type=\"submit\" :disabled=\"loading\">\n        {{ loading ? '注册中...' : '注册' }}\n      </button>\n    </form>\n    <p v-if=\"error\" class=\"error-message\">{{ error }}</p>\n    <p v-if=\"success\" class=\"success-message\">{{ success }}</p>\n    \n    <div class=\"login-link\">\n      <p>已有账户？<router-link to=\"/login\">立即登录</router-link></p>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { authAPI } from '../services/api'\n\nexport default {\n  name: 'Register',\n  data() {\n    return {\n      form: {\n        username: '',\n        password: '',\n        student_id: '',\n        name: ''\n      },\n      confirmPassword: '',\n      error: null,\n      success: null,\n      loading: false\n    }\n  },\n  methods: {\n    async register() {\n      this.error = null\n      this.success = null\n      this.loading = true\n\n      // 前端验证\n      if (!this.form.username) {\n        this.error = '用户名不能为空'\n        this.loading = false\n        return\n      }\n      if (!this.form.password) {\n        this.error = '密码不能为空'\n        this.loading = false\n        return\n      }\n      if (this.form.password !== this.confirmPassword) {\n        this.error = '两次输入的密码不一致'\n        this.loading = false\n        return\n      }\n\n      try {\n        await authAPI.register(this.form)\n        this.success = '注册成功！请登录'\n        \n        // 3秒后跳转到登录页\n        setTimeout(() => {\n          this.$router.push('/login')\n        }, 3000)\n      } catch (err) {\n        this.error = err.response?.data?.msg || '注册失败，请稍后重试'\n        console.error(err)\n      } finally {\n        this.loading = false\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.register {\n  padding: 20px;\n  max-width: 400px;\n  margin: 50px auto;\n  border: 1px solid #ccc;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.form-group {\n  margin-bottom: 15px;\n  text-align: left;\n}\n\n.form-group label {\n  display: block;\n  margin-bottom: 5px;\n  font-weight: bold;\n}\n\n.form-group input {\n  width: 100%;\n  padding: 10px;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  box-sizing: border-box;\n}\n\n.form-group small {\n  color: #666;\n  font-size: 12px;\n  margin-top: 5px;\n  display: block;\n}\n\nbutton {\n  background-color: #42b983;\n  color: white;\n  padding: 10px 15px;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 16px;\n  width: 100%;\n}\n\nbutton:hover:not(:disabled) {\n  background-color: #369f6e;\n}\n\nbutton:disabled {\n  background-color: #ccc;\n  cursor: not-allowed;\n}\n\n.error-message {\n  color: red;\n  margin-top: 10px;\n}\n\n.success-message {\n  color: green;\n  margin-top: 10px;\n}\n\n.login-link {\n  margin-top: 20px;\n  text-align: center;\n}\n\n.login-link a {\n  color: #42b983;\n  text-decoration: none;\n}\n\n.login-link a:hover {\n  text-decoration: underline;\n}\n</style>\n"], "mappings": ";AAuCA,SAASA,OAAM,QAAS,iBAAgB;AAExC,eAAe;EACbC,IAAI,EAAE,UAAU;EAChBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,IAAI,EAAE;QACJC,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE,EAAE;QACZC,UAAU,EAAE,EAAE;QACdL,IAAI,EAAE;MACR,CAAC;MACDM,eAAe,EAAE,EAAE;MACnBC,KAAK,EAAE,IAAI;MACXC,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE;IACX;EACF,CAAC;EACDC,OAAO,EAAE;IACP,MAAMC,QAAQA,CAAA,EAAG;MACf,IAAI,CAACJ,KAAI,GAAI,IAAG;MAChB,IAAI,CAACC,OAAM,GAAI,IAAG;MAClB,IAAI,CAACC,OAAM,GAAI,IAAG;;MAElB;MACA,IAAI,CAAC,IAAI,CAACP,IAAI,CAACC,QAAQ,EAAE;QACvB,IAAI,CAACI,KAAI,GAAI,SAAQ;QACrB,IAAI,CAACE,OAAM,GAAI,KAAI;QACnB;MACF;MACA,IAAI,CAAC,IAAI,CAACP,IAAI,CAACE,QAAQ,EAAE;QACvB,IAAI,CAACG,KAAI,GAAI,QAAO;QACpB,IAAI,CAACE,OAAM,GAAI,KAAI;QACnB;MACF;MACA,IAAI,IAAI,CAACP,IAAI,CAACE,QAAO,KAAM,IAAI,CAACE,eAAe,EAAE;QAC/C,IAAI,CAACC,KAAI,GAAI,YAAW;QACxB,IAAI,CAACE,OAAM,GAAI,KAAI;QACnB;MACF;MAEA,IAAI;QACF,MAAMV,OAAO,CAACY,QAAQ,CAAC,IAAI,CAACT,IAAI;QAChC,IAAI,CAACM,OAAM,GAAI,UAAS;;QAExB;QACAI,UAAU,CAAC,MAAM;UACf,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,QAAQ;QAC5B,CAAC,EAAE,IAAI;MACT,EAAE,OAAOC,GAAG,EAAE;QACZ,IAAI,CAACR,KAAI,GAAIQ,GAAG,CAACC,QAAQ,EAAEf,IAAI,EAAEgB,GAAE,IAAK,YAAW;QACnDC,OAAO,CAACX,KAAK,CAACQ,GAAG;MACnB,UAAU;QACR,IAAI,CAACN,OAAM,GAAI,KAAI;MACrB;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}