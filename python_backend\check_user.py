#!/usr/bin/env python3
import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from models import db, User

def check_admin_user():
    with app.app_context():
        print("检查数据库中的用户...")
        
        # 查找所有用户
        users = User.query.all()
        print(f"数据库中共有 {len(users)} 个用户")
        
        for user in users:
            print(f"用户ID: {user.id}")
            print(f"用户名: {user.username}")
            print(f"姓名: {user.name}")
            print(f"学号: {user.student_id}")
            print(f"角色: {user.role}")
            print(f"密码哈希: {user.password_hash}")
            print(f"创建时间: {user.created_at}")
            
            # 测试密码验证
            test_password = "Admin123!"
            is_valid = user.check_password(test_password)
            print(f"密码 '{test_password}' 验证结果: {is_valid}")
            print("-" * 50)

if __name__ == "__main__":
    check_admin_user()
