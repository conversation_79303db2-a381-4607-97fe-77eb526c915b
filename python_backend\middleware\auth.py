import jwt
from flask import request, jsonify, current_app
from functools import wraps
from models import User

def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None
        if 'Authorization' in request.headers:
            token = request.headers['Authorization'].split(' ')[1]
        elif 'token' in request.cookies: # 检查 HttpOnly cookie
            token = request.cookies.get('token')

        if not token:
            return jsonify({'msg': '未授权，没有 Token'}), 401

        try:
            data = jwt.decode(token, current_app.config['JWT_SECRET_KEY'], algorithms=['HS256'])
            current_user = User.query.get(data['user']['id'])
            if not current_user:
                return jsonify({'msg': '未授权，用户不存在'}), 401
            request.user = current_user # 将用户对象附加到请求
        except jwt.ExpiredSignatureError:
            return jsonify({'msg': '未授权，Token 已过期'}), 401
        except jwt.InvalidTokenError:
            return jsonify({'msg': '未授权，Token 无效'}), 401
        except Exception as e:
            current_app.logger.error(f"Token 验证错误: {e}")
            return jsonify({'msg': '未授权，Token 验证失败'}), 401

        return f(*args, **kwargs)
    return decorated

def authorize_roles(roles):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not hasattr(request, 'user') or request.user.role not in roles:
                return jsonify({'msg': f'用户角色 {request.user.role if hasattr(request, "user") else "未知"} 无权访问此路由'}), 403
            return f(*args, **kwargs)
        return decorated_function
    return decorator
