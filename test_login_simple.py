import requests
import json

def test_login():
    try:
        # 测试服务器是否运行
        health_url = "http://localhost:5000/"
        try:
            health_response = requests.get(health_url, timeout=5)
            print(f"✅ 服务器运行正常: {health_response.text}")
        except Exception as e:
            print(f"❌ 服务器未运行: {e}")
            return False

        # 测试登录
        login_url = "http://localhost:5000/api/auth/login"
        login_data = {
            "username": "admin",
            "password": "Admin123!"
        }
        
        print(f"\n测试登录: {login_url}")
        print(f"登录数据: {json.dumps(login_data, indent=2)}")
        
        response = requests.post(
            login_url,
            json=login_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✅ 登录成功!")
            return True
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保后端服务正在运行")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

if __name__ == "__main__":
    print("开始测试登录系统...")
    success = test_login()
    if success:
        print("\n🎉 登录测试通过!")
    else:
        print("\n💥 登录测试失败")
