import os
from werkzeug.utils import secure_filename
from flask import current_app, request, jsonify
from uuid import uuid4
from PIL import Image # 用于检查图片类型
import openpyxl # 用于检查 Excel 文件
import io

def allowed_file(filename, allowed_extensions):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in allowed_extensions

def upload_file(file, folder_name, allowed_extensions, max_size_mb):
    if file.filename == '':
        return None, '没有选择文件'

    if not allowed_file(file.filename, allowed_extensions):
        return None, f'只允许上传 {", ".join(allowed_extensions)} 类型的文件。'

    # 检查文件大小
    file.seek(0, os.SEEK_END)
    file_size = file.tell()
    file.seek(0) # 重置文件指针
    if file_size > max_size_mb * 1024 * 1024:
        return None, f'文件大小不能超过 {max_size_mb}MB。'

    # 更严格的文件类型验证 (魔术字节)
    file_bytes = file.read(2048) # 读取文件开头一部分字节
    file.seek(0) # 重置文件指针

    if 'png' in allowed_extensions or 'jpeg' in allowed_extensions or 'jpg' in allowed_extensions or 'gif' in allowed_extensions:
        try:
            # 使用 Pillow 验证图片
            image = Image.open(io.BytesIO(file_bytes))
            image_format = image.format.lower() if image.format else None
            # 将 jpeg 格式映射为 jpg
            if image_format == 'jpeg':
                image_format = 'jpg'
            if not image_format or image_format not in allowed_extensions:
                return None, '文件内容与声明的图片类型不符。'
        except Exception:
            return None, '文件不是有效的图片格式。'
    elif 'xlsx' in allowed_extensions or 'xls' in allowed_extensions:
        # 对于 Excel 文件，可以尝试加载工作簿来验证
        try:
            # openpyxl 只能处理 .xlsx，对于 .xls 需要 xlrd (但 xlrd 不再维护)
            # 这里简化处理，只检查 .xlsx
            if file.filename.rsplit('.', 1)[1].lower() == 'xlsx':
                openpyxl.load_workbook(file)
            else: # 对于 .xls 文件，我们暂时不进行内容验证，只依赖扩展名
                pass
        except Exception:
            return None, '文件内容与声明的 Excel 类型不符。'

    # 生成唯一文件名
    filename = secure_filename(file.filename)
    unique_filename = str(uuid4()) + os.path.splitext(filename)[1]
    
    upload_folder = os.path.join(current_app.config['UPLOAD_FOLDER'], folder_name)
    os.makedirs(upload_folder, exist_ok=True) # 确保目录存在

    file_path = os.path.join(upload_folder, unique_filename)
    file.save(file_path)

    return f'/uploads/{folder_name}/{unique_filename}', None
