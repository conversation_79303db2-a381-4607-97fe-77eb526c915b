{"ast": null, "code": "import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"home\"\n};\nconst _hoisted_2 = [\"innerHTML\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[0] || (_cache[0] = _createElementVNode(\"h1\", null, \"NIS 社团文化网站 - 首页\", -1 /* CACHED */)), _cache[1] || (_cache[1] = _createElementVNode(\"p\", null, \"欢迎来到网络信息安全社团！\", -1 /* CACHED */)), _cache[2] || (_cache[2] = _createElementVNode(\"h2\", null, \"社团招新公告\", -1 /* CACHED */)), _createElementVNode(\"div\", {\n    class: \"announcement-content\",\n    innerHTML: $options.purifiedAnnouncementContent\n  }, null, 8 /* PROPS */, _hoisted_2)]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "innerHTML", "$options", "purifiedAnnouncementContent"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Home.vue"], "sourcesContent": ["<template>\r\n  <div class=\"home\">\r\n    <h1>NIS 社团文化网站 - 首页</h1>\r\n    <p>欢迎来到网络信息安全社团！</p>\r\n    <h2>社团招新公告</h2>\r\n    <div class=\"announcement-content\" v-html=\"purifiedAnnouncementContent\"></div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport DOMPurify from 'dompurify'; // 引入 DOMPurify\r\n\r\nexport default {\r\n  name: 'Home',\r\n  data() {\r\n    return {\r\n      // 示例公告内容，实际应从后端获取\r\n      announcementContent: '<p>欢迎参加**网络信息安全社团**的招新活动！</p><script>alert(\"XSS Attack!\");<\\/script>',\r\n    };\r\n  },\r\n  computed: {\r\n    purifiedAnnouncementContent() {\r\n      // 使用 DOMPurify 清理公告内容\r\n      return DOMPurify.sanitize(this.announcementContent);\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.home {\r\n  padding: 20px;\r\n}\r\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAM;;;uBAAjBC,mBAAA,CAKM,OALNC,UAKM,G,0BAJJC,mBAAA,CAAwB,YAApB,iBAAe,qB,0BACnBA,mBAAA,CAAoB,WAAjB,eAAa,qB,0BAChBA,mBAAA,CAAe,YAAX,QAAM,qBACVA,mBAAA,CAA6E;IAAxEH,KAAK,EAAC,sBAAsB;IAACI,SAAoC,EAA5BC,QAAA,CAAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}