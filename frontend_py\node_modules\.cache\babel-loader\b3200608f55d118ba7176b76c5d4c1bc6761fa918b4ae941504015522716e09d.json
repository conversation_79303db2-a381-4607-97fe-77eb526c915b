{"ast": null, "code": "import Navigation from './components/Navigation.vue';\nexport default {\n  name: 'App',\n  components: {\n    Navigation\n  }\n};", "map": {"version": 3, "names": ["Navigation", "name", "components"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\App.vue"], "sourcesContent": ["<template>\n  <div id=\"app\">\n    <Navigation />\n    <main class=\"main-content\">\n      <router-view/>\n    </main>\n  </div>\n</template>\n\n<script>\nimport Navigation from './components/Navigation.vue'\n\nexport default {\n  name: 'App',\n  components: {\n    Navigation\n  }\n}\n</script>\n\n<style>\n#app {\n  font-family: Avenir, Helvetica, Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  text-align: center;\n  color: #2c3e50;\n}\n\nnav {\n  padding: 30px;\n}\n\nnav a {\n  font-weight: bold;\n  color: #2c3e50;\n}\n\nnav a.router-link-exact-active {\n  color: #42b983;\n}\n</style>\n"], "mappings": "AAUA,OAAOA,UAAS,MAAO,6BAA4B;AAEnD,eAAe;EACbC,IAAI,EAAE,KAAK;EACXC,UAAU,EAAE;IACVF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}