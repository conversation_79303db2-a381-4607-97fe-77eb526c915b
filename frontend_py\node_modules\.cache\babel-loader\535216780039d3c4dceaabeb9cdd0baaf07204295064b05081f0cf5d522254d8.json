{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\n/*!\n  * vue-router v4.5.1\n  * (c) 2025 Eduardo San <PERSON> Morote\n  * @license MIT\n  */\nimport { getCurrentInstance, inject, onUnmounted, onDeactivated, onActivated, computed, unref, watchEffect, defineComponent, reactive, h, provide, ref, watch, shallowRef, shallowReactive, nextTick } from 'vue';\nimport { setupDevtoolsPlugin } from '@vue/devtools-api';\nconst isBrowser = typeof document !== 'undefined';\n\n/**\n * Allows differentiating lazy components from functional components and vue-class-component\n * @internal\n *\n * @param component\n */\nfunction isRouteComponent(component) {\n  return typeof component === 'object' || 'displayName' in component || 'props' in component || '__vccOpts' in component;\n}\nfunction isESModule(obj) {\n  return obj.__esModule || obj[Symbol.toStringTag] === 'Module' ||\n  // support CF with dynamic imports that do not\n  // add the Module string tag\n  obj.default && isRouteComponent(obj.default);\n}\nconst assign = Object.assign;\nfunction applyToParams(fn, params) {\n  const newParams = {};\n  for (const key in params) {\n    const value = params[key];\n    newParams[key] = isArray(value) ? value.map(fn) : fn(value);\n  }\n  return newParams;\n}\nconst noop = () => {};\n/**\n * Typesafe alternative to Array.isArray\n * https://github.com/microsoft/TypeScript/pull/48228\n */\nconst isArray = Array.isArray;\nfunction warn(msg) {\n  // avoid using ...args as it breaks in older Edge builds\n  const args = Array.from(arguments).slice(1);\n  console.warn.apply(console, ['[Vue Router warn]: ' + msg].concat(args));\n}\n\n/**\n * Encoding Rules (␣ = Space)\n * - Path: ␣ \" < > # ? { }\n * - Query: ␣ \" < > # & =\n * - Hash: ␣ \" < > `\n *\n * On top of that, the RFC3986 (https://tools.ietf.org/html/rfc3986#section-2.2)\n * defines some extra characters to be encoded. Most browsers do not encode them\n * in encodeURI https://github.com/whatwg/url/issues/369, so it may be safer to\n * also encode `!'()*`. Leaving un-encoded only ASCII alphanumeric(`a-zA-Z0-9`)\n * plus `-._~`. This extra safety should be applied to query by patching the\n * string returned by encodeURIComponent encodeURI also encodes `[\\]^`. `\\`\n * should be encoded to avoid ambiguity. Browsers (IE, FF, C) transform a `\\`\n * into a `/` if directly typed in. The _backtick_ (`````) should also be\n * encoded everywhere because some browsers like FF encode it when directly\n * written while others don't. Safari and IE don't encode ``\"<>{}``` in hash.\n */\n// const EXTRA_RESERVED_RE = /[!'()*]/g\n// const encodeReservedReplacer = (c: string) => '%' + c.charCodeAt(0).toString(16)\nconst HASH_RE = /#/g; // %23\nconst AMPERSAND_RE = /&/g; // %26\nconst SLASH_RE = /\\//g; // %2F\nconst EQUAL_RE = /=/g; // %3D\nconst IM_RE = /\\?/g; // %3F\nconst PLUS_RE = /\\+/g; // %2B\n/**\n * NOTE: It's not clear to me if we should encode the + symbol in queries, it\n * seems to be less flexible than not doing so and I can't find out the legacy\n * systems requiring this for regular requests like text/html. In the standard,\n * the encoding of the plus character is only mentioned for\n * application/x-www-form-urlencoded\n * (https://url.spec.whatwg.org/#urlencoded-parsing) and most browsers seems lo\n * leave the plus character as is in queries. To be more flexible, we allow the\n * plus character on the query, but it can also be manually encoded by the user.\n *\n * Resources:\n * - https://url.spec.whatwg.org/#urlencoded-parsing\n * - https://stackoverflow.com/questions/1634271/url-encoding-the-space-character-or-20\n */\nconst ENC_BRACKET_OPEN_RE = /%5B/g; // [\nconst ENC_BRACKET_CLOSE_RE = /%5D/g; // ]\nconst ENC_CARET_RE = /%5E/g; // ^\nconst ENC_BACKTICK_RE = /%60/g; // `\nconst ENC_CURLY_OPEN_RE = /%7B/g; // {\nconst ENC_PIPE_RE = /%7C/g; // |\nconst ENC_CURLY_CLOSE_RE = /%7D/g; // }\nconst ENC_SPACE_RE = /%20/g; // }\n/**\n * Encode characters that need to be encoded on the path, search and hash\n * sections of the URL.\n *\n * @internal\n * @param text - string to encode\n * @returns encoded string\n */\nfunction commonEncode(text) {\n  return encodeURI('' + text).replace(ENC_PIPE_RE, '|').replace(ENC_BRACKET_OPEN_RE, '[').replace(ENC_BRACKET_CLOSE_RE, ']');\n}\n/**\n * Encode characters that need to be encoded on the hash section of the URL.\n *\n * @param text - string to encode\n * @returns encoded string\n */\nfunction encodeHash(text) {\n  return commonEncode(text).replace(ENC_CURLY_OPEN_RE, '{').replace(ENC_CURLY_CLOSE_RE, '}').replace(ENC_CARET_RE, '^');\n}\n/**\n * Encode characters that need to be encoded query values on the query\n * section of the URL.\n *\n * @param text - string to encode\n * @returns encoded string\n */\nfunction encodeQueryValue(text) {\n  return commonEncode(text)\n  // Encode the space as +, encode the + to differentiate it from the space\n  .replace(PLUS_RE, '%2B').replace(ENC_SPACE_RE, '+').replace(HASH_RE, '%23').replace(AMPERSAND_RE, '%26').replace(ENC_BACKTICK_RE, '`').replace(ENC_CURLY_OPEN_RE, '{').replace(ENC_CURLY_CLOSE_RE, '}').replace(ENC_CARET_RE, '^');\n}\n/**\n * Like `encodeQueryValue` but also encodes the `=` character.\n *\n * @param text - string to encode\n */\nfunction encodeQueryKey(text) {\n  return encodeQueryValue(text).replace(EQUAL_RE, '%3D');\n}\n/**\n * Encode characters that need to be encoded on the path section of the URL.\n *\n * @param text - string to encode\n * @returns encoded string\n */\nfunction encodePath(text) {\n  return commonEncode(text).replace(HASH_RE, '%23').replace(IM_RE, '%3F');\n}\n/**\n * Encode characters that need to be encoded on the path section of the URL as a\n * param. This function encodes everything {@link encodePath} does plus the\n * slash (`/`) character. If `text` is `null` or `undefined`, returns an empty\n * string instead.\n *\n * @param text - string to encode\n * @returns encoded string\n */\nfunction encodeParam(text) {\n  return text == null ? '' : encodePath(text).replace(SLASH_RE, '%2F');\n}\n/**\n * Decode text using `decodeURIComponent`. Returns the original text if it\n * fails.\n *\n * @param text - string to decode\n * @returns decoded string\n */\nfunction decode(text) {\n  try {\n    return decodeURIComponent('' + text);\n  } catch (err) {\n    process.env.NODE_ENV !== 'production' && warn(`Error decoding \"${text}\". Using original value`);\n  }\n  return '' + text;\n}\nconst TRAILING_SLASH_RE = /\\/$/;\nconst removeTrailingSlash = path => path.replace(TRAILING_SLASH_RE, '');\n/**\n * Transforms a URI into a normalized history location\n *\n * @param parseQuery\n * @param location - URI to normalize\n * @param currentLocation - current absolute location. Allows resolving relative\n * paths. Must start with `/`. Defaults to `/`\n * @returns a normalized history location\n */\nfunction parseURL(parseQuery, location, currentLocation = '/') {\n  let path,\n    query = {},\n    searchString = '',\n    hash = '';\n  // Could use URL and URLSearchParams but IE 11 doesn't support it\n  // TODO: move to new URL()\n  const hashPos = location.indexOf('#');\n  let searchPos = location.indexOf('?');\n  // the hash appears before the search, so it's not part of the search string\n  if (hashPos < searchPos && hashPos >= 0) {\n    searchPos = -1;\n  }\n  if (searchPos > -1) {\n    path = location.slice(0, searchPos);\n    searchString = location.slice(searchPos + 1, hashPos > -1 ? hashPos : location.length);\n    query = parseQuery(searchString);\n  }\n  if (hashPos > -1) {\n    path = path || location.slice(0, hashPos);\n    // keep the # character\n    hash = location.slice(hashPos, location.length);\n  }\n  // no search and no query\n  path = resolveRelativePath(path != null ? path : location, currentLocation);\n  // empty path means a relative query or hash `?foo=f`, `#thing`\n  return {\n    fullPath: path + (searchString && '?') + searchString + hash,\n    path,\n    query,\n    hash: decode(hash)\n  };\n}\n/**\n * Stringifies a URL object\n *\n * @param stringifyQuery\n * @param location\n */\nfunction stringifyURL(stringifyQuery, location) {\n  const query = location.query ? stringifyQuery(location.query) : '';\n  return location.path + (query && '?') + query + (location.hash || '');\n}\n/**\n * Strips off the base from the beginning of a location.pathname in a non-case-sensitive way.\n *\n * @param pathname - location.pathname\n * @param base - base to strip off\n */\nfunction stripBase(pathname, base) {\n  // no base or base is not found at the beginning\n  if (!base || !pathname.toLowerCase().startsWith(base.toLowerCase())) return pathname;\n  return pathname.slice(base.length) || '/';\n}\n/**\n * Checks if two RouteLocation are equal. This means that both locations are\n * pointing towards the same {@link RouteRecord} and that all `params`, `query`\n * parameters and `hash` are the same\n *\n * @param stringifyQuery - A function that takes a query object of type LocationQueryRaw and returns a string representation of it.\n * @param a - first {@link RouteLocation}\n * @param b - second {@link RouteLocation}\n */\nfunction isSameRouteLocation(stringifyQuery, a, b) {\n  const aLastIndex = a.matched.length - 1;\n  const bLastIndex = b.matched.length - 1;\n  return aLastIndex > -1 && aLastIndex === bLastIndex && isSameRouteRecord(a.matched[aLastIndex], b.matched[bLastIndex]) && isSameRouteLocationParams(a.params, b.params) && stringifyQuery(a.query) === stringifyQuery(b.query) && a.hash === b.hash;\n}\n/**\n * Check if two `RouteRecords` are equal. Takes into account aliases: they are\n * considered equal to the `RouteRecord` they are aliasing.\n *\n * @param a - first {@link RouteRecord}\n * @param b - second {@link RouteRecord}\n */\nfunction isSameRouteRecord(a, b) {\n  // since the original record has an undefined value for aliasOf\n  // but all aliases point to the original record, this will always compare\n  // the original record\n  return (a.aliasOf || a) === (b.aliasOf || b);\n}\nfunction isSameRouteLocationParams(a, b) {\n  if (Object.keys(a).length !== Object.keys(b).length) return false;\n  for (const key in a) {\n    if (!isSameRouteLocationParamsValue(a[key], b[key])) return false;\n  }\n  return true;\n}\nfunction isSameRouteLocationParamsValue(a, b) {\n  return isArray(a) ? isEquivalentArray(a, b) : isArray(b) ? isEquivalentArray(b, a) : a === b;\n}\n/**\n * Check if two arrays are the same or if an array with one single entry is the\n * same as another primitive value. Used to check query and parameters\n *\n * @param a - array of values\n * @param b - array of values or a single value\n */\nfunction isEquivalentArray(a, b) {\n  return isArray(b) ? a.length === b.length && a.every((value, i) => value === b[i]) : a.length === 1 && a[0] === b;\n}\n/**\n * Resolves a relative path that starts with `.`.\n *\n * @param to - path location we are resolving\n * @param from - currentLocation.path, should start with `/`\n */\nfunction resolveRelativePath(to, from) {\n  if (to.startsWith('/')) return to;\n  if (process.env.NODE_ENV !== 'production' && !from.startsWith('/')) {\n    warn(`Cannot resolve a relative location without an absolute path. Trying to resolve \"${to}\" from \"${from}\". It should look like \"/${from}\".`);\n    return to;\n  }\n  if (!to) return from;\n  const fromSegments = from.split('/');\n  const toSegments = to.split('/');\n  const lastToSegment = toSegments[toSegments.length - 1];\n  // make . and ./ the same (../ === .., ../../ === ../..)\n  // this is the same behavior as new URL()\n  if (lastToSegment === '..' || lastToSegment === '.') {\n    toSegments.push('');\n  }\n  let position = fromSegments.length - 1;\n  let toPosition;\n  let segment;\n  for (toPosition = 0; toPosition < toSegments.length; toPosition++) {\n    segment = toSegments[toPosition];\n    // we stay on the same position\n    if (segment === '.') continue;\n    // go up in the from array\n    if (segment === '..') {\n      // we can't go below zero, but we still need to increment toPosition\n      if (position > 1) position--;\n      // continue\n    }\n    // we reached a non-relative path, we stop here\n    else break;\n  }\n  return fromSegments.slice(0, position).join('/') + '/' + toSegments.slice(toPosition).join('/');\n}\n/**\n * Initial route location where the router is. Can be used in navigation guards\n * to differentiate the initial navigation.\n *\n * @example\n * ```js\n * import { START_LOCATION } from 'vue-router'\n *\n * router.beforeEach((to, from) => {\n *   if (from === START_LOCATION) {\n *     // initial navigation\n *   }\n * })\n * ```\n */\nconst START_LOCATION_NORMALIZED = {\n  path: '/',\n  // TODO: could we use a symbol in the future?\n  name: undefined,\n  params: {},\n  query: {},\n  hash: '',\n  fullPath: '/',\n  matched: [],\n  meta: {},\n  redirectedFrom: undefined\n};\nvar NavigationType;\n(function (NavigationType) {\n  NavigationType[\"pop\"] = \"pop\";\n  NavigationType[\"push\"] = \"push\";\n})(NavigationType || (NavigationType = {}));\nvar NavigationDirection;\n(function (NavigationDirection) {\n  NavigationDirection[\"back\"] = \"back\";\n  NavigationDirection[\"forward\"] = \"forward\";\n  NavigationDirection[\"unknown\"] = \"\";\n})(NavigationDirection || (NavigationDirection = {}));\n/**\n * Starting location for Histories\n */\nconst START = '';\n// Generic utils\n/**\n * Normalizes a base by removing any trailing slash and reading the base tag if\n * present.\n *\n * @param base - base to normalize\n */\nfunction normalizeBase(base) {\n  if (!base) {\n    if (isBrowser) {\n      // respect <base> tag\n      const baseEl = document.querySelector('base');\n      base = baseEl && baseEl.getAttribute('href') || '/';\n      // strip full URL origin\n      base = base.replace(/^\\w+:\\/\\/[^\\/]+/, '');\n    } else {\n      base = '/';\n    }\n  }\n  // ensure leading slash when it was removed by the regex above avoid leading\n  // slash with hash because the file could be read from the disk like file://\n  // and the leading slash would cause problems\n  if (base[0] !== '/' && base[0] !== '#') base = '/' + base;\n  // remove the trailing slash so all other method can just do `base + fullPath`\n  // to build an href\n  return removeTrailingSlash(base);\n}\n// remove any character before the hash\nconst BEFORE_HASH_RE = /^[^#]+#/;\nfunction createHref(base, location) {\n  return base.replace(BEFORE_HASH_RE, '#') + location;\n}\nfunction getElementPosition(el, offset) {\n  const docRect = document.documentElement.getBoundingClientRect();\n  const elRect = el.getBoundingClientRect();\n  return {\n    behavior: offset.behavior,\n    left: elRect.left - docRect.left - (offset.left || 0),\n    top: elRect.top - docRect.top - (offset.top || 0)\n  };\n}\nconst computeScrollPosition = () => ({\n  left: window.scrollX,\n  top: window.scrollY\n});\nfunction scrollToPosition(position) {\n  let scrollToOptions;\n  if ('el' in position) {\n    const positionEl = position.el;\n    const isIdSelector = typeof positionEl === 'string' && positionEl.startsWith('#');\n    /**\n     * `id`s can accept pretty much any characters, including CSS combinators\n     * like `>` or `~`. It's still possible to retrieve elements using\n     * `document.getElementById('~')` but it needs to be escaped when using\n     * `document.querySelector('#\\\\~')` for it to be valid. The only\n     * requirements for `id`s are them to be unique on the page and to not be\n     * empty (`id=\"\"`). Because of that, when passing an id selector, it should\n     * be properly escaped for it to work with `querySelector`. We could check\n     * for the id selector to be simple (no CSS combinators `+ >~`) but that\n     * would make things inconsistent since they are valid characters for an\n     * `id` but would need to be escaped when using `querySelector`, breaking\n     * their usage and ending up in no selector returned. Selectors need to be\n     * escaped:\n     *\n     * - `#1-thing` becomes `#\\31 -thing`\n     * - `#with~symbols` becomes `#with\\\\~symbols`\n     *\n     * - More information about  the topic can be found at\n     *   https://mathiasbynens.be/notes/html5-id-class.\n     * - Practical example: https://mathiasbynens.be/demo/html5-id\n     */\n    if (process.env.NODE_ENV !== 'production' && typeof position.el === 'string') {\n      if (!isIdSelector || !document.getElementById(position.el.slice(1))) {\n        try {\n          const foundEl = document.querySelector(position.el);\n          if (isIdSelector && foundEl) {\n            warn(`The selector \"${position.el}\" should be passed as \"el: document.querySelector('${position.el}')\" because it starts with \"#\".`);\n            // return to avoid other warnings\n            return;\n          }\n        } catch (err) {\n          warn(`The selector \"${position.el}\" is invalid. If you are using an id selector, make sure to escape it. You can find more information about escaping characters in selectors at https://mathiasbynens.be/notes/css-escapes or use CSS.escape (https://developer.mozilla.org/en-US/docs/Web/API/CSS/escape).`);\n          // return to avoid other warnings\n          return;\n        }\n      }\n    }\n    const el = typeof positionEl === 'string' ? isIdSelector ? document.getElementById(positionEl.slice(1)) : document.querySelector(positionEl) : positionEl;\n    if (!el) {\n      process.env.NODE_ENV !== 'production' && warn(`Couldn't find element using selector \"${position.el}\" returned by scrollBehavior.`);\n      return;\n    }\n    scrollToOptions = getElementPosition(el, position);\n  } else {\n    scrollToOptions = position;\n  }\n  if ('scrollBehavior' in document.documentElement.style) window.scrollTo(scrollToOptions);else {\n    window.scrollTo(scrollToOptions.left != null ? scrollToOptions.left : window.scrollX, scrollToOptions.top != null ? scrollToOptions.top : window.scrollY);\n  }\n}\nfunction getScrollKey(path, delta) {\n  const position = history.state ? history.state.position - delta : -1;\n  return position + path;\n}\nconst scrollPositions = new Map();\nfunction saveScrollPosition(key, scrollPosition) {\n  scrollPositions.set(key, scrollPosition);\n}\nfunction getSavedScrollPosition(key) {\n  const scroll = scrollPositions.get(key);\n  // consume it so it's not used again\n  scrollPositions.delete(key);\n  return scroll;\n}\n// TODO: RFC about how to save scroll position\n/**\n * ScrollBehavior instance used by the router to compute and restore the scroll\n * position when navigating.\n */\n// export interface ScrollHandler<ScrollPositionEntry extends HistoryStateValue, ScrollPosition extends ScrollPositionEntry> {\n//   // returns a scroll position that can be saved in history\n//   compute(): ScrollPositionEntry\n//   // can take an extended ScrollPositionEntry\n//   scroll(position: ScrollPosition): void\n// }\n// export const scrollHandler: ScrollHandler<ScrollPosition> = {\n//   compute: computeScroll,\n//   scroll: scrollToPosition,\n// }\n\nlet createBaseLocation = () => location.protocol + '//' + location.host;\n/**\n * Creates a normalized history location from a window.location object\n * @param base - The base path\n * @param location - The window.location object\n */\nfunction createCurrentLocation(base, location) {\n  const {\n    pathname,\n    search,\n    hash\n  } = location;\n  // allows hash bases like #, /#, #/, #!, #!/, /#!/, or even /folder#end\n  const hashPos = base.indexOf('#');\n  if (hashPos > -1) {\n    let slicePos = hash.includes(base.slice(hashPos)) ? base.slice(hashPos).length : 1;\n    let pathFromHash = hash.slice(slicePos);\n    // prepend the starting slash to hash so the url starts with /#\n    if (pathFromHash[0] !== '/') pathFromHash = '/' + pathFromHash;\n    return stripBase(pathFromHash, '');\n  }\n  const path = stripBase(pathname, base);\n  return path + search + hash;\n}\nfunction useHistoryListeners(base, historyState, currentLocation, replace) {\n  let listeners = [];\n  let teardowns = [];\n  // TODO: should it be a stack? a Dict. Check if the popstate listener\n  // can trigger twice\n  let pauseState = null;\n  const popStateHandler = ({\n    state\n  }) => {\n    const to = createCurrentLocation(base, location);\n    const from = currentLocation.value;\n    const fromState = historyState.value;\n    let delta = 0;\n    if (state) {\n      currentLocation.value = to;\n      historyState.value = state;\n      // ignore the popstate and reset the pauseState\n      if (pauseState && pauseState === from) {\n        pauseState = null;\n        return;\n      }\n      delta = fromState ? state.position - fromState.position : 0;\n    } else {\n      replace(to);\n    }\n    // Here we could also revert the navigation by calling history.go(-delta)\n    // this listener will have to be adapted to not trigger again and to wait for the url\n    // to be updated before triggering the listeners. Some kind of validation function would also\n    // need to be passed to the listeners so the navigation can be accepted\n    // call all listeners\n    listeners.forEach(listener => {\n      listener(currentLocation.value, from, {\n        delta,\n        type: NavigationType.pop,\n        direction: delta ? delta > 0 ? NavigationDirection.forward : NavigationDirection.back : NavigationDirection.unknown\n      });\n    });\n  };\n  function pauseListeners() {\n    pauseState = currentLocation.value;\n  }\n  function listen(callback) {\n    // set up the listener and prepare teardown callbacks\n    listeners.push(callback);\n    const teardown = () => {\n      const index = listeners.indexOf(callback);\n      if (index > -1) listeners.splice(index, 1);\n    };\n    teardowns.push(teardown);\n    return teardown;\n  }\n  function beforeUnloadListener() {\n    const {\n      history\n    } = window;\n    if (!history.state) return;\n    history.replaceState(assign({}, history.state, {\n      scroll: computeScrollPosition()\n    }), '');\n  }\n  function destroy() {\n    for (const teardown of teardowns) teardown();\n    teardowns = [];\n    window.removeEventListener('popstate', popStateHandler);\n    window.removeEventListener('beforeunload', beforeUnloadListener);\n  }\n  // set up the listeners and prepare teardown callbacks\n  window.addEventListener('popstate', popStateHandler);\n  // TODO: could we use 'pagehide' or 'visibilitychange' instead?\n  // https://developer.chrome.com/blog/page-lifecycle-api/\n  window.addEventListener('beforeunload', beforeUnloadListener, {\n    passive: true\n  });\n  return {\n    pauseListeners,\n    listen,\n    destroy\n  };\n}\n/**\n * Creates a state object\n */\nfunction buildState(back, current, forward, replaced = false, computeScroll = false) {\n  return {\n    back,\n    current,\n    forward,\n    replaced,\n    position: window.history.length,\n    scroll: computeScroll ? computeScrollPosition() : null\n  };\n}\nfunction useHistoryStateNavigation(base) {\n  const {\n    history,\n    location\n  } = window;\n  // private variables\n  const currentLocation = {\n    value: createCurrentLocation(base, location)\n  };\n  const historyState = {\n    value: history.state\n  };\n  // build current history entry as this is a fresh navigation\n  if (!historyState.value) {\n    changeLocation(currentLocation.value, {\n      back: null,\n      current: currentLocation.value,\n      forward: null,\n      // the length is off by one, we need to decrease it\n      position: history.length - 1,\n      replaced: true,\n      // don't add a scroll as the user may have an anchor, and we want\n      // scrollBehavior to be triggered without a saved position\n      scroll: null\n    }, true);\n  }\n  function changeLocation(to, state, replace) {\n    /**\n     * if a base tag is provided, and we are on a normal domain, we have to\n     * respect the provided `base` attribute because pushState() will use it and\n     * potentially erase anything before the `#` like at\n     * https://github.com/vuejs/router/issues/685 where a base of\n     * `/folder/#` but a base of `/` would erase the `/folder/` section. If\n     * there is no host, the `<base>` tag makes no sense and if there isn't a\n     * base tag we can just use everything after the `#`.\n     */\n    const hashIndex = base.indexOf('#');\n    const url = hashIndex > -1 ? (location.host && document.querySelector('base') ? base : base.slice(hashIndex)) + to : createBaseLocation() + base + to;\n    try {\n      // BROWSER QUIRK\n      // NOTE: Safari throws a SecurityError when calling this function 100 times in 30 seconds\n      history[replace ? 'replaceState' : 'pushState'](state, '', url);\n      historyState.value = state;\n    } catch (err) {\n      if (process.env.NODE_ENV !== 'production') {\n        warn('Error with push/replace State', err);\n      } else {\n        console.error(err);\n      }\n      // Force the navigation, this also resets the call count\n      location[replace ? 'replace' : 'assign'](url);\n    }\n  }\n  function replace(to, data) {\n    const state = assign({}, history.state, buildState(historyState.value.back,\n    // keep back and forward entries but override current position\n    to, historyState.value.forward, true), data, {\n      position: historyState.value.position\n    });\n    changeLocation(to, state, true);\n    currentLocation.value = to;\n  }\n  function push(to, data) {\n    // Add to current entry the information of where we are going\n    // as well as saving the current position\n    const currentState = assign({},\n    // use current history state to gracefully handle a wrong call to\n    // history.replaceState\n    // https://github.com/vuejs/router/issues/366\n    historyState.value, history.state, {\n      forward: to,\n      scroll: computeScrollPosition()\n    });\n    if (process.env.NODE_ENV !== 'production' && !history.state) {\n      warn(`history.state seems to have been manually replaced without preserving the necessary values. Make sure to preserve existing history state if you are manually calling history.replaceState:\\n\\n` + `history.replaceState(history.state, '', url)\\n\\n` + `You can find more information at https://router.vuejs.org/guide/migration/#Usage-of-history-state`);\n    }\n    changeLocation(currentState.current, currentState, true);\n    const state = assign({}, buildState(currentLocation.value, to, null), {\n      position: currentState.position + 1\n    }, data);\n    changeLocation(to, state, false);\n    currentLocation.value = to;\n  }\n  return {\n    location: currentLocation,\n    state: historyState,\n    push,\n    replace\n  };\n}\n/**\n * Creates an HTML5 history. Most common history for single page applications.\n *\n * @param base -\n */\nfunction createWebHistory(base) {\n  base = normalizeBase(base);\n  const historyNavigation = useHistoryStateNavigation(base);\n  const historyListeners = useHistoryListeners(base, historyNavigation.state, historyNavigation.location, historyNavigation.replace);\n  function go(delta, triggerListeners = true) {\n    if (!triggerListeners) historyListeners.pauseListeners();\n    history.go(delta);\n  }\n  const routerHistory = assign({\n    // it's overridden right after\n    location: '',\n    base,\n    go,\n    createHref: createHref.bind(null, base)\n  }, historyNavigation, historyListeners);\n  Object.defineProperty(routerHistory, 'location', {\n    enumerable: true,\n    get: () => historyNavigation.location.value\n  });\n  Object.defineProperty(routerHistory, 'state', {\n    enumerable: true,\n    get: () => historyNavigation.state.value\n  });\n  return routerHistory;\n}\n\n/**\n * Creates an in-memory based history. The main purpose of this history is to handle SSR. It starts in a special location that is nowhere.\n * It's up to the user to replace that location with the starter location by either calling `router.push` or `router.replace`.\n *\n * @param base - Base applied to all urls, defaults to '/'\n * @returns a history object that can be passed to the router constructor\n */\nfunction createMemoryHistory(base = '') {\n  let listeners = [];\n  let queue = [[START, {}]];\n  let position = 0;\n  base = normalizeBase(base);\n  function setLocation(location, state = {}) {\n    position++;\n    if (position !== queue.length) {\n      // we are in the middle, we remove everything from here in the queue\n      queue.splice(position);\n    }\n    queue.push([location, state]);\n  }\n  function triggerListeners(to, from, {\n    direction,\n    delta\n  }) {\n    const info = {\n      direction,\n      delta,\n      type: NavigationType.pop\n    };\n    for (const callback of listeners) {\n      callback(to, from, info);\n    }\n  }\n  const routerHistory = {\n    // rewritten by Object.defineProperty\n    location: START,\n    // rewritten by Object.defineProperty\n    state: {},\n    base,\n    createHref: createHref.bind(null, base),\n    replace(to, state) {\n      // remove current entry and decrement position\n      queue.splice(position--, 1);\n      setLocation(to, state);\n    },\n    push(to, state) {\n      setLocation(to, state);\n    },\n    listen(callback) {\n      listeners.push(callback);\n      return () => {\n        const index = listeners.indexOf(callback);\n        if (index > -1) listeners.splice(index, 1);\n      };\n    },\n    destroy() {\n      listeners = [];\n      queue = [[START, {}]];\n      position = 0;\n    },\n    go(delta, shouldTrigger = true) {\n      const from = this.location;\n      const direction =\n      // we are considering delta === 0 going forward, but in abstract mode\n      // using 0 for the delta doesn't make sense like it does in html5 where\n      // it reloads the page\n      delta < 0 ? NavigationDirection.back : NavigationDirection.forward;\n      position = Math.max(0, Math.min(position + delta, queue.length - 1));\n      if (shouldTrigger) {\n        triggerListeners(this.location, from, {\n          direction,\n          delta\n        });\n      }\n    }\n  };\n  Object.defineProperty(routerHistory, 'location', {\n    enumerable: true,\n    get: () => queue[position][0]\n  });\n  Object.defineProperty(routerHistory, 'state', {\n    enumerable: true,\n    get: () => queue[position][1]\n  });\n  return routerHistory;\n}\n\n/**\n * Creates a hash history. Useful for web applications with no host (e.g. `file://`) or when configuring a server to\n * handle any URL is not possible.\n *\n * @param base - optional base to provide. Defaults to `location.pathname + location.search` If there is a `<base>` tag\n * in the `head`, its value will be ignored in favor of this parameter **but note it affects all the history.pushState()\n * calls**, meaning that if you use a `<base>` tag, it's `href` value **has to match this parameter** (ignoring anything\n * after the `#`).\n *\n * @example\n * ```js\n * // at https://example.com/folder\n * createWebHashHistory() // gives a url of `https://example.com/folder#`\n * createWebHashHistory('/folder/') // gives a url of `https://example.com/folder/#`\n * // if the `#` is provided in the base, it won't be added by `createWebHashHistory`\n * createWebHashHistory('/folder/#/app/') // gives a url of `https://example.com/folder/#/app/`\n * // you should avoid doing this because it changes the original url and breaks copying urls\n * createWebHashHistory('/other-folder/') // gives a url of `https://example.com/other-folder/#`\n *\n * // at file:///usr/etc/folder/index.html\n * // for locations with no `host`, the base is ignored\n * createWebHashHistory('/iAmIgnored') // gives a url of `file:///usr/etc/folder/index.html#`\n * ```\n */\nfunction createWebHashHistory(base) {\n  // Make sure this implementation is fine in terms of encoding, specially for IE11\n  // for `file://`, directly use the pathname and ignore the base\n  // location.pathname contains an initial `/` even at the root: `https://example.com`\n  base = location.host ? base || location.pathname + location.search : '';\n  // allow the user to provide a `#` in the middle: `/base/#/app`\n  if (!base.includes('#')) base += '#';\n  if (process.env.NODE_ENV !== 'production' && !base.endsWith('#/') && !base.endsWith('#')) {\n    warn(`A hash base must end with a \"#\":\\n\"${base}\" should be \"${base.replace(/#.*$/, '#')}\".`);\n  }\n  return createWebHistory(base);\n}\nfunction isRouteLocation(route) {\n  return typeof route === 'string' || route && typeof route === 'object';\n}\nfunction isRouteName(name) {\n  return typeof name === 'string' || typeof name === 'symbol';\n}\nconst NavigationFailureSymbol = Symbol(process.env.NODE_ENV !== 'production' ? 'navigation failure' : '');\n/**\n * Enumeration with all possible types for navigation failures. Can be passed to\n * {@link isNavigationFailure} to check for specific failures.\n */\nvar NavigationFailureType;\n(function (NavigationFailureType) {\n  /**\n   * An aborted navigation is a navigation that failed because a navigation\n   * guard returned `false` or called `next(false)`\n   */\n  NavigationFailureType[NavigationFailureType[\"aborted\"] = 4] = \"aborted\";\n  /**\n   * A cancelled navigation is a navigation that failed because a more recent\n   * navigation finished started (not necessarily finished).\n   */\n  NavigationFailureType[NavigationFailureType[\"cancelled\"] = 8] = \"cancelled\";\n  /**\n   * A duplicated navigation is a navigation that failed because it was\n   * initiated while already being at the exact same location.\n   */\n  NavigationFailureType[NavigationFailureType[\"duplicated\"] = 16] = \"duplicated\";\n})(NavigationFailureType || (NavigationFailureType = {}));\n// DEV only debug messages\nconst ErrorTypeMessages = {\n  [1 /* ErrorTypes.MATCHER_NOT_FOUND */]({\n    location,\n    currentLocation\n  }) {\n    return `No match for\\n ${JSON.stringify(location)}${currentLocation ? '\\nwhile being at\\n' + JSON.stringify(currentLocation) : ''}`;\n  },\n  [2 /* ErrorTypes.NAVIGATION_GUARD_REDIRECT */]({\n    from,\n    to\n  }) {\n    return `Redirected from \"${from.fullPath}\" to \"${stringifyRoute(to)}\" via a navigation guard.`;\n  },\n  [4 /* ErrorTypes.NAVIGATION_ABORTED */]({\n    from,\n    to\n  }) {\n    return `Navigation aborted from \"${from.fullPath}\" to \"${to.fullPath}\" via a navigation guard.`;\n  },\n  [8 /* ErrorTypes.NAVIGATION_CANCELLED */]({\n    from,\n    to\n  }) {\n    return `Navigation cancelled from \"${from.fullPath}\" to \"${to.fullPath}\" with a new navigation.`;\n  },\n  [16 /* ErrorTypes.NAVIGATION_DUPLICATED */]({\n    from,\n    to\n  }) {\n    return `Avoided redundant navigation to current location: \"${from.fullPath}\".`;\n  }\n};\n/**\n * Creates a typed NavigationFailure object.\n * @internal\n * @param type - NavigationFailureType\n * @param params - { from, to }\n */\nfunction createRouterError(type, params) {\n  // keep full error messages in cjs versions\n  if (process.env.NODE_ENV !== 'production' || !true) {\n    return assign(new Error(ErrorTypeMessages[type](params)), {\n      type,\n      [NavigationFailureSymbol]: true\n    }, params);\n  } else {\n    return assign(new Error(), {\n      type,\n      [NavigationFailureSymbol]: true\n    }, params);\n  }\n}\nfunction isNavigationFailure(error, type) {\n  return error instanceof Error && NavigationFailureSymbol in error && (type == null || !!(error.type & type));\n}\nconst propertiesToLog = ['params', 'query', 'hash'];\nfunction stringifyRoute(to) {\n  if (typeof to === 'string') return to;\n  if (to.path != null) return to.path;\n  const location = {};\n  for (const key of propertiesToLog) {\n    if (key in to) location[key] = to[key];\n  }\n  return JSON.stringify(location, null, 2);\n}\n\n// default pattern for a param: non-greedy everything but /\nconst BASE_PARAM_PATTERN = '[^/]+?';\nconst BASE_PATH_PARSER_OPTIONS = {\n  sensitive: false,\n  strict: false,\n  start: true,\n  end: true\n};\n// Special Regex characters that must be escaped in static tokens\nconst REGEX_CHARS_RE = /[.+*?^${}()[\\]/\\\\]/g;\n/**\n * Creates a path parser from an array of Segments (a segment is an array of Tokens)\n *\n * @param segments - array of segments returned by tokenizePath\n * @param extraOptions - optional options for the regexp\n * @returns a PathParser\n */\nfunction tokensToParser(segments, extraOptions) {\n  const options = assign({}, BASE_PATH_PARSER_OPTIONS, extraOptions);\n  // the amount of scores is the same as the length of segments except for the root segment \"/\"\n  const score = [];\n  // the regexp as a string\n  let pattern = options.start ? '^' : '';\n  // extracted keys\n  const keys = [];\n  for (const segment of segments) {\n    // the root segment needs special treatment\n    const segmentScores = segment.length ? [] : [90 /* PathScore.Root */];\n    // allow trailing slash\n    if (options.strict && !segment.length) pattern += '/';\n    for (let tokenIndex = 0; tokenIndex < segment.length; tokenIndex++) {\n      const token = segment[tokenIndex];\n      // resets the score if we are inside a sub-segment /:a-other-:b\n      let subSegmentScore = 40 /* PathScore.Segment */ + (options.sensitive ? 0.25 /* PathScore.BonusCaseSensitive */ : 0);\n      if (token.type === 0 /* TokenType.Static */) {\n        // prepend the slash if we are starting a new segment\n        if (!tokenIndex) pattern += '/';\n        pattern += token.value.replace(REGEX_CHARS_RE, '\\\\$&');\n        subSegmentScore += 40 /* PathScore.Static */;\n      } else if (token.type === 1 /* TokenType.Param */) {\n        const {\n          value,\n          repeatable,\n          optional,\n          regexp\n        } = token;\n        keys.push({\n          name: value,\n          repeatable,\n          optional\n        });\n        const re = regexp ? regexp : BASE_PARAM_PATTERN;\n        // the user provided a custom regexp /:id(\\\\d+)\n        if (re !== BASE_PARAM_PATTERN) {\n          subSegmentScore += 10 /* PathScore.BonusCustomRegExp */;\n          // make sure the regexp is valid before using it\n          try {\n            new RegExp(`(${re})`);\n          } catch (err) {\n            throw new Error(`Invalid custom RegExp for param \"${value}\" (${re}): ` + err.message);\n          }\n        }\n        // when we repeat we must take care of the repeating leading slash\n        let subPattern = repeatable ? `((?:${re})(?:/(?:${re}))*)` : `(${re})`;\n        // prepend the slash if we are starting a new segment\n        if (!tokenIndex) subPattern =\n        // avoid an optional / if there are more segments e.g. /:p?-static\n        // or /:p?-:p2\n        optional && segment.length < 2 ? `(?:/${subPattern})` : '/' + subPattern;\n        if (optional) subPattern += '?';\n        pattern += subPattern;\n        subSegmentScore += 20 /* PathScore.Dynamic */;\n        if (optional) subSegmentScore += -8 /* PathScore.BonusOptional */;\n        if (repeatable) subSegmentScore += -20 /* PathScore.BonusRepeatable */;\n        if (re === '.*') subSegmentScore += -50 /* PathScore.BonusWildcard */;\n      }\n      segmentScores.push(subSegmentScore);\n    }\n    // an empty array like /home/<USER>\n    // if (!segment.length) pattern += '/'\n    score.push(segmentScores);\n  }\n  // only apply the strict bonus to the last score\n  if (options.strict && options.end) {\n    const i = score.length - 1;\n    score[i][score[i].length - 1] += 0.7000000000000001 /* PathScore.BonusStrict */;\n  }\n  // TODO: dev only warn double trailing slash\n  if (!options.strict) pattern += '/?';\n  if (options.end) pattern += '$';\n  // allow paths like /dynamic to only match dynamic or dynamic/... but not dynamic_something_else\n  else if (options.strict && !pattern.endsWith('/')) pattern += '(?:/|$)';\n  const re = new RegExp(pattern, options.sensitive ? '' : 'i');\n  function parse(path) {\n    const match = path.match(re);\n    const params = {};\n    if (!match) return null;\n    for (let i = 1; i < match.length; i++) {\n      const value = match[i] || '';\n      const key = keys[i - 1];\n      params[key.name] = value && key.repeatable ? value.split('/') : value;\n    }\n    return params;\n  }\n  function stringify(params) {\n    let path = '';\n    // for optional parameters to allow to be empty\n    let avoidDuplicatedSlash = false;\n    for (const segment of segments) {\n      if (!avoidDuplicatedSlash || !path.endsWith('/')) path += '/';\n      avoidDuplicatedSlash = false;\n      for (const token of segment) {\n        if (token.type === 0 /* TokenType.Static */) {\n          path += token.value;\n        } else if (token.type === 1 /* TokenType.Param */) {\n          const {\n            value,\n            repeatable,\n            optional\n          } = token;\n          const param = value in params ? params[value] : '';\n          if (isArray(param) && !repeatable) {\n            throw new Error(`Provided param \"${value}\" is an array but it is not repeatable (* or + modifiers)`);\n          }\n          const text = isArray(param) ? param.join('/') : param;\n          if (!text) {\n            if (optional) {\n              // if we have more than one optional param like /:a?-static we don't need to care about the optional param\n              if (segment.length < 2) {\n                // remove the last slash as we could be at the end\n                if (path.endsWith('/')) path = path.slice(0, -1);\n                // do not append a slash on the next iteration\n                else avoidDuplicatedSlash = true;\n              }\n            } else throw new Error(`Missing required param \"${value}\"`);\n          }\n          path += text;\n        }\n      }\n    }\n    // avoid empty path when we have multiple optional params\n    return path || '/';\n  }\n  return {\n    re,\n    score,\n    keys,\n    parse,\n    stringify\n  };\n}\n/**\n * Compares an array of numbers as used in PathParser.score and returns a\n * number. This function can be used to `sort` an array\n *\n * @param a - first array of numbers\n * @param b - second array of numbers\n * @returns 0 if both are equal, < 0 if a should be sorted first, > 0 if b\n * should be sorted first\n */\nfunction compareScoreArray(a, b) {\n  let i = 0;\n  while (i < a.length && i < b.length) {\n    const diff = b[i] - a[i];\n    // only keep going if diff === 0\n    if (diff) return diff;\n    i++;\n  }\n  // if the last subsegment was Static, the shorter segments should be sorted first\n  // otherwise sort the longest segment first\n  if (a.length < b.length) {\n    return a.length === 1 && a[0] === 40 /* PathScore.Static */ + 40 /* PathScore.Segment */ ? -1 : 1;\n  } else if (a.length > b.length) {\n    return b.length === 1 && b[0] === 40 /* PathScore.Static */ + 40 /* PathScore.Segment */ ? 1 : -1;\n  }\n  return 0;\n}\n/**\n * Compare function that can be used with `sort` to sort an array of PathParser\n *\n * @param a - first PathParser\n * @param b - second PathParser\n * @returns 0 if both are equal, < 0 if a should be sorted first, > 0 if b\n */\nfunction comparePathParserScore(a, b) {\n  let i = 0;\n  const aScore = a.score;\n  const bScore = b.score;\n  while (i < aScore.length && i < bScore.length) {\n    const comp = compareScoreArray(aScore[i], bScore[i]);\n    // do not return if both are equal\n    if (comp) return comp;\n    i++;\n  }\n  if (Math.abs(bScore.length - aScore.length) === 1) {\n    if (isLastScoreNegative(aScore)) return 1;\n    if (isLastScoreNegative(bScore)) return -1;\n  }\n  // if a and b share the same score entries but b has more, sort b first\n  return bScore.length - aScore.length;\n  // this is the ternary version\n  // return aScore.length < bScore.length\n  //   ? 1\n  //   : aScore.length > bScore.length\n  //   ? -1\n  //   : 0\n}\n/**\n * This allows detecting splats at the end of a path: /home/<USER>\n *\n * @param score - score to check\n * @returns true if the last entry is negative\n */\nfunction isLastScoreNegative(score) {\n  const last = score[score.length - 1];\n  return score.length > 0 && last[last.length - 1] < 0;\n}\nconst ROOT_TOKEN = {\n  type: 0 /* TokenType.Static */,\n  value: ''\n};\nconst VALID_PARAM_RE = /[a-zA-Z0-9_]/;\n// After some profiling, the cache seems to be unnecessary because tokenizePath\n// (the slowest part of adding a route) is very fast\n// const tokenCache = new Map<string, Token[][]>()\nfunction tokenizePath(path) {\n  if (!path) return [[]];\n  if (path === '/') return [[ROOT_TOKEN]];\n  if (!path.startsWith('/')) {\n    throw new Error(process.env.NODE_ENV !== 'production' ? `Route paths should start with a \"/\": \"${path}\" should be \"/${path}\".` : `Invalid path \"${path}\"`);\n  }\n  // if (tokenCache.has(path)) return tokenCache.get(path)!\n  function crash(message) {\n    throw new Error(`ERR (${state})/\"${buffer}\": ${message}`);\n  }\n  let state = 0 /* TokenizerState.Static */;\n  let previousState = state;\n  const tokens = [];\n  // the segment will always be valid because we get into the initial state\n  // with the leading /\n  let segment;\n  function finalizeSegment() {\n    if (segment) tokens.push(segment);\n    segment = [];\n  }\n  // index on the path\n  let i = 0;\n  // char at index\n  let char;\n  // buffer of the value read\n  let buffer = '';\n  // custom regexp for a param\n  let customRe = '';\n  function consumeBuffer() {\n    if (!buffer) return;\n    if (state === 0 /* TokenizerState.Static */) {\n      segment.push({\n        type: 0 /* TokenType.Static */,\n        value: buffer\n      });\n    } else if (state === 1 /* TokenizerState.Param */ || state === 2 /* TokenizerState.ParamRegExp */ || state === 3 /* TokenizerState.ParamRegExpEnd */) {\n      if (segment.length > 1 && (char === '*' || char === '+')) crash(`A repeatable param (${buffer}) must be alone in its segment. eg: '/:ids+.`);\n      segment.push({\n        type: 1 /* TokenType.Param */,\n        value: buffer,\n        regexp: customRe,\n        repeatable: char === '*' || char === '+',\n        optional: char === '*' || char === '?'\n      });\n    } else {\n      crash('Invalid state to consume buffer');\n    }\n    buffer = '';\n  }\n  function addCharToBuffer() {\n    buffer += char;\n  }\n  while (i < path.length) {\n    char = path[i++];\n    if (char === '\\\\' && state !== 2 /* TokenizerState.ParamRegExp */) {\n      previousState = state;\n      state = 4 /* TokenizerState.EscapeNext */;\n      continue;\n    }\n    switch (state) {\n      case 0 /* TokenizerState.Static */:\n        if (char === '/') {\n          if (buffer) {\n            consumeBuffer();\n          }\n          finalizeSegment();\n        } else if (char === ':') {\n          consumeBuffer();\n          state = 1 /* TokenizerState.Param */;\n        } else {\n          addCharToBuffer();\n        }\n        break;\n      case 4 /* TokenizerState.EscapeNext */:\n        addCharToBuffer();\n        state = previousState;\n        break;\n      case 1 /* TokenizerState.Param */:\n        if (char === '(') {\n          state = 2 /* TokenizerState.ParamRegExp */;\n        } else if (VALID_PARAM_RE.test(char)) {\n          addCharToBuffer();\n        } else {\n          consumeBuffer();\n          state = 0 /* TokenizerState.Static */;\n          // go back one character if we were not modifying\n          if (char !== '*' && char !== '?' && char !== '+') i--;\n        }\n        break;\n      case 2 /* TokenizerState.ParamRegExp */:\n        // TODO: is it worth handling nested regexp? like :p(?:prefix_([^/]+)_suffix)\n        // it already works by escaping the closing )\n        // https://paths.esm.dev/?p=AAMeJbiAwQEcDKbAoAAkP60PG2R6QAvgNaA6AFACM2ABuQBB#\n        // is this really something people need since you can also write\n        // /prefix_:p()_suffix\n        if (char === ')') {\n          // handle the escaped )\n          if (customRe[customRe.length - 1] == '\\\\') customRe = customRe.slice(0, -1) + char;else state = 3 /* TokenizerState.ParamRegExpEnd */;\n        } else {\n          customRe += char;\n        }\n        break;\n      case 3 /* TokenizerState.ParamRegExpEnd */:\n        // same as finalizing a param\n        consumeBuffer();\n        state = 0 /* TokenizerState.Static */;\n        // go back one character if we were not modifying\n        if (char !== '*' && char !== '?' && char !== '+') i--;\n        customRe = '';\n        break;\n      default:\n        crash('Unknown state');\n        break;\n    }\n  }\n  if (state === 2 /* TokenizerState.ParamRegExp */) crash(`Unfinished custom RegExp for param \"${buffer}\"`);\n  consumeBuffer();\n  finalizeSegment();\n  // tokenCache.set(path, tokens)\n  return tokens;\n}\nfunction createRouteRecordMatcher(record, parent, options) {\n  const parser = tokensToParser(tokenizePath(record.path), options);\n  // warn against params with the same name\n  if (process.env.NODE_ENV !== 'production') {\n    const existingKeys = new Set();\n    for (const key of parser.keys) {\n      if (existingKeys.has(key.name)) warn(`Found duplicated params with name \"${key.name}\" for path \"${record.path}\". Only the last one will be available on \"$route.params\".`);\n      existingKeys.add(key.name);\n    }\n  }\n  const matcher = assign(parser, {\n    record,\n    parent,\n    // these needs to be populated by the parent\n    children: [],\n    alias: []\n  });\n  if (parent) {\n    // both are aliases or both are not aliases\n    // we don't want to mix them because the order is used when\n    // passing originalRecord in Matcher.addRoute\n    if (!matcher.record.aliasOf === !parent.record.aliasOf) parent.children.push(matcher);\n  }\n  return matcher;\n}\n\n/**\n * Creates a Router Matcher.\n *\n * @internal\n * @param routes - array of initial routes\n * @param globalOptions - global route options\n */\nfunction createRouterMatcher(routes, globalOptions) {\n  // normalized ordered array of matchers\n  const matchers = [];\n  const matcherMap = new Map();\n  globalOptions = mergeOptions({\n    strict: false,\n    end: true,\n    sensitive: false\n  }, globalOptions);\n  function getRecordMatcher(name) {\n    return matcherMap.get(name);\n  }\n  function addRoute(record, parent, originalRecord) {\n    // used later on to remove by name\n    const isRootAdd = !originalRecord;\n    const mainNormalizedRecord = normalizeRouteRecord(record);\n    if (process.env.NODE_ENV !== 'production') {\n      checkChildMissingNameWithEmptyPath(mainNormalizedRecord, parent);\n    }\n    // we might be the child of an alias\n    mainNormalizedRecord.aliasOf = originalRecord && originalRecord.record;\n    const options = mergeOptions(globalOptions, record);\n    // generate an array of records to correctly handle aliases\n    const normalizedRecords = [mainNormalizedRecord];\n    if ('alias' in record) {\n      const aliases = typeof record.alias === 'string' ? [record.alias] : record.alias;\n      for (const alias of aliases) {\n        normalizedRecords.push(\n        // we need to normalize again to ensure the `mods` property\n        // being non enumerable\n        normalizeRouteRecord(assign({}, mainNormalizedRecord, {\n          // this allows us to hold a copy of the `components` option\n          // so that async components cache is hold on the original record\n          components: originalRecord ? originalRecord.record.components : mainNormalizedRecord.components,\n          path: alias,\n          // we might be the child of an alias\n          aliasOf: originalRecord ? originalRecord.record : mainNormalizedRecord\n          // the aliases are always of the same kind as the original since they\n          // are defined on the same record\n        })));\n      }\n    }\n    let matcher;\n    let originalMatcher;\n    for (const normalizedRecord of normalizedRecords) {\n      const {\n        path\n      } = normalizedRecord;\n      // Build up the path for nested routes if the child isn't an absolute\n      // route. Only add the / delimiter if the child path isn't empty and if the\n      // parent path doesn't have a trailing slash\n      if (parent && path[0] !== '/') {\n        const parentPath = parent.record.path;\n        const connectingSlash = parentPath[parentPath.length - 1] === '/' ? '' : '/';\n        normalizedRecord.path = parent.record.path + (path && connectingSlash + path);\n      }\n      if (process.env.NODE_ENV !== 'production' && normalizedRecord.path === '*') {\n        throw new Error('Catch all routes (\"*\") must now be defined using a param with a custom regexp.\\n' + 'See more at https://router.vuejs.org/guide/migration/#Removed-star-or-catch-all-routes.');\n      }\n      // create the object beforehand, so it can be passed to children\n      matcher = createRouteRecordMatcher(normalizedRecord, parent, options);\n      if (process.env.NODE_ENV !== 'production' && parent && path[0] === '/') checkMissingParamsInAbsolutePath(matcher, parent);\n      // if we are an alias we must tell the original record that we exist,\n      // so we can be removed\n      if (originalRecord) {\n        originalRecord.alias.push(matcher);\n        if (process.env.NODE_ENV !== 'production') {\n          checkSameParams(originalRecord, matcher);\n        }\n      } else {\n        // otherwise, the first record is the original and others are aliases\n        originalMatcher = originalMatcher || matcher;\n        if (originalMatcher !== matcher) originalMatcher.alias.push(matcher);\n        // remove the route if named and only for the top record (avoid in nested calls)\n        // this works because the original record is the first one\n        if (isRootAdd && record.name && !isAliasRecord(matcher)) {\n          if (process.env.NODE_ENV !== 'production') {\n            checkSameNameAsAncestor(record, parent);\n          }\n          removeRoute(record.name);\n        }\n      }\n      // Avoid adding a record that doesn't display anything. This allows passing through records without a component to\n      // not be reached and pass through the catch all route\n      if (isMatchable(matcher)) {\n        insertMatcher(matcher);\n      }\n      if (mainNormalizedRecord.children) {\n        const children = mainNormalizedRecord.children;\n        for (let i = 0; i < children.length; i++) {\n          addRoute(children[i], matcher, originalRecord && originalRecord.children[i]);\n        }\n      }\n      // if there was no original record, then the first one was not an alias and all\n      // other aliases (if any) need to reference this record when adding children\n      originalRecord = originalRecord || matcher;\n      // TODO: add normalized records for more flexibility\n      // if (parent && isAliasRecord(originalRecord)) {\n      //   parent.children.push(originalRecord)\n      // }\n    }\n    return originalMatcher ? () => {\n      // since other matchers are aliases, they should be removed by the original matcher\n      removeRoute(originalMatcher);\n    } : noop;\n  }\n  function removeRoute(matcherRef) {\n    if (isRouteName(matcherRef)) {\n      const matcher = matcherMap.get(matcherRef);\n      if (matcher) {\n        matcherMap.delete(matcherRef);\n        matchers.splice(matchers.indexOf(matcher), 1);\n        matcher.children.forEach(removeRoute);\n        matcher.alias.forEach(removeRoute);\n      }\n    } else {\n      const index = matchers.indexOf(matcherRef);\n      if (index > -1) {\n        matchers.splice(index, 1);\n        if (matcherRef.record.name) matcherMap.delete(matcherRef.record.name);\n        matcherRef.children.forEach(removeRoute);\n        matcherRef.alias.forEach(removeRoute);\n      }\n    }\n  }\n  function getRoutes() {\n    return matchers;\n  }\n  function insertMatcher(matcher) {\n    const index = findInsertionIndex(matcher, matchers);\n    matchers.splice(index, 0, matcher);\n    // only add the original record to the name map\n    if (matcher.record.name && !isAliasRecord(matcher)) matcherMap.set(matcher.record.name, matcher);\n  }\n  function resolve(location, currentLocation) {\n    let matcher;\n    let params = {};\n    let path;\n    let name;\n    if ('name' in location && location.name) {\n      matcher = matcherMap.get(location.name);\n      if (!matcher) throw createRouterError(1 /* ErrorTypes.MATCHER_NOT_FOUND */, {\n        location\n      });\n      // warn if the user is passing invalid params so they can debug it better when they get removed\n      if (process.env.NODE_ENV !== 'production') {\n        const invalidParams = Object.keys(location.params || {}).filter(paramName => !matcher.keys.find(k => k.name === paramName));\n        if (invalidParams.length) {\n          warn(`Discarded invalid param(s) \"${invalidParams.join('\", \"')}\" when navigating. See https://github.com/vuejs/router/blob/main/packages/router/CHANGELOG.md#414-2022-08-22 for more details.`);\n        }\n      }\n      name = matcher.record.name;\n      params = assign(\n      // paramsFromLocation is a new object\n      paramsFromLocation(currentLocation.params,\n      // only keep params that exist in the resolved location\n      // only keep optional params coming from a parent record\n      matcher.keys.filter(k => !k.optional).concat(matcher.parent ? matcher.parent.keys.filter(k => k.optional) : []).map(k => k.name)),\n      // discard any existing params in the current location that do not exist here\n      // #1497 this ensures better active/exact matching\n      location.params && paramsFromLocation(location.params, matcher.keys.map(k => k.name)));\n      // throws if cannot be stringified\n      path = matcher.stringify(params);\n    } else if (location.path != null) {\n      // no need to resolve the path with the matcher as it was provided\n      // this also allows the user to control the encoding\n      path = location.path;\n      if (process.env.NODE_ENV !== 'production' && !path.startsWith('/')) {\n        warn(`The Matcher cannot resolve relative paths but received \"${path}\". Unless you directly called \\`matcher.resolve(\"${path}\")\\`, this is probably a bug in vue-router. Please open an issue at https://github.com/vuejs/router/issues/new/choose.`);\n      }\n      matcher = matchers.find(m => m.re.test(path));\n      // matcher should have a value after the loop\n      if (matcher) {\n        // we know the matcher works because we tested the regexp\n        params = matcher.parse(path);\n        name = matcher.record.name;\n      }\n      // location is a relative path\n    } else {\n      // match by name or path of current route\n      matcher = currentLocation.name ? matcherMap.get(currentLocation.name) : matchers.find(m => m.re.test(currentLocation.path));\n      if (!matcher) throw createRouterError(1 /* ErrorTypes.MATCHER_NOT_FOUND */, {\n        location,\n        currentLocation\n      });\n      name = matcher.record.name;\n      // since we are navigating to the same location, we don't need to pick the\n      // params like when `name` is provided\n      params = assign({}, currentLocation.params, location.params);\n      path = matcher.stringify(params);\n    }\n    const matched = [];\n    let parentMatcher = matcher;\n    while (parentMatcher) {\n      // reversed order so parents are at the beginning\n      matched.unshift(parentMatcher.record);\n      parentMatcher = parentMatcher.parent;\n    }\n    return {\n      name,\n      path,\n      params,\n      matched,\n      meta: mergeMetaFields(matched)\n    };\n  }\n  // add initial routes\n  routes.forEach(route => addRoute(route));\n  function clearRoutes() {\n    matchers.length = 0;\n    matcherMap.clear();\n  }\n  return {\n    addRoute,\n    resolve,\n    removeRoute,\n    clearRoutes,\n    getRoutes,\n    getRecordMatcher\n  };\n}\nfunction paramsFromLocation(params, keys) {\n  const newParams = {};\n  for (const key of keys) {\n    if (key in params) newParams[key] = params[key];\n  }\n  return newParams;\n}\n/**\n * Normalizes a RouteRecordRaw. Creates a copy\n *\n * @param record\n * @returns the normalized version\n */\nfunction normalizeRouteRecord(record) {\n  const normalized = {\n    path: record.path,\n    redirect: record.redirect,\n    name: record.name,\n    meta: record.meta || {},\n    aliasOf: record.aliasOf,\n    beforeEnter: record.beforeEnter,\n    props: normalizeRecordProps(record),\n    children: record.children || [],\n    instances: {},\n    leaveGuards: new Set(),\n    updateGuards: new Set(),\n    enterCallbacks: {},\n    // must be declared afterwards\n    // mods: {},\n    components: 'components' in record ? record.components || null : record.component && {\n      default: record.component\n    }\n  };\n  // mods contain modules and shouldn't be copied,\n  // logged or anything. It's just used for internal\n  // advanced use cases like data loaders\n  Object.defineProperty(normalized, 'mods', {\n    value: {}\n  });\n  return normalized;\n}\n/**\n * Normalize the optional `props` in a record to always be an object similar to\n * components. Also accept a boolean for components.\n * @param record\n */\nfunction normalizeRecordProps(record) {\n  const propsObject = {};\n  // props does not exist on redirect records, but we can set false directly\n  const props = record.props || false;\n  if ('component' in record) {\n    propsObject.default = props;\n  } else {\n    // NOTE: we could also allow a function to be applied to every component.\n    // Would need user feedback for use cases\n    for (const name in record.components) propsObject[name] = typeof props === 'object' ? props[name] : props;\n  }\n  return propsObject;\n}\n/**\n * Checks if a record or any of its parent is an alias\n * @param record\n */\nfunction isAliasRecord(record) {\n  while (record) {\n    if (record.record.aliasOf) return true;\n    record = record.parent;\n  }\n  return false;\n}\n/**\n * Merge meta fields of an array of records\n *\n * @param matched - array of matched records\n */\nfunction mergeMetaFields(matched) {\n  return matched.reduce((meta, record) => assign(meta, record.meta), {});\n}\nfunction mergeOptions(defaults, partialOptions) {\n  const options = {};\n  for (const key in defaults) {\n    options[key] = key in partialOptions ? partialOptions[key] : defaults[key];\n  }\n  return options;\n}\nfunction isSameParam(a, b) {\n  return a.name === b.name && a.optional === b.optional && a.repeatable === b.repeatable;\n}\n/**\n * Check if a path and its alias have the same required params\n *\n * @param a - original record\n * @param b - alias record\n */\nfunction checkSameParams(a, b) {\n  for (const key of a.keys) {\n    if (!key.optional && !b.keys.find(isSameParam.bind(null, key))) return warn(`Alias \"${b.record.path}\" and the original record: \"${a.record.path}\" must have the exact same param named \"${key.name}\"`);\n  }\n  for (const key of b.keys) {\n    if (!key.optional && !a.keys.find(isSameParam.bind(null, key))) return warn(`Alias \"${b.record.path}\" and the original record: \"${a.record.path}\" must have the exact same param named \"${key.name}\"`);\n  }\n}\n/**\n * A route with a name and a child with an empty path without a name should warn when adding the route\n *\n * @param mainNormalizedRecord - RouteRecordNormalized\n * @param parent - RouteRecordMatcher\n */\nfunction checkChildMissingNameWithEmptyPath(mainNormalizedRecord, parent) {\n  if (parent && parent.record.name && !mainNormalizedRecord.name && !mainNormalizedRecord.path) {\n    warn(`The route named \"${String(parent.record.name)}\" has a child without a name and an empty path. Using that name won't render the empty path child so you probably want to move the name to the child instead. If this is intentional, add a name to the child route to remove the warning.`);\n  }\n}\nfunction checkSameNameAsAncestor(record, parent) {\n  for (let ancestor = parent; ancestor; ancestor = ancestor.parent) {\n    if (ancestor.record.name === record.name) {\n      throw new Error(`A route named \"${String(record.name)}\" has been added as a ${parent === ancestor ? 'child' : 'descendant'} of a route with the same name. Route names must be unique and a nested route cannot use the same name as an ancestor.`);\n    }\n  }\n}\nfunction checkMissingParamsInAbsolutePath(record, parent) {\n  for (const key of parent.keys) {\n    if (!record.keys.find(isSameParam.bind(null, key))) return warn(`Absolute path \"${record.record.path}\" must have the exact same param named \"${key.name}\" as its parent \"${parent.record.path}\".`);\n  }\n}\n/**\n * Performs a binary search to find the correct insertion index for a new matcher.\n *\n * Matchers are primarily sorted by their score. If scores are tied then we also consider parent/child relationships,\n * with descendants coming before ancestors. If there's still a tie, new routes are inserted after existing routes.\n *\n * @param matcher - new matcher to be inserted\n * @param matchers - existing matchers\n */\nfunction findInsertionIndex(matcher, matchers) {\n  // First phase: binary search based on score\n  let lower = 0;\n  let upper = matchers.length;\n  while (lower !== upper) {\n    const mid = lower + upper >> 1;\n    const sortOrder = comparePathParserScore(matcher, matchers[mid]);\n    if (sortOrder < 0) {\n      upper = mid;\n    } else {\n      lower = mid + 1;\n    }\n  }\n  // Second phase: check for an ancestor with the same score\n  const insertionAncestor = getInsertionAncestor(matcher);\n  if (insertionAncestor) {\n    upper = matchers.lastIndexOf(insertionAncestor, upper - 1);\n    if (process.env.NODE_ENV !== 'production' && upper < 0) {\n      // This should never happen\n      warn(`Finding ancestor route \"${insertionAncestor.record.path}\" failed for \"${matcher.record.path}\"`);\n    }\n  }\n  return upper;\n}\nfunction getInsertionAncestor(matcher) {\n  let ancestor = matcher;\n  while (ancestor = ancestor.parent) {\n    if (isMatchable(ancestor) && comparePathParserScore(matcher, ancestor) === 0) {\n      return ancestor;\n    }\n  }\n  return;\n}\n/**\n * Checks if a matcher can be reachable. This means if it's possible to reach it as a route. For example, routes without\n * a component, or name, or redirect, are just used to group other routes.\n * @param matcher\n * @param matcher.record record of the matcher\n * @returns\n */\nfunction isMatchable({\n  record\n}) {\n  return !!(record.name || record.components && Object.keys(record.components).length || record.redirect);\n}\n\n/**\n * Transforms a queryString into a {@link LocationQuery} object. Accept both, a\n * version with the leading `?` and without Should work as URLSearchParams\n\n * @internal\n *\n * @param search - search string to parse\n * @returns a query object\n */\nfunction parseQuery(search) {\n  const query = {};\n  // avoid creating an object with an empty key and empty value\n  // because of split('&')\n  if (search === '' || search === '?') return query;\n  const hasLeadingIM = search[0] === '?';\n  const searchParams = (hasLeadingIM ? search.slice(1) : search).split('&');\n  for (let i = 0; i < searchParams.length; ++i) {\n    // pre decode the + into space\n    const searchParam = searchParams[i].replace(PLUS_RE, ' ');\n    // allow the = character\n    const eqPos = searchParam.indexOf('=');\n    const key = decode(eqPos < 0 ? searchParam : searchParam.slice(0, eqPos));\n    const value = eqPos < 0 ? null : decode(searchParam.slice(eqPos + 1));\n    if (key in query) {\n      // an extra variable for ts types\n      let currentValue = query[key];\n      if (!isArray(currentValue)) {\n        currentValue = query[key] = [currentValue];\n      }\n      currentValue.push(value);\n    } else {\n      query[key] = value;\n    }\n  }\n  return query;\n}\n/**\n * Stringifies a {@link LocationQueryRaw} object. Like `URLSearchParams`, it\n * doesn't prepend a `?`\n *\n * @internal\n *\n * @param query - query object to stringify\n * @returns string version of the query without the leading `?`\n */\nfunction stringifyQuery(query) {\n  let search = '';\n  for (let key in query) {\n    const value = query[key];\n    key = encodeQueryKey(key);\n    if (value == null) {\n      // only null adds the value\n      if (value !== undefined) {\n        search += (search.length ? '&' : '') + key;\n      }\n      continue;\n    }\n    // keep null values\n    const values = isArray(value) ? value.map(v => v && encodeQueryValue(v)) : [value && encodeQueryValue(value)];\n    values.forEach(value => {\n      // skip undefined values in arrays as if they were not present\n      // smaller code than using filter\n      if (value !== undefined) {\n        // only append & with non-empty search\n        search += (search.length ? '&' : '') + key;\n        if (value != null) search += '=' + value;\n      }\n    });\n  }\n  return search;\n}\n/**\n * Transforms a {@link LocationQueryRaw} into a {@link LocationQuery} by casting\n * numbers into strings, removing keys with an undefined value and replacing\n * undefined with null in arrays\n *\n * @param query - query object to normalize\n * @returns a normalized query object\n */\nfunction normalizeQuery(query) {\n  const normalizedQuery = {};\n  for (const key in query) {\n    const value = query[key];\n    if (value !== undefined) {\n      normalizedQuery[key] = isArray(value) ? value.map(v => v == null ? null : '' + v) : value == null ? value : '' + value;\n    }\n  }\n  return normalizedQuery;\n}\n\n/**\n * RouteRecord being rendered by the closest ancestor Router View. Used for\n * `onBeforeRouteUpdate` and `onBeforeRouteLeave`. rvlm stands for Router View\n * Location Matched\n *\n * @internal\n */\nconst matchedRouteKey = Symbol(process.env.NODE_ENV !== 'production' ? 'router view location matched' : '');\n/**\n * Allows overriding the router view depth to control which component in\n * `matched` is rendered. rvd stands for Router View Depth\n *\n * @internal\n */\nconst viewDepthKey = Symbol(process.env.NODE_ENV !== 'production' ? 'router view depth' : '');\n/**\n * Allows overriding the router instance returned by `useRouter` in tests. r\n * stands for router\n *\n * @internal\n */\nconst routerKey = Symbol(process.env.NODE_ENV !== 'production' ? 'router' : '');\n/**\n * Allows overriding the current route returned by `useRoute` in tests. rl\n * stands for route location\n *\n * @internal\n */\nconst routeLocationKey = Symbol(process.env.NODE_ENV !== 'production' ? 'route location' : '');\n/**\n * Allows overriding the current route used by router-view. Internally this is\n * used when the `route` prop is passed.\n *\n * @internal\n */\nconst routerViewLocationKey = Symbol(process.env.NODE_ENV !== 'production' ? 'router view location' : '');\n\n/**\n * Create a list of callbacks that can be reset. Used to create before and after navigation guards list\n */\nfunction useCallbacks() {\n  let handlers = [];\n  function add(handler) {\n    handlers.push(handler);\n    return () => {\n      const i = handlers.indexOf(handler);\n      if (i > -1) handlers.splice(i, 1);\n    };\n  }\n  function reset() {\n    handlers = [];\n  }\n  return {\n    add,\n    list: () => handlers.slice(),\n    reset\n  };\n}\nfunction registerGuard(record, name, guard) {\n  const removeFromList = () => {\n    record[name].delete(guard);\n  };\n  onUnmounted(removeFromList);\n  onDeactivated(removeFromList);\n  onActivated(() => {\n    record[name].add(guard);\n  });\n  record[name].add(guard);\n}\n/**\n * Add a navigation guard that triggers whenever the component for the current\n * location is about to be left. Similar to {@link beforeRouteLeave} but can be\n * used in any component. The guard is removed when the component is unmounted.\n *\n * @param leaveGuard - {@link NavigationGuard}\n */\nfunction onBeforeRouteLeave(leaveGuard) {\n  if (process.env.NODE_ENV !== 'production' && !getCurrentInstance()) {\n    warn('getCurrentInstance() returned null. onBeforeRouteLeave() must be called at the top of a setup function');\n    return;\n  }\n  const activeRecord = inject(matchedRouteKey,\n  // to avoid warning\n  {}).value;\n  if (!activeRecord) {\n    process.env.NODE_ENV !== 'production' && warn('No active route record was found when calling `onBeforeRouteLeave()`. Make sure you call this function inside a component child of <router-view>. Maybe you called it inside of App.vue?');\n    return;\n  }\n  registerGuard(activeRecord, 'leaveGuards', leaveGuard);\n}\n/**\n * Add a navigation guard that triggers whenever the current location is about\n * to be updated. Similar to {@link beforeRouteUpdate} but can be used in any\n * component. The guard is removed when the component is unmounted.\n *\n * @param updateGuard - {@link NavigationGuard}\n */\nfunction onBeforeRouteUpdate(updateGuard) {\n  if (process.env.NODE_ENV !== 'production' && !getCurrentInstance()) {\n    warn('getCurrentInstance() returned null. onBeforeRouteUpdate() must be called at the top of a setup function');\n    return;\n  }\n  const activeRecord = inject(matchedRouteKey,\n  // to avoid warning\n  {}).value;\n  if (!activeRecord) {\n    process.env.NODE_ENV !== 'production' && warn('No active route record was found when calling `onBeforeRouteUpdate()`. Make sure you call this function inside a component child of <router-view>. Maybe you called it inside of App.vue?');\n    return;\n  }\n  registerGuard(activeRecord, 'updateGuards', updateGuard);\n}\nfunction guardToPromiseFn(guard, to, from, record, name, runWithContext = fn => fn()) {\n  // keep a reference to the enterCallbackArray to prevent pushing callbacks if a new navigation took place\n  const enterCallbackArray = record && (\n  // name is defined if record is because of the function overload\n  record.enterCallbacks[name] = record.enterCallbacks[name] || []);\n  return () => new Promise((resolve, reject) => {\n    const next = valid => {\n      if (valid === false) {\n        reject(createRouterError(4 /* ErrorTypes.NAVIGATION_ABORTED */, {\n          from,\n          to\n        }));\n      } else if (valid instanceof Error) {\n        reject(valid);\n      } else if (isRouteLocation(valid)) {\n        reject(createRouterError(2 /* ErrorTypes.NAVIGATION_GUARD_REDIRECT */, {\n          from: to,\n          to: valid\n        }));\n      } else {\n        if (enterCallbackArray &&\n        // since enterCallbackArray is truthy, both record and name also are\n        record.enterCallbacks[name] === enterCallbackArray && typeof valid === 'function') {\n          enterCallbackArray.push(valid);\n        }\n        resolve();\n      }\n    };\n    // wrapping with Promise.resolve allows it to work with both async and sync guards\n    const guardReturn = runWithContext(() => guard.call(record && record.instances[name], to, from, process.env.NODE_ENV !== 'production' ? canOnlyBeCalledOnce(next, to, from) : next));\n    let guardCall = Promise.resolve(guardReturn);\n    if (guard.length < 3) guardCall = guardCall.then(next);\n    if (process.env.NODE_ENV !== 'production' && guard.length > 2) {\n      const message = `The \"next\" callback was never called inside of ${guard.name ? '\"' + guard.name + '\"' : ''}:\\n${guard.toString()}\\n. If you are returning a value instead of calling \"next\", make sure to remove the \"next\" parameter from your function.`;\n      if (typeof guardReturn === 'object' && 'then' in guardReturn) {\n        guardCall = guardCall.then(resolvedValue => {\n          // @ts-expect-error: _called is added at canOnlyBeCalledOnce\n          if (!next._called) {\n            warn(message);\n            return Promise.reject(new Error('Invalid navigation guard'));\n          }\n          return resolvedValue;\n        });\n      } else if (guardReturn !== undefined) {\n        // @ts-expect-error: _called is added at canOnlyBeCalledOnce\n        if (!next._called) {\n          warn(message);\n          reject(new Error('Invalid navigation guard'));\n          return;\n        }\n      }\n    }\n    guardCall.catch(err => reject(err));\n  });\n}\nfunction canOnlyBeCalledOnce(next, to, from) {\n  let called = 0;\n  return function () {\n    if (called++ === 1) warn(`The \"next\" callback was called more than once in one navigation guard when going from \"${from.fullPath}\" to \"${to.fullPath}\". It should be called exactly one time in each navigation guard. This will fail in production.`);\n    // @ts-expect-error: we put it in the original one because it's easier to check\n    next._called = true;\n    if (called === 1) next.apply(null, arguments);\n  };\n}\nfunction extractComponentsGuards(matched, guardType, to, from, runWithContext = fn => fn()) {\n  const guards = [];\n  for (const record of matched) {\n    if (process.env.NODE_ENV !== 'production' && !record.components && !record.children.length) {\n      warn(`Record with path \"${record.path}\" is either missing a \"component(s)\"` + ` or \"children\" property.`);\n    }\n    for (const name in record.components) {\n      let rawComponent = record.components[name];\n      if (process.env.NODE_ENV !== 'production') {\n        if (!rawComponent || typeof rawComponent !== 'object' && typeof rawComponent !== 'function') {\n          warn(`Component \"${name}\" in record with path \"${record.path}\" is not` + ` a valid component. Received \"${String(rawComponent)}\".`);\n          // throw to ensure we stop here but warn to ensure the message isn't\n          // missed by the user\n          throw new Error('Invalid route component');\n        } else if ('then' in rawComponent) {\n          // warn if user wrote import('/component.vue') instead of () =>\n          // import('./component.vue')\n          warn(`Component \"${name}\" in record with path \"${record.path}\" is a ` + `Promise instead of a function that returns a Promise. Did you ` + `write \"import('./MyPage.vue')\" instead of ` + `\"() => import('./MyPage.vue')\" ? This will break in ` + `production if not fixed.`);\n          const promise = rawComponent;\n          rawComponent = () => promise;\n        } else if (rawComponent.__asyncLoader &&\n        // warn only once per component\n        !rawComponent.__warnedDefineAsync) {\n          rawComponent.__warnedDefineAsync = true;\n          warn(`Component \"${name}\" in record with path \"${record.path}\" is defined ` + `using \"defineAsyncComponent()\". ` + `Write \"() => import('./MyPage.vue')\" instead of ` + `\"defineAsyncComponent(() => import('./MyPage.vue'))\".`);\n        }\n      }\n      // skip update and leave guards if the route component is not mounted\n      if (guardType !== 'beforeRouteEnter' && !record.instances[name]) continue;\n      if (isRouteComponent(rawComponent)) {\n        // __vccOpts is added by vue-class-component and contain the regular options\n        const options = rawComponent.__vccOpts || rawComponent;\n        const guard = options[guardType];\n        guard && guards.push(guardToPromiseFn(guard, to, from, record, name, runWithContext));\n      } else {\n        // start requesting the chunk already\n        let componentPromise = rawComponent();\n        if (process.env.NODE_ENV !== 'production' && !('catch' in componentPromise)) {\n          warn(`Component \"${name}\" in record with path \"${record.path}\" is a function that does not return a Promise. If you were passing a functional component, make sure to add a \"displayName\" to the component. This will break in production if not fixed.`);\n          componentPromise = Promise.resolve(componentPromise);\n        }\n        guards.push(() => componentPromise.then(resolved => {\n          if (!resolved) throw new Error(`Couldn't resolve component \"${name}\" at \"${record.path}\"`);\n          const resolvedComponent = isESModule(resolved) ? resolved.default : resolved;\n          // keep the resolved module for plugins like data loaders\n          record.mods[name] = resolved;\n          // replace the function with the resolved component\n          // cannot be null or undefined because we went into the for loop\n          record.components[name] = resolvedComponent;\n          // __vccOpts is added by vue-class-component and contain the regular options\n          const options = resolvedComponent.__vccOpts || resolvedComponent;\n          const guard = options[guardType];\n          return guard && guardToPromiseFn(guard, to, from, record, name, runWithContext)();\n        }));\n      }\n    }\n  }\n  return guards;\n}\n/**\n * Ensures a route is loaded, so it can be passed as o prop to `<RouterView>`.\n *\n * @param route - resolved route to load\n */\nfunction loadRouteLocation(route) {\n  return route.matched.every(record => record.redirect) ? Promise.reject(new Error('Cannot load a route that redirects.')) : Promise.all(route.matched.map(record => record.components && Promise.all(Object.keys(record.components).reduce((promises, name) => {\n    const rawComponent = record.components[name];\n    if (typeof rawComponent === 'function' && !('displayName' in rawComponent)) {\n      promises.push(rawComponent().then(resolved => {\n        if (!resolved) return Promise.reject(new Error(`Couldn't resolve component \"${name}\" at \"${record.path}\". Ensure you passed a function that returns a promise.`));\n        const resolvedComponent = isESModule(resolved) ? resolved.default : resolved;\n        // keep the resolved module for plugins like data loaders\n        record.mods[name] = resolved;\n        // replace the function with the resolved component\n        // cannot be null or undefined because we went into the for loop\n        record.components[name] = resolvedComponent;\n        return;\n      }));\n    }\n    return promises;\n  }, [])))).then(() => route);\n}\n\n// TODO: we could allow currentRoute as a prop to expose `isActive` and\n// `isExactActive` behavior should go through an RFC\n/**\n * Returns the internal behavior of a {@link RouterLink} without the rendering part.\n *\n * @param props - a `to` location and an optional `replace` flag\n */\nfunction useLink(props) {\n  const router = inject(routerKey);\n  const currentRoute = inject(routeLocationKey);\n  let hasPrevious = false;\n  let previousTo = null;\n  const route = computed(() => {\n    const to = unref(props.to);\n    if (process.env.NODE_ENV !== 'production' && (!hasPrevious || to !== previousTo)) {\n      if (!isRouteLocation(to)) {\n        if (hasPrevious) {\n          warn(`Invalid value for prop \"to\" in useLink()\\n- to:`, to, `\\n- previous to:`, previousTo, `\\n- props:`, props);\n        } else {\n          warn(`Invalid value for prop \"to\" in useLink()\\n- to:`, to, `\\n- props:`, props);\n        }\n      }\n      previousTo = to;\n      hasPrevious = true;\n    }\n    return router.resolve(to);\n  });\n  const activeRecordIndex = computed(() => {\n    const {\n      matched\n    } = route.value;\n    const {\n      length\n    } = matched;\n    const routeMatched = matched[length - 1];\n    const currentMatched = currentRoute.matched;\n    if (!routeMatched || !currentMatched.length) return -1;\n    const index = currentMatched.findIndex(isSameRouteRecord.bind(null, routeMatched));\n    if (index > -1) return index;\n    // possible parent record\n    const parentRecordPath = getOriginalPath(matched[length - 2]);\n    return (\n      // we are dealing with nested routes\n      length > 1 &&\n      // if the parent and matched route have the same path, this link is\n      // referring to the empty child. Or we currently are on a different\n      // child of the same parent\n      getOriginalPath(routeMatched) === parentRecordPath &&\n      // avoid comparing the child with its parent\n      currentMatched[currentMatched.length - 1].path !== parentRecordPath ? currentMatched.findIndex(isSameRouteRecord.bind(null, matched[length - 2])) : index\n    );\n  });\n  const isActive = computed(() => activeRecordIndex.value > -1 && includesParams(currentRoute.params, route.value.params));\n  const isExactActive = computed(() => activeRecordIndex.value > -1 && activeRecordIndex.value === currentRoute.matched.length - 1 && isSameRouteLocationParams(currentRoute.params, route.value.params));\n  function navigate(e = {}) {\n    if (guardEvent(e)) {\n      const p = router[unref(props.replace) ? 'replace' : 'push'](unref(props.to)\n      // avoid uncaught errors are they are logged anyway\n      ).catch(noop);\n      if (props.viewTransition && typeof document !== 'undefined' && 'startViewTransition' in document) {\n        document.startViewTransition(() => p);\n      }\n      return p;\n    }\n    return Promise.resolve();\n  }\n  // devtools only\n  if ((process.env.NODE_ENV !== 'production' || __VUE_PROD_DEVTOOLS__) && isBrowser) {\n    const instance = getCurrentInstance();\n    if (instance) {\n      const linkContextDevtools = {\n        route: route.value,\n        isActive: isActive.value,\n        isExactActive: isExactActive.value,\n        error: null\n      };\n      // @ts-expect-error: this is internal\n      instance.__vrl_devtools = instance.__vrl_devtools || [];\n      // @ts-expect-error: this is internal\n      instance.__vrl_devtools.push(linkContextDevtools);\n      watchEffect(() => {\n        linkContextDevtools.route = route.value;\n        linkContextDevtools.isActive = isActive.value;\n        linkContextDevtools.isExactActive = isExactActive.value;\n        linkContextDevtools.error = isRouteLocation(unref(props.to)) ? null : 'Invalid \"to\" value';\n      }, {\n        flush: 'post'\n      });\n    }\n  }\n  /**\n   * NOTE: update {@link _RouterLinkI}'s `$slots` type when updating this\n   */\n  return {\n    route,\n    href: computed(() => route.value.href),\n    isActive,\n    isExactActive,\n    navigate\n  };\n}\nfunction preferSingleVNode(vnodes) {\n  return vnodes.length === 1 ? vnodes[0] : vnodes;\n}\nconst RouterLinkImpl = /*#__PURE__*/defineComponent({\n  name: 'RouterLink',\n  compatConfig: {\n    MODE: 3\n  },\n  props: {\n    to: {\n      type: [String, Object],\n      required: true\n    },\n    replace: Boolean,\n    activeClass: String,\n    // inactiveClass: String,\n    exactActiveClass: String,\n    custom: Boolean,\n    ariaCurrentValue: {\n      type: String,\n      default: 'page'\n    },\n    viewTransition: Boolean\n  },\n  useLink,\n  setup(props, {\n    slots\n  }) {\n    const link = reactive(useLink(props));\n    const {\n      options\n    } = inject(routerKey);\n    const elClass = computed(() => ({\n      [getLinkClass(props.activeClass, options.linkActiveClass, 'router-link-active')]: link.isActive,\n      // [getLinkClass(\n      //   props.inactiveClass,\n      //   options.linkInactiveClass,\n      //   'router-link-inactive'\n      // )]: !link.isExactActive,\n      [getLinkClass(props.exactActiveClass, options.linkExactActiveClass, 'router-link-exact-active')]: link.isExactActive\n    }));\n    return () => {\n      const children = slots.default && preferSingleVNode(slots.default(link));\n      return props.custom ? children : h('a', {\n        'aria-current': link.isExactActive ? props.ariaCurrentValue : null,\n        href: link.href,\n        // this would override user added attrs but Vue will still add\n        // the listener, so we end up triggering both\n        onClick: link.navigate,\n        class: elClass.value\n      }, children);\n    };\n  }\n});\n// export the public type for h/tsx inference\n// also to avoid inline import() in generated d.ts files\n/**\n * Component to render a link that triggers a navigation on click.\n */\nconst RouterLink = RouterLinkImpl;\nfunction guardEvent(e) {\n  // don't redirect with control keys\n  if (e.metaKey || e.altKey || e.ctrlKey || e.shiftKey) return;\n  // don't redirect when preventDefault called\n  if (e.defaultPrevented) return;\n  // don't redirect on right click\n  if (e.button !== undefined && e.button !== 0) return;\n  // don't redirect if `target=\"_blank\"`\n  // @ts-expect-error getAttribute does exist\n  if (e.currentTarget && e.currentTarget.getAttribute) {\n    // @ts-expect-error getAttribute exists\n    const target = e.currentTarget.getAttribute('target');\n    if (/\\b_blank\\b/i.test(target)) return;\n  }\n  // this may be a Weex event which doesn't have this method\n  if (e.preventDefault) e.preventDefault();\n  return true;\n}\nfunction includesParams(outer, inner) {\n  for (const key in inner) {\n    const innerValue = inner[key];\n    const outerValue = outer[key];\n    if (typeof innerValue === 'string') {\n      if (innerValue !== outerValue) return false;\n    } else {\n      if (!isArray(outerValue) || outerValue.length !== innerValue.length || innerValue.some((value, i) => value !== outerValue[i])) return false;\n    }\n  }\n  return true;\n}\n/**\n * Get the original path value of a record by following its aliasOf\n * @param record\n */\nfunction getOriginalPath(record) {\n  return record ? record.aliasOf ? record.aliasOf.path : record.path : '';\n}\n/**\n * Utility class to get the active class based on defaults.\n * @param propClass\n * @param globalClass\n * @param defaultClass\n */\nconst getLinkClass = (propClass, globalClass, defaultClass) => propClass != null ? propClass : globalClass != null ? globalClass : defaultClass;\nconst RouterViewImpl = /*#__PURE__*/defineComponent({\n  name: 'RouterView',\n  // #674 we manually inherit them\n  inheritAttrs: false,\n  props: {\n    name: {\n      type: String,\n      default: 'default'\n    },\n    route: Object\n  },\n  // Better compat for @vue/compat users\n  // https://github.com/vuejs/router/issues/1315\n  compatConfig: {\n    MODE: 3\n  },\n  setup(props, {\n    attrs,\n    slots\n  }) {\n    process.env.NODE_ENV !== 'production' && warnDeprecatedUsage();\n    const injectedRoute = inject(routerViewLocationKey);\n    const routeToDisplay = computed(() => props.route || injectedRoute.value);\n    const injectedDepth = inject(viewDepthKey, 0);\n    // The depth changes based on empty components option, which allows passthrough routes e.g. routes with children\n    // that are used to reuse the `path` property\n    const depth = computed(() => {\n      let initialDepth = unref(injectedDepth);\n      const {\n        matched\n      } = routeToDisplay.value;\n      let matchedRoute;\n      while ((matchedRoute = matched[initialDepth]) && !matchedRoute.components) {\n        initialDepth++;\n      }\n      return initialDepth;\n    });\n    const matchedRouteRef = computed(() => routeToDisplay.value.matched[depth.value]);\n    provide(viewDepthKey, computed(() => depth.value + 1));\n    provide(matchedRouteKey, matchedRouteRef);\n    provide(routerViewLocationKey, routeToDisplay);\n    const viewRef = ref();\n    // watch at the same time the component instance, the route record we are\n    // rendering, and the name\n    watch(() => [viewRef.value, matchedRouteRef.value, props.name], ([instance, to, name], [oldInstance, from, oldName]) => {\n      // copy reused instances\n      if (to) {\n        // this will update the instance for new instances as well as reused\n        // instances when navigating to a new route\n        to.instances[name] = instance;\n        // the component instance is reused for a different route or name, so\n        // we copy any saved update or leave guards. With async setup, the\n        // mounting component will mount before the matchedRoute changes,\n        // making instance === oldInstance, so we check if guards have been\n        // added before. This works because we remove guards when\n        // unmounting/deactivating components\n        if (from && from !== to && instance && instance === oldInstance) {\n          if (!to.leaveGuards.size) {\n            to.leaveGuards = from.leaveGuards;\n          }\n          if (!to.updateGuards.size) {\n            to.updateGuards = from.updateGuards;\n          }\n        }\n      }\n      // trigger beforeRouteEnter next callbacks\n      if (instance && to && (\n      // if there is no instance but to and from are the same this might be\n      // the first visit\n      !from || !isSameRouteRecord(to, from) || !oldInstance)) {\n        (to.enterCallbacks[name] || []).forEach(callback => callback(instance));\n      }\n    }, {\n      flush: 'post'\n    });\n    return () => {\n      const route = routeToDisplay.value;\n      // we need the value at the time we render because when we unmount, we\n      // navigated to a different location so the value is different\n      const currentName = props.name;\n      const matchedRoute = matchedRouteRef.value;\n      const ViewComponent = matchedRoute && matchedRoute.components[currentName];\n      if (!ViewComponent) {\n        return normalizeSlot(slots.default, {\n          Component: ViewComponent,\n          route\n        });\n      }\n      // props from route configuration\n      const routePropsOption = matchedRoute.props[currentName];\n      const routeProps = routePropsOption ? routePropsOption === true ? route.params : typeof routePropsOption === 'function' ? routePropsOption(route) : routePropsOption : null;\n      const onVnodeUnmounted = vnode => {\n        // remove the instance reference to prevent leak\n        if (vnode.component.isUnmounted) {\n          matchedRoute.instances[currentName] = null;\n        }\n      };\n      const component = h(ViewComponent, assign({}, routeProps, attrs, {\n        onVnodeUnmounted,\n        ref: viewRef\n      }));\n      if ((process.env.NODE_ENV !== 'production' || __VUE_PROD_DEVTOOLS__) && isBrowser && component.ref) {\n        // TODO: can display if it's an alias, its props\n        const info = {\n          depth: depth.value,\n          name: matchedRoute.name,\n          path: matchedRoute.path,\n          meta: matchedRoute.meta\n        };\n        const internalInstances = isArray(component.ref) ? component.ref.map(r => r.i) : [component.ref.i];\n        internalInstances.forEach(instance => {\n          // @ts-expect-error\n          instance.__vrv_devtools = info;\n        });\n      }\n      return (\n        // pass the vnode to the slot as a prop.\n        // h and <component :is=\"...\"> both accept vnodes\n        normalizeSlot(slots.default, {\n          Component: component,\n          route\n        }) || component\n      );\n    };\n  }\n});\nfunction normalizeSlot(slot, data) {\n  if (!slot) return null;\n  const slotContent = slot(data);\n  return slotContent.length === 1 ? slotContent[0] : slotContent;\n}\n// export the public type for h/tsx inference\n// also to avoid inline import() in generated d.ts files\n/**\n * Component to display the current route the user is at.\n */\nconst RouterView = RouterViewImpl;\n// warn against deprecated usage with <transition> & <keep-alive>\n// due to functional component being no longer eager in Vue 3\nfunction warnDeprecatedUsage() {\n  const instance = getCurrentInstance();\n  const parentName = instance.parent && instance.parent.type.name;\n  const parentSubTreeType = instance.parent && instance.parent.subTree && instance.parent.subTree.type;\n  if (parentName && (parentName === 'KeepAlive' || parentName.includes('Transition')) && typeof parentSubTreeType === 'object' && parentSubTreeType.name === 'RouterView') {\n    const comp = parentName === 'KeepAlive' ? 'keep-alive' : 'transition';\n    warn(`<router-view> can no longer be used directly inside <transition> or <keep-alive>.\\n` + `Use slot props instead:\\n\\n` + `<router-view v-slot=\"{ Component }\">\\n` + `  <${comp}>\\n` + `    <component :is=\"Component\" />\\n` + `  </${comp}>\\n` + `</router-view>`);\n  }\n}\n\n/**\n * Copies a route location and removes any problematic properties that cannot be shown in devtools (e.g. Vue instances).\n *\n * @param routeLocation - routeLocation to format\n * @param tooltip - optional tooltip\n * @returns a copy of the routeLocation\n */\nfunction formatRouteLocation(routeLocation, tooltip) {\n  const copy = assign({}, routeLocation, {\n    // remove variables that can contain vue instances\n    matched: routeLocation.matched.map(matched => omit(matched, ['instances', 'children', 'aliasOf']))\n  });\n  return {\n    _custom: {\n      type: null,\n      readOnly: true,\n      display: routeLocation.fullPath,\n      tooltip,\n      value: copy\n    }\n  };\n}\nfunction formatDisplay(display) {\n  return {\n    _custom: {\n      display\n    }\n  };\n}\n// to support multiple router instances\nlet routerId = 0;\nfunction addDevtools(app, router, matcher) {\n  // Take over router.beforeEach and afterEach\n  // make sure we are not registering the devtool twice\n  if (router.__hasDevtools) return;\n  router.__hasDevtools = true;\n  // increment to support multiple router instances\n  const id = routerId++;\n  setupDevtoolsPlugin({\n    id: 'org.vuejs.router' + (id ? '.' + id : ''),\n    label: 'Vue Router',\n    packageName: 'vue-router',\n    homepage: 'https://router.vuejs.org',\n    logo: 'https://router.vuejs.org/logo.png',\n    componentStateTypes: ['Routing'],\n    app\n  }, api => {\n    if (typeof api.now !== 'function') {\n      console.warn('[Vue Router]: You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html.');\n    }\n    // display state added by the router\n    api.on.inspectComponent((payload, ctx) => {\n      if (payload.instanceData) {\n        payload.instanceData.state.push({\n          type: 'Routing',\n          key: '$route',\n          editable: false,\n          value: formatRouteLocation(router.currentRoute.value, 'Current Route')\n        });\n      }\n    });\n    // mark router-link as active and display tags on router views\n    api.on.visitComponentTree(({\n      treeNode: node,\n      componentInstance\n    }) => {\n      if (componentInstance.__vrv_devtools) {\n        const info = componentInstance.__vrv_devtools;\n        node.tags.push({\n          label: (info.name ? `${info.name.toString()}: ` : '') + info.path,\n          textColor: 0,\n          tooltip: 'This component is rendered by &lt;router-view&gt;',\n          backgroundColor: PINK_500\n        });\n      }\n      // if multiple useLink are used\n      if (isArray(componentInstance.__vrl_devtools)) {\n        componentInstance.__devtoolsApi = api;\n        componentInstance.__vrl_devtools.forEach(devtoolsData => {\n          let label = devtoolsData.route.path;\n          let backgroundColor = ORANGE_400;\n          let tooltip = '';\n          let textColor = 0;\n          if (devtoolsData.error) {\n            label = devtoolsData.error;\n            backgroundColor = RED_100;\n            textColor = RED_700;\n          } else if (devtoolsData.isExactActive) {\n            backgroundColor = LIME_500;\n            tooltip = 'This is exactly active';\n          } else if (devtoolsData.isActive) {\n            backgroundColor = BLUE_600;\n            tooltip = 'This link is active';\n          }\n          node.tags.push({\n            label,\n            textColor,\n            tooltip,\n            backgroundColor\n          });\n        });\n      }\n    });\n    watch(router.currentRoute, () => {\n      // refresh active state\n      refreshRoutesView();\n      api.notifyComponentUpdate();\n      api.sendInspectorTree(routerInspectorId);\n      api.sendInspectorState(routerInspectorId);\n    });\n    const navigationsLayerId = 'router:navigations:' + id;\n    api.addTimelineLayer({\n      id: navigationsLayerId,\n      label: `Router${id ? ' ' + id : ''} Navigations`,\n      color: 0x40a8c4\n    });\n    // const errorsLayerId = 'router:errors'\n    // api.addTimelineLayer({\n    //   id: errorsLayerId,\n    //   label: 'Router Errors',\n    //   color: 0xea5455,\n    // })\n    router.onError((error, to) => {\n      api.addTimelineEvent({\n        layerId: navigationsLayerId,\n        event: {\n          title: 'Error during Navigation',\n          subtitle: to.fullPath,\n          logType: 'error',\n          time: api.now(),\n          data: {\n            error\n          },\n          groupId: to.meta.__navigationId\n        }\n      });\n    });\n    // attached to `meta` and used to group events\n    let navigationId = 0;\n    router.beforeEach((to, from) => {\n      const data = {\n        guard: formatDisplay('beforeEach'),\n        from: formatRouteLocation(from, 'Current Location during this navigation'),\n        to: formatRouteLocation(to, 'Target location')\n      };\n      // Used to group navigations together, hide from devtools\n      Object.defineProperty(to.meta, '__navigationId', {\n        value: navigationId++\n      });\n      api.addTimelineEvent({\n        layerId: navigationsLayerId,\n        event: {\n          time: api.now(),\n          title: 'Start of navigation',\n          subtitle: to.fullPath,\n          data,\n          groupId: to.meta.__navigationId\n        }\n      });\n    });\n    router.afterEach((to, from, failure) => {\n      const data = {\n        guard: formatDisplay('afterEach')\n      };\n      if (failure) {\n        data.failure = {\n          _custom: {\n            type: Error,\n            readOnly: true,\n            display: failure ? failure.message : '',\n            tooltip: 'Navigation Failure',\n            value: failure\n          }\n        };\n        data.status = formatDisplay('❌');\n      } else {\n        data.status = formatDisplay('✅');\n      }\n      // we set here to have the right order\n      data.from = formatRouteLocation(from, 'Current Location during this navigation');\n      data.to = formatRouteLocation(to, 'Target location');\n      api.addTimelineEvent({\n        layerId: navigationsLayerId,\n        event: {\n          title: 'End of navigation',\n          subtitle: to.fullPath,\n          time: api.now(),\n          data,\n          logType: failure ? 'warning' : 'default',\n          groupId: to.meta.__navigationId\n        }\n      });\n    });\n    /**\n     * Inspector of Existing routes\n     */\n    const routerInspectorId = 'router-inspector:' + id;\n    api.addInspector({\n      id: routerInspectorId,\n      label: 'Routes' + (id ? ' ' + id : ''),\n      icon: 'book',\n      treeFilterPlaceholder: 'Search routes'\n    });\n    function refreshRoutesView() {\n      // the routes view isn't active\n      if (!activeRoutesPayload) return;\n      const payload = activeRoutesPayload;\n      // children routes will appear as nested\n      let routes = matcher.getRoutes().filter(route => !route.parent ||\n      // these routes have a parent with no component which will not appear in the view\n      // therefore we still need to include them\n      !route.parent.record.components);\n      // reset match state to false\n      routes.forEach(resetMatchStateOnRouteRecord);\n      // apply a match state if there is a payload\n      if (payload.filter) {\n        routes = routes.filter(route =>\n        // save matches state based on the payload\n        isRouteMatching(route, payload.filter.toLowerCase()));\n      }\n      // mark active routes\n      routes.forEach(route => markRouteRecordActive(route, router.currentRoute.value));\n      payload.rootNodes = routes.map(formatRouteRecordForInspector);\n    }\n    let activeRoutesPayload;\n    api.on.getInspectorTree(payload => {\n      activeRoutesPayload = payload;\n      if (payload.app === app && payload.inspectorId === routerInspectorId) {\n        refreshRoutesView();\n      }\n    });\n    /**\n     * Display information about the currently selected route record\n     */\n    api.on.getInspectorState(payload => {\n      if (payload.app === app && payload.inspectorId === routerInspectorId) {\n        const routes = matcher.getRoutes();\n        const route = routes.find(route => route.record.__vd_id === payload.nodeId);\n        if (route) {\n          payload.state = {\n            options: formatRouteRecordMatcherForStateInspector(route)\n          };\n        }\n      }\n    });\n    api.sendInspectorTree(routerInspectorId);\n    api.sendInspectorState(routerInspectorId);\n  });\n}\nfunction modifierForKey(key) {\n  if (key.optional) {\n    return key.repeatable ? '*' : '?';\n  } else {\n    return key.repeatable ? '+' : '';\n  }\n}\nfunction formatRouteRecordMatcherForStateInspector(route) {\n  const {\n    record\n  } = route;\n  const fields = [{\n    editable: false,\n    key: 'path',\n    value: record.path\n  }];\n  if (record.name != null) {\n    fields.push({\n      editable: false,\n      key: 'name',\n      value: record.name\n    });\n  }\n  fields.push({\n    editable: false,\n    key: 'regexp',\n    value: route.re\n  });\n  if (route.keys.length) {\n    fields.push({\n      editable: false,\n      key: 'keys',\n      value: {\n        _custom: {\n          type: null,\n          readOnly: true,\n          display: route.keys.map(key => `${key.name}${modifierForKey(key)}`).join(' '),\n          tooltip: 'Param keys',\n          value: route.keys\n        }\n      }\n    });\n  }\n  if (record.redirect != null) {\n    fields.push({\n      editable: false,\n      key: 'redirect',\n      value: record.redirect\n    });\n  }\n  if (route.alias.length) {\n    fields.push({\n      editable: false,\n      key: 'aliases',\n      value: route.alias.map(alias => alias.record.path)\n    });\n  }\n  if (Object.keys(route.record.meta).length) {\n    fields.push({\n      editable: false,\n      key: 'meta',\n      value: route.record.meta\n    });\n  }\n  fields.push({\n    key: 'score',\n    editable: false,\n    value: {\n      _custom: {\n        type: null,\n        readOnly: true,\n        display: route.score.map(score => score.join(', ')).join(' | '),\n        tooltip: 'Score used to sort routes',\n        value: route.score\n      }\n    }\n  });\n  return fields;\n}\n/**\n * Extracted from tailwind palette\n */\nconst PINK_500 = 0xec4899;\nconst BLUE_600 = 0x2563eb;\nconst LIME_500 = 0x84cc16;\nconst CYAN_400 = 0x22d3ee;\nconst ORANGE_400 = 0xfb923c;\n// const GRAY_100 = 0xf4f4f5\nconst DARK = 0x666666;\nconst RED_100 = 0xfee2e2;\nconst RED_700 = 0xb91c1c;\nfunction formatRouteRecordForInspector(route) {\n  const tags = [];\n  const {\n    record\n  } = route;\n  if (record.name != null) {\n    tags.push({\n      label: String(record.name),\n      textColor: 0,\n      backgroundColor: CYAN_400\n    });\n  }\n  if (record.aliasOf) {\n    tags.push({\n      label: 'alias',\n      textColor: 0,\n      backgroundColor: ORANGE_400\n    });\n  }\n  if (route.__vd_match) {\n    tags.push({\n      label: 'matches',\n      textColor: 0,\n      backgroundColor: PINK_500\n    });\n  }\n  if (route.__vd_exactActive) {\n    tags.push({\n      label: 'exact',\n      textColor: 0,\n      backgroundColor: LIME_500\n    });\n  }\n  if (route.__vd_active) {\n    tags.push({\n      label: 'active',\n      textColor: 0,\n      backgroundColor: BLUE_600\n    });\n  }\n  if (record.redirect) {\n    tags.push({\n      label: typeof record.redirect === 'string' ? `redirect: ${record.redirect}` : 'redirects',\n      textColor: 0xffffff,\n      backgroundColor: DARK\n    });\n  }\n  // add an id to be able to select it. Using the `path` is not possible because\n  // empty path children would collide with their parents\n  let id = record.__vd_id;\n  if (id == null) {\n    id = String(routeRecordId++);\n    record.__vd_id = id;\n  }\n  return {\n    id,\n    label: record.path,\n    tags,\n    children: route.children.map(formatRouteRecordForInspector)\n  };\n}\n//  incremental id for route records and inspector state\nlet routeRecordId = 0;\nconst EXTRACT_REGEXP_RE = /^\\/(.*)\\/([a-z]*)$/;\nfunction markRouteRecordActive(route, currentRoute) {\n  // no route will be active if matched is empty\n  // reset the matching state\n  const isExactActive = currentRoute.matched.length && isSameRouteRecord(currentRoute.matched[currentRoute.matched.length - 1], route.record);\n  route.__vd_exactActive = route.__vd_active = isExactActive;\n  if (!isExactActive) {\n    route.__vd_active = currentRoute.matched.some(match => isSameRouteRecord(match, route.record));\n  }\n  route.children.forEach(childRoute => markRouteRecordActive(childRoute, currentRoute));\n}\nfunction resetMatchStateOnRouteRecord(route) {\n  route.__vd_match = false;\n  route.children.forEach(resetMatchStateOnRouteRecord);\n}\nfunction isRouteMatching(route, filter) {\n  const found = String(route.re).match(EXTRACT_REGEXP_RE);\n  route.__vd_match = false;\n  if (!found || found.length < 3) {\n    return false;\n  }\n  // use a regexp without $ at the end to match nested routes better\n  const nonEndingRE = new RegExp(found[1].replace(/\\$$/, ''), found[2]);\n  if (nonEndingRE.test(filter)) {\n    // mark children as matches\n    route.children.forEach(child => isRouteMatching(child, filter));\n    // exception case: `/`\n    if (route.record.path !== '/' || filter === '/') {\n      route.__vd_match = route.re.test(filter);\n      return true;\n    }\n    // hide the / route\n    return false;\n  }\n  const path = route.record.path.toLowerCase();\n  const decodedPath = decode(path);\n  // also allow partial matching on the path\n  if (!filter.startsWith('/') && (decodedPath.includes(filter) || path.includes(filter))) return true;\n  if (decodedPath.startsWith(filter) || path.startsWith(filter)) return true;\n  if (route.record.name && String(route.record.name).includes(filter)) return true;\n  return route.children.some(child => isRouteMatching(child, filter));\n}\nfunction omit(obj, keys) {\n  const ret = {};\n  for (const key in obj) {\n    if (!keys.includes(key)) {\n      // @ts-expect-error\n      ret[key] = obj[key];\n    }\n  }\n  return ret;\n}\n\n/**\n * Creates a Router instance that can be used by a Vue app.\n *\n * @param options - {@link RouterOptions}\n */\nfunction createRouter(options) {\n  const matcher = createRouterMatcher(options.routes, options);\n  const parseQuery$1 = options.parseQuery || parseQuery;\n  const stringifyQuery$1 = options.stringifyQuery || stringifyQuery;\n  const routerHistory = options.history;\n  if (process.env.NODE_ENV !== 'production' && !routerHistory) throw new Error('Provide the \"history\" option when calling \"createRouter()\":' + ' https://router.vuejs.org/api/interfaces/RouterOptions.html#history');\n  const beforeGuards = useCallbacks();\n  const beforeResolveGuards = useCallbacks();\n  const afterGuards = useCallbacks();\n  const currentRoute = shallowRef(START_LOCATION_NORMALIZED);\n  let pendingLocation = START_LOCATION_NORMALIZED;\n  // leave the scrollRestoration if no scrollBehavior is provided\n  if (isBrowser && options.scrollBehavior && 'scrollRestoration' in history) {\n    history.scrollRestoration = 'manual';\n  }\n  const normalizeParams = applyToParams.bind(null, paramValue => '' + paramValue);\n  const encodeParams = applyToParams.bind(null, encodeParam);\n  const decodeParams =\n  // @ts-expect-error: intentionally avoid the type check\n  applyToParams.bind(null, decode);\n  function addRoute(parentOrRoute, route) {\n    let parent;\n    let record;\n    if (isRouteName(parentOrRoute)) {\n      parent = matcher.getRecordMatcher(parentOrRoute);\n      if (process.env.NODE_ENV !== 'production' && !parent) {\n        warn(`Parent route \"${String(parentOrRoute)}\" not found when adding child route`, route);\n      }\n      record = route;\n    } else {\n      record = parentOrRoute;\n    }\n    return matcher.addRoute(record, parent);\n  }\n  function removeRoute(name) {\n    const recordMatcher = matcher.getRecordMatcher(name);\n    if (recordMatcher) {\n      matcher.removeRoute(recordMatcher);\n    } else if (process.env.NODE_ENV !== 'production') {\n      warn(`Cannot remove non-existent route \"${String(name)}\"`);\n    }\n  }\n  function getRoutes() {\n    return matcher.getRoutes().map(routeMatcher => routeMatcher.record);\n  }\n  function hasRoute(name) {\n    return !!matcher.getRecordMatcher(name);\n  }\n  function resolve(rawLocation, currentLocation) {\n    // const resolve: Router['resolve'] = (rawLocation: RouteLocationRaw, currentLocation) => {\n    // const objectLocation = routerLocationAsObject(rawLocation)\n    // we create a copy to modify it later\n    currentLocation = assign({}, currentLocation || currentRoute.value);\n    if (typeof rawLocation === 'string') {\n      const locationNormalized = parseURL(parseQuery$1, rawLocation, currentLocation.path);\n      const matchedRoute = matcher.resolve({\n        path: locationNormalized.path\n      }, currentLocation);\n      const href = routerHistory.createHref(locationNormalized.fullPath);\n      if (process.env.NODE_ENV !== 'production') {\n        if (href.startsWith('//')) warn(`Location \"${rawLocation}\" resolved to \"${href}\". A resolved location cannot start with multiple slashes.`);else if (!matchedRoute.matched.length) {\n          warn(`No match found for location with path \"${rawLocation}\"`);\n        }\n      }\n      // locationNormalized is always a new object\n      return assign(locationNormalized, matchedRoute, {\n        params: decodeParams(matchedRoute.params),\n        hash: decode(locationNormalized.hash),\n        redirectedFrom: undefined,\n        href\n      });\n    }\n    if (process.env.NODE_ENV !== 'production' && !isRouteLocation(rawLocation)) {\n      warn(`router.resolve() was passed an invalid location. This will fail in production.\\n- Location:`, rawLocation);\n      return resolve({});\n    }\n    let matcherLocation;\n    // path could be relative in object as well\n    if (rawLocation.path != null) {\n      if (process.env.NODE_ENV !== 'production' && 'params' in rawLocation && !('name' in rawLocation) &&\n      // @ts-expect-error: the type is never\n      Object.keys(rawLocation.params).length) {\n        warn(`Path \"${rawLocation.path}\" was passed with params but they will be ignored. Use a named route alongside params instead.`);\n      }\n      matcherLocation = assign({}, rawLocation, {\n        path: parseURL(parseQuery$1, rawLocation.path, currentLocation.path).path\n      });\n    } else {\n      // remove any nullish param\n      const targetParams = assign({}, rawLocation.params);\n      for (const key in targetParams) {\n        if (targetParams[key] == null) {\n          delete targetParams[key];\n        }\n      }\n      // pass encoded values to the matcher, so it can produce encoded path and fullPath\n      matcherLocation = assign({}, rawLocation, {\n        params: encodeParams(targetParams)\n      });\n      // current location params are decoded, we need to encode them in case the\n      // matcher merges the params\n      currentLocation.params = encodeParams(currentLocation.params);\n    }\n    const matchedRoute = matcher.resolve(matcherLocation, currentLocation);\n    const hash = rawLocation.hash || '';\n    if (process.env.NODE_ENV !== 'production' && hash && !hash.startsWith('#')) {\n      warn(`A \\`hash\\` should always start with the character \"#\". Replace \"${hash}\" with \"#${hash}\".`);\n    }\n    // the matcher might have merged current location params, so\n    // we need to run the decoding again\n    matchedRoute.params = normalizeParams(decodeParams(matchedRoute.params));\n    const fullPath = stringifyURL(stringifyQuery$1, assign({}, rawLocation, {\n      hash: encodeHash(hash),\n      path: matchedRoute.path\n    }));\n    const href = routerHistory.createHref(fullPath);\n    if (process.env.NODE_ENV !== 'production') {\n      if (href.startsWith('//')) {\n        warn(`Location \"${rawLocation}\" resolved to \"${href}\". A resolved location cannot start with multiple slashes.`);\n      } else if (!matchedRoute.matched.length) {\n        warn(`No match found for location with path \"${rawLocation.path != null ? rawLocation.path : rawLocation}\"`);\n      }\n    }\n    return assign({\n      fullPath,\n      // keep the hash encoded so fullPath is effectively path + encodedQuery +\n      // hash\n      hash,\n      query:\n      // if the user is using a custom query lib like qs, we might have\n      // nested objects, so we keep the query as is, meaning it can contain\n      // numbers at `$route.query`, but at the point, the user will have to\n      // use their own type anyway.\n      // https://github.com/vuejs/router/issues/328#issuecomment-649481567\n      stringifyQuery$1 === stringifyQuery ? normalizeQuery(rawLocation.query) : rawLocation.query || {}\n    }, matchedRoute, {\n      redirectedFrom: undefined,\n      href\n    });\n  }\n  function locationAsObject(to) {\n    return typeof to === 'string' ? parseURL(parseQuery$1, to, currentRoute.value.path) : assign({}, to);\n  }\n  function checkCanceledNavigation(to, from) {\n    if (pendingLocation !== to) {\n      return createRouterError(8 /* ErrorTypes.NAVIGATION_CANCELLED */, {\n        from,\n        to\n      });\n    }\n  }\n  function push(to) {\n    return pushWithRedirect(to);\n  }\n  function replace(to) {\n    return push(assign(locationAsObject(to), {\n      replace: true\n    }));\n  }\n  function handleRedirectRecord(to) {\n    const lastMatched = to.matched[to.matched.length - 1];\n    if (lastMatched && lastMatched.redirect) {\n      const {\n        redirect\n      } = lastMatched;\n      let newTargetLocation = typeof redirect === 'function' ? redirect(to) : redirect;\n      if (typeof newTargetLocation === 'string') {\n        newTargetLocation = newTargetLocation.includes('?') || newTargetLocation.includes('#') ? newTargetLocation = locationAsObject(newTargetLocation) :\n        // force empty params\n        {\n          path: newTargetLocation\n        };\n        // @ts-expect-error: force empty params when a string is passed to let\n        // the router parse them again\n        newTargetLocation.params = {};\n      }\n      if (process.env.NODE_ENV !== 'production' && newTargetLocation.path == null && !('name' in newTargetLocation)) {\n        warn(`Invalid redirect found:\\n${JSON.stringify(newTargetLocation, null, 2)}\\n when navigating to \"${to.fullPath}\". A redirect must contain a name or path. This will break in production.`);\n        throw new Error('Invalid redirect');\n      }\n      return assign({\n        query: to.query,\n        hash: to.hash,\n        // avoid transferring params if the redirect has a path\n        params: newTargetLocation.path != null ? {} : to.params\n      }, newTargetLocation);\n    }\n  }\n  function pushWithRedirect(to, redirectedFrom) {\n    const targetLocation = pendingLocation = resolve(to);\n    const from = currentRoute.value;\n    const data = to.state;\n    const force = to.force;\n    // to could be a string where `replace` is a function\n    const replace = to.replace === true;\n    const shouldRedirect = handleRedirectRecord(targetLocation);\n    if (shouldRedirect) return pushWithRedirect(assign(locationAsObject(shouldRedirect), {\n      state: typeof shouldRedirect === 'object' ? assign({}, data, shouldRedirect.state) : data,\n      force,\n      replace\n    }),\n    // keep original redirectedFrom if it exists\n    redirectedFrom || targetLocation);\n    // if it was a redirect we already called `pushWithRedirect` above\n    const toLocation = targetLocation;\n    toLocation.redirectedFrom = redirectedFrom;\n    let failure;\n    if (!force && isSameRouteLocation(stringifyQuery$1, from, targetLocation)) {\n      failure = createRouterError(16 /* ErrorTypes.NAVIGATION_DUPLICATED */, {\n        to: toLocation,\n        from\n      });\n      // trigger scroll to allow scrolling to the same anchor\n      handleScroll(from, from,\n      // this is a push, the only way for it to be triggered from a\n      // history.listen is with a redirect, which makes it become a push\n      true,\n      // This cannot be the first navigation because the initial location\n      // cannot be manually navigated to\n      false);\n    }\n    return (failure ? Promise.resolve(failure) : navigate(toLocation, from)).catch(error => isNavigationFailure(error) ?\n    // navigation redirects still mark the router as ready\n    isNavigationFailure(error, 2 /* ErrorTypes.NAVIGATION_GUARD_REDIRECT */) ? error : markAsReady(error) // also returns the error\n    :\n    // reject any unknown error\n    triggerError(error, toLocation, from)).then(failure => {\n      if (failure) {\n        if (isNavigationFailure(failure, 2 /* ErrorTypes.NAVIGATION_GUARD_REDIRECT */)) {\n          if (process.env.NODE_ENV !== 'production' &&\n          // we are redirecting to the same location we were already at\n          isSameRouteLocation(stringifyQuery$1, resolve(failure.to), toLocation) &&\n          // and we have done it a couple of times\n          redirectedFrom &&\n          // @ts-expect-error: added only in dev\n          (redirectedFrom._count = redirectedFrom._count ?\n          // @ts-expect-error\n          redirectedFrom._count + 1 : 1) > 30) {\n            warn(`Detected a possibly infinite redirection in a navigation guard when going from \"${from.fullPath}\" to \"${toLocation.fullPath}\". Aborting to avoid a Stack Overflow.\\n Are you always returning a new location within a navigation guard? That would lead to this error. Only return when redirecting or aborting, that should fix this. This might break in production if not fixed.`);\n            return Promise.reject(new Error('Infinite redirect in navigation guard'));\n          }\n          return pushWithRedirect(\n          // keep options\n          assign({\n            // preserve an existing replacement but allow the redirect to override it\n            replace\n          }, locationAsObject(failure.to), {\n            state: typeof failure.to === 'object' ? assign({}, data, failure.to.state) : data,\n            force\n          }),\n          // preserve the original redirectedFrom if any\n          redirectedFrom || toLocation);\n        }\n      } else {\n        // if we fail we don't finalize the navigation\n        failure = finalizeNavigation(toLocation, from, true, replace, data);\n      }\n      triggerAfterEach(toLocation, from, failure);\n      return failure;\n    });\n  }\n  /**\n   * Helper to reject and skip all navigation guards if a new navigation happened\n   * @param to\n   * @param from\n   */\n  function checkCanceledNavigationAndReject(to, from) {\n    const error = checkCanceledNavigation(to, from);\n    return error ? Promise.reject(error) : Promise.resolve();\n  }\n  function runWithContext(fn) {\n    const app = installedApps.values().next().value;\n    // support Vue < 3.3\n    return app && typeof app.runWithContext === 'function' ? app.runWithContext(fn) : fn();\n  }\n  // TODO: refactor the whole before guards by internally using router.beforeEach\n  function navigate(to, from) {\n    let guards;\n    const [leavingRecords, updatingRecords, enteringRecords] = extractChangingRecords(to, from);\n    // all components here have been resolved once because we are leaving\n    guards = extractComponentsGuards(leavingRecords.reverse(), 'beforeRouteLeave', to, from);\n    // leavingRecords is already reversed\n    for (const record of leavingRecords) {\n      record.leaveGuards.forEach(guard => {\n        guards.push(guardToPromiseFn(guard, to, from));\n      });\n    }\n    const canceledNavigationCheck = checkCanceledNavigationAndReject.bind(null, to, from);\n    guards.push(canceledNavigationCheck);\n    // run the queue of per route beforeRouteLeave guards\n    return runGuardQueue(guards).then(() => {\n      // check global guards beforeEach\n      guards = [];\n      for (const guard of beforeGuards.list()) {\n        guards.push(guardToPromiseFn(guard, to, from));\n      }\n      guards.push(canceledNavigationCheck);\n      return runGuardQueue(guards);\n    }).then(() => {\n      // check in components beforeRouteUpdate\n      guards = extractComponentsGuards(updatingRecords, 'beforeRouteUpdate', to, from);\n      for (const record of updatingRecords) {\n        record.updateGuards.forEach(guard => {\n          guards.push(guardToPromiseFn(guard, to, from));\n        });\n      }\n      guards.push(canceledNavigationCheck);\n      // run the queue of per route beforeEnter guards\n      return runGuardQueue(guards);\n    }).then(() => {\n      // check the route beforeEnter\n      guards = [];\n      for (const record of enteringRecords) {\n        // do not trigger beforeEnter on reused views\n        if (record.beforeEnter) {\n          if (isArray(record.beforeEnter)) {\n            for (const beforeEnter of record.beforeEnter) guards.push(guardToPromiseFn(beforeEnter, to, from));\n          } else {\n            guards.push(guardToPromiseFn(record.beforeEnter, to, from));\n          }\n        }\n      }\n      guards.push(canceledNavigationCheck);\n      // run the queue of per route beforeEnter guards\n      return runGuardQueue(guards);\n    }).then(() => {\n      // NOTE: at this point to.matched is normalized and does not contain any () => Promise<Component>\n      // clear existing enterCallbacks, these are added by extractComponentsGuards\n      to.matched.forEach(record => record.enterCallbacks = {});\n      // check in-component beforeRouteEnter\n      guards = extractComponentsGuards(enteringRecords, 'beforeRouteEnter', to, from, runWithContext);\n      guards.push(canceledNavigationCheck);\n      // run the queue of per route beforeEnter guards\n      return runGuardQueue(guards);\n    }).then(() => {\n      // check global guards beforeResolve\n      guards = [];\n      for (const guard of beforeResolveGuards.list()) {\n        guards.push(guardToPromiseFn(guard, to, from));\n      }\n      guards.push(canceledNavigationCheck);\n      return runGuardQueue(guards);\n    })\n    // catch any navigation canceled\n    .catch(err => isNavigationFailure(err, 8 /* ErrorTypes.NAVIGATION_CANCELLED */) ? err : Promise.reject(err));\n  }\n  function triggerAfterEach(to, from, failure) {\n    // navigation is confirmed, call afterGuards\n    // TODO: wrap with error handlers\n    afterGuards.list().forEach(guard => runWithContext(() => guard(to, from, failure)));\n  }\n  /**\n   * - Cleans up any navigation guards\n   * - Changes the url if necessary\n   * - Calls the scrollBehavior\n   */\n  function finalizeNavigation(toLocation, from, isPush, replace, data) {\n    // a more recent navigation took place\n    const error = checkCanceledNavigation(toLocation, from);\n    if (error) return error;\n    // only consider as push if it's not the first navigation\n    const isFirstNavigation = from === START_LOCATION_NORMALIZED;\n    const state = !isBrowser ? {} : history.state;\n    // change URL only if the user did a push/replace and if it's not the initial navigation because\n    // it's just reflecting the url\n    if (isPush) {\n      // on the initial navigation, we want to reuse the scroll position from\n      // history state if it exists\n      if (replace || isFirstNavigation) routerHistory.replace(toLocation.fullPath, assign({\n        scroll: isFirstNavigation && state && state.scroll\n      }, data));else routerHistory.push(toLocation.fullPath, data);\n    }\n    // accept current navigation\n    currentRoute.value = toLocation;\n    handleScroll(toLocation, from, isPush, isFirstNavigation);\n    markAsReady();\n  }\n  let removeHistoryListener;\n  // attach listener to history to trigger navigations\n  function setupListeners() {\n    // avoid setting up listeners twice due to an invalid first navigation\n    if (removeHistoryListener) return;\n    removeHistoryListener = routerHistory.listen((to, _from, info) => {\n      if (!router.listening) return;\n      // cannot be a redirect route because it was in history\n      const toLocation = resolve(to);\n      // due to dynamic routing, and to hash history with manual navigation\n      // (manually changing the url or calling history.hash = '#/somewhere'),\n      // there could be a redirect record in history\n      const shouldRedirect = handleRedirectRecord(toLocation);\n      if (shouldRedirect) {\n        pushWithRedirect(assign(shouldRedirect, {\n          replace: true,\n          force: true\n        }), toLocation).catch(noop);\n        return;\n      }\n      pendingLocation = toLocation;\n      const from = currentRoute.value;\n      // TODO: should be moved to web history?\n      if (isBrowser) {\n        saveScrollPosition(getScrollKey(from.fullPath, info.delta), computeScrollPosition());\n      }\n      navigate(toLocation, from).catch(error => {\n        if (isNavigationFailure(error, 4 /* ErrorTypes.NAVIGATION_ABORTED */ | 8 /* ErrorTypes.NAVIGATION_CANCELLED */)) {\n          return error;\n        }\n        if (isNavigationFailure(error, 2 /* ErrorTypes.NAVIGATION_GUARD_REDIRECT */)) {\n          // Here we could call if (info.delta) routerHistory.go(-info.delta,\n          // false) but this is bug prone as we have no way to wait the\n          // navigation to be finished before calling pushWithRedirect. Using\n          // a setTimeout of 16ms seems to work but there is no guarantee for\n          // it to work on every browser. So instead we do not restore the\n          // history entry and trigger a new navigation as requested by the\n          // navigation guard.\n          // the error is already handled by router.push we just want to avoid\n          // logging the error\n          pushWithRedirect(assign(locationAsObject(error.to), {\n            force: true\n          }), toLocation\n          // avoid an uncaught rejection, let push call triggerError\n          ).then(failure => {\n            // manual change in hash history #916 ending up in the URL not\n            // changing, but it was changed by the manual url change, so we\n            // need to manually change it ourselves\n            if (isNavigationFailure(failure, 4 /* ErrorTypes.NAVIGATION_ABORTED */ | 16 /* ErrorTypes.NAVIGATION_DUPLICATED */) && !info.delta && info.type === NavigationType.pop) {\n              routerHistory.go(-1, false);\n            }\n          }).catch(noop);\n          // avoid the then branch\n          return Promise.reject();\n        }\n        // do not restore history on unknown direction\n        if (info.delta) {\n          routerHistory.go(-info.delta, false);\n        }\n        // unrecognized error, transfer to the global handler\n        return triggerError(error, toLocation, from);\n      }).then(failure => {\n        failure = failure || finalizeNavigation(\n        // after navigation, all matched components are resolved\n        toLocation, from, false);\n        // revert the navigation\n        if (failure) {\n          if (info.delta &&\n          // a new navigation has been triggered, so we do not want to revert, that will change the current history\n          // entry while a different route is displayed\n          !isNavigationFailure(failure, 8 /* ErrorTypes.NAVIGATION_CANCELLED */)) {\n            routerHistory.go(-info.delta, false);\n          } else if (info.type === NavigationType.pop && isNavigationFailure(failure, 4 /* ErrorTypes.NAVIGATION_ABORTED */ | 16 /* ErrorTypes.NAVIGATION_DUPLICATED */)) {\n            // manual change in hash history #916\n            // it's like a push but lacks the information of the direction\n            routerHistory.go(-1, false);\n          }\n        }\n        triggerAfterEach(toLocation, from, failure);\n      })\n      // avoid warnings in the console about uncaught rejections, they are logged by triggerErrors\n      .catch(noop);\n    });\n  }\n  // Initialization and Errors\n  let readyHandlers = useCallbacks();\n  let errorListeners = useCallbacks();\n  let ready;\n  /**\n   * Trigger errorListeners added via onError and throws the error as well\n   *\n   * @param error - error to throw\n   * @param to - location we were navigating to when the error happened\n   * @param from - location we were navigating from when the error happened\n   * @returns the error as a rejected promise\n   */\n  function triggerError(error, to, from) {\n    markAsReady(error);\n    const list = errorListeners.list();\n    if (list.length) {\n      list.forEach(handler => handler(error, to, from));\n    } else {\n      if (process.env.NODE_ENV !== 'production') {\n        warn('uncaught error during route navigation:');\n      }\n      console.error(error);\n    }\n    // reject the error no matter there were error listeners or not\n    return Promise.reject(error);\n  }\n  function isReady() {\n    if (ready && currentRoute.value !== START_LOCATION_NORMALIZED) return Promise.resolve();\n    return new Promise((resolve, reject) => {\n      readyHandlers.add([resolve, reject]);\n    });\n  }\n  function markAsReady(err) {\n    if (!ready) {\n      // still not ready if an error happened\n      ready = !err;\n      setupListeners();\n      readyHandlers.list().forEach(([resolve, reject]) => err ? reject(err) : resolve());\n      readyHandlers.reset();\n    }\n    return err;\n  }\n  // Scroll behavior\n  function handleScroll(to, from, isPush, isFirstNavigation) {\n    const {\n      scrollBehavior\n    } = options;\n    if (!isBrowser || !scrollBehavior) return Promise.resolve();\n    const scrollPosition = !isPush && getSavedScrollPosition(getScrollKey(to.fullPath, 0)) || (isFirstNavigation || !isPush) && history.state && history.state.scroll || null;\n    return nextTick().then(() => scrollBehavior(to, from, scrollPosition)).then(position => position && scrollToPosition(position)).catch(err => triggerError(err, to, from));\n  }\n  const go = delta => routerHistory.go(delta);\n  let started;\n  const installedApps = new Set();\n  const router = {\n    currentRoute,\n    listening: true,\n    addRoute,\n    removeRoute,\n    clearRoutes: matcher.clearRoutes,\n    hasRoute,\n    getRoutes,\n    resolve,\n    options,\n    push,\n    replace,\n    go,\n    back: () => go(-1),\n    forward: () => go(1),\n    beforeEach: beforeGuards.add,\n    beforeResolve: beforeResolveGuards.add,\n    afterEach: afterGuards.add,\n    onError: errorListeners.add,\n    isReady,\n    install(app) {\n      const router = this;\n      app.component('RouterLink', RouterLink);\n      app.component('RouterView', RouterView);\n      app.config.globalProperties.$router = router;\n      Object.defineProperty(app.config.globalProperties, '$route', {\n        enumerable: true,\n        get: () => unref(currentRoute)\n      });\n      // this initial navigation is only necessary on client, on server it doesn't\n      // make sense because it will create an extra unnecessary navigation and could\n      // lead to problems\n      if (isBrowser &&\n      // used for the initial navigation client side to avoid pushing\n      // multiple times when the router is used in multiple apps\n      !started && currentRoute.value === START_LOCATION_NORMALIZED) {\n        // see above\n        started = true;\n        push(routerHistory.location).catch(err => {\n          if (process.env.NODE_ENV !== 'production') warn('Unexpected error when starting the router:', err);\n        });\n      }\n      const reactiveRoute = {};\n      for (const key in START_LOCATION_NORMALIZED) {\n        Object.defineProperty(reactiveRoute, key, {\n          get: () => currentRoute.value[key],\n          enumerable: true\n        });\n      }\n      app.provide(routerKey, router);\n      app.provide(routeLocationKey, shallowReactive(reactiveRoute));\n      app.provide(routerViewLocationKey, currentRoute);\n      const unmountApp = app.unmount;\n      installedApps.add(app);\n      app.unmount = function () {\n        installedApps.delete(app);\n        // the router is not attached to an app anymore\n        if (installedApps.size < 1) {\n          // invalidate the current navigation\n          pendingLocation = START_LOCATION_NORMALIZED;\n          removeHistoryListener && removeHistoryListener();\n          removeHistoryListener = null;\n          currentRoute.value = START_LOCATION_NORMALIZED;\n          started = false;\n          ready = false;\n        }\n        unmountApp();\n      };\n      // TODO: this probably needs to be updated so it can be used by vue-termui\n      if ((process.env.NODE_ENV !== 'production' || __VUE_PROD_DEVTOOLS__) && isBrowser) {\n        addDevtools(app, router, matcher);\n      }\n    }\n  };\n  // TODO: type this as NavigationGuardReturn or similar instead of any\n  function runGuardQueue(guards) {\n    return guards.reduce((promise, guard) => promise.then(() => runWithContext(guard)), Promise.resolve());\n  }\n  return router;\n}\nfunction extractChangingRecords(to, from) {\n  const leavingRecords = [];\n  const updatingRecords = [];\n  const enteringRecords = [];\n  const len = Math.max(from.matched.length, to.matched.length);\n  for (let i = 0; i < len; i++) {\n    const recordFrom = from.matched[i];\n    if (recordFrom) {\n      if (to.matched.find(record => isSameRouteRecord(record, recordFrom))) updatingRecords.push(recordFrom);else leavingRecords.push(recordFrom);\n    }\n    const recordTo = to.matched[i];\n    if (recordTo) {\n      // the type doesn't matter because we are comparing per reference\n      if (!from.matched.find(record => isSameRouteRecord(record, recordTo))) {\n        enteringRecords.push(recordTo);\n      }\n    }\n  }\n  return [leavingRecords, updatingRecords, enteringRecords];\n}\n\n/**\n * Returns the router instance. Equivalent to using `$router` inside\n * templates.\n */\nfunction useRouter() {\n  return inject(routerKey);\n}\n/**\n * Returns the current route location. Equivalent to using `$route` inside\n * templates.\n */\nfunction useRoute(_name) {\n  return inject(routeLocationKey);\n}\nexport { NavigationFailureType, RouterLink, RouterView, START_LOCATION_NORMALIZED as START_LOCATION, createMemoryHistory, createRouter, createRouterMatcher, createWebHashHistory, createWebHistory, isNavigationFailure, loadRouteLocation, matchedRouteKey, onBeforeRouteLeave, onBeforeRouteUpdate, parseQuery, routeLocationKey, routerKey, routerViewLocationKey, stringifyQuery, useLink, useRoute, useRouter, viewDepthKey };", "map": {"version": 3, "names": ["getCurrentInstance", "inject", "onUnmounted", "onDeactivated", "onActivated", "computed", "unref", "watchEffect", "defineComponent", "reactive", "h", "provide", "ref", "watch", "shallowRef", "shallowReactive", "nextTick", "setupDevtoolsPlugin", "<PERSON><PERSON><PERSON><PERSON>", "document", "isRouteComponent", "component", "isESModule", "obj", "__esModule", "Symbol", "toStringTag", "default", "assign", "Object", "applyToParams", "fn", "params", "newParams", "key", "value", "isArray", "map", "noop", "Array", "warn", "msg", "args", "from", "arguments", "slice", "console", "apply", "concat", "HASH_RE", "AMPERSAND_RE", "SLASH_RE", "EQUAL_RE", "IM_RE", "PLUS_RE", "ENC_BRACKET_OPEN_RE", "ENC_BRACKET_CLOSE_RE", "ENC_CARET_RE", "ENC_BACKTICK_RE", "ENC_CURLY_OPEN_RE", "ENC_PIPE_RE", "ENC_CURLY_CLOSE_RE", "ENC_SPACE_RE", "commonEncode", "text", "encodeURI", "replace", "encodeHash", "encodeQueryValue", "encode<PERSON>uery<PERSON>ey", "encodePath", "encodeParam", "decode", "decodeURIComponent", "err", "process", "env", "NODE_ENV", "TRAILING_SLASH_RE", "removeTrailingSlash", "path", "parseURL", "parse<PERSON><PERSON>y", "location", "currentLocation", "query", "searchString", "hash", "hashPos", "indexOf", "searchPos", "length", "resolveRelativePath", "fullPath", "stringifyURL", "stringifyQuery", "stripBase", "pathname", "base", "toLowerCase", "startsWith", "isSameRouteLocation", "a", "b", "aLastIndex", "matched", "bLastIndex", "isSameRouteRecord", "isSameRouteLocationParams", "<PERSON><PERSON><PERSON>", "keys", "isSameRouteLocationParamsValue", "isEquivalentArray", "every", "i", "to", "fromSegments", "split", "toSegments", "lastToSegment", "push", "position", "toPosition", "segment", "join", "START_LOCATION_NORMALIZED", "name", "undefined", "meta", "redirectedFrom", "NavigationType", "NavigationDirection", "START", "normalizeBase", "baseEl", "querySelector", "getAttribute", "BEFORE_HASH_RE", "createHref", "getElementPosition", "el", "offset", "docRect", "documentElement", "getBoundingClientRect", "elRect", "behavior", "left", "top", "computeScrollPosition", "window", "scrollX", "scrollY", "scrollToPosition", "scrollToOptions", "positionEl", "isIdSelector", "getElementById", "foundEl", "style", "scrollTo", "getScrollKey", "delta", "history", "state", "scrollPositions", "Map", "saveScrollPosition", "scrollPosition", "set", "getSavedScrollPosition", "scroll", "get", "delete", "createBaseLocation", "protocol", "host", "createCurrentLocation", "search", "slicePos", "includes", "pathFromHash", "useHistoryListeners", "historyState", "listeners", "teardowns", "pauseState", "popStateHandler", "fromState", "for<PERSON>ach", "listener", "type", "pop", "direction", "forward", "back", "unknown", "pauseListeners", "listen", "callback", "teardown", "index", "splice", "beforeUnloadListener", "replaceState", "destroy", "removeEventListener", "addEventListener", "passive", "buildState", "current", "replaced", "computeScroll", "useHistoryStateNavigation", "changeLocation", "hashIndex", "url", "error", "data", "currentState", "createWebHistory", "historyNavigation", "historyListeners", "go", "triggerListeners", "routerHistory", "bind", "defineProperty", "enumerable", "createMemoryHistory", "queue", "setLocation", "info", "should<PERSON><PERSON>ger", "Math", "max", "min", "createWebHashHistory", "endsWith", "isRouteLocation", "route", "isRouteName", "NavigationFailureSymbol", "NavigationFailureType", "ErrorTypeMessages", "JSON", "stringify", "stringifyRoute", "createRouterError", "Error", "isNavigationFailure", "propertiesToLog", "BASE_PARAM_PATTERN", "BASE_PATH_PARSER_OPTIONS", "sensitive", "strict", "start", "end", "REGEX_CHARS_RE", "tokensToParser", "segments", "extraOptions", "options", "score", "pattern", "segmentScores", "tokenIndex", "token", "subSegmentScore", "repeatable", "optional", "regexp", "re", "RegExp", "message", "subPattern", "parse", "match", "avoidDuplicatedSlash", "param", "compareScoreArray", "diff", "comparePathParserScore", "aScore", "bScore", "comp", "abs", "isLastScoreNegative", "last", "ROOT_TOKEN", "VALID_PARAM_RE", "tokenizePath", "crash", "buffer", "previousState", "tokens", "finalizeSegment", "char", "customRe", "consumeBuffer", "addCharToBuffer", "test", "createRouteRecordMatcher", "record", "parent", "parser", "existingKeys", "Set", "has", "add", "matcher", "children", "alias", "createRouterMatcher", "routes", "globalOptions", "matchers", "matcherMap", "mergeOptions", "getRecordMatcher", "addRoute", "originalRecord", "isRootAdd", "mainNormalizedRecord", "normalizeRouteRecord", "checkChildMissingNameWithEmptyPath", "normalizedRecords", "aliases", "components", "originalMatcher", "normalizedRecord", "parentPath", "connectingSlash", "checkMissingParamsInAbsolutePath", "checkSameParams", "isAliasRecord", "checkSameNameAsAncestor", "removeRoute", "isMatchable", "insertMatcher", "matcherRef", "getRoutes", "findInsertionIndex", "resolve", "invalidParams", "filter", "paramName", "find", "k", "paramsFromLocation", "m", "parentMatcher", "unshift", "mergeMetaFields", "clearRoutes", "clear", "normalized", "redirect", "beforeEnter", "props", "normalizeRecordProps", "instances", "leave<PERSON><PERSON>s", "updateGuards", "enterCallbacks", "propsObject", "reduce", "defaults", "partialOptions", "isSameParam", "String", "ancestor", "lower", "upper", "mid", "sortOrder", "insertionAncestor", "getInsertionAncestor", "lastIndexOf", "hasLeadingIM", "searchParams", "searchParam", "eqPos", "currentValue", "values", "v", "normalizeQuery", "normalizedQuery", "matchedRouteKey", "viewDepthKey", "routerKey", "routeLocationKey", "routerViewLocationKey", "useCallbacks", "handlers", "handler", "reset", "list", "registerGuard", "guard", "removeFromList", "onBeforeRouteLeave", "leave<PERSON><PERSON>", "activeRecord", "onBeforeRouteUpdate", "updateGuard", "guardToPromiseFn", "runWithContext", "enterCallbackArray", "Promise", "reject", "next", "valid", "guardReturn", "call", "canOnlyBeCalledOnce", "guard<PERSON><PERSON>", "then", "toString", "resolvedValue", "_called", "catch", "called", "extractComponentsGuards", "guardType", "guards", "rawComponent", "promise", "__as<PERSON><PERSON><PERSON><PERSON>", "__warnedDefineAsync", "__vccOpts", "componentPromise", "resolved", "resolvedComponent", "mods", "loadRouteLocation", "all", "promises", "useLink", "router", "currentRoute", "has<PERSON>revious", "previousTo", "activeRecordIndex", "routeMatched", "currentMatched", "findIndex", "parentRecordPath", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isActive", "includesParams", "isExactActive", "navigate", "e", "guardEvent", "p", "viewTransition", "startViewTransition", "__VUE_PROD_DEVTOOLS__", "instance", "linkContextDevtools", "__vrl_devtools", "flush", "href", "preferSingleVNode", "vnodes", "RouterLinkImpl", "compatConfig", "MODE", "required", "Boolean", "activeClass", "exactActiveClass", "custom", "ariaCurrentValue", "setup", "slots", "link", "elClass", "getLinkClass", "linkActiveClass", "linkExactActiveClass", "onClick", "class", "RouterLink", "metaKey", "altKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "defaultPrevented", "button", "currentTarget", "target", "preventDefault", "outer", "inner", "innerValue", "outerValue", "some", "propClass", "globalClass", "defaultClass", "RouterViewImpl", "inheritAttrs", "attrs", "warnDeprecatedUsage", "injectedRoute", "routeToDisplay", "<PERSON><PERSON><PERSON><PERSON>", "depth", "initialDepth", "matchedRoute", "matchedRouteRef", "viewRef", "oldInstance", "old<PERSON>ame", "size", "currentName", "ViewComponent", "normalizeSlot", "Component", "routePropsOption", "routeProps", "onVnodeUnmounted", "vnode", "isUnmounted", "internalInstances", "r", "__vrv_devtools", "slot", "slotContent", "RouterView", "parentName", "parentSubTreeType", "subTree", "formatRouteLocation", "routeLocation", "tooltip", "copy", "omit", "_custom", "readOnly", "display", "formatDisplay", "routerId", "addDevtools", "app", "__hasDevtools", "id", "label", "packageName", "homepage", "logo", "componentStateTypes", "api", "now", "on", "inspectComponent", "payload", "ctx", "instanceData", "editable", "visitComponentTree", "treeNode", "node", "componentInstance", "tags", "textColor", "backgroundColor", "PINK_500", "__dev<PERSON><PERSON><PERSON><PERSON>", "devtoolsData", "ORANGE_400", "RED_100", "RED_700", "LIME_500", "BLUE_600", "refreshRoutesView", "notifyComponentUpdate", "sendInspectorTree", "routerInspectorId", "sendInspectorState", "navigationsLayerId", "addTimelineLayer", "color", "onError", "addTimelineEvent", "layerId", "event", "title", "subtitle", "logType", "time", "groupId", "__navigationId", "navigationId", "beforeEach", "after<PERSON>ach", "failure", "status", "addInspector", "icon", "treeFilterPlaceholder", "activeRoutesPayload", "resetMatchStateOnRouteRecord", "isRouteMatching", "markRouteRecordActive", "rootNodes", "formatRouteRecordForInspector", "getInspectorTree", "inspectorId", "getInspectorState", "__vd_id", "nodeId", "formatRouteRecordMatcherForStateInspector", "modifierForKey", "fields", "CYAN_400", "DARK", "__vd_match", "__vd_exactActive", "__vd_active", "routeRecordId", "EXTRACT_REGEXP_RE", "childRoute", "found", "nonEndingRE", "child", "decodedPath", "ret", "createRouter", "parseQuery$1", "stringifyQuery$1", "<PERSON><PERSON><PERSON><PERSON>", "beforeResolveGuards", "afterGuards", "pendingLocation", "scroll<PERSON>eh<PERSON>or", "scrollRestoration", "normalizeParams", "paramValue", "encodeParams", "decodeParams", "parentOrRoute", "recordMatcher", "routeMatcher", "hasRoute", "rawLocation", "locationNormalized", "matcherLocation", "targetParams", "locationAsObject", "checkCanceledNavigation", "pushWithRedirect", "handleRedirectRecord", "lastMatched", "newTargetLocation", "targetLocation", "force", "shouldRedirect", "toLocation", "handleScroll", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "triggerError", "_count", "finalizeNavigation", "triggerAfterEach", "checkCanceledNavigationAndReject", "installedApps", "leavingRecords", "updatingRecords", "enteringRecords", "extractChangingRecords", "reverse", "canceledNavigationCheck", "run<PERSON><PERSON><PERSON><PERSON><PERSON>", "isPush", "isFirstNavigation", "removeHistoryListener", "setupListeners", "_from", "listening", "readyHandlers", "errorListeners", "ready", "isReady", "started", "beforeResolve", "install", "config", "globalProperties", "$router", "reactiveRoute", "unmountApp", "unmount", "len", "recordFrom", "recordTo", "useRouter", "useRoute", "_name", "START_LOCATION"], "sources": ["C:/Users/<USER>/Desktop/cs/pacong_py/frontend_py/node_modules/vue-router/dist/vue-router.mjs"], "sourcesContent": ["/*!\n  * vue-router v4.5.1\n  * (c) 2025 <PERSON>\n  * @license MIT\n  */\nimport { getCurrentInstance, inject, onUnmounted, onDeactivated, onActivated, computed, unref, watchEffect, defineComponent, reactive, h, provide, ref, watch, shallowRef, shallowReactive, nextTick } from 'vue';\nimport { setupDevtoolsPlugin } from '@vue/devtools-api';\n\nconst isBrowser = typeof document !== 'undefined';\n\n/**\n * Allows differentiating lazy components from functional components and vue-class-component\n * @internal\n *\n * @param component\n */\nfunction isRouteComponent(component) {\n    return (typeof component === 'object' ||\n        'displayName' in component ||\n        'props' in component ||\n        '__vccOpts' in component);\n}\nfunction isESModule(obj) {\n    return (obj.__esModule ||\n        obj[Symbol.toStringTag] === 'Module' ||\n        // support CF with dynamic imports that do not\n        // add the Module string tag\n        (obj.default && isRouteComponent(obj.default)));\n}\nconst assign = Object.assign;\nfunction applyToParams(fn, params) {\n    const newParams = {};\n    for (const key in params) {\n        const value = params[key];\n        newParams[key] = isArray(value)\n            ? value.map(fn)\n            : fn(value);\n    }\n    return newParams;\n}\nconst noop = () => { };\n/**\n * Typesafe alternative to Array.isArray\n * https://github.com/microsoft/TypeScript/pull/48228\n */\nconst isArray = Array.isArray;\n\nfunction warn(msg) {\n    // avoid using ...args as it breaks in older Edge builds\n    const args = Array.from(arguments).slice(1);\n    console.warn.apply(console, ['[Vue Router warn]: ' + msg].concat(args));\n}\n\n/**\n * Encoding Rules (␣ = Space)\n * - Path: ␣ \" < > # ? { }\n * - Query: ␣ \" < > # & =\n * - Hash: ␣ \" < > `\n *\n * On top of that, the RFC3986 (https://tools.ietf.org/html/rfc3986#section-2.2)\n * defines some extra characters to be encoded. Most browsers do not encode them\n * in encodeURI https://github.com/whatwg/url/issues/369, so it may be safer to\n * also encode `!'()*`. Leaving un-encoded only ASCII alphanumeric(`a-zA-Z0-9`)\n * plus `-._~`. This extra safety should be applied to query by patching the\n * string returned by encodeURIComponent encodeURI also encodes `[\\]^`. `\\`\n * should be encoded to avoid ambiguity. Browsers (IE, FF, C) transform a `\\`\n * into a `/` if directly typed in. The _backtick_ (`````) should also be\n * encoded everywhere because some browsers like FF encode it when directly\n * written while others don't. Safari and IE don't encode ``\"<>{}``` in hash.\n */\n// const EXTRA_RESERVED_RE = /[!'()*]/g\n// const encodeReservedReplacer = (c: string) => '%' + c.charCodeAt(0).toString(16)\nconst HASH_RE = /#/g; // %23\nconst AMPERSAND_RE = /&/g; // %26\nconst SLASH_RE = /\\//g; // %2F\nconst EQUAL_RE = /=/g; // %3D\nconst IM_RE = /\\?/g; // %3F\nconst PLUS_RE = /\\+/g; // %2B\n/**\n * NOTE: It's not clear to me if we should encode the + symbol in queries, it\n * seems to be less flexible than not doing so and I can't find out the legacy\n * systems requiring this for regular requests like text/html. In the standard,\n * the encoding of the plus character is only mentioned for\n * application/x-www-form-urlencoded\n * (https://url.spec.whatwg.org/#urlencoded-parsing) and most browsers seems lo\n * leave the plus character as is in queries. To be more flexible, we allow the\n * plus character on the query, but it can also be manually encoded by the user.\n *\n * Resources:\n * - https://url.spec.whatwg.org/#urlencoded-parsing\n * - https://stackoverflow.com/questions/1634271/url-encoding-the-space-character-or-20\n */\nconst ENC_BRACKET_OPEN_RE = /%5B/g; // [\nconst ENC_BRACKET_CLOSE_RE = /%5D/g; // ]\nconst ENC_CARET_RE = /%5E/g; // ^\nconst ENC_BACKTICK_RE = /%60/g; // `\nconst ENC_CURLY_OPEN_RE = /%7B/g; // {\nconst ENC_PIPE_RE = /%7C/g; // |\nconst ENC_CURLY_CLOSE_RE = /%7D/g; // }\nconst ENC_SPACE_RE = /%20/g; // }\n/**\n * Encode characters that need to be encoded on the path, search and hash\n * sections of the URL.\n *\n * @internal\n * @param text - string to encode\n * @returns encoded string\n */\nfunction commonEncode(text) {\n    return encodeURI('' + text)\n        .replace(ENC_PIPE_RE, '|')\n        .replace(ENC_BRACKET_OPEN_RE, '[')\n        .replace(ENC_BRACKET_CLOSE_RE, ']');\n}\n/**\n * Encode characters that need to be encoded on the hash section of the URL.\n *\n * @param text - string to encode\n * @returns encoded string\n */\nfunction encodeHash(text) {\n    return commonEncode(text)\n        .replace(ENC_CURLY_OPEN_RE, '{')\n        .replace(ENC_CURLY_CLOSE_RE, '}')\n        .replace(ENC_CARET_RE, '^');\n}\n/**\n * Encode characters that need to be encoded query values on the query\n * section of the URL.\n *\n * @param text - string to encode\n * @returns encoded string\n */\nfunction encodeQueryValue(text) {\n    return (commonEncode(text)\n        // Encode the space as +, encode the + to differentiate it from the space\n        .replace(PLUS_RE, '%2B')\n        .replace(ENC_SPACE_RE, '+')\n        .replace(HASH_RE, '%23')\n        .replace(AMPERSAND_RE, '%26')\n        .replace(ENC_BACKTICK_RE, '`')\n        .replace(ENC_CURLY_OPEN_RE, '{')\n        .replace(ENC_CURLY_CLOSE_RE, '}')\n        .replace(ENC_CARET_RE, '^'));\n}\n/**\n * Like `encodeQueryValue` but also encodes the `=` character.\n *\n * @param text - string to encode\n */\nfunction encodeQueryKey(text) {\n    return encodeQueryValue(text).replace(EQUAL_RE, '%3D');\n}\n/**\n * Encode characters that need to be encoded on the path section of the URL.\n *\n * @param text - string to encode\n * @returns encoded string\n */\nfunction encodePath(text) {\n    return commonEncode(text).replace(HASH_RE, '%23').replace(IM_RE, '%3F');\n}\n/**\n * Encode characters that need to be encoded on the path section of the URL as a\n * param. This function encodes everything {@link encodePath} does plus the\n * slash (`/`) character. If `text` is `null` or `undefined`, returns an empty\n * string instead.\n *\n * @param text - string to encode\n * @returns encoded string\n */\nfunction encodeParam(text) {\n    return text == null ? '' : encodePath(text).replace(SLASH_RE, '%2F');\n}\n/**\n * Decode text using `decodeURIComponent`. Returns the original text if it\n * fails.\n *\n * @param text - string to decode\n * @returns decoded string\n */\nfunction decode(text) {\n    try {\n        return decodeURIComponent('' + text);\n    }\n    catch (err) {\n        (process.env.NODE_ENV !== 'production') && warn(`Error decoding \"${text}\". Using original value`);\n    }\n    return '' + text;\n}\n\nconst TRAILING_SLASH_RE = /\\/$/;\nconst removeTrailingSlash = (path) => path.replace(TRAILING_SLASH_RE, '');\n/**\n * Transforms a URI into a normalized history location\n *\n * @param parseQuery\n * @param location - URI to normalize\n * @param currentLocation - current absolute location. Allows resolving relative\n * paths. Must start with `/`. Defaults to `/`\n * @returns a normalized history location\n */\nfunction parseURL(parseQuery, location, currentLocation = '/') {\n    let path, query = {}, searchString = '', hash = '';\n    // Could use URL and URLSearchParams but IE 11 doesn't support it\n    // TODO: move to new URL()\n    const hashPos = location.indexOf('#');\n    let searchPos = location.indexOf('?');\n    // the hash appears before the search, so it's not part of the search string\n    if (hashPos < searchPos && hashPos >= 0) {\n        searchPos = -1;\n    }\n    if (searchPos > -1) {\n        path = location.slice(0, searchPos);\n        searchString = location.slice(searchPos + 1, hashPos > -1 ? hashPos : location.length);\n        query = parseQuery(searchString);\n    }\n    if (hashPos > -1) {\n        path = path || location.slice(0, hashPos);\n        // keep the # character\n        hash = location.slice(hashPos, location.length);\n    }\n    // no search and no query\n    path = resolveRelativePath(path != null ? path : location, currentLocation);\n    // empty path means a relative query or hash `?foo=f`, `#thing`\n    return {\n        fullPath: path + (searchString && '?') + searchString + hash,\n        path,\n        query,\n        hash: decode(hash),\n    };\n}\n/**\n * Stringifies a URL object\n *\n * @param stringifyQuery\n * @param location\n */\nfunction stringifyURL(stringifyQuery, location) {\n    const query = location.query ? stringifyQuery(location.query) : '';\n    return location.path + (query && '?') + query + (location.hash || '');\n}\n/**\n * Strips off the base from the beginning of a location.pathname in a non-case-sensitive way.\n *\n * @param pathname - location.pathname\n * @param base - base to strip off\n */\nfunction stripBase(pathname, base) {\n    // no base or base is not found at the beginning\n    if (!base || !pathname.toLowerCase().startsWith(base.toLowerCase()))\n        return pathname;\n    return pathname.slice(base.length) || '/';\n}\n/**\n * Checks if two RouteLocation are equal. This means that both locations are\n * pointing towards the same {@link RouteRecord} and that all `params`, `query`\n * parameters and `hash` are the same\n *\n * @param stringifyQuery - A function that takes a query object of type LocationQueryRaw and returns a string representation of it.\n * @param a - first {@link RouteLocation}\n * @param b - second {@link RouteLocation}\n */\nfunction isSameRouteLocation(stringifyQuery, a, b) {\n    const aLastIndex = a.matched.length - 1;\n    const bLastIndex = b.matched.length - 1;\n    return (aLastIndex > -1 &&\n        aLastIndex === bLastIndex &&\n        isSameRouteRecord(a.matched[aLastIndex], b.matched[bLastIndex]) &&\n        isSameRouteLocationParams(a.params, b.params) &&\n        stringifyQuery(a.query) === stringifyQuery(b.query) &&\n        a.hash === b.hash);\n}\n/**\n * Check if two `RouteRecords` are equal. Takes into account aliases: they are\n * considered equal to the `RouteRecord` they are aliasing.\n *\n * @param a - first {@link RouteRecord}\n * @param b - second {@link RouteRecord}\n */\nfunction isSameRouteRecord(a, b) {\n    // since the original record has an undefined value for aliasOf\n    // but all aliases point to the original record, this will always compare\n    // the original record\n    return (a.aliasOf || a) === (b.aliasOf || b);\n}\nfunction isSameRouteLocationParams(a, b) {\n    if (Object.keys(a).length !== Object.keys(b).length)\n        return false;\n    for (const key in a) {\n        if (!isSameRouteLocationParamsValue(a[key], b[key]))\n            return false;\n    }\n    return true;\n}\nfunction isSameRouteLocationParamsValue(a, b) {\n    return isArray(a)\n        ? isEquivalentArray(a, b)\n        : isArray(b)\n            ? isEquivalentArray(b, a)\n            : a === b;\n}\n/**\n * Check if two arrays are the same or if an array with one single entry is the\n * same as another primitive value. Used to check query and parameters\n *\n * @param a - array of values\n * @param b - array of values or a single value\n */\nfunction isEquivalentArray(a, b) {\n    return isArray(b)\n        ? a.length === b.length && a.every((value, i) => value === b[i])\n        : a.length === 1 && a[0] === b;\n}\n/**\n * Resolves a relative path that starts with `.`.\n *\n * @param to - path location we are resolving\n * @param from - currentLocation.path, should start with `/`\n */\nfunction resolveRelativePath(to, from) {\n    if (to.startsWith('/'))\n        return to;\n    if ((process.env.NODE_ENV !== 'production') && !from.startsWith('/')) {\n        warn(`Cannot resolve a relative location without an absolute path. Trying to resolve \"${to}\" from \"${from}\". It should look like \"/${from}\".`);\n        return to;\n    }\n    if (!to)\n        return from;\n    const fromSegments = from.split('/');\n    const toSegments = to.split('/');\n    const lastToSegment = toSegments[toSegments.length - 1];\n    // make . and ./ the same (../ === .., ../../ === ../..)\n    // this is the same behavior as new URL()\n    if (lastToSegment === '..' || lastToSegment === '.') {\n        toSegments.push('');\n    }\n    let position = fromSegments.length - 1;\n    let toPosition;\n    let segment;\n    for (toPosition = 0; toPosition < toSegments.length; toPosition++) {\n        segment = toSegments[toPosition];\n        // we stay on the same position\n        if (segment === '.')\n            continue;\n        // go up in the from array\n        if (segment === '..') {\n            // we can't go below zero, but we still need to increment toPosition\n            if (position > 1)\n                position--;\n            // continue\n        }\n        // we reached a non-relative path, we stop here\n        else\n            break;\n    }\n    return (fromSegments.slice(0, position).join('/') +\n        '/' +\n        toSegments.slice(toPosition).join('/'));\n}\n/**\n * Initial route location where the router is. Can be used in navigation guards\n * to differentiate the initial navigation.\n *\n * @example\n * ```js\n * import { START_LOCATION } from 'vue-router'\n *\n * router.beforeEach((to, from) => {\n *   if (from === START_LOCATION) {\n *     // initial navigation\n *   }\n * })\n * ```\n */\nconst START_LOCATION_NORMALIZED = {\n    path: '/',\n    // TODO: could we use a symbol in the future?\n    name: undefined,\n    params: {},\n    query: {},\n    hash: '',\n    fullPath: '/',\n    matched: [],\n    meta: {},\n    redirectedFrom: undefined,\n};\n\nvar NavigationType;\n(function (NavigationType) {\n    NavigationType[\"pop\"] = \"pop\";\n    NavigationType[\"push\"] = \"push\";\n})(NavigationType || (NavigationType = {}));\nvar NavigationDirection;\n(function (NavigationDirection) {\n    NavigationDirection[\"back\"] = \"back\";\n    NavigationDirection[\"forward\"] = \"forward\";\n    NavigationDirection[\"unknown\"] = \"\";\n})(NavigationDirection || (NavigationDirection = {}));\n/**\n * Starting location for Histories\n */\nconst START = '';\n// Generic utils\n/**\n * Normalizes a base by removing any trailing slash and reading the base tag if\n * present.\n *\n * @param base - base to normalize\n */\nfunction normalizeBase(base) {\n    if (!base) {\n        if (isBrowser) {\n            // respect <base> tag\n            const baseEl = document.querySelector('base');\n            base = (baseEl && baseEl.getAttribute('href')) || '/';\n            // strip full URL origin\n            base = base.replace(/^\\w+:\\/\\/[^\\/]+/, '');\n        }\n        else {\n            base = '/';\n        }\n    }\n    // ensure leading slash when it was removed by the regex above avoid leading\n    // slash with hash because the file could be read from the disk like file://\n    // and the leading slash would cause problems\n    if (base[0] !== '/' && base[0] !== '#')\n        base = '/' + base;\n    // remove the trailing slash so all other method can just do `base + fullPath`\n    // to build an href\n    return removeTrailingSlash(base);\n}\n// remove any character before the hash\nconst BEFORE_HASH_RE = /^[^#]+#/;\nfunction createHref(base, location) {\n    return base.replace(BEFORE_HASH_RE, '#') + location;\n}\n\nfunction getElementPosition(el, offset) {\n    const docRect = document.documentElement.getBoundingClientRect();\n    const elRect = el.getBoundingClientRect();\n    return {\n        behavior: offset.behavior,\n        left: elRect.left - docRect.left - (offset.left || 0),\n        top: elRect.top - docRect.top - (offset.top || 0),\n    };\n}\nconst computeScrollPosition = () => ({\n    left: window.scrollX,\n    top: window.scrollY,\n});\nfunction scrollToPosition(position) {\n    let scrollToOptions;\n    if ('el' in position) {\n        const positionEl = position.el;\n        const isIdSelector = typeof positionEl === 'string' && positionEl.startsWith('#');\n        /**\n         * `id`s can accept pretty much any characters, including CSS combinators\n         * like `>` or `~`. It's still possible to retrieve elements using\n         * `document.getElementById('~')` but it needs to be escaped when using\n         * `document.querySelector('#\\\\~')` for it to be valid. The only\n         * requirements for `id`s are them to be unique on the page and to not be\n         * empty (`id=\"\"`). Because of that, when passing an id selector, it should\n         * be properly escaped for it to work with `querySelector`. We could check\n         * for the id selector to be simple (no CSS combinators `+ >~`) but that\n         * would make things inconsistent since they are valid characters for an\n         * `id` but would need to be escaped when using `querySelector`, breaking\n         * their usage and ending up in no selector returned. Selectors need to be\n         * escaped:\n         *\n         * - `#1-thing` becomes `#\\31 -thing`\n         * - `#with~symbols` becomes `#with\\\\~symbols`\n         *\n         * - More information about  the topic can be found at\n         *   https://mathiasbynens.be/notes/html5-id-class.\n         * - Practical example: https://mathiasbynens.be/demo/html5-id\n         */\n        if ((process.env.NODE_ENV !== 'production') && typeof position.el === 'string') {\n            if (!isIdSelector || !document.getElementById(position.el.slice(1))) {\n                try {\n                    const foundEl = document.querySelector(position.el);\n                    if (isIdSelector && foundEl) {\n                        warn(`The selector \"${position.el}\" should be passed as \"el: document.querySelector('${position.el}')\" because it starts with \"#\".`);\n                        // return to avoid other warnings\n                        return;\n                    }\n                }\n                catch (err) {\n                    warn(`The selector \"${position.el}\" is invalid. If you are using an id selector, make sure to escape it. You can find more information about escaping characters in selectors at https://mathiasbynens.be/notes/css-escapes or use CSS.escape (https://developer.mozilla.org/en-US/docs/Web/API/CSS/escape).`);\n                    // return to avoid other warnings\n                    return;\n                }\n            }\n        }\n        const el = typeof positionEl === 'string'\n            ? isIdSelector\n                ? document.getElementById(positionEl.slice(1))\n                : document.querySelector(positionEl)\n            : positionEl;\n        if (!el) {\n            (process.env.NODE_ENV !== 'production') &&\n                warn(`Couldn't find element using selector \"${position.el}\" returned by scrollBehavior.`);\n            return;\n        }\n        scrollToOptions = getElementPosition(el, position);\n    }\n    else {\n        scrollToOptions = position;\n    }\n    if ('scrollBehavior' in document.documentElement.style)\n        window.scrollTo(scrollToOptions);\n    else {\n        window.scrollTo(scrollToOptions.left != null ? scrollToOptions.left : window.scrollX, scrollToOptions.top != null ? scrollToOptions.top : window.scrollY);\n    }\n}\nfunction getScrollKey(path, delta) {\n    const position = history.state ? history.state.position - delta : -1;\n    return position + path;\n}\nconst scrollPositions = new Map();\nfunction saveScrollPosition(key, scrollPosition) {\n    scrollPositions.set(key, scrollPosition);\n}\nfunction getSavedScrollPosition(key) {\n    const scroll = scrollPositions.get(key);\n    // consume it so it's not used again\n    scrollPositions.delete(key);\n    return scroll;\n}\n// TODO: RFC about how to save scroll position\n/**\n * ScrollBehavior instance used by the router to compute and restore the scroll\n * position when navigating.\n */\n// export interface ScrollHandler<ScrollPositionEntry extends HistoryStateValue, ScrollPosition extends ScrollPositionEntry> {\n//   // returns a scroll position that can be saved in history\n//   compute(): ScrollPositionEntry\n//   // can take an extended ScrollPositionEntry\n//   scroll(position: ScrollPosition): void\n// }\n// export const scrollHandler: ScrollHandler<ScrollPosition> = {\n//   compute: computeScroll,\n//   scroll: scrollToPosition,\n// }\n\nlet createBaseLocation = () => location.protocol + '//' + location.host;\n/**\n * Creates a normalized history location from a window.location object\n * @param base - The base path\n * @param location - The window.location object\n */\nfunction createCurrentLocation(base, location) {\n    const { pathname, search, hash } = location;\n    // allows hash bases like #, /#, #/, #!, #!/, /#!/, or even /folder#end\n    const hashPos = base.indexOf('#');\n    if (hashPos > -1) {\n        let slicePos = hash.includes(base.slice(hashPos))\n            ? base.slice(hashPos).length\n            : 1;\n        let pathFromHash = hash.slice(slicePos);\n        // prepend the starting slash to hash so the url starts with /#\n        if (pathFromHash[0] !== '/')\n            pathFromHash = '/' + pathFromHash;\n        return stripBase(pathFromHash, '');\n    }\n    const path = stripBase(pathname, base);\n    return path + search + hash;\n}\nfunction useHistoryListeners(base, historyState, currentLocation, replace) {\n    let listeners = [];\n    let teardowns = [];\n    // TODO: should it be a stack? a Dict. Check if the popstate listener\n    // can trigger twice\n    let pauseState = null;\n    const popStateHandler = ({ state, }) => {\n        const to = createCurrentLocation(base, location);\n        const from = currentLocation.value;\n        const fromState = historyState.value;\n        let delta = 0;\n        if (state) {\n            currentLocation.value = to;\n            historyState.value = state;\n            // ignore the popstate and reset the pauseState\n            if (pauseState && pauseState === from) {\n                pauseState = null;\n                return;\n            }\n            delta = fromState ? state.position - fromState.position : 0;\n        }\n        else {\n            replace(to);\n        }\n        // Here we could also revert the navigation by calling history.go(-delta)\n        // this listener will have to be adapted to not trigger again and to wait for the url\n        // to be updated before triggering the listeners. Some kind of validation function would also\n        // need to be passed to the listeners so the navigation can be accepted\n        // call all listeners\n        listeners.forEach(listener => {\n            listener(currentLocation.value, from, {\n                delta,\n                type: NavigationType.pop,\n                direction: delta\n                    ? delta > 0\n                        ? NavigationDirection.forward\n                        : NavigationDirection.back\n                    : NavigationDirection.unknown,\n            });\n        });\n    };\n    function pauseListeners() {\n        pauseState = currentLocation.value;\n    }\n    function listen(callback) {\n        // set up the listener and prepare teardown callbacks\n        listeners.push(callback);\n        const teardown = () => {\n            const index = listeners.indexOf(callback);\n            if (index > -1)\n                listeners.splice(index, 1);\n        };\n        teardowns.push(teardown);\n        return teardown;\n    }\n    function beforeUnloadListener() {\n        const { history } = window;\n        if (!history.state)\n            return;\n        history.replaceState(assign({}, history.state, { scroll: computeScrollPosition() }), '');\n    }\n    function destroy() {\n        for (const teardown of teardowns)\n            teardown();\n        teardowns = [];\n        window.removeEventListener('popstate', popStateHandler);\n        window.removeEventListener('beforeunload', beforeUnloadListener);\n    }\n    // set up the listeners and prepare teardown callbacks\n    window.addEventListener('popstate', popStateHandler);\n    // TODO: could we use 'pagehide' or 'visibilitychange' instead?\n    // https://developer.chrome.com/blog/page-lifecycle-api/\n    window.addEventListener('beforeunload', beforeUnloadListener, {\n        passive: true,\n    });\n    return {\n        pauseListeners,\n        listen,\n        destroy,\n    };\n}\n/**\n * Creates a state object\n */\nfunction buildState(back, current, forward, replaced = false, computeScroll = false) {\n    return {\n        back,\n        current,\n        forward,\n        replaced,\n        position: window.history.length,\n        scroll: computeScroll ? computeScrollPosition() : null,\n    };\n}\nfunction useHistoryStateNavigation(base) {\n    const { history, location } = window;\n    // private variables\n    const currentLocation = {\n        value: createCurrentLocation(base, location),\n    };\n    const historyState = { value: history.state };\n    // build current history entry as this is a fresh navigation\n    if (!historyState.value) {\n        changeLocation(currentLocation.value, {\n            back: null,\n            current: currentLocation.value,\n            forward: null,\n            // the length is off by one, we need to decrease it\n            position: history.length - 1,\n            replaced: true,\n            // don't add a scroll as the user may have an anchor, and we want\n            // scrollBehavior to be triggered without a saved position\n            scroll: null,\n        }, true);\n    }\n    function changeLocation(to, state, replace) {\n        /**\n         * if a base tag is provided, and we are on a normal domain, we have to\n         * respect the provided `base` attribute because pushState() will use it and\n         * potentially erase anything before the `#` like at\n         * https://github.com/vuejs/router/issues/685 where a base of\n         * `/folder/#` but a base of `/` would erase the `/folder/` section. If\n         * there is no host, the `<base>` tag makes no sense and if there isn't a\n         * base tag we can just use everything after the `#`.\n         */\n        const hashIndex = base.indexOf('#');\n        const url = hashIndex > -1\n            ? (location.host && document.querySelector('base')\n                ? base\n                : base.slice(hashIndex)) + to\n            : createBaseLocation() + base + to;\n        try {\n            // BROWSER QUIRK\n            // NOTE: Safari throws a SecurityError when calling this function 100 times in 30 seconds\n            history[replace ? 'replaceState' : 'pushState'](state, '', url);\n            historyState.value = state;\n        }\n        catch (err) {\n            if ((process.env.NODE_ENV !== 'production')) {\n                warn('Error with push/replace State', err);\n            }\n            else {\n                console.error(err);\n            }\n            // Force the navigation, this also resets the call count\n            location[replace ? 'replace' : 'assign'](url);\n        }\n    }\n    function replace(to, data) {\n        const state = assign({}, history.state, buildState(historyState.value.back, \n        // keep back and forward entries but override current position\n        to, historyState.value.forward, true), data, { position: historyState.value.position });\n        changeLocation(to, state, true);\n        currentLocation.value = to;\n    }\n    function push(to, data) {\n        // Add to current entry the information of where we are going\n        // as well as saving the current position\n        const currentState = assign({}, \n        // use current history state to gracefully handle a wrong call to\n        // history.replaceState\n        // https://github.com/vuejs/router/issues/366\n        historyState.value, history.state, {\n            forward: to,\n            scroll: computeScrollPosition(),\n        });\n        if ((process.env.NODE_ENV !== 'production') && !history.state) {\n            warn(`history.state seems to have been manually replaced without preserving the necessary values. Make sure to preserve existing history state if you are manually calling history.replaceState:\\n\\n` +\n                `history.replaceState(history.state, '', url)\\n\\n` +\n                `You can find more information at https://router.vuejs.org/guide/migration/#Usage-of-history-state`);\n        }\n        changeLocation(currentState.current, currentState, true);\n        const state = assign({}, buildState(currentLocation.value, to, null), { position: currentState.position + 1 }, data);\n        changeLocation(to, state, false);\n        currentLocation.value = to;\n    }\n    return {\n        location: currentLocation,\n        state: historyState,\n        push,\n        replace,\n    };\n}\n/**\n * Creates an HTML5 history. Most common history for single page applications.\n *\n * @param base -\n */\nfunction createWebHistory(base) {\n    base = normalizeBase(base);\n    const historyNavigation = useHistoryStateNavigation(base);\n    const historyListeners = useHistoryListeners(base, historyNavigation.state, historyNavigation.location, historyNavigation.replace);\n    function go(delta, triggerListeners = true) {\n        if (!triggerListeners)\n            historyListeners.pauseListeners();\n        history.go(delta);\n    }\n    const routerHistory = assign({\n        // it's overridden right after\n        location: '',\n        base,\n        go,\n        createHref: createHref.bind(null, base),\n    }, historyNavigation, historyListeners);\n    Object.defineProperty(routerHistory, 'location', {\n        enumerable: true,\n        get: () => historyNavigation.location.value,\n    });\n    Object.defineProperty(routerHistory, 'state', {\n        enumerable: true,\n        get: () => historyNavigation.state.value,\n    });\n    return routerHistory;\n}\n\n/**\n * Creates an in-memory based history. The main purpose of this history is to handle SSR. It starts in a special location that is nowhere.\n * It's up to the user to replace that location with the starter location by either calling `router.push` or `router.replace`.\n *\n * @param base - Base applied to all urls, defaults to '/'\n * @returns a history object that can be passed to the router constructor\n */\nfunction createMemoryHistory(base = '') {\n    let listeners = [];\n    let queue = [[START, {}]];\n    let position = 0;\n    base = normalizeBase(base);\n    function setLocation(location, state = {}) {\n        position++;\n        if (position !== queue.length) {\n            // we are in the middle, we remove everything from here in the queue\n            queue.splice(position);\n        }\n        queue.push([location, state]);\n    }\n    function triggerListeners(to, from, { direction, delta }) {\n        const info = {\n            direction,\n            delta,\n            type: NavigationType.pop,\n        };\n        for (const callback of listeners) {\n            callback(to, from, info);\n        }\n    }\n    const routerHistory = {\n        // rewritten by Object.defineProperty\n        location: START,\n        // rewritten by Object.defineProperty\n        state: {},\n        base,\n        createHref: createHref.bind(null, base),\n        replace(to, state) {\n            // remove current entry and decrement position\n            queue.splice(position--, 1);\n            setLocation(to, state);\n        },\n        push(to, state) {\n            setLocation(to, state);\n        },\n        listen(callback) {\n            listeners.push(callback);\n            return () => {\n                const index = listeners.indexOf(callback);\n                if (index > -1)\n                    listeners.splice(index, 1);\n            };\n        },\n        destroy() {\n            listeners = [];\n            queue = [[START, {}]];\n            position = 0;\n        },\n        go(delta, shouldTrigger = true) {\n            const from = this.location;\n            const direction = \n            // we are considering delta === 0 going forward, but in abstract mode\n            // using 0 for the delta doesn't make sense like it does in html5 where\n            // it reloads the page\n            delta < 0 ? NavigationDirection.back : NavigationDirection.forward;\n            position = Math.max(0, Math.min(position + delta, queue.length - 1));\n            if (shouldTrigger) {\n                triggerListeners(this.location, from, {\n                    direction,\n                    delta,\n                });\n            }\n        },\n    };\n    Object.defineProperty(routerHistory, 'location', {\n        enumerable: true,\n        get: () => queue[position][0],\n    });\n    Object.defineProperty(routerHistory, 'state', {\n        enumerable: true,\n        get: () => queue[position][1],\n    });\n    return routerHistory;\n}\n\n/**\n * Creates a hash history. Useful for web applications with no host (e.g. `file://`) or when configuring a server to\n * handle any URL is not possible.\n *\n * @param base - optional base to provide. Defaults to `location.pathname + location.search` If there is a `<base>` tag\n * in the `head`, its value will be ignored in favor of this parameter **but note it affects all the history.pushState()\n * calls**, meaning that if you use a `<base>` tag, it's `href` value **has to match this parameter** (ignoring anything\n * after the `#`).\n *\n * @example\n * ```js\n * // at https://example.com/folder\n * createWebHashHistory() // gives a url of `https://example.com/folder#`\n * createWebHashHistory('/folder/') // gives a url of `https://example.com/folder/#`\n * // if the `#` is provided in the base, it won't be added by `createWebHashHistory`\n * createWebHashHistory('/folder/#/app/') // gives a url of `https://example.com/folder/#/app/`\n * // you should avoid doing this because it changes the original url and breaks copying urls\n * createWebHashHistory('/other-folder/') // gives a url of `https://example.com/other-folder/#`\n *\n * // at file:///usr/etc/folder/index.html\n * // for locations with no `host`, the base is ignored\n * createWebHashHistory('/iAmIgnored') // gives a url of `file:///usr/etc/folder/index.html#`\n * ```\n */\nfunction createWebHashHistory(base) {\n    // Make sure this implementation is fine in terms of encoding, specially for IE11\n    // for `file://`, directly use the pathname and ignore the base\n    // location.pathname contains an initial `/` even at the root: `https://example.com`\n    base = location.host ? base || location.pathname + location.search : '';\n    // allow the user to provide a `#` in the middle: `/base/#/app`\n    if (!base.includes('#'))\n        base += '#';\n    if ((process.env.NODE_ENV !== 'production') && !base.endsWith('#/') && !base.endsWith('#')) {\n        warn(`A hash base must end with a \"#\":\\n\"${base}\" should be \"${base.replace(/#.*$/, '#')}\".`);\n    }\n    return createWebHistory(base);\n}\n\nfunction isRouteLocation(route) {\n    return typeof route === 'string' || (route && typeof route === 'object');\n}\nfunction isRouteName(name) {\n    return typeof name === 'string' || typeof name === 'symbol';\n}\n\nconst NavigationFailureSymbol = Symbol((process.env.NODE_ENV !== 'production') ? 'navigation failure' : '');\n/**\n * Enumeration with all possible types for navigation failures. Can be passed to\n * {@link isNavigationFailure} to check for specific failures.\n */\nvar NavigationFailureType;\n(function (NavigationFailureType) {\n    /**\n     * An aborted navigation is a navigation that failed because a navigation\n     * guard returned `false` or called `next(false)`\n     */\n    NavigationFailureType[NavigationFailureType[\"aborted\"] = 4] = \"aborted\";\n    /**\n     * A cancelled navigation is a navigation that failed because a more recent\n     * navigation finished started (not necessarily finished).\n     */\n    NavigationFailureType[NavigationFailureType[\"cancelled\"] = 8] = \"cancelled\";\n    /**\n     * A duplicated navigation is a navigation that failed because it was\n     * initiated while already being at the exact same location.\n     */\n    NavigationFailureType[NavigationFailureType[\"duplicated\"] = 16] = \"duplicated\";\n})(NavigationFailureType || (NavigationFailureType = {}));\n// DEV only debug messages\nconst ErrorTypeMessages = {\n    [1 /* ErrorTypes.MATCHER_NOT_FOUND */]({ location, currentLocation }) {\n        return `No match for\\n ${JSON.stringify(location)}${currentLocation\n            ? '\\nwhile being at\\n' + JSON.stringify(currentLocation)\n            : ''}`;\n    },\n    [2 /* ErrorTypes.NAVIGATION_GUARD_REDIRECT */]({ from, to, }) {\n        return `Redirected from \"${from.fullPath}\" to \"${stringifyRoute(to)}\" via a navigation guard.`;\n    },\n    [4 /* ErrorTypes.NAVIGATION_ABORTED */]({ from, to }) {\n        return `Navigation aborted from \"${from.fullPath}\" to \"${to.fullPath}\" via a navigation guard.`;\n    },\n    [8 /* ErrorTypes.NAVIGATION_CANCELLED */]({ from, to }) {\n        return `Navigation cancelled from \"${from.fullPath}\" to \"${to.fullPath}\" with a new navigation.`;\n    },\n    [16 /* ErrorTypes.NAVIGATION_DUPLICATED */]({ from, to }) {\n        return `Avoided redundant navigation to current location: \"${from.fullPath}\".`;\n    },\n};\n/**\n * Creates a typed NavigationFailure object.\n * @internal\n * @param type - NavigationFailureType\n * @param params - { from, to }\n */\nfunction createRouterError(type, params) {\n    // keep full error messages in cjs versions\n    if ((process.env.NODE_ENV !== 'production') || !true) {\n        return assign(new Error(ErrorTypeMessages[type](params)), {\n            type,\n            [NavigationFailureSymbol]: true,\n        }, params);\n    }\n    else {\n        return assign(new Error(), {\n            type,\n            [NavigationFailureSymbol]: true,\n        }, params);\n    }\n}\nfunction isNavigationFailure(error, type) {\n    return (error instanceof Error &&\n        NavigationFailureSymbol in error &&\n        (type == null || !!(error.type & type)));\n}\nconst propertiesToLog = ['params', 'query', 'hash'];\nfunction stringifyRoute(to) {\n    if (typeof to === 'string')\n        return to;\n    if (to.path != null)\n        return to.path;\n    const location = {};\n    for (const key of propertiesToLog) {\n        if (key in to)\n            location[key] = to[key];\n    }\n    return JSON.stringify(location, null, 2);\n}\n\n// default pattern for a param: non-greedy everything but /\nconst BASE_PARAM_PATTERN = '[^/]+?';\nconst BASE_PATH_PARSER_OPTIONS = {\n    sensitive: false,\n    strict: false,\n    start: true,\n    end: true,\n};\n// Special Regex characters that must be escaped in static tokens\nconst REGEX_CHARS_RE = /[.+*?^${}()[\\]/\\\\]/g;\n/**\n * Creates a path parser from an array of Segments (a segment is an array of Tokens)\n *\n * @param segments - array of segments returned by tokenizePath\n * @param extraOptions - optional options for the regexp\n * @returns a PathParser\n */\nfunction tokensToParser(segments, extraOptions) {\n    const options = assign({}, BASE_PATH_PARSER_OPTIONS, extraOptions);\n    // the amount of scores is the same as the length of segments except for the root segment \"/\"\n    const score = [];\n    // the regexp as a string\n    let pattern = options.start ? '^' : '';\n    // extracted keys\n    const keys = [];\n    for (const segment of segments) {\n        // the root segment needs special treatment\n        const segmentScores = segment.length ? [] : [90 /* PathScore.Root */];\n        // allow trailing slash\n        if (options.strict && !segment.length)\n            pattern += '/';\n        for (let tokenIndex = 0; tokenIndex < segment.length; tokenIndex++) {\n            const token = segment[tokenIndex];\n            // resets the score if we are inside a sub-segment /:a-other-:b\n            let subSegmentScore = 40 /* PathScore.Segment */ +\n                (options.sensitive ? 0.25 /* PathScore.BonusCaseSensitive */ : 0);\n            if (token.type === 0 /* TokenType.Static */) {\n                // prepend the slash if we are starting a new segment\n                if (!tokenIndex)\n                    pattern += '/';\n                pattern += token.value.replace(REGEX_CHARS_RE, '\\\\$&');\n                subSegmentScore += 40 /* PathScore.Static */;\n            }\n            else if (token.type === 1 /* TokenType.Param */) {\n                const { value, repeatable, optional, regexp } = token;\n                keys.push({\n                    name: value,\n                    repeatable,\n                    optional,\n                });\n                const re = regexp ? regexp : BASE_PARAM_PATTERN;\n                // the user provided a custom regexp /:id(\\\\d+)\n                if (re !== BASE_PARAM_PATTERN) {\n                    subSegmentScore += 10 /* PathScore.BonusCustomRegExp */;\n                    // make sure the regexp is valid before using it\n                    try {\n                        new RegExp(`(${re})`);\n                    }\n                    catch (err) {\n                        throw new Error(`Invalid custom RegExp for param \"${value}\" (${re}): ` +\n                            err.message);\n                    }\n                }\n                // when we repeat we must take care of the repeating leading slash\n                let subPattern = repeatable ? `((?:${re})(?:/(?:${re}))*)` : `(${re})`;\n                // prepend the slash if we are starting a new segment\n                if (!tokenIndex)\n                    subPattern =\n                        // avoid an optional / if there are more segments e.g. /:p?-static\n                        // or /:p?-:p2\n                        optional && segment.length < 2\n                            ? `(?:/${subPattern})`\n                            : '/' + subPattern;\n                if (optional)\n                    subPattern += '?';\n                pattern += subPattern;\n                subSegmentScore += 20 /* PathScore.Dynamic */;\n                if (optional)\n                    subSegmentScore += -8 /* PathScore.BonusOptional */;\n                if (repeatable)\n                    subSegmentScore += -20 /* PathScore.BonusRepeatable */;\n                if (re === '.*')\n                    subSegmentScore += -50 /* PathScore.BonusWildcard */;\n            }\n            segmentScores.push(subSegmentScore);\n        }\n        // an empty array like /home/<USER>\n        // if (!segment.length) pattern += '/'\n        score.push(segmentScores);\n    }\n    // only apply the strict bonus to the last score\n    if (options.strict && options.end) {\n        const i = score.length - 1;\n        score[i][score[i].length - 1] += 0.7000000000000001 /* PathScore.BonusStrict */;\n    }\n    // TODO: dev only warn double trailing slash\n    if (!options.strict)\n        pattern += '/?';\n    if (options.end)\n        pattern += '$';\n    // allow paths like /dynamic to only match dynamic or dynamic/... but not dynamic_something_else\n    else if (options.strict && !pattern.endsWith('/'))\n        pattern += '(?:/|$)';\n    const re = new RegExp(pattern, options.sensitive ? '' : 'i');\n    function parse(path) {\n        const match = path.match(re);\n        const params = {};\n        if (!match)\n            return null;\n        for (let i = 1; i < match.length; i++) {\n            const value = match[i] || '';\n            const key = keys[i - 1];\n            params[key.name] = value && key.repeatable ? value.split('/') : value;\n        }\n        return params;\n    }\n    function stringify(params) {\n        let path = '';\n        // for optional parameters to allow to be empty\n        let avoidDuplicatedSlash = false;\n        for (const segment of segments) {\n            if (!avoidDuplicatedSlash || !path.endsWith('/'))\n                path += '/';\n            avoidDuplicatedSlash = false;\n            for (const token of segment) {\n                if (token.type === 0 /* TokenType.Static */) {\n                    path += token.value;\n                }\n                else if (token.type === 1 /* TokenType.Param */) {\n                    const { value, repeatable, optional } = token;\n                    const param = value in params ? params[value] : '';\n                    if (isArray(param) && !repeatable) {\n                        throw new Error(`Provided param \"${value}\" is an array but it is not repeatable (* or + modifiers)`);\n                    }\n                    const text = isArray(param)\n                        ? param.join('/')\n                        : param;\n                    if (!text) {\n                        if (optional) {\n                            // if we have more than one optional param like /:a?-static we don't need to care about the optional param\n                            if (segment.length < 2) {\n                                // remove the last slash as we could be at the end\n                                if (path.endsWith('/'))\n                                    path = path.slice(0, -1);\n                                // do not append a slash on the next iteration\n                                else\n                                    avoidDuplicatedSlash = true;\n                            }\n                        }\n                        else\n                            throw new Error(`Missing required param \"${value}\"`);\n                    }\n                    path += text;\n                }\n            }\n        }\n        // avoid empty path when we have multiple optional params\n        return path || '/';\n    }\n    return {\n        re,\n        score,\n        keys,\n        parse,\n        stringify,\n    };\n}\n/**\n * Compares an array of numbers as used in PathParser.score and returns a\n * number. This function can be used to `sort` an array\n *\n * @param a - first array of numbers\n * @param b - second array of numbers\n * @returns 0 if both are equal, < 0 if a should be sorted first, > 0 if b\n * should be sorted first\n */\nfunction compareScoreArray(a, b) {\n    let i = 0;\n    while (i < a.length && i < b.length) {\n        const diff = b[i] - a[i];\n        // only keep going if diff === 0\n        if (diff)\n            return diff;\n        i++;\n    }\n    // if the last subsegment was Static, the shorter segments should be sorted first\n    // otherwise sort the longest segment first\n    if (a.length < b.length) {\n        return a.length === 1 && a[0] === 40 /* PathScore.Static */ + 40 /* PathScore.Segment */\n            ? -1\n            : 1;\n    }\n    else if (a.length > b.length) {\n        return b.length === 1 && b[0] === 40 /* PathScore.Static */ + 40 /* PathScore.Segment */\n            ? 1\n            : -1;\n    }\n    return 0;\n}\n/**\n * Compare function that can be used with `sort` to sort an array of PathParser\n *\n * @param a - first PathParser\n * @param b - second PathParser\n * @returns 0 if both are equal, < 0 if a should be sorted first, > 0 if b\n */\nfunction comparePathParserScore(a, b) {\n    let i = 0;\n    const aScore = a.score;\n    const bScore = b.score;\n    while (i < aScore.length && i < bScore.length) {\n        const comp = compareScoreArray(aScore[i], bScore[i]);\n        // do not return if both are equal\n        if (comp)\n            return comp;\n        i++;\n    }\n    if (Math.abs(bScore.length - aScore.length) === 1) {\n        if (isLastScoreNegative(aScore))\n            return 1;\n        if (isLastScoreNegative(bScore))\n            return -1;\n    }\n    // if a and b share the same score entries but b has more, sort b first\n    return bScore.length - aScore.length;\n    // this is the ternary version\n    // return aScore.length < bScore.length\n    //   ? 1\n    //   : aScore.length > bScore.length\n    //   ? -1\n    //   : 0\n}\n/**\n * This allows detecting splats at the end of a path: /home/<USER>\n *\n * @param score - score to check\n * @returns true if the last entry is negative\n */\nfunction isLastScoreNegative(score) {\n    const last = score[score.length - 1];\n    return score.length > 0 && last[last.length - 1] < 0;\n}\n\nconst ROOT_TOKEN = {\n    type: 0 /* TokenType.Static */,\n    value: '',\n};\nconst VALID_PARAM_RE = /[a-zA-Z0-9_]/;\n// After some profiling, the cache seems to be unnecessary because tokenizePath\n// (the slowest part of adding a route) is very fast\n// const tokenCache = new Map<string, Token[][]>()\nfunction tokenizePath(path) {\n    if (!path)\n        return [[]];\n    if (path === '/')\n        return [[ROOT_TOKEN]];\n    if (!path.startsWith('/')) {\n        throw new Error((process.env.NODE_ENV !== 'production')\n            ? `Route paths should start with a \"/\": \"${path}\" should be \"/${path}\".`\n            : `Invalid path \"${path}\"`);\n    }\n    // if (tokenCache.has(path)) return tokenCache.get(path)!\n    function crash(message) {\n        throw new Error(`ERR (${state})/\"${buffer}\": ${message}`);\n    }\n    let state = 0 /* TokenizerState.Static */;\n    let previousState = state;\n    const tokens = [];\n    // the segment will always be valid because we get into the initial state\n    // with the leading /\n    let segment;\n    function finalizeSegment() {\n        if (segment)\n            tokens.push(segment);\n        segment = [];\n    }\n    // index on the path\n    let i = 0;\n    // char at index\n    let char;\n    // buffer of the value read\n    let buffer = '';\n    // custom regexp for a param\n    let customRe = '';\n    function consumeBuffer() {\n        if (!buffer)\n            return;\n        if (state === 0 /* TokenizerState.Static */) {\n            segment.push({\n                type: 0 /* TokenType.Static */,\n                value: buffer,\n            });\n        }\n        else if (state === 1 /* TokenizerState.Param */ ||\n            state === 2 /* TokenizerState.ParamRegExp */ ||\n            state === 3 /* TokenizerState.ParamRegExpEnd */) {\n            if (segment.length > 1 && (char === '*' || char === '+'))\n                crash(`A repeatable param (${buffer}) must be alone in its segment. eg: '/:ids+.`);\n            segment.push({\n                type: 1 /* TokenType.Param */,\n                value: buffer,\n                regexp: customRe,\n                repeatable: char === '*' || char === '+',\n                optional: char === '*' || char === '?',\n            });\n        }\n        else {\n            crash('Invalid state to consume buffer');\n        }\n        buffer = '';\n    }\n    function addCharToBuffer() {\n        buffer += char;\n    }\n    while (i < path.length) {\n        char = path[i++];\n        if (char === '\\\\' && state !== 2 /* TokenizerState.ParamRegExp */) {\n            previousState = state;\n            state = 4 /* TokenizerState.EscapeNext */;\n            continue;\n        }\n        switch (state) {\n            case 0 /* TokenizerState.Static */:\n                if (char === '/') {\n                    if (buffer) {\n                        consumeBuffer();\n                    }\n                    finalizeSegment();\n                }\n                else if (char === ':') {\n                    consumeBuffer();\n                    state = 1 /* TokenizerState.Param */;\n                }\n                else {\n                    addCharToBuffer();\n                }\n                break;\n            case 4 /* TokenizerState.EscapeNext */:\n                addCharToBuffer();\n                state = previousState;\n                break;\n            case 1 /* TokenizerState.Param */:\n                if (char === '(') {\n                    state = 2 /* TokenizerState.ParamRegExp */;\n                }\n                else if (VALID_PARAM_RE.test(char)) {\n                    addCharToBuffer();\n                }\n                else {\n                    consumeBuffer();\n                    state = 0 /* TokenizerState.Static */;\n                    // go back one character if we were not modifying\n                    if (char !== '*' && char !== '?' && char !== '+')\n                        i--;\n                }\n                break;\n            case 2 /* TokenizerState.ParamRegExp */:\n                // TODO: is it worth handling nested regexp? like :p(?:prefix_([^/]+)_suffix)\n                // it already works by escaping the closing )\n                // https://paths.esm.dev/?p=AAMeJbiAwQEcDKbAoAAkP60PG2R6QAvgNaA6AFACM2ABuQBB#\n                // is this really something people need since you can also write\n                // /prefix_:p()_suffix\n                if (char === ')') {\n                    // handle the escaped )\n                    if (customRe[customRe.length - 1] == '\\\\')\n                        customRe = customRe.slice(0, -1) + char;\n                    else\n                        state = 3 /* TokenizerState.ParamRegExpEnd */;\n                }\n                else {\n                    customRe += char;\n                }\n                break;\n            case 3 /* TokenizerState.ParamRegExpEnd */:\n                // same as finalizing a param\n                consumeBuffer();\n                state = 0 /* TokenizerState.Static */;\n                // go back one character if we were not modifying\n                if (char !== '*' && char !== '?' && char !== '+')\n                    i--;\n                customRe = '';\n                break;\n            default:\n                crash('Unknown state');\n                break;\n        }\n    }\n    if (state === 2 /* TokenizerState.ParamRegExp */)\n        crash(`Unfinished custom RegExp for param \"${buffer}\"`);\n    consumeBuffer();\n    finalizeSegment();\n    // tokenCache.set(path, tokens)\n    return tokens;\n}\n\nfunction createRouteRecordMatcher(record, parent, options) {\n    const parser = tokensToParser(tokenizePath(record.path), options);\n    // warn against params with the same name\n    if ((process.env.NODE_ENV !== 'production')) {\n        const existingKeys = new Set();\n        for (const key of parser.keys) {\n            if (existingKeys.has(key.name))\n                warn(`Found duplicated params with name \"${key.name}\" for path \"${record.path}\". Only the last one will be available on \"$route.params\".`);\n            existingKeys.add(key.name);\n        }\n    }\n    const matcher = assign(parser, {\n        record,\n        parent,\n        // these needs to be populated by the parent\n        children: [],\n        alias: [],\n    });\n    if (parent) {\n        // both are aliases or both are not aliases\n        // we don't want to mix them because the order is used when\n        // passing originalRecord in Matcher.addRoute\n        if (!matcher.record.aliasOf === !parent.record.aliasOf)\n            parent.children.push(matcher);\n    }\n    return matcher;\n}\n\n/**\n * Creates a Router Matcher.\n *\n * @internal\n * @param routes - array of initial routes\n * @param globalOptions - global route options\n */\nfunction createRouterMatcher(routes, globalOptions) {\n    // normalized ordered array of matchers\n    const matchers = [];\n    const matcherMap = new Map();\n    globalOptions = mergeOptions({ strict: false, end: true, sensitive: false }, globalOptions);\n    function getRecordMatcher(name) {\n        return matcherMap.get(name);\n    }\n    function addRoute(record, parent, originalRecord) {\n        // used later on to remove by name\n        const isRootAdd = !originalRecord;\n        const mainNormalizedRecord = normalizeRouteRecord(record);\n        if ((process.env.NODE_ENV !== 'production')) {\n            checkChildMissingNameWithEmptyPath(mainNormalizedRecord, parent);\n        }\n        // we might be the child of an alias\n        mainNormalizedRecord.aliasOf = originalRecord && originalRecord.record;\n        const options = mergeOptions(globalOptions, record);\n        // generate an array of records to correctly handle aliases\n        const normalizedRecords = [mainNormalizedRecord];\n        if ('alias' in record) {\n            const aliases = typeof record.alias === 'string' ? [record.alias] : record.alias;\n            for (const alias of aliases) {\n                normalizedRecords.push(\n                // we need to normalize again to ensure the `mods` property\n                // being non enumerable\n                normalizeRouteRecord(assign({}, mainNormalizedRecord, {\n                    // this allows us to hold a copy of the `components` option\n                    // so that async components cache is hold on the original record\n                    components: originalRecord\n                        ? originalRecord.record.components\n                        : mainNormalizedRecord.components,\n                    path: alias,\n                    // we might be the child of an alias\n                    aliasOf: originalRecord\n                        ? originalRecord.record\n                        : mainNormalizedRecord,\n                    // the aliases are always of the same kind as the original since they\n                    // are defined on the same record\n                })));\n            }\n        }\n        let matcher;\n        let originalMatcher;\n        for (const normalizedRecord of normalizedRecords) {\n            const { path } = normalizedRecord;\n            // Build up the path for nested routes if the child isn't an absolute\n            // route. Only add the / delimiter if the child path isn't empty and if the\n            // parent path doesn't have a trailing slash\n            if (parent && path[0] !== '/') {\n                const parentPath = parent.record.path;\n                const connectingSlash = parentPath[parentPath.length - 1] === '/' ? '' : '/';\n                normalizedRecord.path =\n                    parent.record.path + (path && connectingSlash + path);\n            }\n            if ((process.env.NODE_ENV !== 'production') && normalizedRecord.path === '*') {\n                throw new Error('Catch all routes (\"*\") must now be defined using a param with a custom regexp.\\n' +\n                    'See more at https://router.vuejs.org/guide/migration/#Removed-star-or-catch-all-routes.');\n            }\n            // create the object beforehand, so it can be passed to children\n            matcher = createRouteRecordMatcher(normalizedRecord, parent, options);\n            if ((process.env.NODE_ENV !== 'production') && parent && path[0] === '/')\n                checkMissingParamsInAbsolutePath(matcher, parent);\n            // if we are an alias we must tell the original record that we exist,\n            // so we can be removed\n            if (originalRecord) {\n                originalRecord.alias.push(matcher);\n                if ((process.env.NODE_ENV !== 'production')) {\n                    checkSameParams(originalRecord, matcher);\n                }\n            }\n            else {\n                // otherwise, the first record is the original and others are aliases\n                originalMatcher = originalMatcher || matcher;\n                if (originalMatcher !== matcher)\n                    originalMatcher.alias.push(matcher);\n                // remove the route if named and only for the top record (avoid in nested calls)\n                // this works because the original record is the first one\n                if (isRootAdd && record.name && !isAliasRecord(matcher)) {\n                    if ((process.env.NODE_ENV !== 'production')) {\n                        checkSameNameAsAncestor(record, parent);\n                    }\n                    removeRoute(record.name);\n                }\n            }\n            // Avoid adding a record that doesn't display anything. This allows passing through records without a component to\n            // not be reached and pass through the catch all route\n            if (isMatchable(matcher)) {\n                insertMatcher(matcher);\n            }\n            if (mainNormalizedRecord.children) {\n                const children = mainNormalizedRecord.children;\n                for (let i = 0; i < children.length; i++) {\n                    addRoute(children[i], matcher, originalRecord && originalRecord.children[i]);\n                }\n            }\n            // if there was no original record, then the first one was not an alias and all\n            // other aliases (if any) need to reference this record when adding children\n            originalRecord = originalRecord || matcher;\n            // TODO: add normalized records for more flexibility\n            // if (parent && isAliasRecord(originalRecord)) {\n            //   parent.children.push(originalRecord)\n            // }\n        }\n        return originalMatcher\n            ? () => {\n                // since other matchers are aliases, they should be removed by the original matcher\n                removeRoute(originalMatcher);\n            }\n            : noop;\n    }\n    function removeRoute(matcherRef) {\n        if (isRouteName(matcherRef)) {\n            const matcher = matcherMap.get(matcherRef);\n            if (matcher) {\n                matcherMap.delete(matcherRef);\n                matchers.splice(matchers.indexOf(matcher), 1);\n                matcher.children.forEach(removeRoute);\n                matcher.alias.forEach(removeRoute);\n            }\n        }\n        else {\n            const index = matchers.indexOf(matcherRef);\n            if (index > -1) {\n                matchers.splice(index, 1);\n                if (matcherRef.record.name)\n                    matcherMap.delete(matcherRef.record.name);\n                matcherRef.children.forEach(removeRoute);\n                matcherRef.alias.forEach(removeRoute);\n            }\n        }\n    }\n    function getRoutes() {\n        return matchers;\n    }\n    function insertMatcher(matcher) {\n        const index = findInsertionIndex(matcher, matchers);\n        matchers.splice(index, 0, matcher);\n        // only add the original record to the name map\n        if (matcher.record.name && !isAliasRecord(matcher))\n            matcherMap.set(matcher.record.name, matcher);\n    }\n    function resolve(location, currentLocation) {\n        let matcher;\n        let params = {};\n        let path;\n        let name;\n        if ('name' in location && location.name) {\n            matcher = matcherMap.get(location.name);\n            if (!matcher)\n                throw createRouterError(1 /* ErrorTypes.MATCHER_NOT_FOUND */, {\n                    location,\n                });\n            // warn if the user is passing invalid params so they can debug it better when they get removed\n            if ((process.env.NODE_ENV !== 'production')) {\n                const invalidParams = Object.keys(location.params || {}).filter(paramName => !matcher.keys.find(k => k.name === paramName));\n                if (invalidParams.length) {\n                    warn(`Discarded invalid param(s) \"${invalidParams.join('\", \"')}\" when navigating. See https://github.com/vuejs/router/blob/main/packages/router/CHANGELOG.md#414-2022-08-22 for more details.`);\n                }\n            }\n            name = matcher.record.name;\n            params = assign(\n            // paramsFromLocation is a new object\n            paramsFromLocation(currentLocation.params, \n            // only keep params that exist in the resolved location\n            // only keep optional params coming from a parent record\n            matcher.keys\n                .filter(k => !k.optional)\n                .concat(matcher.parent ? matcher.parent.keys.filter(k => k.optional) : [])\n                .map(k => k.name)), \n            // discard any existing params in the current location that do not exist here\n            // #1497 this ensures better active/exact matching\n            location.params &&\n                paramsFromLocation(location.params, matcher.keys.map(k => k.name)));\n            // throws if cannot be stringified\n            path = matcher.stringify(params);\n        }\n        else if (location.path != null) {\n            // no need to resolve the path with the matcher as it was provided\n            // this also allows the user to control the encoding\n            path = location.path;\n            if ((process.env.NODE_ENV !== 'production') && !path.startsWith('/')) {\n                warn(`The Matcher cannot resolve relative paths but received \"${path}\". Unless you directly called \\`matcher.resolve(\"${path}\")\\`, this is probably a bug in vue-router. Please open an issue at https://github.com/vuejs/router/issues/new/choose.`);\n            }\n            matcher = matchers.find(m => m.re.test(path));\n            // matcher should have a value after the loop\n            if (matcher) {\n                // we know the matcher works because we tested the regexp\n                params = matcher.parse(path);\n                name = matcher.record.name;\n            }\n            // location is a relative path\n        }\n        else {\n            // match by name or path of current route\n            matcher = currentLocation.name\n                ? matcherMap.get(currentLocation.name)\n                : matchers.find(m => m.re.test(currentLocation.path));\n            if (!matcher)\n                throw createRouterError(1 /* ErrorTypes.MATCHER_NOT_FOUND */, {\n                    location,\n                    currentLocation,\n                });\n            name = matcher.record.name;\n            // since we are navigating to the same location, we don't need to pick the\n            // params like when `name` is provided\n            params = assign({}, currentLocation.params, location.params);\n            path = matcher.stringify(params);\n        }\n        const matched = [];\n        let parentMatcher = matcher;\n        while (parentMatcher) {\n            // reversed order so parents are at the beginning\n            matched.unshift(parentMatcher.record);\n            parentMatcher = parentMatcher.parent;\n        }\n        return {\n            name,\n            path,\n            params,\n            matched,\n            meta: mergeMetaFields(matched),\n        };\n    }\n    // add initial routes\n    routes.forEach(route => addRoute(route));\n    function clearRoutes() {\n        matchers.length = 0;\n        matcherMap.clear();\n    }\n    return {\n        addRoute,\n        resolve,\n        removeRoute,\n        clearRoutes,\n        getRoutes,\n        getRecordMatcher,\n    };\n}\nfunction paramsFromLocation(params, keys) {\n    const newParams = {};\n    for (const key of keys) {\n        if (key in params)\n            newParams[key] = params[key];\n    }\n    return newParams;\n}\n/**\n * Normalizes a RouteRecordRaw. Creates a copy\n *\n * @param record\n * @returns the normalized version\n */\nfunction normalizeRouteRecord(record) {\n    const normalized = {\n        path: record.path,\n        redirect: record.redirect,\n        name: record.name,\n        meta: record.meta || {},\n        aliasOf: record.aliasOf,\n        beforeEnter: record.beforeEnter,\n        props: normalizeRecordProps(record),\n        children: record.children || [],\n        instances: {},\n        leaveGuards: new Set(),\n        updateGuards: new Set(),\n        enterCallbacks: {},\n        // must be declared afterwards\n        // mods: {},\n        components: 'components' in record\n            ? record.components || null\n            : record.component && { default: record.component },\n    };\n    // mods contain modules and shouldn't be copied,\n    // logged or anything. It's just used for internal\n    // advanced use cases like data loaders\n    Object.defineProperty(normalized, 'mods', {\n        value: {},\n    });\n    return normalized;\n}\n/**\n * Normalize the optional `props` in a record to always be an object similar to\n * components. Also accept a boolean for components.\n * @param record\n */\nfunction normalizeRecordProps(record) {\n    const propsObject = {};\n    // props does not exist on redirect records, but we can set false directly\n    const props = record.props || false;\n    if ('component' in record) {\n        propsObject.default = props;\n    }\n    else {\n        // NOTE: we could also allow a function to be applied to every component.\n        // Would need user feedback for use cases\n        for (const name in record.components)\n            propsObject[name] = typeof props === 'object' ? props[name] : props;\n    }\n    return propsObject;\n}\n/**\n * Checks if a record or any of its parent is an alias\n * @param record\n */\nfunction isAliasRecord(record) {\n    while (record) {\n        if (record.record.aliasOf)\n            return true;\n        record = record.parent;\n    }\n    return false;\n}\n/**\n * Merge meta fields of an array of records\n *\n * @param matched - array of matched records\n */\nfunction mergeMetaFields(matched) {\n    return matched.reduce((meta, record) => assign(meta, record.meta), {});\n}\nfunction mergeOptions(defaults, partialOptions) {\n    const options = {};\n    for (const key in defaults) {\n        options[key] = key in partialOptions ? partialOptions[key] : defaults[key];\n    }\n    return options;\n}\nfunction isSameParam(a, b) {\n    return (a.name === b.name &&\n        a.optional === b.optional &&\n        a.repeatable === b.repeatable);\n}\n/**\n * Check if a path and its alias have the same required params\n *\n * @param a - original record\n * @param b - alias record\n */\nfunction checkSameParams(a, b) {\n    for (const key of a.keys) {\n        if (!key.optional && !b.keys.find(isSameParam.bind(null, key)))\n            return warn(`Alias \"${b.record.path}\" and the original record: \"${a.record.path}\" must have the exact same param named \"${key.name}\"`);\n    }\n    for (const key of b.keys) {\n        if (!key.optional && !a.keys.find(isSameParam.bind(null, key)))\n            return warn(`Alias \"${b.record.path}\" and the original record: \"${a.record.path}\" must have the exact same param named \"${key.name}\"`);\n    }\n}\n/**\n * A route with a name and a child with an empty path without a name should warn when adding the route\n *\n * @param mainNormalizedRecord - RouteRecordNormalized\n * @param parent - RouteRecordMatcher\n */\nfunction checkChildMissingNameWithEmptyPath(mainNormalizedRecord, parent) {\n    if (parent &&\n        parent.record.name &&\n        !mainNormalizedRecord.name &&\n        !mainNormalizedRecord.path) {\n        warn(`The route named \"${String(parent.record.name)}\" has a child without a name and an empty path. Using that name won't render the empty path child so you probably want to move the name to the child instead. If this is intentional, add a name to the child route to remove the warning.`);\n    }\n}\nfunction checkSameNameAsAncestor(record, parent) {\n    for (let ancestor = parent; ancestor; ancestor = ancestor.parent) {\n        if (ancestor.record.name === record.name) {\n            throw new Error(`A route named \"${String(record.name)}\" has been added as a ${parent === ancestor ? 'child' : 'descendant'} of a route with the same name. Route names must be unique and a nested route cannot use the same name as an ancestor.`);\n        }\n    }\n}\nfunction checkMissingParamsInAbsolutePath(record, parent) {\n    for (const key of parent.keys) {\n        if (!record.keys.find(isSameParam.bind(null, key)))\n            return warn(`Absolute path \"${record.record.path}\" must have the exact same param named \"${key.name}\" as its parent \"${parent.record.path}\".`);\n    }\n}\n/**\n * Performs a binary search to find the correct insertion index for a new matcher.\n *\n * Matchers are primarily sorted by their score. If scores are tied then we also consider parent/child relationships,\n * with descendants coming before ancestors. If there's still a tie, new routes are inserted after existing routes.\n *\n * @param matcher - new matcher to be inserted\n * @param matchers - existing matchers\n */\nfunction findInsertionIndex(matcher, matchers) {\n    // First phase: binary search based on score\n    let lower = 0;\n    let upper = matchers.length;\n    while (lower !== upper) {\n        const mid = (lower + upper) >> 1;\n        const sortOrder = comparePathParserScore(matcher, matchers[mid]);\n        if (sortOrder < 0) {\n            upper = mid;\n        }\n        else {\n            lower = mid + 1;\n        }\n    }\n    // Second phase: check for an ancestor with the same score\n    const insertionAncestor = getInsertionAncestor(matcher);\n    if (insertionAncestor) {\n        upper = matchers.lastIndexOf(insertionAncestor, upper - 1);\n        if ((process.env.NODE_ENV !== 'production') && upper < 0) {\n            // This should never happen\n            warn(`Finding ancestor route \"${insertionAncestor.record.path}\" failed for \"${matcher.record.path}\"`);\n        }\n    }\n    return upper;\n}\nfunction getInsertionAncestor(matcher) {\n    let ancestor = matcher;\n    while ((ancestor = ancestor.parent)) {\n        if (isMatchable(ancestor) &&\n            comparePathParserScore(matcher, ancestor) === 0) {\n            return ancestor;\n        }\n    }\n    return;\n}\n/**\n * Checks if a matcher can be reachable. This means if it's possible to reach it as a route. For example, routes without\n * a component, or name, or redirect, are just used to group other routes.\n * @param matcher\n * @param matcher.record record of the matcher\n * @returns\n */\nfunction isMatchable({ record }) {\n    return !!(record.name ||\n        (record.components && Object.keys(record.components).length) ||\n        record.redirect);\n}\n\n/**\n * Transforms a queryString into a {@link LocationQuery} object. Accept both, a\n * version with the leading `?` and without Should work as URLSearchParams\n\n * @internal\n *\n * @param search - search string to parse\n * @returns a query object\n */\nfunction parseQuery(search) {\n    const query = {};\n    // avoid creating an object with an empty key and empty value\n    // because of split('&')\n    if (search === '' || search === '?')\n        return query;\n    const hasLeadingIM = search[0] === '?';\n    const searchParams = (hasLeadingIM ? search.slice(1) : search).split('&');\n    for (let i = 0; i < searchParams.length; ++i) {\n        // pre decode the + into space\n        const searchParam = searchParams[i].replace(PLUS_RE, ' ');\n        // allow the = character\n        const eqPos = searchParam.indexOf('=');\n        const key = decode(eqPos < 0 ? searchParam : searchParam.slice(0, eqPos));\n        const value = eqPos < 0 ? null : decode(searchParam.slice(eqPos + 1));\n        if (key in query) {\n            // an extra variable for ts types\n            let currentValue = query[key];\n            if (!isArray(currentValue)) {\n                currentValue = query[key] = [currentValue];\n            }\n            currentValue.push(value);\n        }\n        else {\n            query[key] = value;\n        }\n    }\n    return query;\n}\n/**\n * Stringifies a {@link LocationQueryRaw} object. Like `URLSearchParams`, it\n * doesn't prepend a `?`\n *\n * @internal\n *\n * @param query - query object to stringify\n * @returns string version of the query without the leading `?`\n */\nfunction stringifyQuery(query) {\n    let search = '';\n    for (let key in query) {\n        const value = query[key];\n        key = encodeQueryKey(key);\n        if (value == null) {\n            // only null adds the value\n            if (value !== undefined) {\n                search += (search.length ? '&' : '') + key;\n            }\n            continue;\n        }\n        // keep null values\n        const values = isArray(value)\n            ? value.map(v => v && encodeQueryValue(v))\n            : [value && encodeQueryValue(value)];\n        values.forEach(value => {\n            // skip undefined values in arrays as if they were not present\n            // smaller code than using filter\n            if (value !== undefined) {\n                // only append & with non-empty search\n                search += (search.length ? '&' : '') + key;\n                if (value != null)\n                    search += '=' + value;\n            }\n        });\n    }\n    return search;\n}\n/**\n * Transforms a {@link LocationQueryRaw} into a {@link LocationQuery} by casting\n * numbers into strings, removing keys with an undefined value and replacing\n * undefined with null in arrays\n *\n * @param query - query object to normalize\n * @returns a normalized query object\n */\nfunction normalizeQuery(query) {\n    const normalizedQuery = {};\n    for (const key in query) {\n        const value = query[key];\n        if (value !== undefined) {\n            normalizedQuery[key] = isArray(value)\n                ? value.map(v => (v == null ? null : '' + v))\n                : value == null\n                    ? value\n                    : '' + value;\n        }\n    }\n    return normalizedQuery;\n}\n\n/**\n * RouteRecord being rendered by the closest ancestor Router View. Used for\n * `onBeforeRouteUpdate` and `onBeforeRouteLeave`. rvlm stands for Router View\n * Location Matched\n *\n * @internal\n */\nconst matchedRouteKey = Symbol((process.env.NODE_ENV !== 'production') ? 'router view location matched' : '');\n/**\n * Allows overriding the router view depth to control which component in\n * `matched` is rendered. rvd stands for Router View Depth\n *\n * @internal\n */\nconst viewDepthKey = Symbol((process.env.NODE_ENV !== 'production') ? 'router view depth' : '');\n/**\n * Allows overriding the router instance returned by `useRouter` in tests. r\n * stands for router\n *\n * @internal\n */\nconst routerKey = Symbol((process.env.NODE_ENV !== 'production') ? 'router' : '');\n/**\n * Allows overriding the current route returned by `useRoute` in tests. rl\n * stands for route location\n *\n * @internal\n */\nconst routeLocationKey = Symbol((process.env.NODE_ENV !== 'production') ? 'route location' : '');\n/**\n * Allows overriding the current route used by router-view. Internally this is\n * used when the `route` prop is passed.\n *\n * @internal\n */\nconst routerViewLocationKey = Symbol((process.env.NODE_ENV !== 'production') ? 'router view location' : '');\n\n/**\n * Create a list of callbacks that can be reset. Used to create before and after navigation guards list\n */\nfunction useCallbacks() {\n    let handlers = [];\n    function add(handler) {\n        handlers.push(handler);\n        return () => {\n            const i = handlers.indexOf(handler);\n            if (i > -1)\n                handlers.splice(i, 1);\n        };\n    }\n    function reset() {\n        handlers = [];\n    }\n    return {\n        add,\n        list: () => handlers.slice(),\n        reset,\n    };\n}\n\nfunction registerGuard(record, name, guard) {\n    const removeFromList = () => {\n        record[name].delete(guard);\n    };\n    onUnmounted(removeFromList);\n    onDeactivated(removeFromList);\n    onActivated(() => {\n        record[name].add(guard);\n    });\n    record[name].add(guard);\n}\n/**\n * Add a navigation guard that triggers whenever the component for the current\n * location is about to be left. Similar to {@link beforeRouteLeave} but can be\n * used in any component. The guard is removed when the component is unmounted.\n *\n * @param leaveGuard - {@link NavigationGuard}\n */\nfunction onBeforeRouteLeave(leaveGuard) {\n    if ((process.env.NODE_ENV !== 'production') && !getCurrentInstance()) {\n        warn('getCurrentInstance() returned null. onBeforeRouteLeave() must be called at the top of a setup function');\n        return;\n    }\n    const activeRecord = inject(matchedRouteKey, \n    // to avoid warning\n    {}).value;\n    if (!activeRecord) {\n        (process.env.NODE_ENV !== 'production') &&\n            warn('No active route record was found when calling `onBeforeRouteLeave()`. Make sure you call this function inside a component child of <router-view>. Maybe you called it inside of App.vue?');\n        return;\n    }\n    registerGuard(activeRecord, 'leaveGuards', leaveGuard);\n}\n/**\n * Add a navigation guard that triggers whenever the current location is about\n * to be updated. Similar to {@link beforeRouteUpdate} but can be used in any\n * component. The guard is removed when the component is unmounted.\n *\n * @param updateGuard - {@link NavigationGuard}\n */\nfunction onBeforeRouteUpdate(updateGuard) {\n    if ((process.env.NODE_ENV !== 'production') && !getCurrentInstance()) {\n        warn('getCurrentInstance() returned null. onBeforeRouteUpdate() must be called at the top of a setup function');\n        return;\n    }\n    const activeRecord = inject(matchedRouteKey, \n    // to avoid warning\n    {}).value;\n    if (!activeRecord) {\n        (process.env.NODE_ENV !== 'production') &&\n            warn('No active route record was found when calling `onBeforeRouteUpdate()`. Make sure you call this function inside a component child of <router-view>. Maybe you called it inside of App.vue?');\n        return;\n    }\n    registerGuard(activeRecord, 'updateGuards', updateGuard);\n}\nfunction guardToPromiseFn(guard, to, from, record, name, runWithContext = fn => fn()) {\n    // keep a reference to the enterCallbackArray to prevent pushing callbacks if a new navigation took place\n    const enterCallbackArray = record &&\n        // name is defined if record is because of the function overload\n        (record.enterCallbacks[name] = record.enterCallbacks[name] || []);\n    return () => new Promise((resolve, reject) => {\n        const next = (valid) => {\n            if (valid === false) {\n                reject(createRouterError(4 /* ErrorTypes.NAVIGATION_ABORTED */, {\n                    from,\n                    to,\n                }));\n            }\n            else if (valid instanceof Error) {\n                reject(valid);\n            }\n            else if (isRouteLocation(valid)) {\n                reject(createRouterError(2 /* ErrorTypes.NAVIGATION_GUARD_REDIRECT */, {\n                    from: to,\n                    to: valid,\n                }));\n            }\n            else {\n                if (enterCallbackArray &&\n                    // since enterCallbackArray is truthy, both record and name also are\n                    record.enterCallbacks[name] === enterCallbackArray &&\n                    typeof valid === 'function') {\n                    enterCallbackArray.push(valid);\n                }\n                resolve();\n            }\n        };\n        // wrapping with Promise.resolve allows it to work with both async and sync guards\n        const guardReturn = runWithContext(() => guard.call(record && record.instances[name], to, from, (process.env.NODE_ENV !== 'production') ? canOnlyBeCalledOnce(next, to, from) : next));\n        let guardCall = Promise.resolve(guardReturn);\n        if (guard.length < 3)\n            guardCall = guardCall.then(next);\n        if ((process.env.NODE_ENV !== 'production') && guard.length > 2) {\n            const message = `The \"next\" callback was never called inside of ${guard.name ? '\"' + guard.name + '\"' : ''}:\\n${guard.toString()}\\n. If you are returning a value instead of calling \"next\", make sure to remove the \"next\" parameter from your function.`;\n            if (typeof guardReturn === 'object' && 'then' in guardReturn) {\n                guardCall = guardCall.then(resolvedValue => {\n                    // @ts-expect-error: _called is added at canOnlyBeCalledOnce\n                    if (!next._called) {\n                        warn(message);\n                        return Promise.reject(new Error('Invalid navigation guard'));\n                    }\n                    return resolvedValue;\n                });\n            }\n            else if (guardReturn !== undefined) {\n                // @ts-expect-error: _called is added at canOnlyBeCalledOnce\n                if (!next._called) {\n                    warn(message);\n                    reject(new Error('Invalid navigation guard'));\n                    return;\n                }\n            }\n        }\n        guardCall.catch(err => reject(err));\n    });\n}\nfunction canOnlyBeCalledOnce(next, to, from) {\n    let called = 0;\n    return function () {\n        if (called++ === 1)\n            warn(`The \"next\" callback was called more than once in one navigation guard when going from \"${from.fullPath}\" to \"${to.fullPath}\". It should be called exactly one time in each navigation guard. This will fail in production.`);\n        // @ts-expect-error: we put it in the original one because it's easier to check\n        next._called = true;\n        if (called === 1)\n            next.apply(null, arguments);\n    };\n}\nfunction extractComponentsGuards(matched, guardType, to, from, runWithContext = fn => fn()) {\n    const guards = [];\n    for (const record of matched) {\n        if ((process.env.NODE_ENV !== 'production') && !record.components && !record.children.length) {\n            warn(`Record with path \"${record.path}\" is either missing a \"component(s)\"` +\n                ` or \"children\" property.`);\n        }\n        for (const name in record.components) {\n            let rawComponent = record.components[name];\n            if ((process.env.NODE_ENV !== 'production')) {\n                if (!rawComponent ||\n                    (typeof rawComponent !== 'object' &&\n                        typeof rawComponent !== 'function')) {\n                    warn(`Component \"${name}\" in record with path \"${record.path}\" is not` +\n                        ` a valid component. Received \"${String(rawComponent)}\".`);\n                    // throw to ensure we stop here but warn to ensure the message isn't\n                    // missed by the user\n                    throw new Error('Invalid route component');\n                }\n                else if ('then' in rawComponent) {\n                    // warn if user wrote import('/component.vue') instead of () =>\n                    // import('./component.vue')\n                    warn(`Component \"${name}\" in record with path \"${record.path}\" is a ` +\n                        `Promise instead of a function that returns a Promise. Did you ` +\n                        `write \"import('./MyPage.vue')\" instead of ` +\n                        `\"() => import('./MyPage.vue')\" ? This will break in ` +\n                        `production if not fixed.`);\n                    const promise = rawComponent;\n                    rawComponent = () => promise;\n                }\n                else if (rawComponent.__asyncLoader &&\n                    // warn only once per component\n                    !rawComponent.__warnedDefineAsync) {\n                    rawComponent.__warnedDefineAsync = true;\n                    warn(`Component \"${name}\" in record with path \"${record.path}\" is defined ` +\n                        `using \"defineAsyncComponent()\". ` +\n                        `Write \"() => import('./MyPage.vue')\" instead of ` +\n                        `\"defineAsyncComponent(() => import('./MyPage.vue'))\".`);\n                }\n            }\n            // skip update and leave guards if the route component is not mounted\n            if (guardType !== 'beforeRouteEnter' && !record.instances[name])\n                continue;\n            if (isRouteComponent(rawComponent)) {\n                // __vccOpts is added by vue-class-component and contain the regular options\n                const options = rawComponent.__vccOpts || rawComponent;\n                const guard = options[guardType];\n                guard &&\n                    guards.push(guardToPromiseFn(guard, to, from, record, name, runWithContext));\n            }\n            else {\n                // start requesting the chunk already\n                let componentPromise = rawComponent();\n                if ((process.env.NODE_ENV !== 'production') && !('catch' in componentPromise)) {\n                    warn(`Component \"${name}\" in record with path \"${record.path}\" is a function that does not return a Promise. If you were passing a functional component, make sure to add a \"displayName\" to the component. This will break in production if not fixed.`);\n                    componentPromise = Promise.resolve(componentPromise);\n                }\n                guards.push(() => componentPromise.then(resolved => {\n                    if (!resolved)\n                        throw new Error(`Couldn't resolve component \"${name}\" at \"${record.path}\"`);\n                    const resolvedComponent = isESModule(resolved)\n                        ? resolved.default\n                        : resolved;\n                    // keep the resolved module for plugins like data loaders\n                    record.mods[name] = resolved;\n                    // replace the function with the resolved component\n                    // cannot be null or undefined because we went into the for loop\n                    record.components[name] = resolvedComponent;\n                    // __vccOpts is added by vue-class-component and contain the regular options\n                    const options = resolvedComponent.__vccOpts || resolvedComponent;\n                    const guard = options[guardType];\n                    return (guard &&\n                        guardToPromiseFn(guard, to, from, record, name, runWithContext)());\n                }));\n            }\n        }\n    }\n    return guards;\n}\n/**\n * Ensures a route is loaded, so it can be passed as o prop to `<RouterView>`.\n *\n * @param route - resolved route to load\n */\nfunction loadRouteLocation(route) {\n    return route.matched.every(record => record.redirect)\n        ? Promise.reject(new Error('Cannot load a route that redirects.'))\n        : Promise.all(route.matched.map(record => record.components &&\n            Promise.all(Object.keys(record.components).reduce((promises, name) => {\n                const rawComponent = record.components[name];\n                if (typeof rawComponent === 'function' &&\n                    !('displayName' in rawComponent)) {\n                    promises.push(rawComponent().then(resolved => {\n                        if (!resolved)\n                            return Promise.reject(new Error(`Couldn't resolve component \"${name}\" at \"${record.path}\". Ensure you passed a function that returns a promise.`));\n                        const resolvedComponent = isESModule(resolved)\n                            ? resolved.default\n                            : resolved;\n                        // keep the resolved module for plugins like data loaders\n                        record.mods[name] = resolved;\n                        // replace the function with the resolved component\n                        // cannot be null or undefined because we went into the for loop\n                        record.components[name] = resolvedComponent;\n                        return;\n                    }));\n                }\n                return promises;\n            }, [])))).then(() => route);\n}\n\n// TODO: we could allow currentRoute as a prop to expose `isActive` and\n// `isExactActive` behavior should go through an RFC\n/**\n * Returns the internal behavior of a {@link RouterLink} without the rendering part.\n *\n * @param props - a `to` location and an optional `replace` flag\n */\nfunction useLink(props) {\n    const router = inject(routerKey);\n    const currentRoute = inject(routeLocationKey);\n    let hasPrevious = false;\n    let previousTo = null;\n    const route = computed(() => {\n        const to = unref(props.to);\n        if ((process.env.NODE_ENV !== 'production') && (!hasPrevious || to !== previousTo)) {\n            if (!isRouteLocation(to)) {\n                if (hasPrevious) {\n                    warn(`Invalid value for prop \"to\" in useLink()\\n- to:`, to, `\\n- previous to:`, previousTo, `\\n- props:`, props);\n                }\n                else {\n                    warn(`Invalid value for prop \"to\" in useLink()\\n- to:`, to, `\\n- props:`, props);\n                }\n            }\n            previousTo = to;\n            hasPrevious = true;\n        }\n        return router.resolve(to);\n    });\n    const activeRecordIndex = computed(() => {\n        const { matched } = route.value;\n        const { length } = matched;\n        const routeMatched = matched[length - 1];\n        const currentMatched = currentRoute.matched;\n        if (!routeMatched || !currentMatched.length)\n            return -1;\n        const index = currentMatched.findIndex(isSameRouteRecord.bind(null, routeMatched));\n        if (index > -1)\n            return index;\n        // possible parent record\n        const parentRecordPath = getOriginalPath(matched[length - 2]);\n        return (\n        // we are dealing with nested routes\n        length > 1 &&\n            // if the parent and matched route have the same path, this link is\n            // referring to the empty child. Or we currently are on a different\n            // child of the same parent\n            getOriginalPath(routeMatched) === parentRecordPath &&\n            // avoid comparing the child with its parent\n            currentMatched[currentMatched.length - 1].path !== parentRecordPath\n            ? currentMatched.findIndex(isSameRouteRecord.bind(null, matched[length - 2]))\n            : index);\n    });\n    const isActive = computed(() => activeRecordIndex.value > -1 &&\n        includesParams(currentRoute.params, route.value.params));\n    const isExactActive = computed(() => activeRecordIndex.value > -1 &&\n        activeRecordIndex.value === currentRoute.matched.length - 1 &&\n        isSameRouteLocationParams(currentRoute.params, route.value.params));\n    function navigate(e = {}) {\n        if (guardEvent(e)) {\n            const p = router[unref(props.replace) ? 'replace' : 'push'](unref(props.to)\n            // avoid uncaught errors are they are logged anyway\n            ).catch(noop);\n            if (props.viewTransition &&\n                typeof document !== 'undefined' &&\n                'startViewTransition' in document) {\n                document.startViewTransition(() => p);\n            }\n            return p;\n        }\n        return Promise.resolve();\n    }\n    // devtools only\n    if (((process.env.NODE_ENV !== 'production') || __VUE_PROD_DEVTOOLS__) && isBrowser) {\n        const instance = getCurrentInstance();\n        if (instance) {\n            const linkContextDevtools = {\n                route: route.value,\n                isActive: isActive.value,\n                isExactActive: isExactActive.value,\n                error: null,\n            };\n            // @ts-expect-error: this is internal\n            instance.__vrl_devtools = instance.__vrl_devtools || [];\n            // @ts-expect-error: this is internal\n            instance.__vrl_devtools.push(linkContextDevtools);\n            watchEffect(() => {\n                linkContextDevtools.route = route.value;\n                linkContextDevtools.isActive = isActive.value;\n                linkContextDevtools.isExactActive = isExactActive.value;\n                linkContextDevtools.error = isRouteLocation(unref(props.to))\n                    ? null\n                    : 'Invalid \"to\" value';\n            }, { flush: 'post' });\n        }\n    }\n    /**\n     * NOTE: update {@link _RouterLinkI}'s `$slots` type when updating this\n     */\n    return {\n        route,\n        href: computed(() => route.value.href),\n        isActive,\n        isExactActive,\n        navigate,\n    };\n}\nfunction preferSingleVNode(vnodes) {\n    return vnodes.length === 1 ? vnodes[0] : vnodes;\n}\nconst RouterLinkImpl = /*#__PURE__*/ defineComponent({\n    name: 'RouterLink',\n    compatConfig: { MODE: 3 },\n    props: {\n        to: {\n            type: [String, Object],\n            required: true,\n        },\n        replace: Boolean,\n        activeClass: String,\n        // inactiveClass: String,\n        exactActiveClass: String,\n        custom: Boolean,\n        ariaCurrentValue: {\n            type: String,\n            default: 'page',\n        },\n        viewTransition: Boolean,\n    },\n    useLink,\n    setup(props, { slots }) {\n        const link = reactive(useLink(props));\n        const { options } = inject(routerKey);\n        const elClass = computed(() => ({\n            [getLinkClass(props.activeClass, options.linkActiveClass, 'router-link-active')]: link.isActive,\n            // [getLinkClass(\n            //   props.inactiveClass,\n            //   options.linkInactiveClass,\n            //   'router-link-inactive'\n            // )]: !link.isExactActive,\n            [getLinkClass(props.exactActiveClass, options.linkExactActiveClass, 'router-link-exact-active')]: link.isExactActive,\n        }));\n        return () => {\n            const children = slots.default && preferSingleVNode(slots.default(link));\n            return props.custom\n                ? children\n                : h('a', {\n                    'aria-current': link.isExactActive\n                        ? props.ariaCurrentValue\n                        : null,\n                    href: link.href,\n                    // this would override user added attrs but Vue will still add\n                    // the listener, so we end up triggering both\n                    onClick: link.navigate,\n                    class: elClass.value,\n                }, children);\n        };\n    },\n});\n// export the public type for h/tsx inference\n// also to avoid inline import() in generated d.ts files\n/**\n * Component to render a link that triggers a navigation on click.\n */\nconst RouterLink = RouterLinkImpl;\nfunction guardEvent(e) {\n    // don't redirect with control keys\n    if (e.metaKey || e.altKey || e.ctrlKey || e.shiftKey)\n        return;\n    // don't redirect when preventDefault called\n    if (e.defaultPrevented)\n        return;\n    // don't redirect on right click\n    if (e.button !== undefined && e.button !== 0)\n        return;\n    // don't redirect if `target=\"_blank\"`\n    // @ts-expect-error getAttribute does exist\n    if (e.currentTarget && e.currentTarget.getAttribute) {\n        // @ts-expect-error getAttribute exists\n        const target = e.currentTarget.getAttribute('target');\n        if (/\\b_blank\\b/i.test(target))\n            return;\n    }\n    // this may be a Weex event which doesn't have this method\n    if (e.preventDefault)\n        e.preventDefault();\n    return true;\n}\nfunction includesParams(outer, inner) {\n    for (const key in inner) {\n        const innerValue = inner[key];\n        const outerValue = outer[key];\n        if (typeof innerValue === 'string') {\n            if (innerValue !== outerValue)\n                return false;\n        }\n        else {\n            if (!isArray(outerValue) ||\n                outerValue.length !== innerValue.length ||\n                innerValue.some((value, i) => value !== outerValue[i]))\n                return false;\n        }\n    }\n    return true;\n}\n/**\n * Get the original path value of a record by following its aliasOf\n * @param record\n */\nfunction getOriginalPath(record) {\n    return record ? (record.aliasOf ? record.aliasOf.path : record.path) : '';\n}\n/**\n * Utility class to get the active class based on defaults.\n * @param propClass\n * @param globalClass\n * @param defaultClass\n */\nconst getLinkClass = (propClass, globalClass, defaultClass) => propClass != null\n    ? propClass\n    : globalClass != null\n        ? globalClass\n        : defaultClass;\n\nconst RouterViewImpl = /*#__PURE__*/ defineComponent({\n    name: 'RouterView',\n    // #674 we manually inherit them\n    inheritAttrs: false,\n    props: {\n        name: {\n            type: String,\n            default: 'default',\n        },\n        route: Object,\n    },\n    // Better compat for @vue/compat users\n    // https://github.com/vuejs/router/issues/1315\n    compatConfig: { MODE: 3 },\n    setup(props, { attrs, slots }) {\n        (process.env.NODE_ENV !== 'production') && warnDeprecatedUsage();\n        const injectedRoute = inject(routerViewLocationKey);\n        const routeToDisplay = computed(() => props.route || injectedRoute.value);\n        const injectedDepth = inject(viewDepthKey, 0);\n        // The depth changes based on empty components option, which allows passthrough routes e.g. routes with children\n        // that are used to reuse the `path` property\n        const depth = computed(() => {\n            let initialDepth = unref(injectedDepth);\n            const { matched } = routeToDisplay.value;\n            let matchedRoute;\n            while ((matchedRoute = matched[initialDepth]) &&\n                !matchedRoute.components) {\n                initialDepth++;\n            }\n            return initialDepth;\n        });\n        const matchedRouteRef = computed(() => routeToDisplay.value.matched[depth.value]);\n        provide(viewDepthKey, computed(() => depth.value + 1));\n        provide(matchedRouteKey, matchedRouteRef);\n        provide(routerViewLocationKey, routeToDisplay);\n        const viewRef = ref();\n        // watch at the same time the component instance, the route record we are\n        // rendering, and the name\n        watch(() => [viewRef.value, matchedRouteRef.value, props.name], ([instance, to, name], [oldInstance, from, oldName]) => {\n            // copy reused instances\n            if (to) {\n                // this will update the instance for new instances as well as reused\n                // instances when navigating to a new route\n                to.instances[name] = instance;\n                // the component instance is reused for a different route or name, so\n                // we copy any saved update or leave guards. With async setup, the\n                // mounting component will mount before the matchedRoute changes,\n                // making instance === oldInstance, so we check if guards have been\n                // added before. This works because we remove guards when\n                // unmounting/deactivating components\n                if (from && from !== to && instance && instance === oldInstance) {\n                    if (!to.leaveGuards.size) {\n                        to.leaveGuards = from.leaveGuards;\n                    }\n                    if (!to.updateGuards.size) {\n                        to.updateGuards = from.updateGuards;\n                    }\n                }\n            }\n            // trigger beforeRouteEnter next callbacks\n            if (instance &&\n                to &&\n                // if there is no instance but to and from are the same this might be\n                // the first visit\n                (!from || !isSameRouteRecord(to, from) || !oldInstance)) {\n                (to.enterCallbacks[name] || []).forEach(callback => callback(instance));\n            }\n        }, { flush: 'post' });\n        return () => {\n            const route = routeToDisplay.value;\n            // we need the value at the time we render because when we unmount, we\n            // navigated to a different location so the value is different\n            const currentName = props.name;\n            const matchedRoute = matchedRouteRef.value;\n            const ViewComponent = matchedRoute && matchedRoute.components[currentName];\n            if (!ViewComponent) {\n                return normalizeSlot(slots.default, { Component: ViewComponent, route });\n            }\n            // props from route configuration\n            const routePropsOption = matchedRoute.props[currentName];\n            const routeProps = routePropsOption\n                ? routePropsOption === true\n                    ? route.params\n                    : typeof routePropsOption === 'function'\n                        ? routePropsOption(route)\n                        : routePropsOption\n                : null;\n            const onVnodeUnmounted = vnode => {\n                // remove the instance reference to prevent leak\n                if (vnode.component.isUnmounted) {\n                    matchedRoute.instances[currentName] = null;\n                }\n            };\n            const component = h(ViewComponent, assign({}, routeProps, attrs, {\n                onVnodeUnmounted,\n                ref: viewRef,\n            }));\n            if (((process.env.NODE_ENV !== 'production') || __VUE_PROD_DEVTOOLS__) &&\n                isBrowser &&\n                component.ref) {\n                // TODO: can display if it's an alias, its props\n                const info = {\n                    depth: depth.value,\n                    name: matchedRoute.name,\n                    path: matchedRoute.path,\n                    meta: matchedRoute.meta,\n                };\n                const internalInstances = isArray(component.ref)\n                    ? component.ref.map(r => r.i)\n                    : [component.ref.i];\n                internalInstances.forEach(instance => {\n                    // @ts-expect-error\n                    instance.__vrv_devtools = info;\n                });\n            }\n            return (\n            // pass the vnode to the slot as a prop.\n            // h and <component :is=\"...\"> both accept vnodes\n            normalizeSlot(slots.default, { Component: component, route }) ||\n                component);\n        };\n    },\n});\nfunction normalizeSlot(slot, data) {\n    if (!slot)\n        return null;\n    const slotContent = slot(data);\n    return slotContent.length === 1 ? slotContent[0] : slotContent;\n}\n// export the public type for h/tsx inference\n// also to avoid inline import() in generated d.ts files\n/**\n * Component to display the current route the user is at.\n */\nconst RouterView = RouterViewImpl;\n// warn against deprecated usage with <transition> & <keep-alive>\n// due to functional component being no longer eager in Vue 3\nfunction warnDeprecatedUsage() {\n    const instance = getCurrentInstance();\n    const parentName = instance.parent && instance.parent.type.name;\n    const parentSubTreeType = instance.parent && instance.parent.subTree && instance.parent.subTree.type;\n    if (parentName &&\n        (parentName === 'KeepAlive' || parentName.includes('Transition')) &&\n        typeof parentSubTreeType === 'object' &&\n        parentSubTreeType.name === 'RouterView') {\n        const comp = parentName === 'KeepAlive' ? 'keep-alive' : 'transition';\n        warn(`<router-view> can no longer be used directly inside <transition> or <keep-alive>.\\n` +\n            `Use slot props instead:\\n\\n` +\n            `<router-view v-slot=\"{ Component }\">\\n` +\n            `  <${comp}>\\n` +\n            `    <component :is=\"Component\" />\\n` +\n            `  </${comp}>\\n` +\n            `</router-view>`);\n    }\n}\n\n/**\n * Copies a route location and removes any problematic properties that cannot be shown in devtools (e.g. Vue instances).\n *\n * @param routeLocation - routeLocation to format\n * @param tooltip - optional tooltip\n * @returns a copy of the routeLocation\n */\nfunction formatRouteLocation(routeLocation, tooltip) {\n    const copy = assign({}, routeLocation, {\n        // remove variables that can contain vue instances\n        matched: routeLocation.matched.map(matched => omit(matched, ['instances', 'children', 'aliasOf'])),\n    });\n    return {\n        _custom: {\n            type: null,\n            readOnly: true,\n            display: routeLocation.fullPath,\n            tooltip,\n            value: copy,\n        },\n    };\n}\nfunction formatDisplay(display) {\n    return {\n        _custom: {\n            display,\n        },\n    };\n}\n// to support multiple router instances\nlet routerId = 0;\nfunction addDevtools(app, router, matcher) {\n    // Take over router.beforeEach and afterEach\n    // make sure we are not registering the devtool twice\n    if (router.__hasDevtools)\n        return;\n    router.__hasDevtools = true;\n    // increment to support multiple router instances\n    const id = routerId++;\n    setupDevtoolsPlugin({\n        id: 'org.vuejs.router' + (id ? '.' + id : ''),\n        label: 'Vue Router',\n        packageName: 'vue-router',\n        homepage: 'https://router.vuejs.org',\n        logo: 'https://router.vuejs.org/logo.png',\n        componentStateTypes: ['Routing'],\n        app,\n    }, api => {\n        if (typeof api.now !== 'function') {\n            console.warn('[Vue Router]: You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html.');\n        }\n        // display state added by the router\n        api.on.inspectComponent((payload, ctx) => {\n            if (payload.instanceData) {\n                payload.instanceData.state.push({\n                    type: 'Routing',\n                    key: '$route',\n                    editable: false,\n                    value: formatRouteLocation(router.currentRoute.value, 'Current Route'),\n                });\n            }\n        });\n        // mark router-link as active and display tags on router views\n        api.on.visitComponentTree(({ treeNode: node, componentInstance }) => {\n            if (componentInstance.__vrv_devtools) {\n                const info = componentInstance.__vrv_devtools;\n                node.tags.push({\n                    label: (info.name ? `${info.name.toString()}: ` : '') + info.path,\n                    textColor: 0,\n                    tooltip: 'This component is rendered by &lt;router-view&gt;',\n                    backgroundColor: PINK_500,\n                });\n            }\n            // if multiple useLink are used\n            if (isArray(componentInstance.__vrl_devtools)) {\n                componentInstance.__devtoolsApi = api;\n                componentInstance.__vrl_devtools.forEach(devtoolsData => {\n                    let label = devtoolsData.route.path;\n                    let backgroundColor = ORANGE_400;\n                    let tooltip = '';\n                    let textColor = 0;\n                    if (devtoolsData.error) {\n                        label = devtoolsData.error;\n                        backgroundColor = RED_100;\n                        textColor = RED_700;\n                    }\n                    else if (devtoolsData.isExactActive) {\n                        backgroundColor = LIME_500;\n                        tooltip = 'This is exactly active';\n                    }\n                    else if (devtoolsData.isActive) {\n                        backgroundColor = BLUE_600;\n                        tooltip = 'This link is active';\n                    }\n                    node.tags.push({\n                        label,\n                        textColor,\n                        tooltip,\n                        backgroundColor,\n                    });\n                });\n            }\n        });\n        watch(router.currentRoute, () => {\n            // refresh active state\n            refreshRoutesView();\n            api.notifyComponentUpdate();\n            api.sendInspectorTree(routerInspectorId);\n            api.sendInspectorState(routerInspectorId);\n        });\n        const navigationsLayerId = 'router:navigations:' + id;\n        api.addTimelineLayer({\n            id: navigationsLayerId,\n            label: `Router${id ? ' ' + id : ''} Navigations`,\n            color: 0x40a8c4,\n        });\n        // const errorsLayerId = 'router:errors'\n        // api.addTimelineLayer({\n        //   id: errorsLayerId,\n        //   label: 'Router Errors',\n        //   color: 0xea5455,\n        // })\n        router.onError((error, to) => {\n            api.addTimelineEvent({\n                layerId: navigationsLayerId,\n                event: {\n                    title: 'Error during Navigation',\n                    subtitle: to.fullPath,\n                    logType: 'error',\n                    time: api.now(),\n                    data: { error },\n                    groupId: to.meta.__navigationId,\n                },\n            });\n        });\n        // attached to `meta` and used to group events\n        let navigationId = 0;\n        router.beforeEach((to, from) => {\n            const data = {\n                guard: formatDisplay('beforeEach'),\n                from: formatRouteLocation(from, 'Current Location during this navigation'),\n                to: formatRouteLocation(to, 'Target location'),\n            };\n            // Used to group navigations together, hide from devtools\n            Object.defineProperty(to.meta, '__navigationId', {\n                value: navigationId++,\n            });\n            api.addTimelineEvent({\n                layerId: navigationsLayerId,\n                event: {\n                    time: api.now(),\n                    title: 'Start of navigation',\n                    subtitle: to.fullPath,\n                    data,\n                    groupId: to.meta.__navigationId,\n                },\n            });\n        });\n        router.afterEach((to, from, failure) => {\n            const data = {\n                guard: formatDisplay('afterEach'),\n            };\n            if (failure) {\n                data.failure = {\n                    _custom: {\n                        type: Error,\n                        readOnly: true,\n                        display: failure ? failure.message : '',\n                        tooltip: 'Navigation Failure',\n                        value: failure,\n                    },\n                };\n                data.status = formatDisplay('❌');\n            }\n            else {\n                data.status = formatDisplay('✅');\n            }\n            // we set here to have the right order\n            data.from = formatRouteLocation(from, 'Current Location during this navigation');\n            data.to = formatRouteLocation(to, 'Target location');\n            api.addTimelineEvent({\n                layerId: navigationsLayerId,\n                event: {\n                    title: 'End of navigation',\n                    subtitle: to.fullPath,\n                    time: api.now(),\n                    data,\n                    logType: failure ? 'warning' : 'default',\n                    groupId: to.meta.__navigationId,\n                },\n            });\n        });\n        /**\n         * Inspector of Existing routes\n         */\n        const routerInspectorId = 'router-inspector:' + id;\n        api.addInspector({\n            id: routerInspectorId,\n            label: 'Routes' + (id ? ' ' + id : ''),\n            icon: 'book',\n            treeFilterPlaceholder: 'Search routes',\n        });\n        function refreshRoutesView() {\n            // the routes view isn't active\n            if (!activeRoutesPayload)\n                return;\n            const payload = activeRoutesPayload;\n            // children routes will appear as nested\n            let routes = matcher.getRoutes().filter(route => !route.parent ||\n                // these routes have a parent with no component which will not appear in the view\n                // therefore we still need to include them\n                !route.parent.record.components);\n            // reset match state to false\n            routes.forEach(resetMatchStateOnRouteRecord);\n            // apply a match state if there is a payload\n            if (payload.filter) {\n                routes = routes.filter(route => \n                // save matches state based on the payload\n                isRouteMatching(route, payload.filter.toLowerCase()));\n            }\n            // mark active routes\n            routes.forEach(route => markRouteRecordActive(route, router.currentRoute.value));\n            payload.rootNodes = routes.map(formatRouteRecordForInspector);\n        }\n        let activeRoutesPayload;\n        api.on.getInspectorTree(payload => {\n            activeRoutesPayload = payload;\n            if (payload.app === app && payload.inspectorId === routerInspectorId) {\n                refreshRoutesView();\n            }\n        });\n        /**\n         * Display information about the currently selected route record\n         */\n        api.on.getInspectorState(payload => {\n            if (payload.app === app && payload.inspectorId === routerInspectorId) {\n                const routes = matcher.getRoutes();\n                const route = routes.find(route => route.record.__vd_id === payload.nodeId);\n                if (route) {\n                    payload.state = {\n                        options: formatRouteRecordMatcherForStateInspector(route),\n                    };\n                }\n            }\n        });\n        api.sendInspectorTree(routerInspectorId);\n        api.sendInspectorState(routerInspectorId);\n    });\n}\nfunction modifierForKey(key) {\n    if (key.optional) {\n        return key.repeatable ? '*' : '?';\n    }\n    else {\n        return key.repeatable ? '+' : '';\n    }\n}\nfunction formatRouteRecordMatcherForStateInspector(route) {\n    const { record } = route;\n    const fields = [\n        { editable: false, key: 'path', value: record.path },\n    ];\n    if (record.name != null) {\n        fields.push({\n            editable: false,\n            key: 'name',\n            value: record.name,\n        });\n    }\n    fields.push({ editable: false, key: 'regexp', value: route.re });\n    if (route.keys.length) {\n        fields.push({\n            editable: false,\n            key: 'keys',\n            value: {\n                _custom: {\n                    type: null,\n                    readOnly: true,\n                    display: route.keys\n                        .map(key => `${key.name}${modifierForKey(key)}`)\n                        .join(' '),\n                    tooltip: 'Param keys',\n                    value: route.keys,\n                },\n            },\n        });\n    }\n    if (record.redirect != null) {\n        fields.push({\n            editable: false,\n            key: 'redirect',\n            value: record.redirect,\n        });\n    }\n    if (route.alias.length) {\n        fields.push({\n            editable: false,\n            key: 'aliases',\n            value: route.alias.map(alias => alias.record.path),\n        });\n    }\n    if (Object.keys(route.record.meta).length) {\n        fields.push({\n            editable: false,\n            key: 'meta',\n            value: route.record.meta,\n        });\n    }\n    fields.push({\n        key: 'score',\n        editable: false,\n        value: {\n            _custom: {\n                type: null,\n                readOnly: true,\n                display: route.score.map(score => score.join(', ')).join(' | '),\n                tooltip: 'Score used to sort routes',\n                value: route.score,\n            },\n        },\n    });\n    return fields;\n}\n/**\n * Extracted from tailwind palette\n */\nconst PINK_500 = 0xec4899;\nconst BLUE_600 = 0x2563eb;\nconst LIME_500 = 0x84cc16;\nconst CYAN_400 = 0x22d3ee;\nconst ORANGE_400 = 0xfb923c;\n// const GRAY_100 = 0xf4f4f5\nconst DARK = 0x666666;\nconst RED_100 = 0xfee2e2;\nconst RED_700 = 0xb91c1c;\nfunction formatRouteRecordForInspector(route) {\n    const tags = [];\n    const { record } = route;\n    if (record.name != null) {\n        tags.push({\n            label: String(record.name),\n            textColor: 0,\n            backgroundColor: CYAN_400,\n        });\n    }\n    if (record.aliasOf) {\n        tags.push({\n            label: 'alias',\n            textColor: 0,\n            backgroundColor: ORANGE_400,\n        });\n    }\n    if (route.__vd_match) {\n        tags.push({\n            label: 'matches',\n            textColor: 0,\n            backgroundColor: PINK_500,\n        });\n    }\n    if (route.__vd_exactActive) {\n        tags.push({\n            label: 'exact',\n            textColor: 0,\n            backgroundColor: LIME_500,\n        });\n    }\n    if (route.__vd_active) {\n        tags.push({\n            label: 'active',\n            textColor: 0,\n            backgroundColor: BLUE_600,\n        });\n    }\n    if (record.redirect) {\n        tags.push({\n            label: typeof record.redirect === 'string'\n                ? `redirect: ${record.redirect}`\n                : 'redirects',\n            textColor: 0xffffff,\n            backgroundColor: DARK,\n        });\n    }\n    // add an id to be able to select it. Using the `path` is not possible because\n    // empty path children would collide with their parents\n    let id = record.__vd_id;\n    if (id == null) {\n        id = String(routeRecordId++);\n        record.__vd_id = id;\n    }\n    return {\n        id,\n        label: record.path,\n        tags,\n        children: route.children.map(formatRouteRecordForInspector),\n    };\n}\n//  incremental id for route records and inspector state\nlet routeRecordId = 0;\nconst EXTRACT_REGEXP_RE = /^\\/(.*)\\/([a-z]*)$/;\nfunction markRouteRecordActive(route, currentRoute) {\n    // no route will be active if matched is empty\n    // reset the matching state\n    const isExactActive = currentRoute.matched.length &&\n        isSameRouteRecord(currentRoute.matched[currentRoute.matched.length - 1], route.record);\n    route.__vd_exactActive = route.__vd_active = isExactActive;\n    if (!isExactActive) {\n        route.__vd_active = currentRoute.matched.some(match => isSameRouteRecord(match, route.record));\n    }\n    route.children.forEach(childRoute => markRouteRecordActive(childRoute, currentRoute));\n}\nfunction resetMatchStateOnRouteRecord(route) {\n    route.__vd_match = false;\n    route.children.forEach(resetMatchStateOnRouteRecord);\n}\nfunction isRouteMatching(route, filter) {\n    const found = String(route.re).match(EXTRACT_REGEXP_RE);\n    route.__vd_match = false;\n    if (!found || found.length < 3) {\n        return false;\n    }\n    // use a regexp without $ at the end to match nested routes better\n    const nonEndingRE = new RegExp(found[1].replace(/\\$$/, ''), found[2]);\n    if (nonEndingRE.test(filter)) {\n        // mark children as matches\n        route.children.forEach(child => isRouteMatching(child, filter));\n        // exception case: `/`\n        if (route.record.path !== '/' || filter === '/') {\n            route.__vd_match = route.re.test(filter);\n            return true;\n        }\n        // hide the / route\n        return false;\n    }\n    const path = route.record.path.toLowerCase();\n    const decodedPath = decode(path);\n    // also allow partial matching on the path\n    if (!filter.startsWith('/') &&\n        (decodedPath.includes(filter) || path.includes(filter)))\n        return true;\n    if (decodedPath.startsWith(filter) || path.startsWith(filter))\n        return true;\n    if (route.record.name && String(route.record.name).includes(filter))\n        return true;\n    return route.children.some(child => isRouteMatching(child, filter));\n}\nfunction omit(obj, keys) {\n    const ret = {};\n    for (const key in obj) {\n        if (!keys.includes(key)) {\n            // @ts-expect-error\n            ret[key] = obj[key];\n        }\n    }\n    return ret;\n}\n\n/**\n * Creates a Router instance that can be used by a Vue app.\n *\n * @param options - {@link RouterOptions}\n */\nfunction createRouter(options) {\n    const matcher = createRouterMatcher(options.routes, options);\n    const parseQuery$1 = options.parseQuery || parseQuery;\n    const stringifyQuery$1 = options.stringifyQuery || stringifyQuery;\n    const routerHistory = options.history;\n    if ((process.env.NODE_ENV !== 'production') && !routerHistory)\n        throw new Error('Provide the \"history\" option when calling \"createRouter()\":' +\n            ' https://router.vuejs.org/api/interfaces/RouterOptions.html#history');\n    const beforeGuards = useCallbacks();\n    const beforeResolveGuards = useCallbacks();\n    const afterGuards = useCallbacks();\n    const currentRoute = shallowRef(START_LOCATION_NORMALIZED);\n    let pendingLocation = START_LOCATION_NORMALIZED;\n    // leave the scrollRestoration if no scrollBehavior is provided\n    if (isBrowser && options.scrollBehavior && 'scrollRestoration' in history) {\n        history.scrollRestoration = 'manual';\n    }\n    const normalizeParams = applyToParams.bind(null, paramValue => '' + paramValue);\n    const encodeParams = applyToParams.bind(null, encodeParam);\n    const decodeParams = \n    // @ts-expect-error: intentionally avoid the type check\n    applyToParams.bind(null, decode);\n    function addRoute(parentOrRoute, route) {\n        let parent;\n        let record;\n        if (isRouteName(parentOrRoute)) {\n            parent = matcher.getRecordMatcher(parentOrRoute);\n            if ((process.env.NODE_ENV !== 'production') && !parent) {\n                warn(`Parent route \"${String(parentOrRoute)}\" not found when adding child route`, route);\n            }\n            record = route;\n        }\n        else {\n            record = parentOrRoute;\n        }\n        return matcher.addRoute(record, parent);\n    }\n    function removeRoute(name) {\n        const recordMatcher = matcher.getRecordMatcher(name);\n        if (recordMatcher) {\n            matcher.removeRoute(recordMatcher);\n        }\n        else if ((process.env.NODE_ENV !== 'production')) {\n            warn(`Cannot remove non-existent route \"${String(name)}\"`);\n        }\n    }\n    function getRoutes() {\n        return matcher.getRoutes().map(routeMatcher => routeMatcher.record);\n    }\n    function hasRoute(name) {\n        return !!matcher.getRecordMatcher(name);\n    }\n    function resolve(rawLocation, currentLocation) {\n        // const resolve: Router['resolve'] = (rawLocation: RouteLocationRaw, currentLocation) => {\n        // const objectLocation = routerLocationAsObject(rawLocation)\n        // we create a copy to modify it later\n        currentLocation = assign({}, currentLocation || currentRoute.value);\n        if (typeof rawLocation === 'string') {\n            const locationNormalized = parseURL(parseQuery$1, rawLocation, currentLocation.path);\n            const matchedRoute = matcher.resolve({ path: locationNormalized.path }, currentLocation);\n            const href = routerHistory.createHref(locationNormalized.fullPath);\n            if ((process.env.NODE_ENV !== 'production')) {\n                if (href.startsWith('//'))\n                    warn(`Location \"${rawLocation}\" resolved to \"${href}\". A resolved location cannot start with multiple slashes.`);\n                else if (!matchedRoute.matched.length) {\n                    warn(`No match found for location with path \"${rawLocation}\"`);\n                }\n            }\n            // locationNormalized is always a new object\n            return assign(locationNormalized, matchedRoute, {\n                params: decodeParams(matchedRoute.params),\n                hash: decode(locationNormalized.hash),\n                redirectedFrom: undefined,\n                href,\n            });\n        }\n        if ((process.env.NODE_ENV !== 'production') && !isRouteLocation(rawLocation)) {\n            warn(`router.resolve() was passed an invalid location. This will fail in production.\\n- Location:`, rawLocation);\n            return resolve({});\n        }\n        let matcherLocation;\n        // path could be relative in object as well\n        if (rawLocation.path != null) {\n            if ((process.env.NODE_ENV !== 'production') &&\n                'params' in rawLocation &&\n                !('name' in rawLocation) &&\n                // @ts-expect-error: the type is never\n                Object.keys(rawLocation.params).length) {\n                warn(`Path \"${rawLocation.path}\" was passed with params but they will be ignored. Use a named route alongside params instead.`);\n            }\n            matcherLocation = assign({}, rawLocation, {\n                path: parseURL(parseQuery$1, rawLocation.path, currentLocation.path).path,\n            });\n        }\n        else {\n            // remove any nullish param\n            const targetParams = assign({}, rawLocation.params);\n            for (const key in targetParams) {\n                if (targetParams[key] == null) {\n                    delete targetParams[key];\n                }\n            }\n            // pass encoded values to the matcher, so it can produce encoded path and fullPath\n            matcherLocation = assign({}, rawLocation, {\n                params: encodeParams(targetParams),\n            });\n            // current location params are decoded, we need to encode them in case the\n            // matcher merges the params\n            currentLocation.params = encodeParams(currentLocation.params);\n        }\n        const matchedRoute = matcher.resolve(matcherLocation, currentLocation);\n        const hash = rawLocation.hash || '';\n        if ((process.env.NODE_ENV !== 'production') && hash && !hash.startsWith('#')) {\n            warn(`A \\`hash\\` should always start with the character \"#\". Replace \"${hash}\" with \"#${hash}\".`);\n        }\n        // the matcher might have merged current location params, so\n        // we need to run the decoding again\n        matchedRoute.params = normalizeParams(decodeParams(matchedRoute.params));\n        const fullPath = stringifyURL(stringifyQuery$1, assign({}, rawLocation, {\n            hash: encodeHash(hash),\n            path: matchedRoute.path,\n        }));\n        const href = routerHistory.createHref(fullPath);\n        if ((process.env.NODE_ENV !== 'production')) {\n            if (href.startsWith('//')) {\n                warn(`Location \"${rawLocation}\" resolved to \"${href}\". A resolved location cannot start with multiple slashes.`);\n            }\n            else if (!matchedRoute.matched.length) {\n                warn(`No match found for location with path \"${rawLocation.path != null ? rawLocation.path : rawLocation}\"`);\n            }\n        }\n        return assign({\n            fullPath,\n            // keep the hash encoded so fullPath is effectively path + encodedQuery +\n            // hash\n            hash,\n            query: \n            // if the user is using a custom query lib like qs, we might have\n            // nested objects, so we keep the query as is, meaning it can contain\n            // numbers at `$route.query`, but at the point, the user will have to\n            // use their own type anyway.\n            // https://github.com/vuejs/router/issues/328#issuecomment-649481567\n            stringifyQuery$1 === stringifyQuery\n                ? normalizeQuery(rawLocation.query)\n                : (rawLocation.query || {}),\n        }, matchedRoute, {\n            redirectedFrom: undefined,\n            href,\n        });\n    }\n    function locationAsObject(to) {\n        return typeof to === 'string'\n            ? parseURL(parseQuery$1, to, currentRoute.value.path)\n            : assign({}, to);\n    }\n    function checkCanceledNavigation(to, from) {\n        if (pendingLocation !== to) {\n            return createRouterError(8 /* ErrorTypes.NAVIGATION_CANCELLED */, {\n                from,\n                to,\n            });\n        }\n    }\n    function push(to) {\n        return pushWithRedirect(to);\n    }\n    function replace(to) {\n        return push(assign(locationAsObject(to), { replace: true }));\n    }\n    function handleRedirectRecord(to) {\n        const lastMatched = to.matched[to.matched.length - 1];\n        if (lastMatched && lastMatched.redirect) {\n            const { redirect } = lastMatched;\n            let newTargetLocation = typeof redirect === 'function' ? redirect(to) : redirect;\n            if (typeof newTargetLocation === 'string') {\n                newTargetLocation =\n                    newTargetLocation.includes('?') || newTargetLocation.includes('#')\n                        ? (newTargetLocation = locationAsObject(newTargetLocation))\n                        : // force empty params\n                            { path: newTargetLocation };\n                // @ts-expect-error: force empty params when a string is passed to let\n                // the router parse them again\n                newTargetLocation.params = {};\n            }\n            if ((process.env.NODE_ENV !== 'production') &&\n                newTargetLocation.path == null &&\n                !('name' in newTargetLocation)) {\n                warn(`Invalid redirect found:\\n${JSON.stringify(newTargetLocation, null, 2)}\\n when navigating to \"${to.fullPath}\". A redirect must contain a name or path. This will break in production.`);\n                throw new Error('Invalid redirect');\n            }\n            return assign({\n                query: to.query,\n                hash: to.hash,\n                // avoid transferring params if the redirect has a path\n                params: newTargetLocation.path != null ? {} : to.params,\n            }, newTargetLocation);\n        }\n    }\n    function pushWithRedirect(to, redirectedFrom) {\n        const targetLocation = (pendingLocation = resolve(to));\n        const from = currentRoute.value;\n        const data = to.state;\n        const force = to.force;\n        // to could be a string where `replace` is a function\n        const replace = to.replace === true;\n        const shouldRedirect = handleRedirectRecord(targetLocation);\n        if (shouldRedirect)\n            return pushWithRedirect(assign(locationAsObject(shouldRedirect), {\n                state: typeof shouldRedirect === 'object'\n                    ? assign({}, data, shouldRedirect.state)\n                    : data,\n                force,\n                replace,\n            }), \n            // keep original redirectedFrom if it exists\n            redirectedFrom || targetLocation);\n        // if it was a redirect we already called `pushWithRedirect` above\n        const toLocation = targetLocation;\n        toLocation.redirectedFrom = redirectedFrom;\n        let failure;\n        if (!force && isSameRouteLocation(stringifyQuery$1, from, targetLocation)) {\n            failure = createRouterError(16 /* ErrorTypes.NAVIGATION_DUPLICATED */, { to: toLocation, from });\n            // trigger scroll to allow scrolling to the same anchor\n            handleScroll(from, from, \n            // this is a push, the only way for it to be triggered from a\n            // history.listen is with a redirect, which makes it become a push\n            true, \n            // This cannot be the first navigation because the initial location\n            // cannot be manually navigated to\n            false);\n        }\n        return (failure ? Promise.resolve(failure) : navigate(toLocation, from))\n            .catch((error) => isNavigationFailure(error)\n            ? // navigation redirects still mark the router as ready\n                isNavigationFailure(error, 2 /* ErrorTypes.NAVIGATION_GUARD_REDIRECT */)\n                    ? error\n                    : markAsReady(error) // also returns the error\n            : // reject any unknown error\n                triggerError(error, toLocation, from))\n            .then((failure) => {\n            if (failure) {\n                if (isNavigationFailure(failure, 2 /* ErrorTypes.NAVIGATION_GUARD_REDIRECT */)) {\n                    if ((process.env.NODE_ENV !== 'production') &&\n                        // we are redirecting to the same location we were already at\n                        isSameRouteLocation(stringifyQuery$1, resolve(failure.to), toLocation) &&\n                        // and we have done it a couple of times\n                        redirectedFrom &&\n                        // @ts-expect-error: added only in dev\n                        (redirectedFrom._count = redirectedFrom._count\n                            ? // @ts-expect-error\n                                redirectedFrom._count + 1\n                            : 1) > 30) {\n                        warn(`Detected a possibly infinite redirection in a navigation guard when going from \"${from.fullPath}\" to \"${toLocation.fullPath}\". Aborting to avoid a Stack Overflow.\\n Are you always returning a new location within a navigation guard? That would lead to this error. Only return when redirecting or aborting, that should fix this. This might break in production if not fixed.`);\n                        return Promise.reject(new Error('Infinite redirect in navigation guard'));\n                    }\n                    return pushWithRedirect(\n                    // keep options\n                    assign({\n                        // preserve an existing replacement but allow the redirect to override it\n                        replace,\n                    }, locationAsObject(failure.to), {\n                        state: typeof failure.to === 'object'\n                            ? assign({}, data, failure.to.state)\n                            : data,\n                        force,\n                    }), \n                    // preserve the original redirectedFrom if any\n                    redirectedFrom || toLocation);\n                }\n            }\n            else {\n                // if we fail we don't finalize the navigation\n                failure = finalizeNavigation(toLocation, from, true, replace, data);\n            }\n            triggerAfterEach(toLocation, from, failure);\n            return failure;\n        });\n    }\n    /**\n     * Helper to reject and skip all navigation guards if a new navigation happened\n     * @param to\n     * @param from\n     */\n    function checkCanceledNavigationAndReject(to, from) {\n        const error = checkCanceledNavigation(to, from);\n        return error ? Promise.reject(error) : Promise.resolve();\n    }\n    function runWithContext(fn) {\n        const app = installedApps.values().next().value;\n        // support Vue < 3.3\n        return app && typeof app.runWithContext === 'function'\n            ? app.runWithContext(fn)\n            : fn();\n    }\n    // TODO: refactor the whole before guards by internally using router.beforeEach\n    function navigate(to, from) {\n        let guards;\n        const [leavingRecords, updatingRecords, enteringRecords] = extractChangingRecords(to, from);\n        // all components here have been resolved once because we are leaving\n        guards = extractComponentsGuards(leavingRecords.reverse(), 'beforeRouteLeave', to, from);\n        // leavingRecords is already reversed\n        for (const record of leavingRecords) {\n            record.leaveGuards.forEach(guard => {\n                guards.push(guardToPromiseFn(guard, to, from));\n            });\n        }\n        const canceledNavigationCheck = checkCanceledNavigationAndReject.bind(null, to, from);\n        guards.push(canceledNavigationCheck);\n        // run the queue of per route beforeRouteLeave guards\n        return (runGuardQueue(guards)\n            .then(() => {\n            // check global guards beforeEach\n            guards = [];\n            for (const guard of beforeGuards.list()) {\n                guards.push(guardToPromiseFn(guard, to, from));\n            }\n            guards.push(canceledNavigationCheck);\n            return runGuardQueue(guards);\n        })\n            .then(() => {\n            // check in components beforeRouteUpdate\n            guards = extractComponentsGuards(updatingRecords, 'beforeRouteUpdate', to, from);\n            for (const record of updatingRecords) {\n                record.updateGuards.forEach(guard => {\n                    guards.push(guardToPromiseFn(guard, to, from));\n                });\n            }\n            guards.push(canceledNavigationCheck);\n            // run the queue of per route beforeEnter guards\n            return runGuardQueue(guards);\n        })\n            .then(() => {\n            // check the route beforeEnter\n            guards = [];\n            for (const record of enteringRecords) {\n                // do not trigger beforeEnter on reused views\n                if (record.beforeEnter) {\n                    if (isArray(record.beforeEnter)) {\n                        for (const beforeEnter of record.beforeEnter)\n                            guards.push(guardToPromiseFn(beforeEnter, to, from));\n                    }\n                    else {\n                        guards.push(guardToPromiseFn(record.beforeEnter, to, from));\n                    }\n                }\n            }\n            guards.push(canceledNavigationCheck);\n            // run the queue of per route beforeEnter guards\n            return runGuardQueue(guards);\n        })\n            .then(() => {\n            // NOTE: at this point to.matched is normalized and does not contain any () => Promise<Component>\n            // clear existing enterCallbacks, these are added by extractComponentsGuards\n            to.matched.forEach(record => (record.enterCallbacks = {}));\n            // check in-component beforeRouteEnter\n            guards = extractComponentsGuards(enteringRecords, 'beforeRouteEnter', to, from, runWithContext);\n            guards.push(canceledNavigationCheck);\n            // run the queue of per route beforeEnter guards\n            return runGuardQueue(guards);\n        })\n            .then(() => {\n            // check global guards beforeResolve\n            guards = [];\n            for (const guard of beforeResolveGuards.list()) {\n                guards.push(guardToPromiseFn(guard, to, from));\n            }\n            guards.push(canceledNavigationCheck);\n            return runGuardQueue(guards);\n        })\n            // catch any navigation canceled\n            .catch(err => isNavigationFailure(err, 8 /* ErrorTypes.NAVIGATION_CANCELLED */)\n            ? err\n            : Promise.reject(err)));\n    }\n    function triggerAfterEach(to, from, failure) {\n        // navigation is confirmed, call afterGuards\n        // TODO: wrap with error handlers\n        afterGuards\n            .list()\n            .forEach(guard => runWithContext(() => guard(to, from, failure)));\n    }\n    /**\n     * - Cleans up any navigation guards\n     * - Changes the url if necessary\n     * - Calls the scrollBehavior\n     */\n    function finalizeNavigation(toLocation, from, isPush, replace, data) {\n        // a more recent navigation took place\n        const error = checkCanceledNavigation(toLocation, from);\n        if (error)\n            return error;\n        // only consider as push if it's not the first navigation\n        const isFirstNavigation = from === START_LOCATION_NORMALIZED;\n        const state = !isBrowser ? {} : history.state;\n        // change URL only if the user did a push/replace and if it's not the initial navigation because\n        // it's just reflecting the url\n        if (isPush) {\n            // on the initial navigation, we want to reuse the scroll position from\n            // history state if it exists\n            if (replace || isFirstNavigation)\n                routerHistory.replace(toLocation.fullPath, assign({\n                    scroll: isFirstNavigation && state && state.scroll,\n                }, data));\n            else\n                routerHistory.push(toLocation.fullPath, data);\n        }\n        // accept current navigation\n        currentRoute.value = toLocation;\n        handleScroll(toLocation, from, isPush, isFirstNavigation);\n        markAsReady();\n    }\n    let removeHistoryListener;\n    // attach listener to history to trigger navigations\n    function setupListeners() {\n        // avoid setting up listeners twice due to an invalid first navigation\n        if (removeHistoryListener)\n            return;\n        removeHistoryListener = routerHistory.listen((to, _from, info) => {\n            if (!router.listening)\n                return;\n            // cannot be a redirect route because it was in history\n            const toLocation = resolve(to);\n            // due to dynamic routing, and to hash history with manual navigation\n            // (manually changing the url or calling history.hash = '#/somewhere'),\n            // there could be a redirect record in history\n            const shouldRedirect = handleRedirectRecord(toLocation);\n            if (shouldRedirect) {\n                pushWithRedirect(assign(shouldRedirect, { replace: true, force: true }), toLocation).catch(noop);\n                return;\n            }\n            pendingLocation = toLocation;\n            const from = currentRoute.value;\n            // TODO: should be moved to web history?\n            if (isBrowser) {\n                saveScrollPosition(getScrollKey(from.fullPath, info.delta), computeScrollPosition());\n            }\n            navigate(toLocation, from)\n                .catch((error) => {\n                if (isNavigationFailure(error, 4 /* ErrorTypes.NAVIGATION_ABORTED */ | 8 /* ErrorTypes.NAVIGATION_CANCELLED */)) {\n                    return error;\n                }\n                if (isNavigationFailure(error, 2 /* ErrorTypes.NAVIGATION_GUARD_REDIRECT */)) {\n                    // Here we could call if (info.delta) routerHistory.go(-info.delta,\n                    // false) but this is bug prone as we have no way to wait the\n                    // navigation to be finished before calling pushWithRedirect. Using\n                    // a setTimeout of 16ms seems to work but there is no guarantee for\n                    // it to work on every browser. So instead we do not restore the\n                    // history entry and trigger a new navigation as requested by the\n                    // navigation guard.\n                    // the error is already handled by router.push we just want to avoid\n                    // logging the error\n                    pushWithRedirect(assign(locationAsObject(error.to), {\n                        force: true,\n                    }), toLocation\n                    // avoid an uncaught rejection, let push call triggerError\n                    )\n                        .then(failure => {\n                        // manual change in hash history #916 ending up in the URL not\n                        // changing, but it was changed by the manual url change, so we\n                        // need to manually change it ourselves\n                        if (isNavigationFailure(failure, 4 /* ErrorTypes.NAVIGATION_ABORTED */ |\n                            16 /* ErrorTypes.NAVIGATION_DUPLICATED */) &&\n                            !info.delta &&\n                            info.type === NavigationType.pop) {\n                            routerHistory.go(-1, false);\n                        }\n                    })\n                        .catch(noop);\n                    // avoid the then branch\n                    return Promise.reject();\n                }\n                // do not restore history on unknown direction\n                if (info.delta) {\n                    routerHistory.go(-info.delta, false);\n                }\n                // unrecognized error, transfer to the global handler\n                return triggerError(error, toLocation, from);\n            })\n                .then((failure) => {\n                failure =\n                    failure ||\n                        finalizeNavigation(\n                        // after navigation, all matched components are resolved\n                        toLocation, from, false);\n                // revert the navigation\n                if (failure) {\n                    if (info.delta &&\n                        // a new navigation has been triggered, so we do not want to revert, that will change the current history\n                        // entry while a different route is displayed\n                        !isNavigationFailure(failure, 8 /* ErrorTypes.NAVIGATION_CANCELLED */)) {\n                        routerHistory.go(-info.delta, false);\n                    }\n                    else if (info.type === NavigationType.pop &&\n                        isNavigationFailure(failure, 4 /* ErrorTypes.NAVIGATION_ABORTED */ | 16 /* ErrorTypes.NAVIGATION_DUPLICATED */)) {\n                        // manual change in hash history #916\n                        // it's like a push but lacks the information of the direction\n                        routerHistory.go(-1, false);\n                    }\n                }\n                triggerAfterEach(toLocation, from, failure);\n            })\n                // avoid warnings in the console about uncaught rejections, they are logged by triggerErrors\n                .catch(noop);\n        });\n    }\n    // Initialization and Errors\n    let readyHandlers = useCallbacks();\n    let errorListeners = useCallbacks();\n    let ready;\n    /**\n     * Trigger errorListeners added via onError and throws the error as well\n     *\n     * @param error - error to throw\n     * @param to - location we were navigating to when the error happened\n     * @param from - location we were navigating from when the error happened\n     * @returns the error as a rejected promise\n     */\n    function triggerError(error, to, from) {\n        markAsReady(error);\n        const list = errorListeners.list();\n        if (list.length) {\n            list.forEach(handler => handler(error, to, from));\n        }\n        else {\n            if ((process.env.NODE_ENV !== 'production')) {\n                warn('uncaught error during route navigation:');\n            }\n            console.error(error);\n        }\n        // reject the error no matter there were error listeners or not\n        return Promise.reject(error);\n    }\n    function isReady() {\n        if (ready && currentRoute.value !== START_LOCATION_NORMALIZED)\n            return Promise.resolve();\n        return new Promise((resolve, reject) => {\n            readyHandlers.add([resolve, reject]);\n        });\n    }\n    function markAsReady(err) {\n        if (!ready) {\n            // still not ready if an error happened\n            ready = !err;\n            setupListeners();\n            readyHandlers\n                .list()\n                .forEach(([resolve, reject]) => (err ? reject(err) : resolve()));\n            readyHandlers.reset();\n        }\n        return err;\n    }\n    // Scroll behavior\n    function handleScroll(to, from, isPush, isFirstNavigation) {\n        const { scrollBehavior } = options;\n        if (!isBrowser || !scrollBehavior)\n            return Promise.resolve();\n        const scrollPosition = (!isPush && getSavedScrollPosition(getScrollKey(to.fullPath, 0))) ||\n            ((isFirstNavigation || !isPush) &&\n                history.state &&\n                history.state.scroll) ||\n            null;\n        return nextTick()\n            .then(() => scrollBehavior(to, from, scrollPosition))\n            .then(position => position && scrollToPosition(position))\n            .catch(err => triggerError(err, to, from));\n    }\n    const go = (delta) => routerHistory.go(delta);\n    let started;\n    const installedApps = new Set();\n    const router = {\n        currentRoute,\n        listening: true,\n        addRoute,\n        removeRoute,\n        clearRoutes: matcher.clearRoutes,\n        hasRoute,\n        getRoutes,\n        resolve,\n        options,\n        push,\n        replace,\n        go,\n        back: () => go(-1),\n        forward: () => go(1),\n        beforeEach: beforeGuards.add,\n        beforeResolve: beforeResolveGuards.add,\n        afterEach: afterGuards.add,\n        onError: errorListeners.add,\n        isReady,\n        install(app) {\n            const router = this;\n            app.component('RouterLink', RouterLink);\n            app.component('RouterView', RouterView);\n            app.config.globalProperties.$router = router;\n            Object.defineProperty(app.config.globalProperties, '$route', {\n                enumerable: true,\n                get: () => unref(currentRoute),\n            });\n            // this initial navigation is only necessary on client, on server it doesn't\n            // make sense because it will create an extra unnecessary navigation and could\n            // lead to problems\n            if (isBrowser &&\n                // used for the initial navigation client side to avoid pushing\n                // multiple times when the router is used in multiple apps\n                !started &&\n                currentRoute.value === START_LOCATION_NORMALIZED) {\n                // see above\n                started = true;\n                push(routerHistory.location).catch(err => {\n                    if ((process.env.NODE_ENV !== 'production'))\n                        warn('Unexpected error when starting the router:', err);\n                });\n            }\n            const reactiveRoute = {};\n            for (const key in START_LOCATION_NORMALIZED) {\n                Object.defineProperty(reactiveRoute, key, {\n                    get: () => currentRoute.value[key],\n                    enumerable: true,\n                });\n            }\n            app.provide(routerKey, router);\n            app.provide(routeLocationKey, shallowReactive(reactiveRoute));\n            app.provide(routerViewLocationKey, currentRoute);\n            const unmountApp = app.unmount;\n            installedApps.add(app);\n            app.unmount = function () {\n                installedApps.delete(app);\n                // the router is not attached to an app anymore\n                if (installedApps.size < 1) {\n                    // invalidate the current navigation\n                    pendingLocation = START_LOCATION_NORMALIZED;\n                    removeHistoryListener && removeHistoryListener();\n                    removeHistoryListener = null;\n                    currentRoute.value = START_LOCATION_NORMALIZED;\n                    started = false;\n                    ready = false;\n                }\n                unmountApp();\n            };\n            // TODO: this probably needs to be updated so it can be used by vue-termui\n            if (((process.env.NODE_ENV !== 'production') || __VUE_PROD_DEVTOOLS__) && isBrowser) {\n                addDevtools(app, router, matcher);\n            }\n        },\n    };\n    // TODO: type this as NavigationGuardReturn or similar instead of any\n    function runGuardQueue(guards) {\n        return guards.reduce((promise, guard) => promise.then(() => runWithContext(guard)), Promise.resolve());\n    }\n    return router;\n}\nfunction extractChangingRecords(to, from) {\n    const leavingRecords = [];\n    const updatingRecords = [];\n    const enteringRecords = [];\n    const len = Math.max(from.matched.length, to.matched.length);\n    for (let i = 0; i < len; i++) {\n        const recordFrom = from.matched[i];\n        if (recordFrom) {\n            if (to.matched.find(record => isSameRouteRecord(record, recordFrom)))\n                updatingRecords.push(recordFrom);\n            else\n                leavingRecords.push(recordFrom);\n        }\n        const recordTo = to.matched[i];\n        if (recordTo) {\n            // the type doesn't matter because we are comparing per reference\n            if (!from.matched.find(record => isSameRouteRecord(record, recordTo))) {\n                enteringRecords.push(recordTo);\n            }\n        }\n    }\n    return [leavingRecords, updatingRecords, enteringRecords];\n}\n\n/**\n * Returns the router instance. Equivalent to using `$router` inside\n * templates.\n */\nfunction useRouter() {\n    return inject(routerKey);\n}\n/**\n * Returns the current route location. Equivalent to using `$route` inside\n * templates.\n */\nfunction useRoute(_name) {\n    return inject(routeLocationKey);\n}\n\nexport { NavigationFailureType, RouterLink, RouterView, START_LOCATION_NORMALIZED as START_LOCATION, createMemoryHistory, createRouter, createRouterMatcher, createWebHashHistory, createWebHistory, isNavigationFailure, loadRouteLocation, matchedRouteKey, onBeforeRouteLeave, onBeforeRouteUpdate, parseQuery, routeLocationKey, routerKey, routerViewLocationKey, stringifyQuery, useLink, useRoute, useRouter, viewDepthKey };\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA,SAASA,kBAAkB,EAAEC,MAAM,EAAEC,WAAW,EAAEC,aAAa,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,WAAW,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,CAAC,EAAEC,OAAO,EAAEC,GAAG,EAAEC,KAAK,EAAEC,UAAU,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,KAAK;AACjN,SAASC,mBAAmB,QAAQ,mBAAmB;AAEvD,MAAMC,SAAS,GAAG,OAAOC,QAAQ,KAAK,WAAW;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,SAAS,EAAE;EACjC,OAAQ,OAAOA,SAAS,KAAK,QAAQ,IACjC,aAAa,IAAIA,SAAS,IAC1B,OAAO,IAAIA,SAAS,IACpB,WAAW,IAAIA,SAAS;AAChC;AACA,SAASC,UAAUA,CAACC,GAAG,EAAE;EACrB,OAAQA,GAAG,CAACC,UAAU,IAClBD,GAAG,CAACE,MAAM,CAACC,WAAW,CAAC,KAAK,QAAQ;EACpC;EACA;EACCH,GAAG,CAACI,OAAO,IAAIP,gBAAgB,CAACG,GAAG,CAACI,OAAO,CAAE;AACtD;AACA,MAAMC,MAAM,GAAGC,MAAM,CAACD,MAAM;AAC5B,SAASE,aAAaA,CAACC,EAAE,EAAEC,MAAM,EAAE;EAC/B,MAAMC,SAAS,GAAG,CAAC,CAAC;EACpB,KAAK,MAAMC,GAAG,IAAIF,MAAM,EAAE;IACtB,MAAMG,KAAK,GAAGH,MAAM,CAACE,GAAG,CAAC;IACzBD,SAAS,CAACC,GAAG,CAAC,GAAGE,OAAO,CAACD,KAAK,CAAC,GACzBA,KAAK,CAACE,GAAG,CAACN,EAAE,CAAC,GACbA,EAAE,CAACI,KAAK,CAAC;EACnB;EACA,OAAOF,SAAS;AACpB;AACA,MAAMK,IAAI,GAAGA,CAAA,KAAM,CAAE,CAAC;AACtB;AACA;AACA;AACA;AACA,MAAMF,OAAO,GAAGG,KAAK,CAACH,OAAO;AAE7B,SAASI,IAAIA,CAACC,GAAG,EAAE;EACf;EACA,MAAMC,IAAI,GAAGH,KAAK,CAACI,IAAI,CAACC,SAAS,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC;EAC3CC,OAAO,CAACN,IAAI,CAACO,KAAK,CAACD,OAAO,EAAE,CAAC,qBAAqB,GAAGL,GAAG,CAAC,CAACO,MAAM,CAACN,IAAI,CAAC,CAAC;AAC3E;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMO,OAAO,GAAG,IAAI,CAAC,CAAC;AACtB,MAAMC,YAAY,GAAG,IAAI,CAAC,CAAC;AAC3B,MAAMC,QAAQ,GAAG,KAAK,CAAC,CAAC;AACxB,MAAMC,QAAQ,GAAG,IAAI,CAAC,CAAC;AACvB,MAAMC,KAAK,GAAG,KAAK,CAAC,CAAC;AACrB,MAAMC,OAAO,GAAG,KAAK,CAAC,CAAC;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,GAAG,MAAM,CAAC,CAAC;AACpC,MAAMC,oBAAoB,GAAG,MAAM,CAAC,CAAC;AACrC,MAAMC,YAAY,GAAG,MAAM,CAAC,CAAC;AAC7B,MAAMC,eAAe,GAAG,MAAM,CAAC,CAAC;AAChC,MAAMC,iBAAiB,GAAG,MAAM,CAAC,CAAC;AAClC,MAAMC,WAAW,GAAG,MAAM,CAAC,CAAC;AAC5B,MAAMC,kBAAkB,GAAG,MAAM,CAAC,CAAC;AACnC,MAAMC,YAAY,GAAG,MAAM,CAAC,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,IAAI,EAAE;EACxB,OAAOC,SAAS,CAAC,EAAE,GAAGD,IAAI,CAAC,CACtBE,OAAO,CAACN,WAAW,EAAE,GAAG,CAAC,CACzBM,OAAO,CAACX,mBAAmB,EAAE,GAAG,CAAC,CACjCW,OAAO,CAACV,oBAAoB,EAAE,GAAG,CAAC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASW,UAAUA,CAACH,IAAI,EAAE;EACtB,OAAOD,YAAY,CAACC,IAAI,CAAC,CACpBE,OAAO,CAACP,iBAAiB,EAAE,GAAG,CAAC,CAC/BO,OAAO,CAACL,kBAAkB,EAAE,GAAG,CAAC,CAChCK,OAAO,CAACT,YAAY,EAAE,GAAG,CAAC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASW,gBAAgBA,CAACJ,IAAI,EAAE;EAC5B,OAAQD,YAAY,CAACC,IAAI;EACrB;EAAA,CACCE,OAAO,CAACZ,OAAO,EAAE,KAAK,CAAC,CACvBY,OAAO,CAACJ,YAAY,EAAE,GAAG,CAAC,CAC1BI,OAAO,CAACjB,OAAO,EAAE,KAAK,CAAC,CACvBiB,OAAO,CAAChB,YAAY,EAAE,KAAK,CAAC,CAC5BgB,OAAO,CAACR,eAAe,EAAE,GAAG,CAAC,CAC7BQ,OAAO,CAACP,iBAAiB,EAAE,GAAG,CAAC,CAC/BO,OAAO,CAACL,kBAAkB,EAAE,GAAG,CAAC,CAChCK,OAAO,CAACT,YAAY,EAAE,GAAG,CAAC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA,SAASY,cAAcA,CAACL,IAAI,EAAE;EAC1B,OAAOI,gBAAgB,CAACJ,IAAI,CAAC,CAACE,OAAO,CAACd,QAAQ,EAAE,KAAK,CAAC;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkB,UAAUA,CAACN,IAAI,EAAE;EACtB,OAAOD,YAAY,CAACC,IAAI,CAAC,CAACE,OAAO,CAACjB,OAAO,EAAE,KAAK,CAAC,CAACiB,OAAO,CAACb,KAAK,EAAE,KAAK,CAAC;AAC3E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkB,WAAWA,CAACP,IAAI,EAAE;EACvB,OAAOA,IAAI,IAAI,IAAI,GAAG,EAAE,GAAGM,UAAU,CAACN,IAAI,CAAC,CAACE,OAAO,CAACf,QAAQ,EAAE,KAAK,CAAC;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASqB,MAAMA,CAACR,IAAI,EAAE;EAClB,IAAI;IACA,OAAOS,kBAAkB,CAAC,EAAE,GAAGT,IAAI,CAAC;EACxC,CAAC,CACD,OAAOU,GAAG,EAAE;IACPC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAKrC,IAAI,CAAC,mBAAmBwB,IAAI,yBAAyB,CAAC;EACrG;EACA,OAAO,EAAE,GAAGA,IAAI;AACpB;AAEA,MAAMc,iBAAiB,GAAG,KAAK;AAC/B,MAAMC,mBAAmB,GAAIC,IAAI,IAAKA,IAAI,CAACd,OAAO,CAACY,iBAAiB,EAAE,EAAE,CAAC;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,QAAQA,CAACC,UAAU,EAAEC,QAAQ,EAAEC,eAAe,GAAG,GAAG,EAAE;EAC3D,IAAIJ,IAAI;IAAEK,KAAK,GAAG,CAAC,CAAC;IAAEC,YAAY,GAAG,EAAE;IAAEC,IAAI,GAAG,EAAE;EAClD;EACA;EACA,MAAMC,OAAO,GAAGL,QAAQ,CAACM,OAAO,CAAC,GAAG,CAAC;EACrC,IAAIC,SAAS,GAAGP,QAAQ,CAACM,OAAO,CAAC,GAAG,CAAC;EACrC;EACA,IAAID,OAAO,GAAGE,SAAS,IAAIF,OAAO,IAAI,CAAC,EAAE;IACrCE,SAAS,GAAG,CAAC,CAAC;EAClB;EACA,IAAIA,SAAS,GAAG,CAAC,CAAC,EAAE;IAChBV,IAAI,GAAGG,QAAQ,CAACtC,KAAK,CAAC,CAAC,EAAE6C,SAAS,CAAC;IACnCJ,YAAY,GAAGH,QAAQ,CAACtC,KAAK,CAAC6C,SAAS,GAAG,CAAC,EAAEF,OAAO,GAAG,CAAC,CAAC,GAAGA,OAAO,GAAGL,QAAQ,CAACQ,MAAM,CAAC;IACtFN,KAAK,GAAGH,UAAU,CAACI,YAAY,CAAC;EACpC;EACA,IAAIE,OAAO,GAAG,CAAC,CAAC,EAAE;IACdR,IAAI,GAAGA,IAAI,IAAIG,QAAQ,CAACtC,KAAK,CAAC,CAAC,EAAE2C,OAAO,CAAC;IACzC;IACAD,IAAI,GAAGJ,QAAQ,CAACtC,KAAK,CAAC2C,OAAO,EAAEL,QAAQ,CAACQ,MAAM,CAAC;EACnD;EACA;EACAX,IAAI,GAAGY,mBAAmB,CAACZ,IAAI,IAAI,IAAI,GAAGA,IAAI,GAAGG,QAAQ,EAAEC,eAAe,CAAC;EAC3E;EACA,OAAO;IACHS,QAAQ,EAAEb,IAAI,IAAIM,YAAY,IAAI,GAAG,CAAC,GAAGA,YAAY,GAAGC,IAAI;IAC5DP,IAAI;IACJK,KAAK;IACLE,IAAI,EAAEf,MAAM,CAACe,IAAI;EACrB,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASO,YAAYA,CAACC,cAAc,EAAEZ,QAAQ,EAAE;EAC5C,MAAME,KAAK,GAAGF,QAAQ,CAACE,KAAK,GAAGU,cAAc,CAACZ,QAAQ,CAACE,KAAK,CAAC,GAAG,EAAE;EAClE,OAAOF,QAAQ,CAACH,IAAI,IAAIK,KAAK,IAAI,GAAG,CAAC,GAAGA,KAAK,IAAIF,QAAQ,CAACI,IAAI,IAAI,EAAE,CAAC;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASS,SAASA,CAACC,QAAQ,EAAEC,IAAI,EAAE;EAC/B;EACA,IAAI,CAACA,IAAI,IAAI,CAACD,QAAQ,CAACE,WAAW,CAAC,CAAC,CAACC,UAAU,CAACF,IAAI,CAACC,WAAW,CAAC,CAAC,CAAC,EAC/D,OAAOF,QAAQ;EACnB,OAAOA,QAAQ,CAACpD,KAAK,CAACqD,IAAI,CAACP,MAAM,CAAC,IAAI,GAAG;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASU,mBAAmBA,CAACN,cAAc,EAAEO,CAAC,EAAEC,CAAC,EAAE;EAC/C,MAAMC,UAAU,GAAGF,CAAC,CAACG,OAAO,CAACd,MAAM,GAAG,CAAC;EACvC,MAAMe,UAAU,GAAGH,CAAC,CAACE,OAAO,CAACd,MAAM,GAAG,CAAC;EACvC,OAAQa,UAAU,GAAG,CAAC,CAAC,IACnBA,UAAU,KAAKE,UAAU,IACzBC,iBAAiB,CAACL,CAAC,CAACG,OAAO,CAACD,UAAU,CAAC,EAAED,CAAC,CAACE,OAAO,CAACC,UAAU,CAAC,CAAC,IAC/DE,yBAAyB,CAACN,CAAC,CAACtE,MAAM,EAAEuE,CAAC,CAACvE,MAAM,CAAC,IAC7C+D,cAAc,CAACO,CAAC,CAACjB,KAAK,CAAC,KAAKU,cAAc,CAACQ,CAAC,CAAClB,KAAK,CAAC,IACnDiB,CAAC,CAACf,IAAI,KAAKgB,CAAC,CAAChB,IAAI;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASoB,iBAAiBA,CAACL,CAAC,EAAEC,CAAC,EAAE;EAC7B;EACA;EACA;EACA,OAAO,CAACD,CAAC,CAACO,OAAO,IAAIP,CAAC,OAAOC,CAAC,CAACM,OAAO,IAAIN,CAAC,CAAC;AAChD;AACA,SAASK,yBAAyBA,CAACN,CAAC,EAAEC,CAAC,EAAE;EACrC,IAAI1E,MAAM,CAACiF,IAAI,CAACR,CAAC,CAAC,CAACX,MAAM,KAAK9D,MAAM,CAACiF,IAAI,CAACP,CAAC,CAAC,CAACZ,MAAM,EAC/C,OAAO,KAAK;EAChB,KAAK,MAAMzD,GAAG,IAAIoE,CAAC,EAAE;IACjB,IAAI,CAACS,8BAA8B,CAACT,CAAC,CAACpE,GAAG,CAAC,EAAEqE,CAAC,CAACrE,GAAG,CAAC,CAAC,EAC/C,OAAO,KAAK;EACpB;EACA,OAAO,IAAI;AACf;AACA,SAAS6E,8BAA8BA,CAACT,CAAC,EAAEC,CAAC,EAAE;EAC1C,OAAOnE,OAAO,CAACkE,CAAC,CAAC,GACXU,iBAAiB,CAACV,CAAC,EAAEC,CAAC,CAAC,GACvBnE,OAAO,CAACmE,CAAC,CAAC,GACNS,iBAAiB,CAACT,CAAC,EAAED,CAAC,CAAC,GACvBA,CAAC,KAAKC,CAAC;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASS,iBAAiBA,CAACV,CAAC,EAAEC,CAAC,EAAE;EAC7B,OAAOnE,OAAO,CAACmE,CAAC,CAAC,GACXD,CAAC,CAACX,MAAM,KAAKY,CAAC,CAACZ,MAAM,IAAIW,CAAC,CAACW,KAAK,CAAC,CAAC9E,KAAK,EAAE+E,CAAC,KAAK/E,KAAK,KAAKoE,CAAC,CAACW,CAAC,CAAC,CAAC,GAC9DZ,CAAC,CAACX,MAAM,KAAK,CAAC,IAAIW,CAAC,CAAC,CAAC,CAAC,KAAKC,CAAC;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASX,mBAAmBA,CAACuB,EAAE,EAAExE,IAAI,EAAE;EACnC,IAAIwE,EAAE,CAACf,UAAU,CAAC,GAAG,CAAC,EAClB,OAAOe,EAAE;EACb,IAAKxC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAK,CAAClC,IAAI,CAACyD,UAAU,CAAC,GAAG,CAAC,EAAE;IAClE5D,IAAI,CAAC,mFAAmF2E,EAAE,WAAWxE,IAAI,4BAA4BA,IAAI,IAAI,CAAC;IAC9I,OAAOwE,EAAE;EACb;EACA,IAAI,CAACA,EAAE,EACH,OAAOxE,IAAI;EACf,MAAMyE,YAAY,GAAGzE,IAAI,CAAC0E,KAAK,CAAC,GAAG,CAAC;EACpC,MAAMC,UAAU,GAAGH,EAAE,CAACE,KAAK,CAAC,GAAG,CAAC;EAChC,MAAME,aAAa,GAAGD,UAAU,CAACA,UAAU,CAAC3B,MAAM,GAAG,CAAC,CAAC;EACvD;EACA;EACA,IAAI4B,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,GAAG,EAAE;IACjDD,UAAU,CAACE,IAAI,CAAC,EAAE,CAAC;EACvB;EACA,IAAIC,QAAQ,GAAGL,YAAY,CAACzB,MAAM,GAAG,CAAC;EACtC,IAAI+B,UAAU;EACd,IAAIC,OAAO;EACX,KAAKD,UAAU,GAAG,CAAC,EAAEA,UAAU,GAAGJ,UAAU,CAAC3B,MAAM,EAAE+B,UAAU,EAAE,EAAE;IAC/DC,OAAO,GAAGL,UAAU,CAACI,UAAU,CAAC;IAChC;IACA,IAAIC,OAAO,KAAK,GAAG,EACf;IACJ;IACA,IAAIA,OAAO,KAAK,IAAI,EAAE;MAClB;MACA,IAAIF,QAAQ,GAAG,CAAC,EACZA,QAAQ,EAAE;MACd;IACJ;IACA;IAAA,KAEI;EACR;EACA,OAAQL,YAAY,CAACvE,KAAK,CAAC,CAAC,EAAE4E,QAAQ,CAAC,CAACG,IAAI,CAAC,GAAG,CAAC,GAC7C,GAAG,GACHN,UAAU,CAACzE,KAAK,CAAC6E,UAAU,CAAC,CAACE,IAAI,CAAC,GAAG,CAAC;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,yBAAyB,GAAG;EAC9B7C,IAAI,EAAE,GAAG;EACT;EACA8C,IAAI,EAAEC,SAAS;EACf/F,MAAM,EAAE,CAAC,CAAC;EACVqD,KAAK,EAAE,CAAC,CAAC;EACTE,IAAI,EAAE,EAAE;EACRM,QAAQ,EAAE,GAAG;EACbY,OAAO,EAAE,EAAE;EACXuB,IAAI,EAAE,CAAC,CAAC;EACRC,cAAc,EAAEF;AACpB,CAAC;AAED,IAAIG,cAAc;AAClB,CAAC,UAAUA,cAAc,EAAE;EACvBA,cAAc,CAAC,KAAK,CAAC,GAAG,KAAK;EAC7BA,cAAc,CAAC,MAAM,CAAC,GAAG,MAAM;AACnC,CAAC,EAAEA,cAAc,KAAKA,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3C,IAAIC,mBAAmB;AACvB,CAAC,UAAUA,mBAAmB,EAAE;EAC5BA,mBAAmB,CAAC,MAAM,CAAC,GAAG,MAAM;EACpCA,mBAAmB,CAAC,SAAS,CAAC,GAAG,SAAS;EAC1CA,mBAAmB,CAAC,SAAS,CAAC,GAAG,EAAE;AACvC,CAAC,EAAEA,mBAAmB,KAAKA,mBAAmB,GAAG,CAAC,CAAC,CAAC,CAAC;AACrD;AACA;AACA;AACA,MAAMC,KAAK,GAAG,EAAE;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACnC,IAAI,EAAE;EACzB,IAAI,CAACA,IAAI,EAAE;IACP,IAAIhF,SAAS,EAAE;MACX;MACA,MAAMoH,MAAM,GAAGnH,QAAQ,CAACoH,aAAa,CAAC,MAAM,CAAC;MAC7CrC,IAAI,GAAIoC,MAAM,IAAIA,MAAM,CAACE,YAAY,CAAC,MAAM,CAAC,IAAK,GAAG;MACrD;MACAtC,IAAI,GAAGA,IAAI,CAAChC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC;IAC9C,CAAC,MACI;MACDgC,IAAI,GAAG,GAAG;IACd;EACJ;EACA;EACA;EACA;EACA,IAAIA,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIA,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAClCA,IAAI,GAAG,GAAG,GAAGA,IAAI;EACrB;EACA;EACA,OAAOnB,mBAAmB,CAACmB,IAAI,CAAC;AACpC;AACA;AACA,MAAMuC,cAAc,GAAG,SAAS;AAChC,SAASC,UAAUA,CAACxC,IAAI,EAAEf,QAAQ,EAAE;EAChC,OAAOe,IAAI,CAAChC,OAAO,CAACuE,cAAc,EAAE,GAAG,CAAC,GAAGtD,QAAQ;AACvD;AAEA,SAASwD,kBAAkBA,CAACC,EAAE,EAAEC,MAAM,EAAE;EACpC,MAAMC,OAAO,GAAG3H,QAAQ,CAAC4H,eAAe,CAACC,qBAAqB,CAAC,CAAC;EAChE,MAAMC,MAAM,GAAGL,EAAE,CAACI,qBAAqB,CAAC,CAAC;EACzC,OAAO;IACHE,QAAQ,EAAEL,MAAM,CAACK,QAAQ;IACzBC,IAAI,EAAEF,MAAM,CAACE,IAAI,GAAGL,OAAO,CAACK,IAAI,IAAIN,MAAM,CAACM,IAAI,IAAI,CAAC,CAAC;IACrDC,GAAG,EAAEH,MAAM,CAACG,GAAG,GAAGN,OAAO,CAACM,GAAG,IAAIP,MAAM,CAACO,GAAG,IAAI,CAAC;EACpD,CAAC;AACL;AACA,MAAMC,qBAAqB,GAAGA,CAAA,MAAO;EACjCF,IAAI,EAAEG,MAAM,CAACC,OAAO;EACpBH,GAAG,EAAEE,MAAM,CAACE;AAChB,CAAC,CAAC;AACF,SAASC,gBAAgBA,CAAChC,QAAQ,EAAE;EAChC,IAAIiC,eAAe;EACnB,IAAI,IAAI,IAAIjC,QAAQ,EAAE;IAClB,MAAMkC,UAAU,GAAGlC,QAAQ,CAACmB,EAAE;IAC9B,MAAMgB,YAAY,GAAG,OAAOD,UAAU,KAAK,QAAQ,IAAIA,UAAU,CAACvD,UAAU,CAAC,GAAG,CAAC;IACjF;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAKzB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAK,OAAO4C,QAAQ,CAACmB,EAAE,KAAK,QAAQ,EAAE;MAC5E,IAAI,CAACgB,YAAY,IAAI,CAACzI,QAAQ,CAAC0I,cAAc,CAACpC,QAAQ,CAACmB,EAAE,CAAC/F,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;QACjE,IAAI;UACA,MAAMiH,OAAO,GAAG3I,QAAQ,CAACoH,aAAa,CAACd,QAAQ,CAACmB,EAAE,CAAC;UACnD,IAAIgB,YAAY,IAAIE,OAAO,EAAE;YACzBtH,IAAI,CAAC,iBAAiBiF,QAAQ,CAACmB,EAAE,sDAAsDnB,QAAQ,CAACmB,EAAE,iCAAiC,CAAC;YACpI;YACA;UACJ;QACJ,CAAC,CACD,OAAOlE,GAAG,EAAE;UACRlC,IAAI,CAAC,iBAAiBiF,QAAQ,CAACmB,EAAE,4QAA4Q,CAAC;UAC9S;UACA;QACJ;MACJ;IACJ;IACA,MAAMA,EAAE,GAAG,OAAOe,UAAU,KAAK,QAAQ,GACnCC,YAAY,GACRzI,QAAQ,CAAC0I,cAAc,CAACF,UAAU,CAAC9G,KAAK,CAAC,CAAC,CAAC,CAAC,GAC5C1B,QAAQ,CAACoH,aAAa,CAACoB,UAAU,CAAC,GACtCA,UAAU;IAChB,IAAI,CAACf,EAAE,EAAE;MACJjE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAClCrC,IAAI,CAAC,yCAAyCiF,QAAQ,CAACmB,EAAE,+BAA+B,CAAC;MAC7F;IACJ;IACAc,eAAe,GAAGf,kBAAkB,CAACC,EAAE,EAAEnB,QAAQ,CAAC;EACtD,CAAC,MACI;IACDiC,eAAe,GAAGjC,QAAQ;EAC9B;EACA,IAAI,gBAAgB,IAAItG,QAAQ,CAAC4H,eAAe,CAACgB,KAAK,EAClDT,MAAM,CAACU,QAAQ,CAACN,eAAe,CAAC,CAAC,KAChC;IACDJ,MAAM,CAACU,QAAQ,CAACN,eAAe,CAACP,IAAI,IAAI,IAAI,GAAGO,eAAe,CAACP,IAAI,GAAGG,MAAM,CAACC,OAAO,EAAEG,eAAe,CAACN,GAAG,IAAI,IAAI,GAAGM,eAAe,CAACN,GAAG,GAAGE,MAAM,CAACE,OAAO,CAAC;EAC7J;AACJ;AACA,SAASS,YAAYA,CAACjF,IAAI,EAAEkF,KAAK,EAAE;EAC/B,MAAMzC,QAAQ,GAAG0C,OAAO,CAACC,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC3C,QAAQ,GAAGyC,KAAK,GAAG,CAAC,CAAC;EACpE,OAAOzC,QAAQ,GAAGzC,IAAI;AAC1B;AACA,MAAMqF,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;AACjC,SAASC,kBAAkBA,CAACrI,GAAG,EAAEsI,cAAc,EAAE;EAC7CH,eAAe,CAACI,GAAG,CAACvI,GAAG,EAAEsI,cAAc,CAAC;AAC5C;AACA,SAASE,sBAAsBA,CAACxI,GAAG,EAAE;EACjC,MAAMyI,MAAM,GAAGN,eAAe,CAACO,GAAG,CAAC1I,GAAG,CAAC;EACvC;EACAmI,eAAe,CAACQ,MAAM,CAAC3I,GAAG,CAAC;EAC3B,OAAOyI,MAAM;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIG,kBAAkB,GAAGA,CAAA,KAAM3F,QAAQ,CAAC4F,QAAQ,GAAG,IAAI,GAAG5F,QAAQ,CAAC6F,IAAI;AACvE;AACA;AACA;AACA;AACA;AACA,SAASC,qBAAqBA,CAAC/E,IAAI,EAAEf,QAAQ,EAAE;EAC3C,MAAM;IAAEc,QAAQ;IAAEiF,MAAM;IAAE3F;EAAK,CAAC,GAAGJ,QAAQ;EAC3C;EACA,MAAMK,OAAO,GAAGU,IAAI,CAACT,OAAO,CAAC,GAAG,CAAC;EACjC,IAAID,OAAO,GAAG,CAAC,CAAC,EAAE;IACd,IAAI2F,QAAQ,GAAG5F,IAAI,CAAC6F,QAAQ,CAAClF,IAAI,CAACrD,KAAK,CAAC2C,OAAO,CAAC,CAAC,GAC3CU,IAAI,CAACrD,KAAK,CAAC2C,OAAO,CAAC,CAACG,MAAM,GAC1B,CAAC;IACP,IAAI0F,YAAY,GAAG9F,IAAI,CAAC1C,KAAK,CAACsI,QAAQ,CAAC;IACvC;IACA,IAAIE,YAAY,CAAC,CAAC,CAAC,KAAK,GAAG,EACvBA,YAAY,GAAG,GAAG,GAAGA,YAAY;IACrC,OAAOrF,SAAS,CAACqF,YAAY,EAAE,EAAE,CAAC;EACtC;EACA,MAAMrG,IAAI,GAAGgB,SAAS,CAACC,QAAQ,EAAEC,IAAI,CAAC;EACtC,OAAOlB,IAAI,GAAGkG,MAAM,GAAG3F,IAAI;AAC/B;AACA,SAAS+F,mBAAmBA,CAACpF,IAAI,EAAEqF,YAAY,EAAEnG,eAAe,EAAElB,OAAO,EAAE;EACvE,IAAIsH,SAAS,GAAG,EAAE;EAClB,IAAIC,SAAS,GAAG,EAAE;EAClB;EACA;EACA,IAAIC,UAAU,GAAG,IAAI;EACrB,MAAMC,eAAe,GAAGA,CAAC;IAAEvB;EAAO,CAAC,KAAK;IACpC,MAAMjD,EAAE,GAAG8D,qBAAqB,CAAC/E,IAAI,EAAEf,QAAQ,CAAC;IAChD,MAAMxC,IAAI,GAAGyC,eAAe,CAACjD,KAAK;IAClC,MAAMyJ,SAAS,GAAGL,YAAY,CAACpJ,KAAK;IACpC,IAAI+H,KAAK,GAAG,CAAC;IACb,IAAIE,KAAK,EAAE;MACPhF,eAAe,CAACjD,KAAK,GAAGgF,EAAE;MAC1BoE,YAAY,CAACpJ,KAAK,GAAGiI,KAAK;MAC1B;MACA,IAAIsB,UAAU,IAAIA,UAAU,KAAK/I,IAAI,EAAE;QACnC+I,UAAU,GAAG,IAAI;QACjB;MACJ;MACAxB,KAAK,GAAG0B,SAAS,GAAGxB,KAAK,CAAC3C,QAAQ,GAAGmE,SAAS,CAACnE,QAAQ,GAAG,CAAC;IAC/D,CAAC,MACI;MACDvD,OAAO,CAACiD,EAAE,CAAC;IACf;IACA;IACA;IACA;IACA;IACA;IACAqE,SAAS,CAACK,OAAO,CAACC,QAAQ,IAAI;MAC1BA,QAAQ,CAAC1G,eAAe,CAACjD,KAAK,EAAEQ,IAAI,EAAE;QAClCuH,KAAK;QACL6B,IAAI,EAAE7D,cAAc,CAAC8D,GAAG;QACxBC,SAAS,EAAE/B,KAAK,GACVA,KAAK,GAAG,CAAC,GACL/B,mBAAmB,CAAC+D,OAAO,GAC3B/D,mBAAmB,CAACgE,IAAI,GAC5BhE,mBAAmB,CAACiE;MAC9B,CAAC,CAAC;IACN,CAAC,CAAC;EACN,CAAC;EACD,SAASC,cAAcA,CAAA,EAAG;IACtBX,UAAU,GAAGtG,eAAe,CAACjD,KAAK;EACtC;EACA,SAASmK,MAAMA,CAACC,QAAQ,EAAE;IACtB;IACAf,SAAS,CAAChE,IAAI,CAAC+E,QAAQ,CAAC;IACxB,MAAMC,QAAQ,GAAGA,CAAA,KAAM;MACnB,MAAMC,KAAK,GAAGjB,SAAS,CAAC/F,OAAO,CAAC8G,QAAQ,CAAC;MACzC,IAAIE,KAAK,GAAG,CAAC,CAAC,EACVjB,SAAS,CAACkB,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;IAClC,CAAC;IACDhB,SAAS,CAACjE,IAAI,CAACgF,QAAQ,CAAC;IACxB,OAAOA,QAAQ;EACnB;EACA,SAASG,oBAAoBA,CAAA,EAAG;IAC5B,MAAM;MAAExC;IAAQ,CAAC,GAAGb,MAAM;IAC1B,IAAI,CAACa,OAAO,CAACC,KAAK,EACd;IACJD,OAAO,CAACyC,YAAY,CAAChL,MAAM,CAAC,CAAC,CAAC,EAAEuI,OAAO,CAACC,KAAK,EAAE;MAAEO,MAAM,EAAEtB,qBAAqB,CAAC;IAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EAC5F;EACA,SAASwD,OAAOA,CAAA,EAAG;IACf,KAAK,MAAML,QAAQ,IAAIf,SAAS,EAC5Be,QAAQ,CAAC,CAAC;IACdf,SAAS,GAAG,EAAE;IACdnC,MAAM,CAACwD,mBAAmB,CAAC,UAAU,EAAEnB,eAAe,CAAC;IACvDrC,MAAM,CAACwD,mBAAmB,CAAC,cAAc,EAAEH,oBAAoB,CAAC;EACpE;EACA;EACArD,MAAM,CAACyD,gBAAgB,CAAC,UAAU,EAAEpB,eAAe,CAAC;EACpD;EACA;EACArC,MAAM,CAACyD,gBAAgB,CAAC,cAAc,EAAEJ,oBAAoB,EAAE;IAC1DK,OAAO,EAAE;EACb,CAAC,CAAC;EACF,OAAO;IACHX,cAAc;IACdC,MAAM;IACNO;EACJ,CAAC;AACL;AACA;AACA;AACA;AACA,SAASI,UAAUA,CAACd,IAAI,EAAEe,OAAO,EAAEhB,OAAO,EAAEiB,QAAQ,GAAG,KAAK,EAAEC,aAAa,GAAG,KAAK,EAAE;EACjF,OAAO;IACHjB,IAAI;IACJe,OAAO;IACPhB,OAAO;IACPiB,QAAQ;IACR1F,QAAQ,EAAE6B,MAAM,CAACa,OAAO,CAACxE,MAAM;IAC/BgF,MAAM,EAAEyC,aAAa,GAAG/D,qBAAqB,CAAC,CAAC,GAAG;EACtD,CAAC;AACL;AACA,SAASgE,yBAAyBA,CAACnH,IAAI,EAAE;EACrC,MAAM;IAAEiE,OAAO;IAAEhF;EAAS,CAAC,GAAGmE,MAAM;EACpC;EACA,MAAMlE,eAAe,GAAG;IACpBjD,KAAK,EAAE8I,qBAAqB,CAAC/E,IAAI,EAAEf,QAAQ;EAC/C,CAAC;EACD,MAAMoG,YAAY,GAAG;IAAEpJ,KAAK,EAAEgI,OAAO,CAACC;EAAM,CAAC;EAC7C;EACA,IAAI,CAACmB,YAAY,CAACpJ,KAAK,EAAE;IACrBmL,cAAc,CAAClI,eAAe,CAACjD,KAAK,EAAE;MAClCgK,IAAI,EAAE,IAAI;MACVe,OAAO,EAAE9H,eAAe,CAACjD,KAAK;MAC9B+J,OAAO,EAAE,IAAI;MACb;MACAzE,QAAQ,EAAE0C,OAAO,CAACxE,MAAM,GAAG,CAAC;MAC5BwH,QAAQ,EAAE,IAAI;MACd;MACA;MACAxC,MAAM,EAAE;IACZ,CAAC,EAAE,IAAI,CAAC;EACZ;EACA,SAAS2C,cAAcA,CAACnG,EAAE,EAAEiD,KAAK,EAAElG,OAAO,EAAE;IACxC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMqJ,SAAS,GAAGrH,IAAI,CAACT,OAAO,CAAC,GAAG,CAAC;IACnC,MAAM+H,GAAG,GAAGD,SAAS,GAAG,CAAC,CAAC,GACpB,CAACpI,QAAQ,CAAC6F,IAAI,IAAI7J,QAAQ,CAACoH,aAAa,CAAC,MAAM,CAAC,GAC5CrC,IAAI,GACJA,IAAI,CAACrD,KAAK,CAAC0K,SAAS,CAAC,IAAIpG,EAAE,GAC/B2D,kBAAkB,CAAC,CAAC,GAAG5E,IAAI,GAAGiB,EAAE;IACtC,IAAI;MACA;MACA;MACAgD,OAAO,CAACjG,OAAO,GAAG,cAAc,GAAG,WAAW,CAAC,CAACkG,KAAK,EAAE,EAAE,EAAEoD,GAAG,CAAC;MAC/DjC,YAAY,CAACpJ,KAAK,GAAGiI,KAAK;IAC9B,CAAC,CACD,OAAO1F,GAAG,EAAE;MACR,IAAKC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAG;QACzCrC,IAAI,CAAC,+BAA+B,EAAEkC,GAAG,CAAC;MAC9C,CAAC,MACI;QACD5B,OAAO,CAAC2K,KAAK,CAAC/I,GAAG,CAAC;MACtB;MACA;MACAS,QAAQ,CAACjB,OAAO,GAAG,SAAS,GAAG,QAAQ,CAAC,CAACsJ,GAAG,CAAC;IACjD;EACJ;EACA,SAAStJ,OAAOA,CAACiD,EAAE,EAAEuG,IAAI,EAAE;IACvB,MAAMtD,KAAK,GAAGxI,MAAM,CAAC,CAAC,CAAC,EAAEuI,OAAO,CAACC,KAAK,EAAE6C,UAAU,CAAC1B,YAAY,CAACpJ,KAAK,CAACgK,IAAI;IAC1E;IACAhF,EAAE,EAAEoE,YAAY,CAACpJ,KAAK,CAAC+J,OAAO,EAAE,IAAI,CAAC,EAAEwB,IAAI,EAAE;MAAEjG,QAAQ,EAAE8D,YAAY,CAACpJ,KAAK,CAACsF;IAAS,CAAC,CAAC;IACvF6F,cAAc,CAACnG,EAAE,EAAEiD,KAAK,EAAE,IAAI,CAAC;IAC/BhF,eAAe,CAACjD,KAAK,GAAGgF,EAAE;EAC9B;EACA,SAASK,IAAIA,CAACL,EAAE,EAAEuG,IAAI,EAAE;IACpB;IACA;IACA,MAAMC,YAAY,GAAG/L,MAAM,CAAC,CAAC,CAAC;IAC9B;IACA;IACA;IACA2J,YAAY,CAACpJ,KAAK,EAAEgI,OAAO,CAACC,KAAK,EAAE;MAC/B8B,OAAO,EAAE/E,EAAE;MACXwD,MAAM,EAAEtB,qBAAqB,CAAC;IAClC,CAAC,CAAC;IACF,IAAK1E,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAK,CAACsF,OAAO,CAACC,KAAK,EAAE;MAC3D5H,IAAI,CAAC,gMAAgM,GACjM,kDAAkD,GAClD,mGAAmG,CAAC;IAC5G;IACA8K,cAAc,CAACK,YAAY,CAACT,OAAO,EAAES,YAAY,EAAE,IAAI,CAAC;IACxD,MAAMvD,KAAK,GAAGxI,MAAM,CAAC,CAAC,CAAC,EAAEqL,UAAU,CAAC7H,eAAe,CAACjD,KAAK,EAAEgF,EAAE,EAAE,IAAI,CAAC,EAAE;MAAEM,QAAQ,EAAEkG,YAAY,CAAClG,QAAQ,GAAG;IAAE,CAAC,EAAEiG,IAAI,CAAC;IACpHJ,cAAc,CAACnG,EAAE,EAAEiD,KAAK,EAAE,KAAK,CAAC;IAChChF,eAAe,CAACjD,KAAK,GAAGgF,EAAE;EAC9B;EACA,OAAO;IACHhC,QAAQ,EAAEC,eAAe;IACzBgF,KAAK,EAAEmB,YAAY;IACnB/D,IAAI;IACJtD;EACJ,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA,SAAS0J,gBAAgBA,CAAC1H,IAAI,EAAE;EAC5BA,IAAI,GAAGmC,aAAa,CAACnC,IAAI,CAAC;EAC1B,MAAM2H,iBAAiB,GAAGR,yBAAyB,CAACnH,IAAI,CAAC;EACzD,MAAM4H,gBAAgB,GAAGxC,mBAAmB,CAACpF,IAAI,EAAE2H,iBAAiB,CAACzD,KAAK,EAAEyD,iBAAiB,CAAC1I,QAAQ,EAAE0I,iBAAiB,CAAC3J,OAAO,CAAC;EAClI,SAAS6J,EAAEA,CAAC7D,KAAK,EAAE8D,gBAAgB,GAAG,IAAI,EAAE;IACxC,IAAI,CAACA,gBAAgB,EACjBF,gBAAgB,CAACzB,cAAc,CAAC,CAAC;IACrClC,OAAO,CAAC4D,EAAE,CAAC7D,KAAK,CAAC;EACrB;EACA,MAAM+D,aAAa,GAAGrM,MAAM,CAAC;IACzB;IACAuD,QAAQ,EAAE,EAAE;IACZe,IAAI;IACJ6H,EAAE;IACFrF,UAAU,EAAEA,UAAU,CAACwF,IAAI,CAAC,IAAI,EAAEhI,IAAI;EAC1C,CAAC,EAAE2H,iBAAiB,EAAEC,gBAAgB,CAAC;EACvCjM,MAAM,CAACsM,cAAc,CAACF,aAAa,EAAE,UAAU,EAAE;IAC7CG,UAAU,EAAE,IAAI;IAChBxD,GAAG,EAAEA,CAAA,KAAMiD,iBAAiB,CAAC1I,QAAQ,CAAChD;EAC1C,CAAC,CAAC;EACFN,MAAM,CAACsM,cAAc,CAACF,aAAa,EAAE,OAAO,EAAE;IAC1CG,UAAU,EAAE,IAAI;IAChBxD,GAAG,EAAEA,CAAA,KAAMiD,iBAAiB,CAACzD,KAAK,CAACjI;EACvC,CAAC,CAAC;EACF,OAAO8L,aAAa;AACxB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,mBAAmBA,CAACnI,IAAI,GAAG,EAAE,EAAE;EACpC,IAAIsF,SAAS,GAAG,EAAE;EAClB,IAAI8C,KAAK,GAAG,CAAC,CAAClG,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;EACzB,IAAIX,QAAQ,GAAG,CAAC;EAChBvB,IAAI,GAAGmC,aAAa,CAACnC,IAAI,CAAC;EAC1B,SAASqI,WAAWA,CAACpJ,QAAQ,EAAEiF,KAAK,GAAG,CAAC,CAAC,EAAE;IACvC3C,QAAQ,EAAE;IACV,IAAIA,QAAQ,KAAK6G,KAAK,CAAC3I,MAAM,EAAE;MAC3B;MACA2I,KAAK,CAAC5B,MAAM,CAACjF,QAAQ,CAAC;IAC1B;IACA6G,KAAK,CAAC9G,IAAI,CAAC,CAACrC,QAAQ,EAAEiF,KAAK,CAAC,CAAC;EACjC;EACA,SAAS4D,gBAAgBA,CAAC7G,EAAE,EAAExE,IAAI,EAAE;IAAEsJ,SAAS;IAAE/B;EAAM,CAAC,EAAE;IACtD,MAAMsE,IAAI,GAAG;MACTvC,SAAS;MACT/B,KAAK;MACL6B,IAAI,EAAE7D,cAAc,CAAC8D;IACzB,CAAC;IACD,KAAK,MAAMO,QAAQ,IAAIf,SAAS,EAAE;MAC9Be,QAAQ,CAACpF,EAAE,EAAExE,IAAI,EAAE6L,IAAI,CAAC;IAC5B;EACJ;EACA,MAAMP,aAAa,GAAG;IAClB;IACA9I,QAAQ,EAAEiD,KAAK;IACf;IACAgC,KAAK,EAAE,CAAC,CAAC;IACTlE,IAAI;IACJwC,UAAU,EAAEA,UAAU,CAACwF,IAAI,CAAC,IAAI,EAAEhI,IAAI,CAAC;IACvChC,OAAOA,CAACiD,EAAE,EAAEiD,KAAK,EAAE;MACf;MACAkE,KAAK,CAAC5B,MAAM,CAACjF,QAAQ,EAAE,EAAE,CAAC,CAAC;MAC3B8G,WAAW,CAACpH,EAAE,EAAEiD,KAAK,CAAC;IAC1B,CAAC;IACD5C,IAAIA,CAACL,EAAE,EAAEiD,KAAK,EAAE;MACZmE,WAAW,CAACpH,EAAE,EAAEiD,KAAK,CAAC;IAC1B,CAAC;IACDkC,MAAMA,CAACC,QAAQ,EAAE;MACbf,SAAS,CAAChE,IAAI,CAAC+E,QAAQ,CAAC;MACxB,OAAO,MAAM;QACT,MAAME,KAAK,GAAGjB,SAAS,CAAC/F,OAAO,CAAC8G,QAAQ,CAAC;QACzC,IAAIE,KAAK,GAAG,CAAC,CAAC,EACVjB,SAAS,CAACkB,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;MAClC,CAAC;IACL,CAAC;IACDI,OAAOA,CAAA,EAAG;MACNrB,SAAS,GAAG,EAAE;MACd8C,KAAK,GAAG,CAAC,CAAClG,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;MACrBX,QAAQ,GAAG,CAAC;IAChB,CAAC;IACDsG,EAAEA,CAAC7D,KAAK,EAAEuE,aAAa,GAAG,IAAI,EAAE;MAC5B,MAAM9L,IAAI,GAAG,IAAI,CAACwC,QAAQ;MAC1B,MAAM8G,SAAS;MACf;MACA;MACA;MACA/B,KAAK,GAAG,CAAC,GAAG/B,mBAAmB,CAACgE,IAAI,GAAGhE,mBAAmB,CAAC+D,OAAO;MAClEzE,QAAQ,GAAGiH,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAACnH,QAAQ,GAAGyC,KAAK,EAAEoE,KAAK,CAAC3I,MAAM,GAAG,CAAC,CAAC,CAAC;MACpE,IAAI8I,aAAa,EAAE;QACfT,gBAAgB,CAAC,IAAI,CAAC7I,QAAQ,EAAExC,IAAI,EAAE;UAClCsJ,SAAS;UACT/B;QACJ,CAAC,CAAC;MACN;IACJ;EACJ,CAAC;EACDrI,MAAM,CAACsM,cAAc,CAACF,aAAa,EAAE,UAAU,EAAE;IAC7CG,UAAU,EAAE,IAAI;IAChBxD,GAAG,EAAEA,CAAA,KAAM0D,KAAK,CAAC7G,QAAQ,CAAC,CAAC,CAAC;EAChC,CAAC,CAAC;EACF5F,MAAM,CAACsM,cAAc,CAACF,aAAa,EAAE,OAAO,EAAE;IAC1CG,UAAU,EAAE,IAAI;IAChBxD,GAAG,EAAEA,CAAA,KAAM0D,KAAK,CAAC7G,QAAQ,CAAC,CAAC,CAAC;EAChC,CAAC,CAAC;EACF,OAAOwG,aAAa;AACxB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASY,oBAAoBA,CAAC3I,IAAI,EAAE;EAChC;EACA;EACA;EACAA,IAAI,GAAGf,QAAQ,CAAC6F,IAAI,GAAG9E,IAAI,IAAIf,QAAQ,CAACc,QAAQ,GAAGd,QAAQ,CAAC+F,MAAM,GAAG,EAAE;EACvE;EACA,IAAI,CAAChF,IAAI,CAACkF,QAAQ,CAAC,GAAG,CAAC,EACnBlF,IAAI,IAAI,GAAG;EACf,IAAKvB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAK,CAACqB,IAAI,CAAC4I,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC5I,IAAI,CAAC4I,QAAQ,CAAC,GAAG,CAAC,EAAE;IACxFtM,IAAI,CAAC,sCAAsC0D,IAAI,gBAAgBA,IAAI,CAAChC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC;EACjG;EACA,OAAO0J,gBAAgB,CAAC1H,IAAI,CAAC;AACjC;AAEA,SAAS6I,eAAeA,CAACC,KAAK,EAAE;EAC5B,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAKA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAS;AAC5E;AACA,SAASC,WAAWA,CAACnH,IAAI,EAAE;EACvB,OAAO,OAAOA,IAAI,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,QAAQ;AAC/D;AAEA,MAAMoH,uBAAuB,GAAGzN,MAAM,CAAEkD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAI,oBAAoB,GAAG,EAAE,CAAC;AAC3G;AACA;AACA;AACA;AACA,IAAIsK,qBAAqB;AACzB,CAAC,UAAUA,qBAAqB,EAAE;EAC9B;AACJ;AACA;AACA;EACIA,qBAAqB,CAACA,qBAAqB,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EACvE;AACJ;AACA;AACA;EACIA,qBAAqB,CAACA,qBAAqB,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW;EAC3E;AACJ;AACA;AACA;EACIA,qBAAqB,CAACA,qBAAqB,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,GAAG,YAAY;AAClF,CAAC,EAAEA,qBAAqB,KAAKA,qBAAqB,GAAG,CAAC,CAAC,CAAC,CAAC;AACzD;AACA,MAAMC,iBAAiB,GAAG;EACtB,CAAC,CAAC,CAAC,oCAAoC;IAAEjK,QAAQ;IAAEC;EAAgB,CAAC,EAAE;IAClE,OAAO,kBAAkBiK,IAAI,CAACC,SAAS,CAACnK,QAAQ,CAAC,GAAGC,eAAe,GAC7D,oBAAoB,GAAGiK,IAAI,CAACC,SAAS,CAAClK,eAAe,CAAC,GACtD,EAAE,EAAE;EACd,CAAC;EACD,CAAC,CAAC,CAAC,4CAA4C;IAAEzC,IAAI;IAAEwE;EAAI,CAAC,EAAE;IAC1D,OAAO,oBAAoBxE,IAAI,CAACkD,QAAQ,SAAS0J,cAAc,CAACpI,EAAE,CAAC,2BAA2B;EAClG,CAAC;EACD,CAAC,CAAC,CAAC,qCAAqC;IAAExE,IAAI;IAAEwE;EAAG,CAAC,EAAE;IAClD,OAAO,4BAA4BxE,IAAI,CAACkD,QAAQ,SAASsB,EAAE,CAACtB,QAAQ,2BAA2B;EACnG,CAAC;EACD,CAAC,CAAC,CAAC,uCAAuC;IAAElD,IAAI;IAAEwE;EAAG,CAAC,EAAE;IACpD,OAAO,8BAA8BxE,IAAI,CAACkD,QAAQ,SAASsB,EAAE,CAACtB,QAAQ,0BAA0B;EACpG,CAAC;EACD,CAAC,EAAE,CAAC,wCAAwC;IAAElD,IAAI;IAAEwE;EAAG,CAAC,EAAE;IACtD,OAAO,sDAAsDxE,IAAI,CAACkD,QAAQ,IAAI;EAClF;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS2J,iBAAiBA,CAACzD,IAAI,EAAE/J,MAAM,EAAE;EACrC;EACA,IAAK2C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAK,CAAC,IAAI,EAAE;IAClD,OAAOjD,MAAM,CAAC,IAAI6N,KAAK,CAACL,iBAAiB,CAACrD,IAAI,CAAC,CAAC/J,MAAM,CAAC,CAAC,EAAE;MACtD+J,IAAI;MACJ,CAACmD,uBAAuB,GAAG;IAC/B,CAAC,EAAElN,MAAM,CAAC;EACd,CAAC,MACI;IACD,OAAOJ,MAAM,CAAC,IAAI6N,KAAK,CAAC,CAAC,EAAE;MACvB1D,IAAI;MACJ,CAACmD,uBAAuB,GAAG;IAC/B,CAAC,EAAElN,MAAM,CAAC;EACd;AACJ;AACA,SAAS0N,mBAAmBA,CAACjC,KAAK,EAAE1B,IAAI,EAAE;EACtC,OAAQ0B,KAAK,YAAYgC,KAAK,IAC1BP,uBAAuB,IAAIzB,KAAK,KAC/B1B,IAAI,IAAI,IAAI,IAAI,CAAC,EAAE0B,KAAK,CAAC1B,IAAI,GAAGA,IAAI,CAAC,CAAC;AAC/C;AACA,MAAM4D,eAAe,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC;AACnD,SAASJ,cAAcA,CAACpI,EAAE,EAAE;EACxB,IAAI,OAAOA,EAAE,KAAK,QAAQ,EACtB,OAAOA,EAAE;EACb,IAAIA,EAAE,CAACnC,IAAI,IAAI,IAAI,EACf,OAAOmC,EAAE,CAACnC,IAAI;EAClB,MAAMG,QAAQ,GAAG,CAAC,CAAC;EACnB,KAAK,MAAMjD,GAAG,IAAIyN,eAAe,EAAE;IAC/B,IAAIzN,GAAG,IAAIiF,EAAE,EACThC,QAAQ,CAACjD,GAAG,CAAC,GAAGiF,EAAE,CAACjF,GAAG,CAAC;EAC/B;EACA,OAAOmN,IAAI,CAACC,SAAS,CAACnK,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;AAC5C;;AAEA;AACA,MAAMyK,kBAAkB,GAAG,QAAQ;AACnC,MAAMC,wBAAwB,GAAG;EAC7BC,SAAS,EAAE,KAAK;EAChBC,MAAM,EAAE,KAAK;EACbC,KAAK,EAAE,IAAI;EACXC,GAAG,EAAE;AACT,CAAC;AACD;AACA,MAAMC,cAAc,GAAG,qBAAqB;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,QAAQ,EAAEC,YAAY,EAAE;EAC5C,MAAMC,OAAO,GAAG1O,MAAM,CAAC,CAAC,CAAC,EAAEiO,wBAAwB,EAAEQ,YAAY,CAAC;EAClE;EACA,MAAME,KAAK,GAAG,EAAE;EAChB;EACA,IAAIC,OAAO,GAAGF,OAAO,CAACN,KAAK,GAAG,GAAG,GAAG,EAAE;EACtC;EACA,MAAMlJ,IAAI,GAAG,EAAE;EACf,KAAK,MAAMa,OAAO,IAAIyI,QAAQ,EAAE;IAC5B;IACA,MAAMK,aAAa,GAAG9I,OAAO,CAAChC,MAAM,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,qBAAqB;IACrE;IACA,IAAI2K,OAAO,CAACP,MAAM,IAAI,CAACpI,OAAO,CAAChC,MAAM,EACjC6K,OAAO,IAAI,GAAG;IAClB,KAAK,IAAIE,UAAU,GAAG,CAAC,EAAEA,UAAU,GAAG/I,OAAO,CAAChC,MAAM,EAAE+K,UAAU,EAAE,EAAE;MAChE,MAAMC,KAAK,GAAGhJ,OAAO,CAAC+I,UAAU,CAAC;MACjC;MACA,IAAIE,eAAe,GAAG,EAAE,CAAC,2BACpBN,OAAO,CAACR,SAAS,GAAG,IAAI,CAAC,qCAAqC,CAAC,CAAC;MACrE,IAAIa,KAAK,CAAC5E,IAAI,KAAK,CAAC,CAAC,wBAAwB;QACzC;QACA,IAAI,CAAC2E,UAAU,EACXF,OAAO,IAAI,GAAG;QAClBA,OAAO,IAAIG,KAAK,CAACxO,KAAK,CAAC+B,OAAO,CAACgM,cAAc,EAAE,MAAM,CAAC;QACtDU,eAAe,IAAI,EAAE,CAAC;MAC1B,CAAC,MACI,IAAID,KAAK,CAAC5E,IAAI,KAAK,CAAC,CAAC,uBAAuB;QAC7C,MAAM;UAAE5J,KAAK;UAAE0O,UAAU;UAAEC,QAAQ;UAAEC;QAAO,CAAC,GAAGJ,KAAK;QACrD7J,IAAI,CAACU,IAAI,CAAC;UACNM,IAAI,EAAE3F,KAAK;UACX0O,UAAU;UACVC;QACJ,CAAC,CAAC;QACF,MAAME,EAAE,GAAGD,MAAM,GAAGA,MAAM,GAAGnB,kBAAkB;QAC/C;QACA,IAAIoB,EAAE,KAAKpB,kBAAkB,EAAE;UAC3BgB,eAAe,IAAI,EAAE,CAAC;UACtB;UACA,IAAI;YACA,IAAIK,MAAM,CAAC,IAAID,EAAE,GAAG,CAAC;UACzB,CAAC,CACD,OAAOtM,GAAG,EAAE;YACR,MAAM,IAAI+K,KAAK,CAAC,oCAAoCtN,KAAK,MAAM6O,EAAE,KAAK,GAClEtM,GAAG,CAACwM,OAAO,CAAC;UACpB;QACJ;QACA;QACA,IAAIC,UAAU,GAAGN,UAAU,GAAG,OAAOG,EAAE,WAAWA,EAAE,MAAM,GAAG,IAAIA,EAAE,GAAG;QACtE;QACA,IAAI,CAACN,UAAU,EACXS,UAAU;QACN;QACA;QACAL,QAAQ,IAAInJ,OAAO,CAAChC,MAAM,GAAG,CAAC,GACxB,OAAOwL,UAAU,GAAG,GACpB,GAAG,GAAGA,UAAU;QAC9B,IAAIL,QAAQ,EACRK,UAAU,IAAI,GAAG;QACrBX,OAAO,IAAIW,UAAU;QACrBP,eAAe,IAAI,EAAE,CAAC;QACtB,IAAIE,QAAQ,EACRF,eAAe,IAAI,CAAC,CAAC,CAAC;QAC1B,IAAIC,UAAU,EACVD,eAAe,IAAI,CAAC,EAAE,CAAC;QAC3B,IAAII,EAAE,KAAK,IAAI,EACXJ,eAAe,IAAI,CAAC,EAAE,CAAC;MAC/B;MACAH,aAAa,CAACjJ,IAAI,CAACoJ,eAAe,CAAC;IACvC;IACA;IACA;IACAL,KAAK,CAAC/I,IAAI,CAACiJ,aAAa,CAAC;EAC7B;EACA;EACA,IAAIH,OAAO,CAACP,MAAM,IAAIO,OAAO,CAACL,GAAG,EAAE;IAC/B,MAAM/I,CAAC,GAAGqJ,KAAK,CAAC5K,MAAM,GAAG,CAAC;IAC1B4K,KAAK,CAACrJ,CAAC,CAAC,CAACqJ,KAAK,CAACrJ,CAAC,CAAC,CAACvB,MAAM,GAAG,CAAC,CAAC,IAAI,kBAAkB,CAAC;EACxD;EACA;EACA,IAAI,CAAC2K,OAAO,CAACP,MAAM,EACfS,OAAO,IAAI,IAAI;EACnB,IAAIF,OAAO,CAACL,GAAG,EACXO,OAAO,IAAI,GAAG;EAClB;EAAA,KACK,IAAIF,OAAO,CAACP,MAAM,IAAI,CAACS,OAAO,CAAC1B,QAAQ,CAAC,GAAG,CAAC,EAC7C0B,OAAO,IAAI,SAAS;EACxB,MAAMQ,EAAE,GAAG,IAAIC,MAAM,CAACT,OAAO,EAAEF,OAAO,CAACR,SAAS,GAAG,EAAE,GAAG,GAAG,CAAC;EAC5D,SAASsB,KAAKA,CAACpM,IAAI,EAAE;IACjB,MAAMqM,KAAK,GAAGrM,IAAI,CAACqM,KAAK,CAACL,EAAE,CAAC;IAC5B,MAAMhP,MAAM,GAAG,CAAC,CAAC;IACjB,IAAI,CAACqP,KAAK,EACN,OAAO,IAAI;IACf,KAAK,IAAInK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmK,KAAK,CAAC1L,MAAM,EAAEuB,CAAC,EAAE,EAAE;MACnC,MAAM/E,KAAK,GAAGkP,KAAK,CAACnK,CAAC,CAAC,IAAI,EAAE;MAC5B,MAAMhF,GAAG,GAAG4E,IAAI,CAACI,CAAC,GAAG,CAAC,CAAC;MACvBlF,MAAM,CAACE,GAAG,CAAC4F,IAAI,CAAC,GAAG3F,KAAK,IAAID,GAAG,CAAC2O,UAAU,GAAG1O,KAAK,CAACkF,KAAK,CAAC,GAAG,CAAC,GAAGlF,KAAK;IACzE;IACA,OAAOH,MAAM;EACjB;EACA,SAASsN,SAASA,CAACtN,MAAM,EAAE;IACvB,IAAIgD,IAAI,GAAG,EAAE;IACb;IACA,IAAIsM,oBAAoB,GAAG,KAAK;IAChC,KAAK,MAAM3J,OAAO,IAAIyI,QAAQ,EAAE;MAC5B,IAAI,CAACkB,oBAAoB,IAAI,CAACtM,IAAI,CAAC8J,QAAQ,CAAC,GAAG,CAAC,EAC5C9J,IAAI,IAAI,GAAG;MACfsM,oBAAoB,GAAG,KAAK;MAC5B,KAAK,MAAMX,KAAK,IAAIhJ,OAAO,EAAE;QACzB,IAAIgJ,KAAK,CAAC5E,IAAI,KAAK,CAAC,CAAC,wBAAwB;UACzC/G,IAAI,IAAI2L,KAAK,CAACxO,KAAK;QACvB,CAAC,MACI,IAAIwO,KAAK,CAAC5E,IAAI,KAAK,CAAC,CAAC,uBAAuB;UAC7C,MAAM;YAAE5J,KAAK;YAAE0O,UAAU;YAAEC;UAAS,CAAC,GAAGH,KAAK;UAC7C,MAAMY,KAAK,GAAGpP,KAAK,IAAIH,MAAM,GAAGA,MAAM,CAACG,KAAK,CAAC,GAAG,EAAE;UAClD,IAAIC,OAAO,CAACmP,KAAK,CAAC,IAAI,CAACV,UAAU,EAAE;YAC/B,MAAM,IAAIpB,KAAK,CAAC,mBAAmBtN,KAAK,2DAA2D,CAAC;UACxG;UACA,MAAM6B,IAAI,GAAG5B,OAAO,CAACmP,KAAK,CAAC,GACrBA,KAAK,CAAC3J,IAAI,CAAC,GAAG,CAAC,GACf2J,KAAK;UACX,IAAI,CAACvN,IAAI,EAAE;YACP,IAAI8M,QAAQ,EAAE;cACV;cACA,IAAInJ,OAAO,CAAChC,MAAM,GAAG,CAAC,EAAE;gBACpB;gBACA,IAAIX,IAAI,CAAC8J,QAAQ,CAAC,GAAG,CAAC,EAClB9J,IAAI,GAAGA,IAAI,CAACnC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC5B;gBAAA,KAEIyO,oBAAoB,GAAG,IAAI;cACnC;YACJ,CAAC,MAEG,MAAM,IAAI7B,KAAK,CAAC,2BAA2BtN,KAAK,GAAG,CAAC;UAC5D;UACA6C,IAAI,IAAIhB,IAAI;QAChB;MACJ;IACJ;IACA;IACA,OAAOgB,IAAI,IAAI,GAAG;EACtB;EACA,OAAO;IACHgM,EAAE;IACFT,KAAK;IACLzJ,IAAI;IACJsK,KAAK;IACL9B;EACJ,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkC,iBAAiBA,CAAClL,CAAC,EAAEC,CAAC,EAAE;EAC7B,IAAIW,CAAC,GAAG,CAAC;EACT,OAAOA,CAAC,GAAGZ,CAAC,CAACX,MAAM,IAAIuB,CAAC,GAAGX,CAAC,CAACZ,MAAM,EAAE;IACjC,MAAM8L,IAAI,GAAGlL,CAAC,CAACW,CAAC,CAAC,GAAGZ,CAAC,CAACY,CAAC,CAAC;IACxB;IACA,IAAIuK,IAAI,EACJ,OAAOA,IAAI;IACfvK,CAAC,EAAE;EACP;EACA;EACA;EACA,IAAIZ,CAAC,CAACX,MAAM,GAAGY,CAAC,CAACZ,MAAM,EAAE;IACrB,OAAOW,CAAC,CAACX,MAAM,KAAK,CAAC,IAAIW,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,yBAAyB,EAAE,CAAC,0BAC3D,CAAC,CAAC,GACF,CAAC;EACX,CAAC,MACI,IAAIA,CAAC,CAACX,MAAM,GAAGY,CAAC,CAACZ,MAAM,EAAE;IAC1B,OAAOY,CAAC,CAACZ,MAAM,KAAK,CAAC,IAAIY,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,yBAAyB,EAAE,CAAC,0BAC3D,CAAC,GACD,CAAC,CAAC;EACZ;EACA,OAAO,CAAC;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmL,sBAAsBA,CAACpL,CAAC,EAAEC,CAAC,EAAE;EAClC,IAAIW,CAAC,GAAG,CAAC;EACT,MAAMyK,MAAM,GAAGrL,CAAC,CAACiK,KAAK;EACtB,MAAMqB,MAAM,GAAGrL,CAAC,CAACgK,KAAK;EACtB,OAAOrJ,CAAC,GAAGyK,MAAM,CAAChM,MAAM,IAAIuB,CAAC,GAAG0K,MAAM,CAACjM,MAAM,EAAE;IAC3C,MAAMkM,IAAI,GAAGL,iBAAiB,CAACG,MAAM,CAACzK,CAAC,CAAC,EAAE0K,MAAM,CAAC1K,CAAC,CAAC,CAAC;IACpD;IACA,IAAI2K,IAAI,EACJ,OAAOA,IAAI;IACf3K,CAAC,EAAE;EACP;EACA,IAAIwH,IAAI,CAACoD,GAAG,CAACF,MAAM,CAACjM,MAAM,GAAGgM,MAAM,CAAChM,MAAM,CAAC,KAAK,CAAC,EAAE;IAC/C,IAAIoM,mBAAmB,CAACJ,MAAM,CAAC,EAC3B,OAAO,CAAC;IACZ,IAAII,mBAAmB,CAACH,MAAM,CAAC,EAC3B,OAAO,CAAC,CAAC;EACjB;EACA;EACA,OAAOA,MAAM,CAACjM,MAAM,GAAGgM,MAAM,CAAChM,MAAM;EACpC;EACA;EACA;EACA;EACA;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASoM,mBAAmBA,CAACxB,KAAK,EAAE;EAChC,MAAMyB,IAAI,GAAGzB,KAAK,CAACA,KAAK,CAAC5K,MAAM,GAAG,CAAC,CAAC;EACpC,OAAO4K,KAAK,CAAC5K,MAAM,GAAG,CAAC,IAAIqM,IAAI,CAACA,IAAI,CAACrM,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC;AACxD;AAEA,MAAMsM,UAAU,GAAG;EACflG,IAAI,EAAE,CAAC,CAAC;EACR5J,KAAK,EAAE;AACX,CAAC;AACD,MAAM+P,cAAc,GAAG,cAAc;AACrC;AACA;AACA;AACA,SAASC,YAAYA,CAACnN,IAAI,EAAE;EACxB,IAAI,CAACA,IAAI,EACL,OAAO,CAAC,EAAE,CAAC;EACf,IAAIA,IAAI,KAAK,GAAG,EACZ,OAAO,CAAC,CAACiN,UAAU,CAAC,CAAC;EACzB,IAAI,CAACjN,IAAI,CAACoB,UAAU,CAAC,GAAG,CAAC,EAAE;IACvB,MAAM,IAAIqJ,KAAK,CAAE9K,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAChD,yCAAyCG,IAAI,iBAAiBA,IAAI,IAAI,GACtE,iBAAiBA,IAAI,GAAG,CAAC;EACnC;EACA;EACA,SAASoN,KAAKA,CAAClB,OAAO,EAAE;IACpB,MAAM,IAAIzB,KAAK,CAAC,QAAQrF,KAAK,MAAMiI,MAAM,MAAMnB,OAAO,EAAE,CAAC;EAC7D;EACA,IAAI9G,KAAK,GAAG,CAAC,CAAC;EACd,IAAIkI,aAAa,GAAGlI,KAAK;EACzB,MAAMmI,MAAM,GAAG,EAAE;EACjB;EACA;EACA,IAAI5K,OAAO;EACX,SAAS6K,eAAeA,CAAA,EAAG;IACvB,IAAI7K,OAAO,EACP4K,MAAM,CAAC/K,IAAI,CAACG,OAAO,CAAC;IACxBA,OAAO,GAAG,EAAE;EAChB;EACA;EACA,IAAIT,CAAC,GAAG,CAAC;EACT;EACA,IAAIuL,IAAI;EACR;EACA,IAAIJ,MAAM,GAAG,EAAE;EACf;EACA,IAAIK,QAAQ,GAAG,EAAE;EACjB,SAASC,aAAaA,CAAA,EAAG;IACrB,IAAI,CAACN,MAAM,EACP;IACJ,IAAIjI,KAAK,KAAK,CAAC,CAAC,6BAA6B;MACzCzC,OAAO,CAACH,IAAI,CAAC;QACTuE,IAAI,EAAE,CAAC,CAAC;QACR5J,KAAK,EAAEkQ;MACX,CAAC,CAAC;IACN,CAAC,MACI,IAAIjI,KAAK,KAAK,CAAC,CAAC,8BACjBA,KAAK,KAAK,CAAC,CAAC,oCACZA,KAAK,KAAK,CAAC,CAAC,qCAAqC;MACjD,IAAIzC,OAAO,CAAChC,MAAM,GAAG,CAAC,KAAK8M,IAAI,KAAK,GAAG,IAAIA,IAAI,KAAK,GAAG,CAAC,EACpDL,KAAK,CAAC,uBAAuBC,MAAM,8CAA8C,CAAC;MACtF1K,OAAO,CAACH,IAAI,CAAC;QACTuE,IAAI,EAAE,CAAC,CAAC;QACR5J,KAAK,EAAEkQ,MAAM;QACbtB,MAAM,EAAE2B,QAAQ;QAChB7B,UAAU,EAAE4B,IAAI,KAAK,GAAG,IAAIA,IAAI,KAAK,GAAG;QACxC3B,QAAQ,EAAE2B,IAAI,KAAK,GAAG,IAAIA,IAAI,KAAK;MACvC,CAAC,CAAC;IACN,CAAC,MACI;MACDL,KAAK,CAAC,iCAAiC,CAAC;IAC5C;IACAC,MAAM,GAAG,EAAE;EACf;EACA,SAASO,eAAeA,CAAA,EAAG;IACvBP,MAAM,IAAII,IAAI;EAClB;EACA,OAAOvL,CAAC,GAAGlC,IAAI,CAACW,MAAM,EAAE;IACpB8M,IAAI,GAAGzN,IAAI,CAACkC,CAAC,EAAE,CAAC;IAChB,IAAIuL,IAAI,KAAK,IAAI,IAAIrI,KAAK,KAAK,CAAC,CAAC,kCAAkC;MAC/DkI,aAAa,GAAGlI,KAAK;MACrBA,KAAK,GAAG,CAAC,CAAC;MACV;IACJ;IACA,QAAQA,KAAK;MACT,KAAK,CAAC,CAAC;QACH,IAAIqI,IAAI,KAAK,GAAG,EAAE;UACd,IAAIJ,MAAM,EAAE;YACRM,aAAa,CAAC,CAAC;UACnB;UACAH,eAAe,CAAC,CAAC;QACrB,CAAC,MACI,IAAIC,IAAI,KAAK,GAAG,EAAE;UACnBE,aAAa,CAAC,CAAC;UACfvI,KAAK,GAAG,CAAC,CAAC;QACd,CAAC,MACI;UACDwI,eAAe,CAAC,CAAC;QACrB;QACA;MACJ,KAAK,CAAC,CAAC;QACHA,eAAe,CAAC,CAAC;QACjBxI,KAAK,GAAGkI,aAAa;QACrB;MACJ,KAAK,CAAC,CAAC;QACH,IAAIG,IAAI,KAAK,GAAG,EAAE;UACdrI,KAAK,GAAG,CAAC,CAAC;QACd,CAAC,MACI,IAAI8H,cAAc,CAACW,IAAI,CAACJ,IAAI,CAAC,EAAE;UAChCG,eAAe,CAAC,CAAC;QACrB,CAAC,MACI;UACDD,aAAa,CAAC,CAAC;UACfvI,KAAK,GAAG,CAAC,CAAC;UACV;UACA,IAAIqI,IAAI,KAAK,GAAG,IAAIA,IAAI,KAAK,GAAG,IAAIA,IAAI,KAAK,GAAG,EAC5CvL,CAAC,EAAE;QACX;QACA;MACJ,KAAK,CAAC,CAAC;QACH;QACA;QACA;QACA;QACA;QACA,IAAIuL,IAAI,KAAK,GAAG,EAAE;UACd;UACA,IAAIC,QAAQ,CAACA,QAAQ,CAAC/M,MAAM,GAAG,CAAC,CAAC,IAAI,IAAI,EACrC+M,QAAQ,GAAGA,QAAQ,CAAC7P,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG4P,IAAI,CAAC,KAExCrI,KAAK,GAAG,CAAC,CAAC;QAClB,CAAC,MACI;UACDsI,QAAQ,IAAID,IAAI;QACpB;QACA;MACJ,KAAK,CAAC,CAAC;QACH;QACAE,aAAa,CAAC,CAAC;QACfvI,KAAK,GAAG,CAAC,CAAC;QACV;QACA,IAAIqI,IAAI,KAAK,GAAG,IAAIA,IAAI,KAAK,GAAG,IAAIA,IAAI,KAAK,GAAG,EAC5CvL,CAAC,EAAE;QACPwL,QAAQ,GAAG,EAAE;QACb;MACJ;QACIN,KAAK,CAAC,eAAe,CAAC;QACtB;IACR;EACJ;EACA,IAAIhI,KAAK,KAAK,CAAC,CAAC,kCACZgI,KAAK,CAAC,uCAAuCC,MAAM,GAAG,CAAC;EAC3DM,aAAa,CAAC,CAAC;EACfH,eAAe,CAAC,CAAC;EACjB;EACA,OAAOD,MAAM;AACjB;AAEA,SAASO,wBAAwBA,CAACC,MAAM,EAAEC,MAAM,EAAE1C,OAAO,EAAE;EACvD,MAAM2C,MAAM,GAAG9C,cAAc,CAACgC,YAAY,CAACY,MAAM,CAAC/N,IAAI,CAAC,EAAEsL,OAAO,CAAC;EACjE;EACA,IAAK3L,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAG;IACzC,MAAMqO,YAAY,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC9B,KAAK,MAAMjR,GAAG,IAAI+Q,MAAM,CAACnM,IAAI,EAAE;MAC3B,IAAIoM,YAAY,CAACE,GAAG,CAAClR,GAAG,CAAC4F,IAAI,CAAC,EAC1BtF,IAAI,CAAC,sCAAsCN,GAAG,CAAC4F,IAAI,eAAeiL,MAAM,CAAC/N,IAAI,4DAA4D,CAAC;MAC9IkO,YAAY,CAACG,GAAG,CAACnR,GAAG,CAAC4F,IAAI,CAAC;IAC9B;EACJ;EACA,MAAMwL,OAAO,GAAG1R,MAAM,CAACqR,MAAM,EAAE;IAC3BF,MAAM;IACNC,MAAM;IACN;IACAO,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE;EACX,CAAC,CAAC;EACF,IAAIR,MAAM,EAAE;IACR;IACA;IACA;IACA,IAAI,CAACM,OAAO,CAACP,MAAM,CAAClM,OAAO,KAAK,CAACmM,MAAM,CAACD,MAAM,CAAClM,OAAO,EAClDmM,MAAM,CAACO,QAAQ,CAAC/L,IAAI,CAAC8L,OAAO,CAAC;EACrC;EACA,OAAOA,OAAO;AAClB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,mBAAmBA,CAACC,MAAM,EAAEC,aAAa,EAAE;EAChD;EACA,MAAMC,QAAQ,GAAG,EAAE;EACnB,MAAMC,UAAU,GAAG,IAAIvJ,GAAG,CAAC,CAAC;EAC5BqJ,aAAa,GAAGG,YAAY,CAAC;IAAE/D,MAAM,EAAE,KAAK;IAAEE,GAAG,EAAE,IAAI;IAAEH,SAAS,EAAE;EAAM,CAAC,EAAE6D,aAAa,CAAC;EAC3F,SAASI,gBAAgBA,CAACjM,IAAI,EAAE;IAC5B,OAAO+L,UAAU,CAACjJ,GAAG,CAAC9C,IAAI,CAAC;EAC/B;EACA,SAASkM,QAAQA,CAACjB,MAAM,EAAEC,MAAM,EAAEiB,cAAc,EAAE;IAC9C;IACA,MAAMC,SAAS,GAAG,CAACD,cAAc;IACjC,MAAME,oBAAoB,GAAGC,oBAAoB,CAACrB,MAAM,CAAC;IACzD,IAAKpO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAG;MACzCwP,kCAAkC,CAACF,oBAAoB,EAAEnB,MAAM,CAAC;IACpE;IACA;IACAmB,oBAAoB,CAACtN,OAAO,GAAGoN,cAAc,IAAIA,cAAc,CAAClB,MAAM;IACtE,MAAMzC,OAAO,GAAGwD,YAAY,CAACH,aAAa,EAAEZ,MAAM,CAAC;IACnD;IACA,MAAMuB,iBAAiB,GAAG,CAACH,oBAAoB,CAAC;IAChD,IAAI,OAAO,IAAIpB,MAAM,EAAE;MACnB,MAAMwB,OAAO,GAAG,OAAOxB,MAAM,CAACS,KAAK,KAAK,QAAQ,GAAG,CAACT,MAAM,CAACS,KAAK,CAAC,GAAGT,MAAM,CAACS,KAAK;MAChF,KAAK,MAAMA,KAAK,IAAIe,OAAO,EAAE;QACzBD,iBAAiB,CAAC9M,IAAI;QACtB;QACA;QACA4M,oBAAoB,CAACxS,MAAM,CAAC,CAAC,CAAC,EAAEuS,oBAAoB,EAAE;UAClD;UACA;UACAK,UAAU,EAAEP,cAAc,GACpBA,cAAc,CAAClB,MAAM,CAACyB,UAAU,GAChCL,oBAAoB,CAACK,UAAU;UACrCxP,IAAI,EAAEwO,KAAK;UACX;UACA3M,OAAO,EAAEoN,cAAc,GACjBA,cAAc,CAAClB,MAAM,GACrBoB;UACN;UACA;QACJ,CAAC,CAAC,CAAC,CAAC;MACR;IACJ;IACA,IAAIb,OAAO;IACX,IAAImB,eAAe;IACnB,KAAK,MAAMC,gBAAgB,IAAIJ,iBAAiB,EAAE;MAC9C,MAAM;QAAEtP;MAAK,CAAC,GAAG0P,gBAAgB;MACjC;MACA;MACA;MACA,IAAI1B,MAAM,IAAIhO,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QAC3B,MAAM2P,UAAU,GAAG3B,MAAM,CAACD,MAAM,CAAC/N,IAAI;QACrC,MAAM4P,eAAe,GAAGD,UAAU,CAACA,UAAU,CAAChP,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,GAAG,EAAE,GAAG,GAAG;QAC5E+O,gBAAgB,CAAC1P,IAAI,GACjBgO,MAAM,CAACD,MAAM,CAAC/N,IAAI,IAAIA,IAAI,IAAI4P,eAAe,GAAG5P,IAAI,CAAC;MAC7D;MACA,IAAKL,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAK6P,gBAAgB,CAAC1P,IAAI,KAAK,GAAG,EAAE;QAC1E,MAAM,IAAIyK,KAAK,CAAC,kFAAkF,GAC9F,yFAAyF,CAAC;MAClG;MACA;MACA6D,OAAO,GAAGR,wBAAwB,CAAC4B,gBAAgB,EAAE1B,MAAM,EAAE1C,OAAO,CAAC;MACrE,IAAK3L,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAKmO,MAAM,IAAIhO,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EACpE6P,gCAAgC,CAACvB,OAAO,EAAEN,MAAM,CAAC;MACrD;MACA;MACA,IAAIiB,cAAc,EAAE;QAChBA,cAAc,CAACT,KAAK,CAAChM,IAAI,CAAC8L,OAAO,CAAC;QAClC,IAAK3O,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAG;UACzCiQ,eAAe,CAACb,cAAc,EAAEX,OAAO,CAAC;QAC5C;MACJ,CAAC,MACI;QACD;QACAmB,eAAe,GAAGA,eAAe,IAAInB,OAAO;QAC5C,IAAImB,eAAe,KAAKnB,OAAO,EAC3BmB,eAAe,CAACjB,KAAK,CAAChM,IAAI,CAAC8L,OAAO,CAAC;QACvC;QACA;QACA,IAAIY,SAAS,IAAInB,MAAM,CAACjL,IAAI,IAAI,CAACiN,aAAa,CAACzB,OAAO,CAAC,EAAE;UACrD,IAAK3O,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAG;YACzCmQ,uBAAuB,CAACjC,MAAM,EAAEC,MAAM,CAAC;UAC3C;UACAiC,WAAW,CAAClC,MAAM,CAACjL,IAAI,CAAC;QAC5B;MACJ;MACA;MACA;MACA,IAAIoN,WAAW,CAAC5B,OAAO,CAAC,EAAE;QACtB6B,aAAa,CAAC7B,OAAO,CAAC;MAC1B;MACA,IAAIa,oBAAoB,CAACZ,QAAQ,EAAE;QAC/B,MAAMA,QAAQ,GAAGY,oBAAoB,CAACZ,QAAQ;QAC9C,KAAK,IAAIrM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqM,QAAQ,CAAC5N,MAAM,EAAEuB,CAAC,EAAE,EAAE;UACtC8M,QAAQ,CAACT,QAAQ,CAACrM,CAAC,CAAC,EAAEoM,OAAO,EAAEW,cAAc,IAAIA,cAAc,CAACV,QAAQ,CAACrM,CAAC,CAAC,CAAC;QAChF;MACJ;MACA;MACA;MACA+M,cAAc,GAAGA,cAAc,IAAIX,OAAO;MAC1C;MACA;MACA;MACA;IACJ;IACA,OAAOmB,eAAe,GAChB,MAAM;MACJ;MACAQ,WAAW,CAACR,eAAe,CAAC;IAChC,CAAC,GACCnS,IAAI;EACd;EACA,SAAS2S,WAAWA,CAACG,UAAU,EAAE;IAC7B,IAAInG,WAAW,CAACmG,UAAU,CAAC,EAAE;MACzB,MAAM9B,OAAO,GAAGO,UAAU,CAACjJ,GAAG,CAACwK,UAAU,CAAC;MAC1C,IAAI9B,OAAO,EAAE;QACTO,UAAU,CAAChJ,MAAM,CAACuK,UAAU,CAAC;QAC7BxB,QAAQ,CAAClH,MAAM,CAACkH,QAAQ,CAACnO,OAAO,CAAC6N,OAAO,CAAC,EAAE,CAAC,CAAC;QAC7CA,OAAO,CAACC,QAAQ,CAAC1H,OAAO,CAACoJ,WAAW,CAAC;QACrC3B,OAAO,CAACE,KAAK,CAAC3H,OAAO,CAACoJ,WAAW,CAAC;MACtC;IACJ,CAAC,MACI;MACD,MAAMxI,KAAK,GAAGmH,QAAQ,CAACnO,OAAO,CAAC2P,UAAU,CAAC;MAC1C,IAAI3I,KAAK,GAAG,CAAC,CAAC,EAAE;QACZmH,QAAQ,CAAClH,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;QACzB,IAAI2I,UAAU,CAACrC,MAAM,CAACjL,IAAI,EACtB+L,UAAU,CAAChJ,MAAM,CAACuK,UAAU,CAACrC,MAAM,CAACjL,IAAI,CAAC;QAC7CsN,UAAU,CAAC7B,QAAQ,CAAC1H,OAAO,CAACoJ,WAAW,CAAC;QACxCG,UAAU,CAAC5B,KAAK,CAAC3H,OAAO,CAACoJ,WAAW,CAAC;MACzC;IACJ;EACJ;EACA,SAASI,SAASA,CAAA,EAAG;IACjB,OAAOzB,QAAQ;EACnB;EACA,SAASuB,aAAaA,CAAC7B,OAAO,EAAE;IAC5B,MAAM7G,KAAK,GAAG6I,kBAAkB,CAAChC,OAAO,EAAEM,QAAQ,CAAC;IACnDA,QAAQ,CAAClH,MAAM,CAACD,KAAK,EAAE,CAAC,EAAE6G,OAAO,CAAC;IAClC;IACA,IAAIA,OAAO,CAACP,MAAM,CAACjL,IAAI,IAAI,CAACiN,aAAa,CAACzB,OAAO,CAAC,EAC9CO,UAAU,CAACpJ,GAAG,CAAC6I,OAAO,CAACP,MAAM,CAACjL,IAAI,EAAEwL,OAAO,CAAC;EACpD;EACA,SAASiC,OAAOA,CAACpQ,QAAQ,EAAEC,eAAe,EAAE;IACxC,IAAIkO,OAAO;IACX,IAAItR,MAAM,GAAG,CAAC,CAAC;IACf,IAAIgD,IAAI;IACR,IAAI8C,IAAI;IACR,IAAI,MAAM,IAAI3C,QAAQ,IAAIA,QAAQ,CAAC2C,IAAI,EAAE;MACrCwL,OAAO,GAAGO,UAAU,CAACjJ,GAAG,CAACzF,QAAQ,CAAC2C,IAAI,CAAC;MACvC,IAAI,CAACwL,OAAO,EACR,MAAM9D,iBAAiB,CAAC,CAAC,CAAC,oCAAoC;QAC1DrK;MACJ,CAAC,CAAC;MACN;MACA,IAAKR,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAG;QACzC,MAAM2Q,aAAa,GAAG3T,MAAM,CAACiF,IAAI,CAAC3B,QAAQ,CAACnD,MAAM,IAAI,CAAC,CAAC,CAAC,CAACyT,MAAM,CAACC,SAAS,IAAI,CAACpC,OAAO,CAACxM,IAAI,CAAC6O,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC9N,IAAI,KAAK4N,SAAS,CAAC,CAAC;QAC3H,IAAIF,aAAa,CAAC7P,MAAM,EAAE;UACtBnD,IAAI,CAAC,+BAA+BgT,aAAa,CAAC5N,IAAI,CAAC,MAAM,CAAC,gIAAgI,CAAC;QACnM;MACJ;MACAE,IAAI,GAAGwL,OAAO,CAACP,MAAM,CAACjL,IAAI;MAC1B9F,MAAM,GAAGJ,MAAM;MACf;MACAiU,kBAAkB,CAACzQ,eAAe,CAACpD,MAAM;MACzC;MACA;MACAsR,OAAO,CAACxM,IAAI,CACP2O,MAAM,CAACG,CAAC,IAAI,CAACA,CAAC,CAAC9E,QAAQ,CAAC,CACxB9N,MAAM,CAACsQ,OAAO,CAACN,MAAM,GAAGM,OAAO,CAACN,MAAM,CAAClM,IAAI,CAAC2O,MAAM,CAACG,CAAC,IAAIA,CAAC,CAAC9E,QAAQ,CAAC,GAAG,EAAE,CAAC,CACzEzO,GAAG,CAACuT,CAAC,IAAIA,CAAC,CAAC9N,IAAI,CAAC,CAAC;MACtB;MACA;MACA3C,QAAQ,CAACnD,MAAM,IACX6T,kBAAkB,CAAC1Q,QAAQ,CAACnD,MAAM,EAAEsR,OAAO,CAACxM,IAAI,CAACzE,GAAG,CAACuT,CAAC,IAAIA,CAAC,CAAC9N,IAAI,CAAC,CAAC,CAAC;MACvE;MACA9C,IAAI,GAAGsO,OAAO,CAAChE,SAAS,CAACtN,MAAM,CAAC;IACpC,CAAC,MACI,IAAImD,QAAQ,CAACH,IAAI,IAAI,IAAI,EAAE;MAC5B;MACA;MACAA,IAAI,GAAGG,QAAQ,CAACH,IAAI;MACpB,IAAKL,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAK,CAACG,IAAI,CAACoB,UAAU,CAAC,GAAG,CAAC,EAAE;QAClE5D,IAAI,CAAC,2DAA2DwC,IAAI,oDAAoDA,IAAI,wHAAwH,CAAC;MACzP;MACAsO,OAAO,GAAGM,QAAQ,CAAC+B,IAAI,CAACG,CAAC,IAAIA,CAAC,CAAC9E,EAAE,CAAC6B,IAAI,CAAC7N,IAAI,CAAC,CAAC;MAC7C;MACA,IAAIsO,OAAO,EAAE;QACT;QACAtR,MAAM,GAAGsR,OAAO,CAAClC,KAAK,CAACpM,IAAI,CAAC;QAC5B8C,IAAI,GAAGwL,OAAO,CAACP,MAAM,CAACjL,IAAI;MAC9B;MACA;IACJ,CAAC,MACI;MACD;MACAwL,OAAO,GAAGlO,eAAe,CAAC0C,IAAI,GACxB+L,UAAU,CAACjJ,GAAG,CAACxF,eAAe,CAAC0C,IAAI,CAAC,GACpC8L,QAAQ,CAAC+B,IAAI,CAACG,CAAC,IAAIA,CAAC,CAAC9E,EAAE,CAAC6B,IAAI,CAACzN,eAAe,CAACJ,IAAI,CAAC,CAAC;MACzD,IAAI,CAACsO,OAAO,EACR,MAAM9D,iBAAiB,CAAC,CAAC,CAAC,oCAAoC;QAC1DrK,QAAQ;QACRC;MACJ,CAAC,CAAC;MACN0C,IAAI,GAAGwL,OAAO,CAACP,MAAM,CAACjL,IAAI;MAC1B;MACA;MACA9F,MAAM,GAAGJ,MAAM,CAAC,CAAC,CAAC,EAAEwD,eAAe,CAACpD,MAAM,EAAEmD,QAAQ,CAACnD,MAAM,CAAC;MAC5DgD,IAAI,GAAGsO,OAAO,CAAChE,SAAS,CAACtN,MAAM,CAAC;IACpC;IACA,MAAMyE,OAAO,GAAG,EAAE;IAClB,IAAIsP,aAAa,GAAGzC,OAAO;IAC3B,OAAOyC,aAAa,EAAE;MAClB;MACAtP,OAAO,CAACuP,OAAO,CAACD,aAAa,CAAChD,MAAM,CAAC;MACrCgD,aAAa,GAAGA,aAAa,CAAC/C,MAAM;IACxC;IACA,OAAO;MACHlL,IAAI;MACJ9C,IAAI;MACJhD,MAAM;MACNyE,OAAO;MACPuB,IAAI,EAAEiO,eAAe,CAACxP,OAAO;IACjC,CAAC;EACL;EACA;EACAiN,MAAM,CAAC7H,OAAO,CAACmD,KAAK,IAAIgF,QAAQ,CAAChF,KAAK,CAAC,CAAC;EACxC,SAASkH,WAAWA,CAAA,EAAG;IACnBtC,QAAQ,CAACjO,MAAM,GAAG,CAAC;IACnBkO,UAAU,CAACsC,KAAK,CAAC,CAAC;EACtB;EACA,OAAO;IACHnC,QAAQ;IACRuB,OAAO;IACPN,WAAW;IACXiB,WAAW;IACXb,SAAS;IACTtB;EACJ,CAAC;AACL;AACA,SAAS8B,kBAAkBA,CAAC7T,MAAM,EAAE8E,IAAI,EAAE;EACtC,MAAM7E,SAAS,GAAG,CAAC,CAAC;EACpB,KAAK,MAAMC,GAAG,IAAI4E,IAAI,EAAE;IACpB,IAAI5E,GAAG,IAAIF,MAAM,EACbC,SAAS,CAACC,GAAG,CAAC,GAAGF,MAAM,CAACE,GAAG,CAAC;EACpC;EACA,OAAOD,SAAS;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmS,oBAAoBA,CAACrB,MAAM,EAAE;EAClC,MAAMqD,UAAU,GAAG;IACfpR,IAAI,EAAE+N,MAAM,CAAC/N,IAAI;IACjBqR,QAAQ,EAAEtD,MAAM,CAACsD,QAAQ;IACzBvO,IAAI,EAAEiL,MAAM,CAACjL,IAAI;IACjBE,IAAI,EAAE+K,MAAM,CAAC/K,IAAI,IAAI,CAAC,CAAC;IACvBnB,OAAO,EAAEkM,MAAM,CAAClM,OAAO;IACvByP,WAAW,EAAEvD,MAAM,CAACuD,WAAW;IAC/BC,KAAK,EAAEC,oBAAoB,CAACzD,MAAM,CAAC;IACnCQ,QAAQ,EAAER,MAAM,CAACQ,QAAQ,IAAI,EAAE;IAC/BkD,SAAS,EAAE,CAAC,CAAC;IACbC,WAAW,EAAE,IAAIvD,GAAG,CAAC,CAAC;IACtBwD,YAAY,EAAE,IAAIxD,GAAG,CAAC,CAAC;IACvByD,cAAc,EAAE,CAAC,CAAC;IAClB;IACA;IACApC,UAAU,EAAE,YAAY,IAAIzB,MAAM,GAC5BA,MAAM,CAACyB,UAAU,IAAI,IAAI,GACzBzB,MAAM,CAAC1R,SAAS,IAAI;MAAEM,OAAO,EAAEoR,MAAM,CAAC1R;IAAU;EAC1D,CAAC;EACD;EACA;EACA;EACAQ,MAAM,CAACsM,cAAc,CAACiI,UAAU,EAAE,MAAM,EAAE;IACtCjU,KAAK,EAAE,CAAC;EACZ,CAAC,CAAC;EACF,OAAOiU,UAAU;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,oBAAoBA,CAACzD,MAAM,EAAE;EAClC,MAAM8D,WAAW,GAAG,CAAC,CAAC;EACtB;EACA,MAAMN,KAAK,GAAGxD,MAAM,CAACwD,KAAK,IAAI,KAAK;EACnC,IAAI,WAAW,IAAIxD,MAAM,EAAE;IACvB8D,WAAW,CAAClV,OAAO,GAAG4U,KAAK;EAC/B,CAAC,MACI;IACD;IACA;IACA,KAAK,MAAMzO,IAAI,IAAIiL,MAAM,CAACyB,UAAU,EAChCqC,WAAW,CAAC/O,IAAI,CAAC,GAAG,OAAOyO,KAAK,KAAK,QAAQ,GAAGA,KAAK,CAACzO,IAAI,CAAC,GAAGyO,KAAK;EAC3E;EACA,OAAOM,WAAW;AACtB;AACA;AACA;AACA;AACA;AACA,SAAS9B,aAAaA,CAAChC,MAAM,EAAE;EAC3B,OAAOA,MAAM,EAAE;IACX,IAAIA,MAAM,CAACA,MAAM,CAAClM,OAAO,EACrB,OAAO,IAAI;IACfkM,MAAM,GAAGA,MAAM,CAACC,MAAM;EAC1B;EACA,OAAO,KAAK;AAChB;AACA;AACA;AACA;AACA;AACA;AACA,SAASiD,eAAeA,CAACxP,OAAO,EAAE;EAC9B,OAAOA,OAAO,CAACqQ,MAAM,CAAC,CAAC9O,IAAI,EAAE+K,MAAM,KAAKnR,MAAM,CAACoG,IAAI,EAAE+K,MAAM,CAAC/K,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AAC1E;AACA,SAAS8L,YAAYA,CAACiD,QAAQ,EAAEC,cAAc,EAAE;EAC5C,MAAM1G,OAAO,GAAG,CAAC,CAAC;EAClB,KAAK,MAAMpO,GAAG,IAAI6U,QAAQ,EAAE;IACxBzG,OAAO,CAACpO,GAAG,CAAC,GAAGA,GAAG,IAAI8U,cAAc,GAAGA,cAAc,CAAC9U,GAAG,CAAC,GAAG6U,QAAQ,CAAC7U,GAAG,CAAC;EAC9E;EACA,OAAOoO,OAAO;AAClB;AACA,SAAS2G,WAAWA,CAAC3Q,CAAC,EAAEC,CAAC,EAAE;EACvB,OAAQD,CAAC,CAACwB,IAAI,KAAKvB,CAAC,CAACuB,IAAI,IACrBxB,CAAC,CAACwK,QAAQ,KAAKvK,CAAC,CAACuK,QAAQ,IACzBxK,CAAC,CAACuK,UAAU,KAAKtK,CAAC,CAACsK,UAAU;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASiE,eAAeA,CAACxO,CAAC,EAAEC,CAAC,EAAE;EAC3B,KAAK,MAAMrE,GAAG,IAAIoE,CAAC,CAACQ,IAAI,EAAE;IACtB,IAAI,CAAC5E,GAAG,CAAC4O,QAAQ,IAAI,CAACvK,CAAC,CAACO,IAAI,CAAC6O,IAAI,CAACsB,WAAW,CAAC/I,IAAI,CAAC,IAAI,EAAEhM,GAAG,CAAC,CAAC,EAC1D,OAAOM,IAAI,CAAC,UAAU+D,CAAC,CAACwM,MAAM,CAAC/N,IAAI,+BAA+BsB,CAAC,CAACyM,MAAM,CAAC/N,IAAI,2CAA2C9C,GAAG,CAAC4F,IAAI,GAAG,CAAC;EAC9I;EACA,KAAK,MAAM5F,GAAG,IAAIqE,CAAC,CAACO,IAAI,EAAE;IACtB,IAAI,CAAC5E,GAAG,CAAC4O,QAAQ,IAAI,CAACxK,CAAC,CAACQ,IAAI,CAAC6O,IAAI,CAACsB,WAAW,CAAC/I,IAAI,CAAC,IAAI,EAAEhM,GAAG,CAAC,CAAC,EAC1D,OAAOM,IAAI,CAAC,UAAU+D,CAAC,CAACwM,MAAM,CAAC/N,IAAI,+BAA+BsB,CAAC,CAACyM,MAAM,CAAC/N,IAAI,2CAA2C9C,GAAG,CAAC4F,IAAI,GAAG,CAAC;EAC9I;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuM,kCAAkCA,CAACF,oBAAoB,EAAEnB,MAAM,EAAE;EACtE,IAAIA,MAAM,IACNA,MAAM,CAACD,MAAM,CAACjL,IAAI,IAClB,CAACqM,oBAAoB,CAACrM,IAAI,IAC1B,CAACqM,oBAAoB,CAACnP,IAAI,EAAE;IAC5BxC,IAAI,CAAC,oBAAoB0U,MAAM,CAAClE,MAAM,CAACD,MAAM,CAACjL,IAAI,CAAC,4OAA4O,CAAC;EACpS;AACJ;AACA,SAASkN,uBAAuBA,CAACjC,MAAM,EAAEC,MAAM,EAAE;EAC7C,KAAK,IAAImE,QAAQ,GAAGnE,MAAM,EAAEmE,QAAQ,EAAEA,QAAQ,GAAGA,QAAQ,CAACnE,MAAM,EAAE;IAC9D,IAAImE,QAAQ,CAACpE,MAAM,CAACjL,IAAI,KAAKiL,MAAM,CAACjL,IAAI,EAAE;MACtC,MAAM,IAAI2H,KAAK,CAAC,kBAAkByH,MAAM,CAACnE,MAAM,CAACjL,IAAI,CAAC,yBAAyBkL,MAAM,KAAKmE,QAAQ,GAAG,OAAO,GAAG,YAAY,wHAAwH,CAAC;IACvP;EACJ;AACJ;AACA,SAAStC,gCAAgCA,CAAC9B,MAAM,EAAEC,MAAM,EAAE;EACtD,KAAK,MAAM9Q,GAAG,IAAI8Q,MAAM,CAAClM,IAAI,EAAE;IAC3B,IAAI,CAACiM,MAAM,CAACjM,IAAI,CAAC6O,IAAI,CAACsB,WAAW,CAAC/I,IAAI,CAAC,IAAI,EAAEhM,GAAG,CAAC,CAAC,EAC9C,OAAOM,IAAI,CAAC,kBAAkBuQ,MAAM,CAACA,MAAM,CAAC/N,IAAI,2CAA2C9C,GAAG,CAAC4F,IAAI,oBAAoBkL,MAAM,CAACD,MAAM,CAAC/N,IAAI,IAAI,CAAC;EACtJ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASsQ,kBAAkBA,CAAChC,OAAO,EAAEM,QAAQ,EAAE;EAC3C;EACA,IAAIwD,KAAK,GAAG,CAAC;EACb,IAAIC,KAAK,GAAGzD,QAAQ,CAACjO,MAAM;EAC3B,OAAOyR,KAAK,KAAKC,KAAK,EAAE;IACpB,MAAMC,GAAG,GAAIF,KAAK,GAAGC,KAAK,IAAK,CAAC;IAChC,MAAME,SAAS,GAAG7F,sBAAsB,CAAC4B,OAAO,EAAEM,QAAQ,CAAC0D,GAAG,CAAC,CAAC;IAChE,IAAIC,SAAS,GAAG,CAAC,EAAE;MACfF,KAAK,GAAGC,GAAG;IACf,CAAC,MACI;MACDF,KAAK,GAAGE,GAAG,GAAG,CAAC;IACnB;EACJ;EACA;EACA,MAAME,iBAAiB,GAAGC,oBAAoB,CAACnE,OAAO,CAAC;EACvD,IAAIkE,iBAAiB,EAAE;IACnBH,KAAK,GAAGzD,QAAQ,CAAC8D,WAAW,CAACF,iBAAiB,EAAEH,KAAK,GAAG,CAAC,CAAC;IAC1D,IAAK1S,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAKwS,KAAK,GAAG,CAAC,EAAE;MACtD;MACA7U,IAAI,CAAC,2BAA2BgV,iBAAiB,CAACzE,MAAM,CAAC/N,IAAI,iBAAiBsO,OAAO,CAACP,MAAM,CAAC/N,IAAI,GAAG,CAAC;IACzG;EACJ;EACA,OAAOqS,KAAK;AAChB;AACA,SAASI,oBAAoBA,CAACnE,OAAO,EAAE;EACnC,IAAI6D,QAAQ,GAAG7D,OAAO;EACtB,OAAQ6D,QAAQ,GAAGA,QAAQ,CAACnE,MAAM,EAAG;IACjC,IAAIkC,WAAW,CAACiC,QAAQ,CAAC,IACrBzF,sBAAsB,CAAC4B,OAAO,EAAE6D,QAAQ,CAAC,KAAK,CAAC,EAAE;MACjD,OAAOA,QAAQ;IACnB;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASjC,WAAWA,CAAC;EAAEnC;AAAO,CAAC,EAAE;EAC7B,OAAO,CAAC,EAAEA,MAAM,CAACjL,IAAI,IAChBiL,MAAM,CAACyB,UAAU,IAAI3S,MAAM,CAACiF,IAAI,CAACiM,MAAM,CAACyB,UAAU,CAAC,CAAC7O,MAAO,IAC5DoN,MAAM,CAACsD,QAAQ,CAAC;AACxB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASnR,UAAUA,CAACgG,MAAM,EAAE;EACxB,MAAM7F,KAAK,GAAG,CAAC,CAAC;EAChB;EACA;EACA,IAAI6F,MAAM,KAAK,EAAE,IAAIA,MAAM,KAAK,GAAG,EAC/B,OAAO7F,KAAK;EAChB,MAAMsS,YAAY,GAAGzM,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG;EACtC,MAAM0M,YAAY,GAAG,CAACD,YAAY,GAAGzM,MAAM,CAACrI,KAAK,CAAC,CAAC,CAAC,GAAGqI,MAAM,EAAE7D,KAAK,CAAC,GAAG,CAAC;EACzE,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0Q,YAAY,CAACjS,MAAM,EAAE,EAAEuB,CAAC,EAAE;IAC1C;IACA,MAAM2Q,WAAW,GAAGD,YAAY,CAAC1Q,CAAC,CAAC,CAAChD,OAAO,CAACZ,OAAO,EAAE,GAAG,CAAC;IACzD;IACA,MAAMwU,KAAK,GAAGD,WAAW,CAACpS,OAAO,CAAC,GAAG,CAAC;IACtC,MAAMvD,GAAG,GAAGsC,MAAM,CAACsT,KAAK,GAAG,CAAC,GAAGD,WAAW,GAAGA,WAAW,CAAChV,KAAK,CAAC,CAAC,EAAEiV,KAAK,CAAC,CAAC;IACzE,MAAM3V,KAAK,GAAG2V,KAAK,GAAG,CAAC,GAAG,IAAI,GAAGtT,MAAM,CAACqT,WAAW,CAAChV,KAAK,CAACiV,KAAK,GAAG,CAAC,CAAC,CAAC;IACrE,IAAI5V,GAAG,IAAImD,KAAK,EAAE;MACd;MACA,IAAI0S,YAAY,GAAG1S,KAAK,CAACnD,GAAG,CAAC;MAC7B,IAAI,CAACE,OAAO,CAAC2V,YAAY,CAAC,EAAE;QACxBA,YAAY,GAAG1S,KAAK,CAACnD,GAAG,CAAC,GAAG,CAAC6V,YAAY,CAAC;MAC9C;MACAA,YAAY,CAACvQ,IAAI,CAACrF,KAAK,CAAC;IAC5B,CAAC,MACI;MACDkD,KAAK,CAACnD,GAAG,CAAC,GAAGC,KAAK;IACtB;EACJ;EACA,OAAOkD,KAAK;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASU,cAAcA,CAACV,KAAK,EAAE;EAC3B,IAAI6F,MAAM,GAAG,EAAE;EACf,KAAK,IAAIhJ,GAAG,IAAImD,KAAK,EAAE;IACnB,MAAMlD,KAAK,GAAGkD,KAAK,CAACnD,GAAG,CAAC;IACxBA,GAAG,GAAGmC,cAAc,CAACnC,GAAG,CAAC;IACzB,IAAIC,KAAK,IAAI,IAAI,EAAE;MACf;MACA,IAAIA,KAAK,KAAK4F,SAAS,EAAE;QACrBmD,MAAM,IAAI,CAACA,MAAM,CAACvF,MAAM,GAAG,GAAG,GAAG,EAAE,IAAIzD,GAAG;MAC9C;MACA;IACJ;IACA;IACA,MAAM8V,MAAM,GAAG5V,OAAO,CAACD,KAAK,CAAC,GACvBA,KAAK,CAACE,GAAG,CAAC4V,CAAC,IAAIA,CAAC,IAAI7T,gBAAgB,CAAC6T,CAAC,CAAC,CAAC,GACxC,CAAC9V,KAAK,IAAIiC,gBAAgB,CAACjC,KAAK,CAAC,CAAC;IACxC6V,MAAM,CAACnM,OAAO,CAAC1J,KAAK,IAAI;MACpB;MACA;MACA,IAAIA,KAAK,KAAK4F,SAAS,EAAE;QACrB;QACAmD,MAAM,IAAI,CAACA,MAAM,CAACvF,MAAM,GAAG,GAAG,GAAG,EAAE,IAAIzD,GAAG;QAC1C,IAAIC,KAAK,IAAI,IAAI,EACb+I,MAAM,IAAI,GAAG,GAAG/I,KAAK;MAC7B;IACJ,CAAC,CAAC;EACN;EACA,OAAO+I,MAAM;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASgN,cAAcA,CAAC7S,KAAK,EAAE;EAC3B,MAAM8S,eAAe,GAAG,CAAC,CAAC;EAC1B,KAAK,MAAMjW,GAAG,IAAImD,KAAK,EAAE;IACrB,MAAMlD,KAAK,GAAGkD,KAAK,CAACnD,GAAG,CAAC;IACxB,IAAIC,KAAK,KAAK4F,SAAS,EAAE;MACrBoQ,eAAe,CAACjW,GAAG,CAAC,GAAGE,OAAO,CAACD,KAAK,CAAC,GAC/BA,KAAK,CAACE,GAAG,CAAC4V,CAAC,IAAKA,CAAC,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,GAAGA,CAAE,CAAC,GAC3C9V,KAAK,IAAI,IAAI,GACTA,KAAK,GACL,EAAE,GAAGA,KAAK;IACxB;EACJ;EACA,OAAOgW,eAAe;AAC1B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAG3W,MAAM,CAAEkD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAI,8BAA8B,GAAG,EAAE,CAAC;AAC7G;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwT,YAAY,GAAG5W,MAAM,CAAEkD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAI,mBAAmB,GAAG,EAAE,CAAC;AAC/F;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyT,SAAS,GAAG7W,MAAM,CAAEkD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAI,QAAQ,GAAG,EAAE,CAAC;AACjF;AACA;AACA;AACA;AACA;AACA;AACA,MAAM0T,gBAAgB,GAAG9W,MAAM,CAAEkD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAI,gBAAgB,GAAG,EAAE,CAAC;AAChG;AACA;AACA;AACA;AACA;AACA;AACA,MAAM2T,qBAAqB,GAAG/W,MAAM,CAAEkD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAI,sBAAsB,GAAG,EAAE,CAAC;;AAE3G;AACA;AACA;AACA,SAAS4T,YAAYA,CAAA,EAAG;EACpB,IAAIC,QAAQ,GAAG,EAAE;EACjB,SAASrF,GAAGA,CAACsF,OAAO,EAAE;IAClBD,QAAQ,CAAClR,IAAI,CAACmR,OAAO,CAAC;IACtB,OAAO,MAAM;MACT,MAAMzR,CAAC,GAAGwR,QAAQ,CAACjT,OAAO,CAACkT,OAAO,CAAC;MACnC,IAAIzR,CAAC,GAAG,CAAC,CAAC,EACNwR,QAAQ,CAAChM,MAAM,CAACxF,CAAC,EAAE,CAAC,CAAC;IAC7B,CAAC;EACL;EACA,SAAS0R,KAAKA,CAAA,EAAG;IACbF,QAAQ,GAAG,EAAE;EACjB;EACA,OAAO;IACHrF,GAAG;IACHwF,IAAI,EAAEA,CAAA,KAAMH,QAAQ,CAAC7V,KAAK,CAAC,CAAC;IAC5B+V;EACJ,CAAC;AACL;AAEA,SAASE,aAAaA,CAAC/F,MAAM,EAAEjL,IAAI,EAAEiR,KAAK,EAAE;EACxC,MAAMC,cAAc,GAAGA,CAAA,KAAM;IACzBjG,MAAM,CAACjL,IAAI,CAAC,CAAC+C,MAAM,CAACkO,KAAK,CAAC;EAC9B,CAAC;EACD7Y,WAAW,CAAC8Y,cAAc,CAAC;EAC3B7Y,aAAa,CAAC6Y,cAAc,CAAC;EAC7B5Y,WAAW,CAAC,MAAM;IACd2S,MAAM,CAACjL,IAAI,CAAC,CAACuL,GAAG,CAAC0F,KAAK,CAAC;EAC3B,CAAC,CAAC;EACFhG,MAAM,CAACjL,IAAI,CAAC,CAACuL,GAAG,CAAC0F,KAAK,CAAC;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,kBAAkBA,CAACC,UAAU,EAAE;EACpC,IAAKvU,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAK,CAAC7E,kBAAkB,CAAC,CAAC,EAAE;IAClEwC,IAAI,CAAC,wGAAwG,CAAC;IAC9G;EACJ;EACA,MAAM2W,YAAY,GAAGlZ,MAAM,CAACmY,eAAe;EAC3C;EACA,CAAC,CAAC,CAAC,CAACjW,KAAK;EACT,IAAI,CAACgX,YAAY,EAAE;IACdxU,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAClCrC,IAAI,CAAC,0LAA0L,CAAC;IACpM;EACJ;EACAsW,aAAa,CAACK,YAAY,EAAE,aAAa,EAAED,UAAU,CAAC;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,mBAAmBA,CAACC,WAAW,EAAE;EACtC,IAAK1U,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAK,CAAC7E,kBAAkB,CAAC,CAAC,EAAE;IAClEwC,IAAI,CAAC,yGAAyG,CAAC;IAC/G;EACJ;EACA,MAAM2W,YAAY,GAAGlZ,MAAM,CAACmY,eAAe;EAC3C;EACA,CAAC,CAAC,CAAC,CAACjW,KAAK;EACT,IAAI,CAACgX,YAAY,EAAE;IACdxU,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAClCrC,IAAI,CAAC,2LAA2L,CAAC;IACrM;EACJ;EACAsW,aAAa,CAACK,YAAY,EAAE,cAAc,EAAEE,WAAW,CAAC;AAC5D;AACA,SAASC,gBAAgBA,CAACP,KAAK,EAAE5R,EAAE,EAAExE,IAAI,EAAEoQ,MAAM,EAAEjL,IAAI,EAAEyR,cAAc,GAAGxX,EAAE,IAAIA,EAAE,CAAC,CAAC,EAAE;EAClF;EACA,MAAMyX,kBAAkB,GAAGzG,MAAM;EAC7B;EACCA,MAAM,CAAC6D,cAAc,CAAC9O,IAAI,CAAC,GAAGiL,MAAM,CAAC6D,cAAc,CAAC9O,IAAI,CAAC,IAAI,EAAE,CAAC;EACrE,OAAO,MAAM,IAAI2R,OAAO,CAAC,CAAClE,OAAO,EAAEmE,MAAM,KAAK;IAC1C,MAAMC,IAAI,GAAIC,KAAK,IAAK;MACpB,IAAIA,KAAK,KAAK,KAAK,EAAE;QACjBF,MAAM,CAAClK,iBAAiB,CAAC,CAAC,CAAC,qCAAqC;UAC5D7M,IAAI;UACJwE;QACJ,CAAC,CAAC,CAAC;MACP,CAAC,MACI,IAAIyS,KAAK,YAAYnK,KAAK,EAAE;QAC7BiK,MAAM,CAACE,KAAK,CAAC;MACjB,CAAC,MACI,IAAI7K,eAAe,CAAC6K,KAAK,CAAC,EAAE;QAC7BF,MAAM,CAAClK,iBAAiB,CAAC,CAAC,CAAC,4CAA4C;UACnE7M,IAAI,EAAEwE,EAAE;UACRA,EAAE,EAAEyS;QACR,CAAC,CAAC,CAAC;MACP,CAAC,MACI;QACD,IAAIJ,kBAAkB;QAClB;QACAzG,MAAM,CAAC6D,cAAc,CAAC9O,IAAI,CAAC,KAAK0R,kBAAkB,IAClD,OAAOI,KAAK,KAAK,UAAU,EAAE;UAC7BJ,kBAAkB,CAAChS,IAAI,CAACoS,KAAK,CAAC;QAClC;QACArE,OAAO,CAAC,CAAC;MACb;IACJ,CAAC;IACD;IACA,MAAMsE,WAAW,GAAGN,cAAc,CAAC,MAAMR,KAAK,CAACe,IAAI,CAAC/G,MAAM,IAAIA,MAAM,CAAC0D,SAAS,CAAC3O,IAAI,CAAC,EAAEX,EAAE,EAAExE,IAAI,EAAGgC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAIkV,mBAAmB,CAACJ,IAAI,EAAExS,EAAE,EAAExE,IAAI,CAAC,GAAGgX,IAAI,CAAC,CAAC;IACtL,IAAIK,SAAS,GAAGP,OAAO,CAAClE,OAAO,CAACsE,WAAW,CAAC;IAC5C,IAAId,KAAK,CAACpT,MAAM,GAAG,CAAC,EAChBqU,SAAS,GAAGA,SAAS,CAACC,IAAI,CAACN,IAAI,CAAC;IACpC,IAAKhV,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAKkU,KAAK,CAACpT,MAAM,GAAG,CAAC,EAAE;MAC7D,MAAMuL,OAAO,GAAG,kDAAkD6H,KAAK,CAACjR,IAAI,GAAG,GAAG,GAAGiR,KAAK,CAACjR,IAAI,GAAG,GAAG,GAAG,EAAE,MAAMiR,KAAK,CAACmB,QAAQ,CAAC,CAAC,0HAA0H;MAC1P,IAAI,OAAOL,WAAW,KAAK,QAAQ,IAAI,MAAM,IAAIA,WAAW,EAAE;QAC1DG,SAAS,GAAGA,SAAS,CAACC,IAAI,CAACE,aAAa,IAAI;UACxC;UACA,IAAI,CAACR,IAAI,CAACS,OAAO,EAAE;YACf5X,IAAI,CAAC0O,OAAO,CAAC;YACb,OAAOuI,OAAO,CAACC,MAAM,CAAC,IAAIjK,KAAK,CAAC,0BAA0B,CAAC,CAAC;UAChE;UACA,OAAO0K,aAAa;QACxB,CAAC,CAAC;MACN,CAAC,MACI,IAAIN,WAAW,KAAK9R,SAAS,EAAE;QAChC;QACA,IAAI,CAAC4R,IAAI,CAACS,OAAO,EAAE;UACf5X,IAAI,CAAC0O,OAAO,CAAC;UACbwI,MAAM,CAAC,IAAIjK,KAAK,CAAC,0BAA0B,CAAC,CAAC;UAC7C;QACJ;MACJ;IACJ;IACAuK,SAAS,CAACK,KAAK,CAAC3V,GAAG,IAAIgV,MAAM,CAAChV,GAAG,CAAC,CAAC;EACvC,CAAC,CAAC;AACN;AACA,SAASqV,mBAAmBA,CAACJ,IAAI,EAAExS,EAAE,EAAExE,IAAI,EAAE;EACzC,IAAI2X,MAAM,GAAG,CAAC;EACd,OAAO,YAAY;IACf,IAAIA,MAAM,EAAE,KAAK,CAAC,EACd9X,IAAI,CAAC,0FAA0FG,IAAI,CAACkD,QAAQ,SAASsB,EAAE,CAACtB,QAAQ,iGAAiG,CAAC;IACtO;IACA8T,IAAI,CAACS,OAAO,GAAG,IAAI;IACnB,IAAIE,MAAM,KAAK,CAAC,EACZX,IAAI,CAAC5W,KAAK,CAAC,IAAI,EAAEH,SAAS,CAAC;EACnC,CAAC;AACL;AACA,SAAS2X,uBAAuBA,CAAC9T,OAAO,EAAE+T,SAAS,EAAErT,EAAE,EAAExE,IAAI,EAAE4W,cAAc,GAAGxX,EAAE,IAAIA,EAAE,CAAC,CAAC,EAAE;EACxF,MAAM0Y,MAAM,GAAG,EAAE;EACjB,KAAK,MAAM1H,MAAM,IAAItM,OAAO,EAAE;IAC1B,IAAK9B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAK,CAACkO,MAAM,CAACyB,UAAU,IAAI,CAACzB,MAAM,CAACQ,QAAQ,CAAC5N,MAAM,EAAE;MAC1FnD,IAAI,CAAC,qBAAqBuQ,MAAM,CAAC/N,IAAI,sCAAsC,GACvE,0BAA0B,CAAC;IACnC;IACA,KAAK,MAAM8C,IAAI,IAAIiL,MAAM,CAACyB,UAAU,EAAE;MAClC,IAAIkG,YAAY,GAAG3H,MAAM,CAACyB,UAAU,CAAC1M,IAAI,CAAC;MAC1C,IAAKnD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAG;QACzC,IAAI,CAAC6V,YAAY,IACZ,OAAOA,YAAY,KAAK,QAAQ,IAC7B,OAAOA,YAAY,KAAK,UAAW,EAAE;UACzClY,IAAI,CAAC,cAAcsF,IAAI,0BAA0BiL,MAAM,CAAC/N,IAAI,UAAU,GAClE,iCAAiCkS,MAAM,CAACwD,YAAY,CAAC,IAAI,CAAC;UAC9D;UACA;UACA,MAAM,IAAIjL,KAAK,CAAC,yBAAyB,CAAC;QAC9C,CAAC,MACI,IAAI,MAAM,IAAIiL,YAAY,EAAE;UAC7B;UACA;UACAlY,IAAI,CAAC,cAAcsF,IAAI,0BAA0BiL,MAAM,CAAC/N,IAAI,SAAS,GACjE,gEAAgE,GAChE,4CAA4C,GAC5C,sDAAsD,GACtD,0BAA0B,CAAC;UAC/B,MAAM2V,OAAO,GAAGD,YAAY;UAC5BA,YAAY,GAAGA,CAAA,KAAMC,OAAO;QAChC,CAAC,MACI,IAAID,YAAY,CAACE,aAAa;QAC/B;QACA,CAACF,YAAY,CAACG,mBAAmB,EAAE;UACnCH,YAAY,CAACG,mBAAmB,GAAG,IAAI;UACvCrY,IAAI,CAAC,cAAcsF,IAAI,0BAA0BiL,MAAM,CAAC/N,IAAI,eAAe,GACvE,kCAAkC,GAClC,kDAAkD,GAClD,uDAAuD,CAAC;QAChE;MACJ;MACA;MACA,IAAIwV,SAAS,KAAK,kBAAkB,IAAI,CAACzH,MAAM,CAAC0D,SAAS,CAAC3O,IAAI,CAAC,EAC3D;MACJ,IAAI1G,gBAAgB,CAACsZ,YAAY,CAAC,EAAE;QAChC;QACA,MAAMpK,OAAO,GAAGoK,YAAY,CAACI,SAAS,IAAIJ,YAAY;QACtD,MAAM3B,KAAK,GAAGzI,OAAO,CAACkK,SAAS,CAAC;QAChCzB,KAAK,IACD0B,MAAM,CAACjT,IAAI,CAAC8R,gBAAgB,CAACP,KAAK,EAAE5R,EAAE,EAAExE,IAAI,EAAEoQ,MAAM,EAAEjL,IAAI,EAAEyR,cAAc,CAAC,CAAC;MACpF,CAAC,MACI;QACD;QACA,IAAIwB,gBAAgB,GAAGL,YAAY,CAAC,CAAC;QACrC,IAAK/V,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAK,EAAE,OAAO,IAAIkW,gBAAgB,CAAC,EAAE;UAC3EvY,IAAI,CAAC,cAAcsF,IAAI,0BAA0BiL,MAAM,CAAC/N,IAAI,4LAA4L,CAAC;UACzP+V,gBAAgB,GAAGtB,OAAO,CAAClE,OAAO,CAACwF,gBAAgB,CAAC;QACxD;QACAN,MAAM,CAACjT,IAAI,CAAC,MAAMuT,gBAAgB,CAACd,IAAI,CAACe,QAAQ,IAAI;UAChD,IAAI,CAACA,QAAQ,EACT,MAAM,IAAIvL,KAAK,CAAC,+BAA+B3H,IAAI,SAASiL,MAAM,CAAC/N,IAAI,GAAG,CAAC;UAC/E,MAAMiW,iBAAiB,GAAG3Z,UAAU,CAAC0Z,QAAQ,CAAC,GACxCA,QAAQ,CAACrZ,OAAO,GAChBqZ,QAAQ;UACd;UACAjI,MAAM,CAACmI,IAAI,CAACpT,IAAI,CAAC,GAAGkT,QAAQ;UAC5B;UACA;UACAjI,MAAM,CAACyB,UAAU,CAAC1M,IAAI,CAAC,GAAGmT,iBAAiB;UAC3C;UACA,MAAM3K,OAAO,GAAG2K,iBAAiB,CAACH,SAAS,IAAIG,iBAAiB;UAChE,MAAMlC,KAAK,GAAGzI,OAAO,CAACkK,SAAS,CAAC;UAChC,OAAQzB,KAAK,IACTO,gBAAgB,CAACP,KAAK,EAAE5R,EAAE,EAAExE,IAAI,EAAEoQ,MAAM,EAAEjL,IAAI,EAAEyR,cAAc,CAAC,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;MACP;IACJ;EACJ;EACA,OAAOkB,MAAM;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,SAASU,iBAAiBA,CAACnM,KAAK,EAAE;EAC9B,OAAOA,KAAK,CAACvI,OAAO,CAACQ,KAAK,CAAC8L,MAAM,IAAIA,MAAM,CAACsD,QAAQ,CAAC,GAC/CoD,OAAO,CAACC,MAAM,CAAC,IAAIjK,KAAK,CAAC,qCAAqC,CAAC,CAAC,GAChEgK,OAAO,CAAC2B,GAAG,CAACpM,KAAK,CAACvI,OAAO,CAACpE,GAAG,CAAC0Q,MAAM,IAAIA,MAAM,CAACyB,UAAU,IACvDiF,OAAO,CAAC2B,GAAG,CAACvZ,MAAM,CAACiF,IAAI,CAACiM,MAAM,CAACyB,UAAU,CAAC,CAACsC,MAAM,CAAC,CAACuE,QAAQ,EAAEvT,IAAI,KAAK;IAClE,MAAM4S,YAAY,GAAG3H,MAAM,CAACyB,UAAU,CAAC1M,IAAI,CAAC;IAC5C,IAAI,OAAO4S,YAAY,KAAK,UAAU,IAClC,EAAE,aAAa,IAAIA,YAAY,CAAC,EAAE;MAClCW,QAAQ,CAAC7T,IAAI,CAACkT,YAAY,CAAC,CAAC,CAACT,IAAI,CAACe,QAAQ,IAAI;QAC1C,IAAI,CAACA,QAAQ,EACT,OAAOvB,OAAO,CAACC,MAAM,CAAC,IAAIjK,KAAK,CAAC,+BAA+B3H,IAAI,SAASiL,MAAM,CAAC/N,IAAI,yDAAyD,CAAC,CAAC;QACtJ,MAAMiW,iBAAiB,GAAG3Z,UAAU,CAAC0Z,QAAQ,CAAC,GACxCA,QAAQ,CAACrZ,OAAO,GAChBqZ,QAAQ;QACd;QACAjI,MAAM,CAACmI,IAAI,CAACpT,IAAI,CAAC,GAAGkT,QAAQ;QAC5B;QACA;QACAjI,MAAM,CAACyB,UAAU,CAAC1M,IAAI,CAAC,GAAGmT,iBAAiB;QAC3C;MACJ,CAAC,CAAC,CAAC;IACP;IACA,OAAOI,QAAQ;EACnB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAACpB,IAAI,CAAC,MAAMjL,KAAK,CAAC;AACvC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASsM,OAAOA,CAAC/E,KAAK,EAAE;EACpB,MAAMgF,MAAM,GAAGtb,MAAM,CAACqY,SAAS,CAAC;EAChC,MAAMkD,YAAY,GAAGvb,MAAM,CAACsY,gBAAgB,CAAC;EAC7C,IAAIkD,WAAW,GAAG,KAAK;EACvB,IAAIC,UAAU,GAAG,IAAI;EACrB,MAAM1M,KAAK,GAAG3O,QAAQ,CAAC,MAAM;IACzB,MAAM8G,EAAE,GAAG7G,KAAK,CAACiW,KAAK,CAACpP,EAAE,CAAC;IAC1B,IAAKxC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,KAAM,CAAC4W,WAAW,IAAItU,EAAE,KAAKuU,UAAU,CAAC,EAAE;MAChF,IAAI,CAAC3M,eAAe,CAAC5H,EAAE,CAAC,EAAE;QACtB,IAAIsU,WAAW,EAAE;UACbjZ,IAAI,CAAC,iDAAiD,EAAE2E,EAAE,EAAE,kBAAkB,EAAEuU,UAAU,EAAE,YAAY,EAAEnF,KAAK,CAAC;QACpH,CAAC,MACI;UACD/T,IAAI,CAAC,iDAAiD,EAAE2E,EAAE,EAAE,YAAY,EAAEoP,KAAK,CAAC;QACpF;MACJ;MACAmF,UAAU,GAAGvU,EAAE;MACfsU,WAAW,GAAG,IAAI;IACtB;IACA,OAAOF,MAAM,CAAChG,OAAO,CAACpO,EAAE,CAAC;EAC7B,CAAC,CAAC;EACF,MAAMwU,iBAAiB,GAAGtb,QAAQ,CAAC,MAAM;IACrC,MAAM;MAAEoG;IAAQ,CAAC,GAAGuI,KAAK,CAAC7M,KAAK;IAC/B,MAAM;MAAEwD;IAAO,CAAC,GAAGc,OAAO;IAC1B,MAAMmV,YAAY,GAAGnV,OAAO,CAACd,MAAM,GAAG,CAAC,CAAC;IACxC,MAAMkW,cAAc,GAAGL,YAAY,CAAC/U,OAAO;IAC3C,IAAI,CAACmV,YAAY,IAAI,CAACC,cAAc,CAAClW,MAAM,EACvC,OAAO,CAAC,CAAC;IACb,MAAM8G,KAAK,GAAGoP,cAAc,CAACC,SAAS,CAACnV,iBAAiB,CAACuH,IAAI,CAAC,IAAI,EAAE0N,YAAY,CAAC,CAAC;IAClF,IAAInP,KAAK,GAAG,CAAC,CAAC,EACV,OAAOA,KAAK;IAChB;IACA,MAAMsP,gBAAgB,GAAGC,eAAe,CAACvV,OAAO,CAACd,MAAM,GAAG,CAAC,CAAC,CAAC;IAC7D;MACA;MACAA,MAAM,GAAG,CAAC;MACN;MACA;MACA;MACAqW,eAAe,CAACJ,YAAY,CAAC,KAAKG,gBAAgB;MAClD;MACAF,cAAc,CAACA,cAAc,CAAClW,MAAM,GAAG,CAAC,CAAC,CAACX,IAAI,KAAK+W,gBAAgB,GACjEF,cAAc,CAACC,SAAS,CAACnV,iBAAiB,CAACuH,IAAI,CAAC,IAAI,EAAEzH,OAAO,CAACd,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAC3E8G;IAAK;EACf,CAAC,CAAC;EACF,MAAMwP,QAAQ,GAAG5b,QAAQ,CAAC,MAAMsb,iBAAiB,CAACxZ,KAAK,GAAG,CAAC,CAAC,IACxD+Z,cAAc,CAACV,YAAY,CAACxZ,MAAM,EAAEgN,KAAK,CAAC7M,KAAK,CAACH,MAAM,CAAC,CAAC;EAC5D,MAAMma,aAAa,GAAG9b,QAAQ,CAAC,MAAMsb,iBAAiB,CAACxZ,KAAK,GAAG,CAAC,CAAC,IAC7DwZ,iBAAiB,CAACxZ,KAAK,KAAKqZ,YAAY,CAAC/U,OAAO,CAACd,MAAM,GAAG,CAAC,IAC3DiB,yBAAyB,CAAC4U,YAAY,CAACxZ,MAAM,EAAEgN,KAAK,CAAC7M,KAAK,CAACH,MAAM,CAAC,CAAC;EACvE,SAASoa,QAAQA,CAACC,CAAC,GAAG,CAAC,CAAC,EAAE;IACtB,IAAIC,UAAU,CAACD,CAAC,CAAC,EAAE;MACf,MAAME,CAAC,GAAGhB,MAAM,CAACjb,KAAK,CAACiW,KAAK,CAACrS,OAAO,CAAC,GAAG,SAAS,GAAG,MAAM,CAAC,CAAC5D,KAAK,CAACiW,KAAK,CAACpP,EAAE;MAC1E;MACA,CAAC,CAACkT,KAAK,CAAC/X,IAAI,CAAC;MACb,IAAIiU,KAAK,CAACiG,cAAc,IACpB,OAAOrb,QAAQ,KAAK,WAAW,IAC/B,qBAAqB,IAAIA,QAAQ,EAAE;QACnCA,QAAQ,CAACsb,mBAAmB,CAAC,MAAMF,CAAC,CAAC;MACzC;MACA,OAAOA,CAAC;IACZ;IACA,OAAO9C,OAAO,CAAClE,OAAO,CAAC,CAAC;EAC5B;EACA;EACA,IAAI,CAAE5Q,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAK6X,qBAAqB,KAAKxb,SAAS,EAAE;IACjF,MAAMyb,QAAQ,GAAG3c,kBAAkB,CAAC,CAAC;IACrC,IAAI2c,QAAQ,EAAE;MACV,MAAMC,mBAAmB,GAAG;QACxB5N,KAAK,EAAEA,KAAK,CAAC7M,KAAK;QAClB8Z,QAAQ,EAAEA,QAAQ,CAAC9Z,KAAK;QACxBga,aAAa,EAAEA,aAAa,CAACha,KAAK;QAClCsL,KAAK,EAAE;MACX,CAAC;MACD;MACAkP,QAAQ,CAACE,cAAc,GAAGF,QAAQ,CAACE,cAAc,IAAI,EAAE;MACvD;MACAF,QAAQ,CAACE,cAAc,CAACrV,IAAI,CAACoV,mBAAmB,CAAC;MACjDrc,WAAW,CAAC,MAAM;QACdqc,mBAAmB,CAAC5N,KAAK,GAAGA,KAAK,CAAC7M,KAAK;QACvCya,mBAAmB,CAACX,QAAQ,GAAGA,QAAQ,CAAC9Z,KAAK;QAC7Cya,mBAAmB,CAACT,aAAa,GAAGA,aAAa,CAACha,KAAK;QACvDya,mBAAmB,CAACnP,KAAK,GAAGsB,eAAe,CAACzO,KAAK,CAACiW,KAAK,CAACpP,EAAE,CAAC,CAAC,GACtD,IAAI,GACJ,oBAAoB;MAC9B,CAAC,EAAE;QAAE2V,KAAK,EAAE;MAAO,CAAC,CAAC;IACzB;EACJ;EACA;AACJ;AACA;EACI,OAAO;IACH9N,KAAK;IACL+N,IAAI,EAAE1c,QAAQ,CAAC,MAAM2O,KAAK,CAAC7M,KAAK,CAAC4a,IAAI,CAAC;IACtCd,QAAQ;IACRE,aAAa;IACbC;EACJ,CAAC;AACL;AACA,SAASY,iBAAiBA,CAACC,MAAM,EAAE;EAC/B,OAAOA,MAAM,CAACtX,MAAM,KAAK,CAAC,GAAGsX,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM;AACnD;AACA,MAAMC,cAAc,GAAG,aAAc1c,eAAe,CAAC;EACjDsH,IAAI,EAAE,YAAY;EAClBqV,YAAY,EAAE;IAAEC,IAAI,EAAE;EAAE,CAAC;EACzB7G,KAAK,EAAE;IACHpP,EAAE,EAAE;MACA4E,IAAI,EAAE,CAACmL,MAAM,EAAErV,MAAM,CAAC;MACtBwb,QAAQ,EAAE;IACd,CAAC;IACDnZ,OAAO,EAAEoZ,OAAO;IAChBC,WAAW,EAAErG,MAAM;IACnB;IACAsG,gBAAgB,EAAEtG,MAAM;IACxBuG,MAAM,EAAEH,OAAO;IACfI,gBAAgB,EAAE;MACd3R,IAAI,EAAEmL,MAAM;MACZvV,OAAO,EAAE;IACb,CAAC;IACD6a,cAAc,EAAEc;EACpB,CAAC;EACDhC,OAAO;EACPqC,KAAKA,CAACpH,KAAK,EAAE;IAAEqH;EAAM,CAAC,EAAE;IACpB,MAAMC,IAAI,GAAGpd,QAAQ,CAAC6a,OAAO,CAAC/E,KAAK,CAAC,CAAC;IACrC,MAAM;MAAEjG;IAAQ,CAAC,GAAGrQ,MAAM,CAACqY,SAAS,CAAC;IACrC,MAAMwF,OAAO,GAAGzd,QAAQ,CAAC,OAAO;MAC5B,CAAC0d,YAAY,CAACxH,KAAK,CAACgH,WAAW,EAAEjN,OAAO,CAAC0N,eAAe,EAAE,oBAAoB,CAAC,GAAGH,IAAI,CAAC5B,QAAQ;MAC/F;MACA;MACA;MACA;MACA;MACA,CAAC8B,YAAY,CAACxH,KAAK,CAACiH,gBAAgB,EAAElN,OAAO,CAAC2N,oBAAoB,EAAE,0BAA0B,CAAC,GAAGJ,IAAI,CAAC1B;IAC3G,CAAC,CAAC,CAAC;IACH,OAAO,MAAM;MACT,MAAM5I,QAAQ,GAAGqK,KAAK,CAACjc,OAAO,IAAIqb,iBAAiB,CAACY,KAAK,CAACjc,OAAO,CAACkc,IAAI,CAAC,CAAC;MACxE,OAAOtH,KAAK,CAACkH,MAAM,GACblK,QAAQ,GACR7S,CAAC,CAAC,GAAG,EAAE;QACL,cAAc,EAAEmd,IAAI,CAAC1B,aAAa,GAC5B5F,KAAK,CAACmH,gBAAgB,GACtB,IAAI;QACVX,IAAI,EAAEc,IAAI,CAACd,IAAI;QACf;QACA;QACAmB,OAAO,EAAEL,IAAI,CAACzB,QAAQ;QACtB+B,KAAK,EAAEL,OAAO,CAAC3b;MACnB,CAAC,EAAEoR,QAAQ,CAAC;IACpB,CAAC;EACL;AACJ,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,MAAM6K,UAAU,GAAGlB,cAAc;AACjC,SAASZ,UAAUA,CAACD,CAAC,EAAE;EACnB;EACA,IAAIA,CAAC,CAACgC,OAAO,IAAIhC,CAAC,CAACiC,MAAM,IAAIjC,CAAC,CAACkC,OAAO,IAAIlC,CAAC,CAACmC,QAAQ,EAChD;EACJ;EACA,IAAInC,CAAC,CAACoC,gBAAgB,EAClB;EACJ;EACA,IAAIpC,CAAC,CAACqC,MAAM,KAAK3W,SAAS,IAAIsU,CAAC,CAACqC,MAAM,KAAK,CAAC,EACxC;EACJ;EACA;EACA,IAAIrC,CAAC,CAACsC,aAAa,IAAItC,CAAC,CAACsC,aAAa,CAACnW,YAAY,EAAE;IACjD;IACA,MAAMoW,MAAM,GAAGvC,CAAC,CAACsC,aAAa,CAACnW,YAAY,CAAC,QAAQ,CAAC;IACrD,IAAI,aAAa,CAACqK,IAAI,CAAC+L,MAAM,CAAC,EAC1B;EACR;EACA;EACA,IAAIvC,CAAC,CAACwC,cAAc,EAChBxC,CAAC,CAACwC,cAAc,CAAC,CAAC;EACtB,OAAO,IAAI;AACf;AACA,SAAS3C,cAAcA,CAAC4C,KAAK,EAAEC,KAAK,EAAE;EAClC,KAAK,MAAM7c,GAAG,IAAI6c,KAAK,EAAE;IACrB,MAAMC,UAAU,GAAGD,KAAK,CAAC7c,GAAG,CAAC;IAC7B,MAAM+c,UAAU,GAAGH,KAAK,CAAC5c,GAAG,CAAC;IAC7B,IAAI,OAAO8c,UAAU,KAAK,QAAQ,EAAE;MAChC,IAAIA,UAAU,KAAKC,UAAU,EACzB,OAAO,KAAK;IACpB,CAAC,MACI;MACD,IAAI,CAAC7c,OAAO,CAAC6c,UAAU,CAAC,IACpBA,UAAU,CAACtZ,MAAM,KAAKqZ,UAAU,CAACrZ,MAAM,IACvCqZ,UAAU,CAACE,IAAI,CAAC,CAAC/c,KAAK,EAAE+E,CAAC,KAAK/E,KAAK,KAAK8c,UAAU,CAAC/X,CAAC,CAAC,CAAC,EACtD,OAAO,KAAK;IACpB;EACJ;EACA,OAAO,IAAI;AACf;AACA;AACA;AACA;AACA;AACA,SAAS8U,eAAeA,CAACjJ,MAAM,EAAE;EAC7B,OAAOA,MAAM,GAAIA,MAAM,CAAClM,OAAO,GAAGkM,MAAM,CAAClM,OAAO,CAAC7B,IAAI,GAAG+N,MAAM,CAAC/N,IAAI,GAAI,EAAE;AAC7E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM+Y,YAAY,GAAGA,CAACoB,SAAS,EAAEC,WAAW,EAAEC,YAAY,KAAKF,SAAS,IAAI,IAAI,GAC1EA,SAAS,GACTC,WAAW,IAAI,IAAI,GACfA,WAAW,GACXC,YAAY;AAEtB,MAAMC,cAAc,GAAG,aAAc9e,eAAe,CAAC;EACjDsH,IAAI,EAAE,YAAY;EAClB;EACAyX,YAAY,EAAE,KAAK;EACnBhJ,KAAK,EAAE;IACHzO,IAAI,EAAE;MACFiE,IAAI,EAAEmL,MAAM;MACZvV,OAAO,EAAE;IACb,CAAC;IACDqN,KAAK,EAAEnN;EACX,CAAC;EACD;EACA;EACAsb,YAAY,EAAE;IAAEC,IAAI,EAAE;EAAE,CAAC;EACzBO,KAAKA,CAACpH,KAAK,EAAE;IAAEiJ,KAAK;IAAE5B;EAAM,CAAC,EAAE;IAC1BjZ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAK4a,mBAAmB,CAAC,CAAC;IAChE,MAAMC,aAAa,GAAGzf,MAAM,CAACuY,qBAAqB,CAAC;IACnD,MAAMmH,cAAc,GAAGtf,QAAQ,CAAC,MAAMkW,KAAK,CAACvH,KAAK,IAAI0Q,aAAa,CAACvd,KAAK,CAAC;IACzE,MAAMyd,aAAa,GAAG3f,MAAM,CAACoY,YAAY,EAAE,CAAC,CAAC;IAC7C;IACA;IACA,MAAMwH,KAAK,GAAGxf,QAAQ,CAAC,MAAM;MACzB,IAAIyf,YAAY,GAAGxf,KAAK,CAACsf,aAAa,CAAC;MACvC,MAAM;QAAEnZ;MAAQ,CAAC,GAAGkZ,cAAc,CAACxd,KAAK;MACxC,IAAI4d,YAAY;MAChB,OAAO,CAACA,YAAY,GAAGtZ,OAAO,CAACqZ,YAAY,CAAC,KACxC,CAACC,YAAY,CAACvL,UAAU,EAAE;QAC1BsL,YAAY,EAAE;MAClB;MACA,OAAOA,YAAY;IACvB,CAAC,CAAC;IACF,MAAME,eAAe,GAAG3f,QAAQ,CAAC,MAAMsf,cAAc,CAACxd,KAAK,CAACsE,OAAO,CAACoZ,KAAK,CAAC1d,KAAK,CAAC,CAAC;IACjFxB,OAAO,CAAC0X,YAAY,EAAEhY,QAAQ,CAAC,MAAMwf,KAAK,CAAC1d,KAAK,GAAG,CAAC,CAAC,CAAC;IACtDxB,OAAO,CAACyX,eAAe,EAAE4H,eAAe,CAAC;IACzCrf,OAAO,CAAC6X,qBAAqB,EAAEmH,cAAc,CAAC;IAC9C,MAAMM,OAAO,GAAGrf,GAAG,CAAC,CAAC;IACrB;IACA;IACAC,KAAK,CAAC,MAAM,CAACof,OAAO,CAAC9d,KAAK,EAAE6d,eAAe,CAAC7d,KAAK,EAAEoU,KAAK,CAACzO,IAAI,CAAC,EAAE,CAAC,CAAC6U,QAAQ,EAAExV,EAAE,EAAEW,IAAI,CAAC,EAAE,CAACoY,WAAW,EAAEvd,IAAI,EAAEwd,OAAO,CAAC,KAAK;MACpH;MACA,IAAIhZ,EAAE,EAAE;QACJ;QACA;QACAA,EAAE,CAACsP,SAAS,CAAC3O,IAAI,CAAC,GAAG6U,QAAQ;QAC7B;QACA;QACA;QACA;QACA;QACA;QACA,IAAIha,IAAI,IAAIA,IAAI,KAAKwE,EAAE,IAAIwV,QAAQ,IAAIA,QAAQ,KAAKuD,WAAW,EAAE;UAC7D,IAAI,CAAC/Y,EAAE,CAACuP,WAAW,CAAC0J,IAAI,EAAE;YACtBjZ,EAAE,CAACuP,WAAW,GAAG/T,IAAI,CAAC+T,WAAW;UACrC;UACA,IAAI,CAACvP,EAAE,CAACwP,YAAY,CAACyJ,IAAI,EAAE;YACvBjZ,EAAE,CAACwP,YAAY,GAAGhU,IAAI,CAACgU,YAAY;UACvC;QACJ;MACJ;MACA;MACA,IAAIgG,QAAQ,IACRxV,EAAE;MACF;MACA;MACC,CAACxE,IAAI,IAAI,CAACgE,iBAAiB,CAACQ,EAAE,EAAExE,IAAI,CAAC,IAAI,CAACud,WAAW,CAAC,EAAE;QACzD,CAAC/Y,EAAE,CAACyP,cAAc,CAAC9O,IAAI,CAAC,IAAI,EAAE,EAAE+D,OAAO,CAACU,QAAQ,IAAIA,QAAQ,CAACoQ,QAAQ,CAAC,CAAC;MAC3E;IACJ,CAAC,EAAE;MAAEG,KAAK,EAAE;IAAO,CAAC,CAAC;IACrB,OAAO,MAAM;MACT,MAAM9N,KAAK,GAAG2Q,cAAc,CAACxd,KAAK;MAClC;MACA;MACA,MAAMke,WAAW,GAAG9J,KAAK,CAACzO,IAAI;MAC9B,MAAMiY,YAAY,GAAGC,eAAe,CAAC7d,KAAK;MAC1C,MAAMme,aAAa,GAAGP,YAAY,IAAIA,YAAY,CAACvL,UAAU,CAAC6L,WAAW,CAAC;MAC1E,IAAI,CAACC,aAAa,EAAE;QAChB,OAAOC,aAAa,CAAC3C,KAAK,CAACjc,OAAO,EAAE;UAAE6e,SAAS,EAAEF,aAAa;UAAEtR;QAAM,CAAC,CAAC;MAC5E;MACA;MACA,MAAMyR,gBAAgB,GAAGV,YAAY,CAACxJ,KAAK,CAAC8J,WAAW,CAAC;MACxD,MAAMK,UAAU,GAAGD,gBAAgB,GAC7BA,gBAAgB,KAAK,IAAI,GACrBzR,KAAK,CAAChN,MAAM,GACZ,OAAOye,gBAAgB,KAAK,UAAU,GAClCA,gBAAgB,CAACzR,KAAK,CAAC,GACvByR,gBAAgB,GACxB,IAAI;MACV,MAAME,gBAAgB,GAAGC,KAAK,IAAI;QAC9B;QACA,IAAIA,KAAK,CAACvf,SAAS,CAACwf,WAAW,EAAE;UAC7Bd,YAAY,CAACtJ,SAAS,CAAC4J,WAAW,CAAC,GAAG,IAAI;QAC9C;MACJ,CAAC;MACD,MAAMhf,SAAS,GAAGX,CAAC,CAAC4f,aAAa,EAAE1e,MAAM,CAAC,CAAC,CAAC,EAAE8e,UAAU,EAAElB,KAAK,EAAE;QAC7DmB,gBAAgB;QAChB/f,GAAG,EAAEqf;MACT,CAAC,CAAC,CAAC;MACH,IAAI,CAAEtb,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAK6X,qBAAqB,KACjExb,SAAS,IACTG,SAAS,CAACT,GAAG,EAAE;QACf;QACA,MAAM4N,IAAI,GAAG;UACTqR,KAAK,EAAEA,KAAK,CAAC1d,KAAK;UAClB2F,IAAI,EAAEiY,YAAY,CAACjY,IAAI;UACvB9C,IAAI,EAAE+a,YAAY,CAAC/a,IAAI;UACvBgD,IAAI,EAAE+X,YAAY,CAAC/X;QACvB,CAAC;QACD,MAAM8Y,iBAAiB,GAAG1e,OAAO,CAACf,SAAS,CAACT,GAAG,CAAC,GAC1CS,SAAS,CAACT,GAAG,CAACyB,GAAG,CAAC0e,CAAC,IAAIA,CAAC,CAAC7Z,CAAC,CAAC,GAC3B,CAAC7F,SAAS,CAACT,GAAG,CAACsG,CAAC,CAAC;QACvB4Z,iBAAiB,CAACjV,OAAO,CAAC8Q,QAAQ,IAAI;UAClC;UACAA,QAAQ,CAACqE,cAAc,GAAGxS,IAAI;QAClC,CAAC,CAAC;MACN;MACA;QACA;QACA;QACA+R,aAAa,CAAC3C,KAAK,CAACjc,OAAO,EAAE;UAAE6e,SAAS,EAAEnf,SAAS;UAAE2N;QAAM,CAAC,CAAC,IACzD3N;MAAS;IACjB,CAAC;EACL;AACJ,CAAC,CAAC;AACF,SAASkf,aAAaA,CAACU,IAAI,EAAEvT,IAAI,EAAE;EAC/B,IAAI,CAACuT,IAAI,EACL,OAAO,IAAI;EACf,MAAMC,WAAW,GAAGD,IAAI,CAACvT,IAAI,CAAC;EAC9B,OAAOwT,WAAW,CAACvb,MAAM,KAAK,CAAC,GAAGub,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW;AAClE;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,GAAG7B,cAAc;AACjC;AACA;AACA,SAASG,mBAAmBA,CAAA,EAAG;EAC3B,MAAM9C,QAAQ,GAAG3c,kBAAkB,CAAC,CAAC;EACrC,MAAMohB,UAAU,GAAGzE,QAAQ,CAAC3J,MAAM,IAAI2J,QAAQ,CAAC3J,MAAM,CAACjH,IAAI,CAACjE,IAAI;EAC/D,MAAMuZ,iBAAiB,GAAG1E,QAAQ,CAAC3J,MAAM,IAAI2J,QAAQ,CAAC3J,MAAM,CAACsO,OAAO,IAAI3E,QAAQ,CAAC3J,MAAM,CAACsO,OAAO,CAACvV,IAAI;EACpG,IAAIqV,UAAU,KACTA,UAAU,KAAK,WAAW,IAAIA,UAAU,CAAChW,QAAQ,CAAC,YAAY,CAAC,CAAC,IACjE,OAAOiW,iBAAiB,KAAK,QAAQ,IACrCA,iBAAiB,CAACvZ,IAAI,KAAK,YAAY,EAAE;IACzC,MAAM+J,IAAI,GAAGuP,UAAU,KAAK,WAAW,GAAG,YAAY,GAAG,YAAY;IACrE5e,IAAI,CAAC,qFAAqF,GACtF,6BAA6B,GAC7B,wCAAwC,GACxC,MAAMqP,IAAI,KAAK,GACf,qCAAqC,GACrC,OAAOA,IAAI,KAAK,GAChB,gBAAgB,CAAC;EACzB;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS0P,mBAAmBA,CAACC,aAAa,EAAEC,OAAO,EAAE;EACjD,MAAMC,IAAI,GAAG9f,MAAM,CAAC,CAAC,CAAC,EAAE4f,aAAa,EAAE;IACnC;IACA/a,OAAO,EAAE+a,aAAa,CAAC/a,OAAO,CAACpE,GAAG,CAACoE,OAAO,IAAIkb,IAAI,CAAClb,OAAO,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;EACrG,CAAC,CAAC;EACF,OAAO;IACHmb,OAAO,EAAE;MACL7V,IAAI,EAAE,IAAI;MACV8V,QAAQ,EAAE,IAAI;MACdC,OAAO,EAAEN,aAAa,CAAC3b,QAAQ;MAC/B4b,OAAO;MACPtf,KAAK,EAAEuf;IACX;EACJ,CAAC;AACL;AACA,SAASK,aAAaA,CAACD,OAAO,EAAE;EAC5B,OAAO;IACHF,OAAO,EAAE;MACLE;IACJ;EACJ,CAAC;AACL;AACA;AACA,IAAIE,QAAQ,GAAG,CAAC;AAChB,SAASC,WAAWA,CAACC,GAAG,EAAE3G,MAAM,EAAEjI,OAAO,EAAE;EACvC;EACA;EACA,IAAIiI,MAAM,CAAC4G,aAAa,EACpB;EACJ5G,MAAM,CAAC4G,aAAa,GAAG,IAAI;EAC3B;EACA,MAAMC,EAAE,GAAGJ,QAAQ,EAAE;EACrB/gB,mBAAmB,CAAC;IAChBmhB,EAAE,EAAE,kBAAkB,IAAIA,EAAE,GAAG,GAAG,GAAGA,EAAE,GAAG,EAAE,CAAC;IAC7CC,KAAK,EAAE,YAAY;IACnBC,WAAW,EAAE,YAAY;IACzBC,QAAQ,EAAE,0BAA0B;IACpCC,IAAI,EAAE,mCAAmC;IACzCC,mBAAmB,EAAE,CAAC,SAAS,CAAC;IAChCP;EACJ,CAAC,EAAEQ,GAAG,IAAI;IACN,IAAI,OAAOA,GAAG,CAACC,GAAG,KAAK,UAAU,EAAE;MAC/B7f,OAAO,CAACN,IAAI,CAAC,uNAAuN,CAAC;IACzO;IACA;IACAkgB,GAAG,CAACE,EAAE,CAACC,gBAAgB,CAAC,CAACC,OAAO,EAAEC,GAAG,KAAK;MACtC,IAAID,OAAO,CAACE,YAAY,EAAE;QACtBF,OAAO,CAACE,YAAY,CAAC5Y,KAAK,CAAC5C,IAAI,CAAC;UAC5BuE,IAAI,EAAE,SAAS;UACf7J,GAAG,EAAE,QAAQ;UACb+gB,QAAQ,EAAE,KAAK;UACf9gB,KAAK,EAAEof,mBAAmB,CAAChG,MAAM,CAACC,YAAY,CAACrZ,KAAK,EAAE,eAAe;QACzE,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;IACF;IACAugB,GAAG,CAACE,EAAE,CAACM,kBAAkB,CAAC,CAAC;MAAEC,QAAQ,EAAEC,IAAI;MAAEC;IAAkB,CAAC,KAAK;MACjE,IAAIA,iBAAiB,CAACrC,cAAc,EAAE;QAClC,MAAMxS,IAAI,GAAG6U,iBAAiB,CAACrC,cAAc;QAC7CoC,IAAI,CAACE,IAAI,CAAC9b,IAAI,CAAC;UACX6a,KAAK,EAAE,CAAC7T,IAAI,CAAC1G,IAAI,GAAG,GAAG0G,IAAI,CAAC1G,IAAI,CAACoS,QAAQ,CAAC,CAAC,IAAI,GAAG,EAAE,IAAI1L,IAAI,CAACxJ,IAAI;UACjEue,SAAS,EAAE,CAAC;UACZ9B,OAAO,EAAE,mDAAmD;UAC5D+B,eAAe,EAAEC;QACrB,CAAC,CAAC;MACN;MACA;MACA,IAAIrhB,OAAO,CAACihB,iBAAiB,CAACxG,cAAc,CAAC,EAAE;QAC3CwG,iBAAiB,CAACK,aAAa,GAAGhB,GAAG;QACrCW,iBAAiB,CAACxG,cAAc,CAAChR,OAAO,CAAC8X,YAAY,IAAI;UACrD,IAAItB,KAAK,GAAGsB,YAAY,CAAC3U,KAAK,CAAChK,IAAI;UACnC,IAAIwe,eAAe,GAAGI,UAAU;UAChC,IAAInC,OAAO,GAAG,EAAE;UAChB,IAAI8B,SAAS,GAAG,CAAC;UACjB,IAAII,YAAY,CAAClW,KAAK,EAAE;YACpB4U,KAAK,GAAGsB,YAAY,CAAClW,KAAK;YAC1B+V,eAAe,GAAGK,OAAO;YACzBN,SAAS,GAAGO,OAAO;UACvB,CAAC,MACI,IAAIH,YAAY,CAACxH,aAAa,EAAE;YACjCqH,eAAe,GAAGO,QAAQ;YAC1BtC,OAAO,GAAG,wBAAwB;UACtC,CAAC,MACI,IAAIkC,YAAY,CAAC1H,QAAQ,EAAE;YAC5BuH,eAAe,GAAGQ,QAAQ;YAC1BvC,OAAO,GAAG,qBAAqB;UACnC;UACA2B,IAAI,CAACE,IAAI,CAAC9b,IAAI,CAAC;YACX6a,KAAK;YACLkB,SAAS;YACT9B,OAAO;YACP+B;UACJ,CAAC,CAAC;QACN,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;IACF3iB,KAAK,CAAC0a,MAAM,CAACC,YAAY,EAAE,MAAM;MAC7B;MACAyI,iBAAiB,CAAC,CAAC;MACnBvB,GAAG,CAACwB,qBAAqB,CAAC,CAAC;MAC3BxB,GAAG,CAACyB,iBAAiB,CAACC,iBAAiB,CAAC;MACxC1B,GAAG,CAAC2B,kBAAkB,CAACD,iBAAiB,CAAC;IAC7C,CAAC,CAAC;IACF,MAAME,kBAAkB,GAAG,qBAAqB,GAAGlC,EAAE;IACrDM,GAAG,CAAC6B,gBAAgB,CAAC;MACjBnC,EAAE,EAAEkC,kBAAkB;MACtBjC,KAAK,EAAE,SAASD,EAAE,GAAG,GAAG,GAAGA,EAAE,GAAG,EAAE,cAAc;MAChDoC,KAAK,EAAE;IACX,CAAC,CAAC;IACF;IACA;IACA;IACA;IACA;IACA;IACAjJ,MAAM,CAACkJ,OAAO,CAAC,CAAChX,KAAK,EAAEtG,EAAE,KAAK;MAC1Bub,GAAG,CAACgC,gBAAgB,CAAC;QACjBC,OAAO,EAAEL,kBAAkB;QAC3BM,KAAK,EAAE;UACHC,KAAK,EAAE,yBAAyB;UAChCC,QAAQ,EAAE3d,EAAE,CAACtB,QAAQ;UACrBkf,OAAO,EAAE,OAAO;UAChBC,IAAI,EAAEtC,GAAG,CAACC,GAAG,CAAC,CAAC;UACfjV,IAAI,EAAE;YAAED;UAAM,CAAC;UACfwX,OAAO,EAAE9d,EAAE,CAACa,IAAI,CAACkd;QACrB;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;IACF;IACA,IAAIC,YAAY,GAAG,CAAC;IACpB5J,MAAM,CAAC6J,UAAU,CAAC,CAACje,EAAE,EAAExE,IAAI,KAAK;MAC5B,MAAM+K,IAAI,GAAG;QACTqL,KAAK,EAAEgJ,aAAa,CAAC,YAAY,CAAC;QAClCpf,IAAI,EAAE4e,mBAAmB,CAAC5e,IAAI,EAAE,yCAAyC,CAAC;QAC1EwE,EAAE,EAAEoa,mBAAmB,CAACpa,EAAE,EAAE,iBAAiB;MACjD,CAAC;MACD;MACAtF,MAAM,CAACsM,cAAc,CAAChH,EAAE,CAACa,IAAI,EAAE,gBAAgB,EAAE;QAC7C7F,KAAK,EAAEgjB,YAAY;MACvB,CAAC,CAAC;MACFzC,GAAG,CAACgC,gBAAgB,CAAC;QACjBC,OAAO,EAAEL,kBAAkB;QAC3BM,KAAK,EAAE;UACHI,IAAI,EAAEtC,GAAG,CAACC,GAAG,CAAC,CAAC;UACfkC,KAAK,EAAE,qBAAqB;UAC5BC,QAAQ,EAAE3d,EAAE,CAACtB,QAAQ;UACrB6H,IAAI;UACJuX,OAAO,EAAE9d,EAAE,CAACa,IAAI,CAACkd;QACrB;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;IACF3J,MAAM,CAAC8J,SAAS,CAAC,CAACle,EAAE,EAAExE,IAAI,EAAE2iB,OAAO,KAAK;MACpC,MAAM5X,IAAI,GAAG;QACTqL,KAAK,EAAEgJ,aAAa,CAAC,WAAW;MACpC,CAAC;MACD,IAAIuD,OAAO,EAAE;QACT5X,IAAI,CAAC4X,OAAO,GAAG;UACX1D,OAAO,EAAE;YACL7V,IAAI,EAAE0D,KAAK;YACXoS,QAAQ,EAAE,IAAI;YACdC,OAAO,EAAEwD,OAAO,GAAGA,OAAO,CAACpU,OAAO,GAAG,EAAE;YACvCuQ,OAAO,EAAE,oBAAoB;YAC7Btf,KAAK,EAAEmjB;UACX;QACJ,CAAC;QACD5X,IAAI,CAAC6X,MAAM,GAAGxD,aAAa,CAAC,GAAG,CAAC;MACpC,CAAC,MACI;QACDrU,IAAI,CAAC6X,MAAM,GAAGxD,aAAa,CAAC,GAAG,CAAC;MACpC;MACA;MACArU,IAAI,CAAC/K,IAAI,GAAG4e,mBAAmB,CAAC5e,IAAI,EAAE,yCAAyC,CAAC;MAChF+K,IAAI,CAACvG,EAAE,GAAGoa,mBAAmB,CAACpa,EAAE,EAAE,iBAAiB,CAAC;MACpDub,GAAG,CAACgC,gBAAgB,CAAC;QACjBC,OAAO,EAAEL,kBAAkB;QAC3BM,KAAK,EAAE;UACHC,KAAK,EAAE,mBAAmB;UAC1BC,QAAQ,EAAE3d,EAAE,CAACtB,QAAQ;UACrBmf,IAAI,EAAEtC,GAAG,CAACC,GAAG,CAAC,CAAC;UACfjV,IAAI;UACJqX,OAAO,EAAEO,OAAO,GAAG,SAAS,GAAG,SAAS;UACxCL,OAAO,EAAE9d,EAAE,CAACa,IAAI,CAACkd;QACrB;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;IACF;AACR;AACA;IACQ,MAAMd,iBAAiB,GAAG,mBAAmB,GAAGhC,EAAE;IAClDM,GAAG,CAAC8C,YAAY,CAAC;MACbpD,EAAE,EAAEgC,iBAAiB;MACrB/B,KAAK,EAAE,QAAQ,IAAID,EAAE,GAAG,GAAG,GAAGA,EAAE,GAAG,EAAE,CAAC;MACtCqD,IAAI,EAAE,MAAM;MACZC,qBAAqB,EAAE;IAC3B,CAAC,CAAC;IACF,SAASzB,iBAAiBA,CAAA,EAAG;MACzB;MACA,IAAI,CAAC0B,mBAAmB,EACpB;MACJ,MAAM7C,OAAO,GAAG6C,mBAAmB;MACnC;MACA,IAAIjS,MAAM,GAAGJ,OAAO,CAAC+B,SAAS,CAAC,CAAC,CAACI,MAAM,CAACzG,KAAK,IAAI,CAACA,KAAK,CAACgE,MAAM;MAC1D;MACA;MACA,CAAChE,KAAK,CAACgE,MAAM,CAACD,MAAM,CAACyB,UAAU,CAAC;MACpC;MACAd,MAAM,CAAC7H,OAAO,CAAC+Z,4BAA4B,CAAC;MAC5C;MACA,IAAI9C,OAAO,CAACrN,MAAM,EAAE;QAChB/B,MAAM,GAAGA,MAAM,CAAC+B,MAAM,CAACzG,KAAK;QAC5B;QACA6W,eAAe,CAAC7W,KAAK,EAAE8T,OAAO,CAACrN,MAAM,CAACtP,WAAW,CAAC,CAAC,CAAC,CAAC;MACzD;MACA;MACAuN,MAAM,CAAC7H,OAAO,CAACmD,KAAK,IAAI8W,qBAAqB,CAAC9W,KAAK,EAAEuM,MAAM,CAACC,YAAY,CAACrZ,KAAK,CAAC,CAAC;MAChF2gB,OAAO,CAACiD,SAAS,GAAGrS,MAAM,CAACrR,GAAG,CAAC2jB,6BAA6B,CAAC;IACjE;IACA,IAAIL,mBAAmB;IACvBjD,GAAG,CAACE,EAAE,CAACqD,gBAAgB,CAACnD,OAAO,IAAI;MAC/B6C,mBAAmB,GAAG7C,OAAO;MAC7B,IAAIA,OAAO,CAACZ,GAAG,KAAKA,GAAG,IAAIY,OAAO,CAACoD,WAAW,KAAK9B,iBAAiB,EAAE;QAClEH,iBAAiB,CAAC,CAAC;MACvB;IACJ,CAAC,CAAC;IACF;AACR;AACA;IACQvB,GAAG,CAACE,EAAE,CAACuD,iBAAiB,CAACrD,OAAO,IAAI;MAChC,IAAIA,OAAO,CAACZ,GAAG,KAAKA,GAAG,IAAIY,OAAO,CAACoD,WAAW,KAAK9B,iBAAiB,EAAE;QAClE,MAAM1Q,MAAM,GAAGJ,OAAO,CAAC+B,SAAS,CAAC,CAAC;QAClC,MAAMrG,KAAK,GAAG0E,MAAM,CAACiC,IAAI,CAAC3G,KAAK,IAAIA,KAAK,CAAC+D,MAAM,CAACqT,OAAO,KAAKtD,OAAO,CAACuD,MAAM,CAAC;QAC3E,IAAIrX,KAAK,EAAE;UACP8T,OAAO,CAAC1Y,KAAK,GAAG;YACZkG,OAAO,EAAEgW,yCAAyC,CAACtX,KAAK;UAC5D,CAAC;QACL;MACJ;IACJ,CAAC,CAAC;IACF0T,GAAG,CAACyB,iBAAiB,CAACC,iBAAiB,CAAC;IACxC1B,GAAG,CAAC2B,kBAAkB,CAACD,iBAAiB,CAAC;EAC7C,CAAC,CAAC;AACN;AACA,SAASmC,cAAcA,CAACrkB,GAAG,EAAE;EACzB,IAAIA,GAAG,CAAC4O,QAAQ,EAAE;IACd,OAAO5O,GAAG,CAAC2O,UAAU,GAAG,GAAG,GAAG,GAAG;EACrC,CAAC,MACI;IACD,OAAO3O,GAAG,CAAC2O,UAAU,GAAG,GAAG,GAAG,EAAE;EACpC;AACJ;AACA,SAASyV,yCAAyCA,CAACtX,KAAK,EAAE;EACtD,MAAM;IAAE+D;EAAO,CAAC,GAAG/D,KAAK;EACxB,MAAMwX,MAAM,GAAG,CACX;IAAEvD,QAAQ,EAAE,KAAK;IAAE/gB,GAAG,EAAE,MAAM;IAAEC,KAAK,EAAE4Q,MAAM,CAAC/N;EAAK,CAAC,CACvD;EACD,IAAI+N,MAAM,CAACjL,IAAI,IAAI,IAAI,EAAE;IACrB0e,MAAM,CAAChf,IAAI,CAAC;MACRyb,QAAQ,EAAE,KAAK;MACf/gB,GAAG,EAAE,MAAM;MACXC,KAAK,EAAE4Q,MAAM,CAACjL;IAClB,CAAC,CAAC;EACN;EACA0e,MAAM,CAAChf,IAAI,CAAC;IAAEyb,QAAQ,EAAE,KAAK;IAAE/gB,GAAG,EAAE,QAAQ;IAAEC,KAAK,EAAE6M,KAAK,CAACgC;EAAG,CAAC,CAAC;EAChE,IAAIhC,KAAK,CAAClI,IAAI,CAACnB,MAAM,EAAE;IACnB6gB,MAAM,CAAChf,IAAI,CAAC;MACRyb,QAAQ,EAAE,KAAK;MACf/gB,GAAG,EAAE,MAAM;MACXC,KAAK,EAAE;QACHyf,OAAO,EAAE;UACL7V,IAAI,EAAE,IAAI;UACV8V,QAAQ,EAAE,IAAI;UACdC,OAAO,EAAE9S,KAAK,CAAClI,IAAI,CACdzE,GAAG,CAACH,GAAG,IAAI,GAAGA,GAAG,CAAC4F,IAAI,GAAGye,cAAc,CAACrkB,GAAG,CAAC,EAAE,CAAC,CAC/C0F,IAAI,CAAC,GAAG,CAAC;UACd6Z,OAAO,EAAE,YAAY;UACrBtf,KAAK,EAAE6M,KAAK,CAAClI;QACjB;MACJ;IACJ,CAAC,CAAC;EACN;EACA,IAAIiM,MAAM,CAACsD,QAAQ,IAAI,IAAI,EAAE;IACzBmQ,MAAM,CAAChf,IAAI,CAAC;MACRyb,QAAQ,EAAE,KAAK;MACf/gB,GAAG,EAAE,UAAU;MACfC,KAAK,EAAE4Q,MAAM,CAACsD;IAClB,CAAC,CAAC;EACN;EACA,IAAIrH,KAAK,CAACwE,KAAK,CAAC7N,MAAM,EAAE;IACpB6gB,MAAM,CAAChf,IAAI,CAAC;MACRyb,QAAQ,EAAE,KAAK;MACf/gB,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE6M,KAAK,CAACwE,KAAK,CAACnR,GAAG,CAACmR,KAAK,IAAIA,KAAK,CAACT,MAAM,CAAC/N,IAAI;IACrD,CAAC,CAAC;EACN;EACA,IAAInD,MAAM,CAACiF,IAAI,CAACkI,KAAK,CAAC+D,MAAM,CAAC/K,IAAI,CAAC,CAACrC,MAAM,EAAE;IACvC6gB,MAAM,CAAChf,IAAI,CAAC;MACRyb,QAAQ,EAAE,KAAK;MACf/gB,GAAG,EAAE,MAAM;MACXC,KAAK,EAAE6M,KAAK,CAAC+D,MAAM,CAAC/K;IACxB,CAAC,CAAC;EACN;EACAwe,MAAM,CAAChf,IAAI,CAAC;IACRtF,GAAG,EAAE,OAAO;IACZ+gB,QAAQ,EAAE,KAAK;IACf9gB,KAAK,EAAE;MACHyf,OAAO,EAAE;QACL7V,IAAI,EAAE,IAAI;QACV8V,QAAQ,EAAE,IAAI;QACdC,OAAO,EAAE9S,KAAK,CAACuB,KAAK,CAAClO,GAAG,CAACkO,KAAK,IAAIA,KAAK,CAAC3I,IAAI,CAAC,IAAI,CAAC,CAAC,CAACA,IAAI,CAAC,KAAK,CAAC;QAC/D6Z,OAAO,EAAE,2BAA2B;QACpCtf,KAAK,EAAE6M,KAAK,CAACuB;MACjB;IACJ;EACJ,CAAC,CAAC;EACF,OAAOiW,MAAM;AACjB;AACA;AACA;AACA;AACA,MAAM/C,QAAQ,GAAG,QAAQ;AACzB,MAAMO,QAAQ,GAAG,QAAQ;AACzB,MAAMD,QAAQ,GAAG,QAAQ;AACzB,MAAM0C,QAAQ,GAAG,QAAQ;AACzB,MAAM7C,UAAU,GAAG,QAAQ;AAC3B;AACA,MAAM8C,IAAI,GAAG,QAAQ;AACrB,MAAM7C,OAAO,GAAG,QAAQ;AACxB,MAAMC,OAAO,GAAG,QAAQ;AACxB,SAASkC,6BAA6BA,CAAChX,KAAK,EAAE;EAC1C,MAAMsU,IAAI,GAAG,EAAE;EACf,MAAM;IAAEvQ;EAAO,CAAC,GAAG/D,KAAK;EACxB,IAAI+D,MAAM,CAACjL,IAAI,IAAI,IAAI,EAAE;IACrBwb,IAAI,CAAC9b,IAAI,CAAC;MACN6a,KAAK,EAAEnL,MAAM,CAACnE,MAAM,CAACjL,IAAI,CAAC;MAC1Byb,SAAS,EAAE,CAAC;MACZC,eAAe,EAAEiD;IACrB,CAAC,CAAC;EACN;EACA,IAAI1T,MAAM,CAAClM,OAAO,EAAE;IAChByc,IAAI,CAAC9b,IAAI,CAAC;MACN6a,KAAK,EAAE,OAAO;MACdkB,SAAS,EAAE,CAAC;MACZC,eAAe,EAAEI;IACrB,CAAC,CAAC;EACN;EACA,IAAI5U,KAAK,CAAC2X,UAAU,EAAE;IAClBrD,IAAI,CAAC9b,IAAI,CAAC;MACN6a,KAAK,EAAE,SAAS;MAChBkB,SAAS,EAAE,CAAC;MACZC,eAAe,EAAEC;IACrB,CAAC,CAAC;EACN;EACA,IAAIzU,KAAK,CAAC4X,gBAAgB,EAAE;IACxBtD,IAAI,CAAC9b,IAAI,CAAC;MACN6a,KAAK,EAAE,OAAO;MACdkB,SAAS,EAAE,CAAC;MACZC,eAAe,EAAEO;IACrB,CAAC,CAAC;EACN;EACA,IAAI/U,KAAK,CAAC6X,WAAW,EAAE;IACnBvD,IAAI,CAAC9b,IAAI,CAAC;MACN6a,KAAK,EAAE,QAAQ;MACfkB,SAAS,EAAE,CAAC;MACZC,eAAe,EAAEQ;IACrB,CAAC,CAAC;EACN;EACA,IAAIjR,MAAM,CAACsD,QAAQ,EAAE;IACjBiN,IAAI,CAAC9b,IAAI,CAAC;MACN6a,KAAK,EAAE,OAAOtP,MAAM,CAACsD,QAAQ,KAAK,QAAQ,GACpC,aAAatD,MAAM,CAACsD,QAAQ,EAAE,GAC9B,WAAW;MACjBkN,SAAS,EAAE,QAAQ;MACnBC,eAAe,EAAEkD;IACrB,CAAC,CAAC;EACN;EACA;EACA;EACA,IAAItE,EAAE,GAAGrP,MAAM,CAACqT,OAAO;EACvB,IAAIhE,EAAE,IAAI,IAAI,EAAE;IACZA,EAAE,GAAGlL,MAAM,CAAC4P,aAAa,EAAE,CAAC;IAC5B/T,MAAM,CAACqT,OAAO,GAAGhE,EAAE;EACvB;EACA,OAAO;IACHA,EAAE;IACFC,KAAK,EAAEtP,MAAM,CAAC/N,IAAI;IAClBse,IAAI;IACJ/P,QAAQ,EAAEvE,KAAK,CAACuE,QAAQ,CAAClR,GAAG,CAAC2jB,6BAA6B;EAC9D,CAAC;AACL;AACA;AACA,IAAIc,aAAa,GAAG,CAAC;AACrB,MAAMC,iBAAiB,GAAG,oBAAoB;AAC9C,SAASjB,qBAAqBA,CAAC9W,KAAK,EAAEwM,YAAY,EAAE;EAChD;EACA;EACA,MAAMW,aAAa,GAAGX,YAAY,CAAC/U,OAAO,CAACd,MAAM,IAC7CgB,iBAAiB,CAAC6U,YAAY,CAAC/U,OAAO,CAAC+U,YAAY,CAAC/U,OAAO,CAACd,MAAM,GAAG,CAAC,CAAC,EAAEqJ,KAAK,CAAC+D,MAAM,CAAC;EAC1F/D,KAAK,CAAC4X,gBAAgB,GAAG5X,KAAK,CAAC6X,WAAW,GAAG1K,aAAa;EAC1D,IAAI,CAACA,aAAa,EAAE;IAChBnN,KAAK,CAAC6X,WAAW,GAAGrL,YAAY,CAAC/U,OAAO,CAACyY,IAAI,CAAC7N,KAAK,IAAI1K,iBAAiB,CAAC0K,KAAK,EAAErC,KAAK,CAAC+D,MAAM,CAAC,CAAC;EAClG;EACA/D,KAAK,CAACuE,QAAQ,CAAC1H,OAAO,CAACmb,UAAU,IAAIlB,qBAAqB,CAACkB,UAAU,EAAExL,YAAY,CAAC,CAAC;AACzF;AACA,SAASoK,4BAA4BA,CAAC5W,KAAK,EAAE;EACzCA,KAAK,CAAC2X,UAAU,GAAG,KAAK;EACxB3X,KAAK,CAACuE,QAAQ,CAAC1H,OAAO,CAAC+Z,4BAA4B,CAAC;AACxD;AACA,SAASC,eAAeA,CAAC7W,KAAK,EAAEyG,MAAM,EAAE;EACpC,MAAMwR,KAAK,GAAG/P,MAAM,CAAClI,KAAK,CAACgC,EAAE,CAAC,CAACK,KAAK,CAAC0V,iBAAiB,CAAC;EACvD/X,KAAK,CAAC2X,UAAU,GAAG,KAAK;EACxB,IAAI,CAACM,KAAK,IAAIA,KAAK,CAACthB,MAAM,GAAG,CAAC,EAAE;IAC5B,OAAO,KAAK;EAChB;EACA;EACA,MAAMuhB,WAAW,GAAG,IAAIjW,MAAM,CAACgW,KAAK,CAAC,CAAC,CAAC,CAAC/iB,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE+iB,KAAK,CAAC,CAAC,CAAC,CAAC;EACrE,IAAIC,WAAW,CAACrU,IAAI,CAAC4C,MAAM,CAAC,EAAE;IAC1B;IACAzG,KAAK,CAACuE,QAAQ,CAAC1H,OAAO,CAACsb,KAAK,IAAItB,eAAe,CAACsB,KAAK,EAAE1R,MAAM,CAAC,CAAC;IAC/D;IACA,IAAIzG,KAAK,CAAC+D,MAAM,CAAC/N,IAAI,KAAK,GAAG,IAAIyQ,MAAM,KAAK,GAAG,EAAE;MAC7CzG,KAAK,CAAC2X,UAAU,GAAG3X,KAAK,CAACgC,EAAE,CAAC6B,IAAI,CAAC4C,MAAM,CAAC;MACxC,OAAO,IAAI;IACf;IACA;IACA,OAAO,KAAK;EAChB;EACA,MAAMzQ,IAAI,GAAGgK,KAAK,CAAC+D,MAAM,CAAC/N,IAAI,CAACmB,WAAW,CAAC,CAAC;EAC5C,MAAMihB,WAAW,GAAG5iB,MAAM,CAACQ,IAAI,CAAC;EAChC;EACA,IAAI,CAACyQ,MAAM,CAACrP,UAAU,CAAC,GAAG,CAAC,KACtBghB,WAAW,CAAChc,QAAQ,CAACqK,MAAM,CAAC,IAAIzQ,IAAI,CAACoG,QAAQ,CAACqK,MAAM,CAAC,CAAC,EACvD,OAAO,IAAI;EACf,IAAI2R,WAAW,CAAChhB,UAAU,CAACqP,MAAM,CAAC,IAAIzQ,IAAI,CAACoB,UAAU,CAACqP,MAAM,CAAC,EACzD,OAAO,IAAI;EACf,IAAIzG,KAAK,CAAC+D,MAAM,CAACjL,IAAI,IAAIoP,MAAM,CAAClI,KAAK,CAAC+D,MAAM,CAACjL,IAAI,CAAC,CAACsD,QAAQ,CAACqK,MAAM,CAAC,EAC/D,OAAO,IAAI;EACf,OAAOzG,KAAK,CAACuE,QAAQ,CAAC2L,IAAI,CAACiI,KAAK,IAAItB,eAAe,CAACsB,KAAK,EAAE1R,MAAM,CAAC,CAAC;AACvE;AACA,SAASkM,IAAIA,CAACpgB,GAAG,EAAEuF,IAAI,EAAE;EACrB,MAAMugB,GAAG,GAAG,CAAC,CAAC;EACd,KAAK,MAAMnlB,GAAG,IAAIX,GAAG,EAAE;IACnB,IAAI,CAACuF,IAAI,CAACsE,QAAQ,CAAClJ,GAAG,CAAC,EAAE;MACrB;MACAmlB,GAAG,CAACnlB,GAAG,CAAC,GAAGX,GAAG,CAACW,GAAG,CAAC;IACvB;EACJ;EACA,OAAOmlB,GAAG;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAAChX,OAAO,EAAE;EAC3B,MAAMgD,OAAO,GAAGG,mBAAmB,CAACnD,OAAO,CAACoD,MAAM,EAAEpD,OAAO,CAAC;EAC5D,MAAMiX,YAAY,GAAGjX,OAAO,CAACpL,UAAU,IAAIA,UAAU;EACrD,MAAMsiB,gBAAgB,GAAGlX,OAAO,CAACvK,cAAc,IAAIA,cAAc;EACjE,MAAMkI,aAAa,GAAGqC,OAAO,CAACnG,OAAO;EACrC,IAAKxF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAK,CAACoJ,aAAa,EACzD,MAAM,IAAIwB,KAAK,CAAC,6DAA6D,GACzE,qEAAqE,CAAC;EAC9E,MAAMgY,YAAY,GAAGhP,YAAY,CAAC,CAAC;EACnC,MAAMiP,mBAAmB,GAAGjP,YAAY,CAAC,CAAC;EAC1C,MAAMkP,WAAW,GAAGlP,YAAY,CAAC,CAAC;EAClC,MAAM+C,YAAY,GAAG1a,UAAU,CAAC+G,yBAAyB,CAAC;EAC1D,IAAI+f,eAAe,GAAG/f,yBAAyB;EAC/C;EACA,IAAI3G,SAAS,IAAIoP,OAAO,CAACuX,cAAc,IAAI,mBAAmB,IAAI1d,OAAO,EAAE;IACvEA,OAAO,CAAC2d,iBAAiB,GAAG,QAAQ;EACxC;EACA,MAAMC,eAAe,GAAGjmB,aAAa,CAACoM,IAAI,CAAC,IAAI,EAAE8Z,UAAU,IAAI,EAAE,GAAGA,UAAU,CAAC;EAC/E,MAAMC,YAAY,GAAGnmB,aAAa,CAACoM,IAAI,CAAC,IAAI,EAAE3J,WAAW,CAAC;EAC1D,MAAM2jB,YAAY;EAClB;EACApmB,aAAa,CAACoM,IAAI,CAAC,IAAI,EAAE1J,MAAM,CAAC;EAChC,SAASwP,QAAQA,CAACmU,aAAa,EAAEnZ,KAAK,EAAE;IACpC,IAAIgE,MAAM;IACV,IAAID,MAAM;IACV,IAAI9D,WAAW,CAACkZ,aAAa,CAAC,EAAE;MAC5BnV,MAAM,GAAGM,OAAO,CAACS,gBAAgB,CAACoU,aAAa,CAAC;MAChD,IAAKxjB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAK,CAACmO,MAAM,EAAE;QACpDxQ,IAAI,CAAC,iBAAiB0U,MAAM,CAACiR,aAAa,CAAC,qCAAqC,EAAEnZ,KAAK,CAAC;MAC5F;MACA+D,MAAM,GAAG/D,KAAK;IAClB,CAAC,MACI;MACD+D,MAAM,GAAGoV,aAAa;IAC1B;IACA,OAAO7U,OAAO,CAACU,QAAQ,CAACjB,MAAM,EAAEC,MAAM,CAAC;EAC3C;EACA,SAASiC,WAAWA,CAACnN,IAAI,EAAE;IACvB,MAAMsgB,aAAa,GAAG9U,OAAO,CAACS,gBAAgB,CAACjM,IAAI,CAAC;IACpD,IAAIsgB,aAAa,EAAE;MACf9U,OAAO,CAAC2B,WAAW,CAACmT,aAAa,CAAC;IACtC,CAAC,MACI,IAAKzjB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAG;MAC9CrC,IAAI,CAAC,qCAAqC0U,MAAM,CAACpP,IAAI,CAAC,GAAG,CAAC;IAC9D;EACJ;EACA,SAASuN,SAASA,CAAA,EAAG;IACjB,OAAO/B,OAAO,CAAC+B,SAAS,CAAC,CAAC,CAAChT,GAAG,CAACgmB,YAAY,IAAIA,YAAY,CAACtV,MAAM,CAAC;EACvE;EACA,SAASuV,QAAQA,CAACxgB,IAAI,EAAE;IACpB,OAAO,CAAC,CAACwL,OAAO,CAACS,gBAAgB,CAACjM,IAAI,CAAC;EAC3C;EACA,SAASyN,OAAOA,CAACgT,WAAW,EAAEnjB,eAAe,EAAE;IAC3C;IACA;IACA;IACAA,eAAe,GAAGxD,MAAM,CAAC,CAAC,CAAC,EAAEwD,eAAe,IAAIoW,YAAY,CAACrZ,KAAK,CAAC;IACnE,IAAI,OAAOomB,WAAW,KAAK,QAAQ,EAAE;MACjC,MAAMC,kBAAkB,GAAGvjB,QAAQ,CAACsiB,YAAY,EAAEgB,WAAW,EAAEnjB,eAAe,CAACJ,IAAI,CAAC;MACpF,MAAM+a,YAAY,GAAGzM,OAAO,CAACiC,OAAO,CAAC;QAAEvQ,IAAI,EAAEwjB,kBAAkB,CAACxjB;MAAK,CAAC,EAAEI,eAAe,CAAC;MACxF,MAAM2X,IAAI,GAAG9O,aAAa,CAACvF,UAAU,CAAC8f,kBAAkB,CAAC3iB,QAAQ,CAAC;MAClE,IAAKlB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAG;QACzC,IAAIkY,IAAI,CAAC3W,UAAU,CAAC,IAAI,CAAC,EACrB5D,IAAI,CAAC,aAAa+lB,WAAW,kBAAkBxL,IAAI,4DAA4D,CAAC,CAAC,KAChH,IAAI,CAACgD,YAAY,CAACtZ,OAAO,CAACd,MAAM,EAAE;UACnCnD,IAAI,CAAC,0CAA0C+lB,WAAW,GAAG,CAAC;QAClE;MACJ;MACA;MACA,OAAO3mB,MAAM,CAAC4mB,kBAAkB,EAAEzI,YAAY,EAAE;QAC5C/d,MAAM,EAAEkmB,YAAY,CAACnI,YAAY,CAAC/d,MAAM,CAAC;QACzCuD,IAAI,EAAEf,MAAM,CAACgkB,kBAAkB,CAACjjB,IAAI,CAAC;QACrC0C,cAAc,EAAEF,SAAS;QACzBgV;MACJ,CAAC,CAAC;IACN;IACA,IAAKpY,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAK,CAACkK,eAAe,CAACwZ,WAAW,CAAC,EAAE;MAC1E/lB,IAAI,CAAC,6FAA6F,EAAE+lB,WAAW,CAAC;MAChH,OAAOhT,OAAO,CAAC,CAAC,CAAC,CAAC;IACtB;IACA,IAAIkT,eAAe;IACnB;IACA,IAAIF,WAAW,CAACvjB,IAAI,IAAI,IAAI,EAAE;MAC1B,IAAKL,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACtC,QAAQ,IAAI0jB,WAAW,IACvB,EAAE,MAAM,IAAIA,WAAW,CAAC;MACxB;MACA1mB,MAAM,CAACiF,IAAI,CAACyhB,WAAW,CAACvmB,MAAM,CAAC,CAAC2D,MAAM,EAAE;QACxCnD,IAAI,CAAC,SAAS+lB,WAAW,CAACvjB,IAAI,gGAAgG,CAAC;MACnI;MACAyjB,eAAe,GAAG7mB,MAAM,CAAC,CAAC,CAAC,EAAE2mB,WAAW,EAAE;QACtCvjB,IAAI,EAAEC,QAAQ,CAACsiB,YAAY,EAAEgB,WAAW,CAACvjB,IAAI,EAAEI,eAAe,CAACJ,IAAI,CAAC,CAACA;MACzE,CAAC,CAAC;IACN,CAAC,MACI;MACD;MACA,MAAM0jB,YAAY,GAAG9mB,MAAM,CAAC,CAAC,CAAC,EAAE2mB,WAAW,CAACvmB,MAAM,CAAC;MACnD,KAAK,MAAME,GAAG,IAAIwmB,YAAY,EAAE;QAC5B,IAAIA,YAAY,CAACxmB,GAAG,CAAC,IAAI,IAAI,EAAE;UAC3B,OAAOwmB,YAAY,CAACxmB,GAAG,CAAC;QAC5B;MACJ;MACA;MACAumB,eAAe,GAAG7mB,MAAM,CAAC,CAAC,CAAC,EAAE2mB,WAAW,EAAE;QACtCvmB,MAAM,EAAEimB,YAAY,CAACS,YAAY;MACrC,CAAC,CAAC;MACF;MACA;MACAtjB,eAAe,CAACpD,MAAM,GAAGimB,YAAY,CAAC7iB,eAAe,CAACpD,MAAM,CAAC;IACjE;IACA,MAAM+d,YAAY,GAAGzM,OAAO,CAACiC,OAAO,CAACkT,eAAe,EAAErjB,eAAe,CAAC;IACtE,MAAMG,IAAI,GAAGgjB,WAAW,CAAChjB,IAAI,IAAI,EAAE;IACnC,IAAKZ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAKU,IAAI,IAAI,CAACA,IAAI,CAACa,UAAU,CAAC,GAAG,CAAC,EAAE;MAC1E5D,IAAI,CAAC,mEAAmE+C,IAAI,YAAYA,IAAI,IAAI,CAAC;IACrG;IACA;IACA;IACAwa,YAAY,CAAC/d,MAAM,GAAG+lB,eAAe,CAACG,YAAY,CAACnI,YAAY,CAAC/d,MAAM,CAAC,CAAC;IACxE,MAAM6D,QAAQ,GAAGC,YAAY,CAAC0hB,gBAAgB,EAAE5lB,MAAM,CAAC,CAAC,CAAC,EAAE2mB,WAAW,EAAE;MACpEhjB,IAAI,EAAEpB,UAAU,CAACoB,IAAI,CAAC;MACtBP,IAAI,EAAE+a,YAAY,CAAC/a;IACvB,CAAC,CAAC,CAAC;IACH,MAAM+X,IAAI,GAAG9O,aAAa,CAACvF,UAAU,CAAC7C,QAAQ,CAAC;IAC/C,IAAKlB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAG;MACzC,IAAIkY,IAAI,CAAC3W,UAAU,CAAC,IAAI,CAAC,EAAE;QACvB5D,IAAI,CAAC,aAAa+lB,WAAW,kBAAkBxL,IAAI,4DAA4D,CAAC;MACpH,CAAC,MACI,IAAI,CAACgD,YAAY,CAACtZ,OAAO,CAACd,MAAM,EAAE;QACnCnD,IAAI,CAAC,0CAA0C+lB,WAAW,CAACvjB,IAAI,IAAI,IAAI,GAAGujB,WAAW,CAACvjB,IAAI,GAAGujB,WAAW,GAAG,CAAC;MAChH;IACJ;IACA,OAAO3mB,MAAM,CAAC;MACViE,QAAQ;MACR;MACA;MACAN,IAAI;MACJF,KAAK;MACL;MACA;MACA;MACA;MACA;MACAmiB,gBAAgB,KAAKzhB,cAAc,GAC7BmS,cAAc,CAACqQ,WAAW,CAACljB,KAAK,CAAC,GAChCkjB,WAAW,CAACljB,KAAK,IAAI,CAAC;IACjC,CAAC,EAAE0a,YAAY,EAAE;MACb9X,cAAc,EAAEF,SAAS;MACzBgV;IACJ,CAAC,CAAC;EACN;EACA,SAAS4L,gBAAgBA,CAACxhB,EAAE,EAAE;IAC1B,OAAO,OAAOA,EAAE,KAAK,QAAQ,GACvBlC,QAAQ,CAACsiB,YAAY,EAAEpgB,EAAE,EAAEqU,YAAY,CAACrZ,KAAK,CAAC6C,IAAI,CAAC,GACnDpD,MAAM,CAAC,CAAC,CAAC,EAAEuF,EAAE,CAAC;EACxB;EACA,SAASyhB,uBAAuBA,CAACzhB,EAAE,EAAExE,IAAI,EAAE;IACvC,IAAIilB,eAAe,KAAKzgB,EAAE,EAAE;MACxB,OAAOqI,iBAAiB,CAAC,CAAC,CAAC,uCAAuC;QAC9D7M,IAAI;QACJwE;MACJ,CAAC,CAAC;IACN;EACJ;EACA,SAASK,IAAIA,CAACL,EAAE,EAAE;IACd,OAAO0hB,gBAAgB,CAAC1hB,EAAE,CAAC;EAC/B;EACA,SAASjD,OAAOA,CAACiD,EAAE,EAAE;IACjB,OAAOK,IAAI,CAAC5F,MAAM,CAAC+mB,gBAAgB,CAACxhB,EAAE,CAAC,EAAE;MAAEjD,OAAO,EAAE;IAAK,CAAC,CAAC,CAAC;EAChE;EACA,SAAS4kB,oBAAoBA,CAAC3hB,EAAE,EAAE;IAC9B,MAAM4hB,WAAW,GAAG5hB,EAAE,CAACV,OAAO,CAACU,EAAE,CAACV,OAAO,CAACd,MAAM,GAAG,CAAC,CAAC;IACrD,IAAIojB,WAAW,IAAIA,WAAW,CAAC1S,QAAQ,EAAE;MACrC,MAAM;QAAEA;MAAS,CAAC,GAAG0S,WAAW;MAChC,IAAIC,iBAAiB,GAAG,OAAO3S,QAAQ,KAAK,UAAU,GAAGA,QAAQ,CAAClP,EAAE,CAAC,GAAGkP,QAAQ;MAChF,IAAI,OAAO2S,iBAAiB,KAAK,QAAQ,EAAE;QACvCA,iBAAiB,GACbA,iBAAiB,CAAC5d,QAAQ,CAAC,GAAG,CAAC,IAAI4d,iBAAiB,CAAC5d,QAAQ,CAAC,GAAG,CAAC,GAC3D4d,iBAAiB,GAAGL,gBAAgB,CAACK,iBAAiB,CAAC;QACxD;QACE;UAAEhkB,IAAI,EAAEgkB;QAAkB,CAAC;QACvC;QACA;QACAA,iBAAiB,CAAChnB,MAAM,GAAG,CAAC,CAAC;MACjC;MACA,IAAK2C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACtCmkB,iBAAiB,CAAChkB,IAAI,IAAI,IAAI,IAC9B,EAAE,MAAM,IAAIgkB,iBAAiB,CAAC,EAAE;QAChCxmB,IAAI,CAAC,4BAA4B6M,IAAI,CAACC,SAAS,CAAC0Z,iBAAiB,EAAE,IAAI,EAAE,CAAC,CAAC,0BAA0B7hB,EAAE,CAACtB,QAAQ,2EAA2E,CAAC;QAC5L,MAAM,IAAI4J,KAAK,CAAC,kBAAkB,CAAC;MACvC;MACA,OAAO7N,MAAM,CAAC;QACVyD,KAAK,EAAE8B,EAAE,CAAC9B,KAAK;QACfE,IAAI,EAAE4B,EAAE,CAAC5B,IAAI;QACb;QACAvD,MAAM,EAAEgnB,iBAAiB,CAAChkB,IAAI,IAAI,IAAI,GAAG,CAAC,CAAC,GAAGmC,EAAE,CAACnF;MACrD,CAAC,EAAEgnB,iBAAiB,CAAC;IACzB;EACJ;EACA,SAASH,gBAAgBA,CAAC1hB,EAAE,EAAEc,cAAc,EAAE;IAC1C,MAAMghB,cAAc,GAAIrB,eAAe,GAAGrS,OAAO,CAACpO,EAAE,CAAE;IACtD,MAAMxE,IAAI,GAAG6Y,YAAY,CAACrZ,KAAK;IAC/B,MAAMuL,IAAI,GAAGvG,EAAE,CAACiD,KAAK;IACrB,MAAM8e,KAAK,GAAG/hB,EAAE,CAAC+hB,KAAK;IACtB;IACA,MAAMhlB,OAAO,GAAGiD,EAAE,CAACjD,OAAO,KAAK,IAAI;IACnC,MAAMilB,cAAc,GAAGL,oBAAoB,CAACG,cAAc,CAAC;IAC3D,IAAIE,cAAc,EACd,OAAON,gBAAgB,CAACjnB,MAAM,CAAC+mB,gBAAgB,CAACQ,cAAc,CAAC,EAAE;MAC7D/e,KAAK,EAAE,OAAO+e,cAAc,KAAK,QAAQ,GACnCvnB,MAAM,CAAC,CAAC,CAAC,EAAE8L,IAAI,EAAEyb,cAAc,CAAC/e,KAAK,CAAC,GACtCsD,IAAI;MACVwb,KAAK;MACLhlB;IACJ,CAAC,CAAC;IACF;IACA+D,cAAc,IAAIghB,cAAc,CAAC;IACrC;IACA,MAAMG,UAAU,GAAGH,cAAc;IACjCG,UAAU,CAACnhB,cAAc,GAAGA,cAAc;IAC1C,IAAIqd,OAAO;IACX,IAAI,CAAC4D,KAAK,IAAI7iB,mBAAmB,CAACmhB,gBAAgB,EAAE7kB,IAAI,EAAEsmB,cAAc,CAAC,EAAE;MACvE3D,OAAO,GAAG9V,iBAAiB,CAAC,EAAE,CAAC,wCAAwC;QAAErI,EAAE,EAAEiiB,UAAU;QAAEzmB;MAAK,CAAC,CAAC;MAChG;MACA0mB,YAAY,CAAC1mB,IAAI,EAAEA,IAAI;MACvB;MACA;MACA,IAAI;MACJ;MACA;MACA,KAAK,CAAC;IACV;IACA,OAAO,CAAC2iB,OAAO,GAAG7L,OAAO,CAAClE,OAAO,CAAC+P,OAAO,CAAC,GAAGlJ,QAAQ,CAACgN,UAAU,EAAEzmB,IAAI,CAAC,EAClE0X,KAAK,CAAE5M,KAAK,IAAKiC,mBAAmB,CAACjC,KAAK,CAAC;IAC1C;IACEiC,mBAAmB,CAACjC,KAAK,EAAE,CAAC,CAAC,0CAA0C,CAAC,GAClEA,KAAK,GACL6b,WAAW,CAAC7b,KAAK,CAAC,CAAC;IAAA;IAC3B;IACE8b,YAAY,CAAC9b,KAAK,EAAE2b,UAAU,EAAEzmB,IAAI,CAAC,CAAC,CACzCsX,IAAI,CAAEqL,OAAO,IAAK;MACnB,IAAIA,OAAO,EAAE;QACT,IAAI5V,mBAAmB,CAAC4V,OAAO,EAAE,CAAC,CAAC,0CAA0C,CAAC,EAAE;UAC5E,IAAK3gB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY;UACtC;UACAwB,mBAAmB,CAACmhB,gBAAgB,EAAEjS,OAAO,CAAC+P,OAAO,CAACne,EAAE,CAAC,EAAEiiB,UAAU,CAAC;UACtE;UACAnhB,cAAc;UACd;UACA,CAACA,cAAc,CAACuhB,MAAM,GAAGvhB,cAAc,CAACuhB,MAAM;UACxC;UACEvhB,cAAc,CAACuhB,MAAM,GAAG,CAAC,GAC3B,CAAC,IAAI,EAAE,EAAE;YACfhnB,IAAI,CAAC,mFAAmFG,IAAI,CAACkD,QAAQ,SAASujB,UAAU,CAACvjB,QAAQ,yPAAyP,CAAC;YAC3X,OAAO4T,OAAO,CAACC,MAAM,CAAC,IAAIjK,KAAK,CAAC,uCAAuC,CAAC,CAAC;UAC7E;UACA,OAAOoZ,gBAAgB;UACvB;UACAjnB,MAAM,CAAC;YACH;YACAsC;UACJ,CAAC,EAAEykB,gBAAgB,CAACrD,OAAO,CAACne,EAAE,CAAC,EAAE;YAC7BiD,KAAK,EAAE,OAAOkb,OAAO,CAACne,EAAE,KAAK,QAAQ,GAC/BvF,MAAM,CAAC,CAAC,CAAC,EAAE8L,IAAI,EAAE4X,OAAO,CAACne,EAAE,CAACiD,KAAK,CAAC,GAClCsD,IAAI;YACVwb;UACJ,CAAC,CAAC;UACF;UACAjhB,cAAc,IAAImhB,UAAU,CAAC;QACjC;MACJ,CAAC,MACI;QACD;QACA9D,OAAO,GAAGmE,kBAAkB,CAACL,UAAU,EAAEzmB,IAAI,EAAE,IAAI,EAAEuB,OAAO,EAAEwJ,IAAI,CAAC;MACvE;MACAgc,gBAAgB,CAACN,UAAU,EAAEzmB,IAAI,EAAE2iB,OAAO,CAAC;MAC3C,OAAOA,OAAO;IAClB,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACI,SAASqE,gCAAgCA,CAACxiB,EAAE,EAAExE,IAAI,EAAE;IAChD,MAAM8K,KAAK,GAAGmb,uBAAuB,CAACzhB,EAAE,EAAExE,IAAI,CAAC;IAC/C,OAAO8K,KAAK,GAAGgM,OAAO,CAACC,MAAM,CAACjM,KAAK,CAAC,GAAGgM,OAAO,CAAClE,OAAO,CAAC,CAAC;EAC5D;EACA,SAASgE,cAAcA,CAACxX,EAAE,EAAE;IACxB,MAAMmgB,GAAG,GAAG0H,aAAa,CAAC5R,MAAM,CAAC,CAAC,CAAC2B,IAAI,CAAC,CAAC,CAACxX,KAAK;IAC/C;IACA,OAAO+f,GAAG,IAAI,OAAOA,GAAG,CAAC3I,cAAc,KAAK,UAAU,GAChD2I,GAAG,CAAC3I,cAAc,CAACxX,EAAE,CAAC,GACtBA,EAAE,CAAC,CAAC;EACd;EACA;EACA,SAASqa,QAAQA,CAACjV,EAAE,EAAExE,IAAI,EAAE;IACxB,IAAI8X,MAAM;IACV,MAAM,CAACoP,cAAc,EAAEC,eAAe,EAAEC,eAAe,CAAC,GAAGC,sBAAsB,CAAC7iB,EAAE,EAAExE,IAAI,CAAC;IAC3F;IACA8X,MAAM,GAAGF,uBAAuB,CAACsP,cAAc,CAACI,OAAO,CAAC,CAAC,EAAE,kBAAkB,EAAE9iB,EAAE,EAAExE,IAAI,CAAC;IACxF;IACA,KAAK,MAAMoQ,MAAM,IAAI8W,cAAc,EAAE;MACjC9W,MAAM,CAAC2D,WAAW,CAAC7K,OAAO,CAACkN,KAAK,IAAI;QAChC0B,MAAM,CAACjT,IAAI,CAAC8R,gBAAgB,CAACP,KAAK,EAAE5R,EAAE,EAAExE,IAAI,CAAC,CAAC;MAClD,CAAC,CAAC;IACN;IACA,MAAMunB,uBAAuB,GAAGP,gCAAgC,CAACzb,IAAI,CAAC,IAAI,EAAE/G,EAAE,EAAExE,IAAI,CAAC;IACrF8X,MAAM,CAACjT,IAAI,CAAC0iB,uBAAuB,CAAC;IACpC;IACA,OAAQC,aAAa,CAAC1P,MAAM,CAAC,CACxBR,IAAI,CAAC,MAAM;MACZ;MACAQ,MAAM,GAAG,EAAE;MACX,KAAK,MAAM1B,KAAK,IAAI0O,YAAY,CAAC5O,IAAI,CAAC,CAAC,EAAE;QACrC4B,MAAM,CAACjT,IAAI,CAAC8R,gBAAgB,CAACP,KAAK,EAAE5R,EAAE,EAAExE,IAAI,CAAC,CAAC;MAClD;MACA8X,MAAM,CAACjT,IAAI,CAAC0iB,uBAAuB,CAAC;MACpC,OAAOC,aAAa,CAAC1P,MAAM,CAAC;IAChC,CAAC,CAAC,CACGR,IAAI,CAAC,MAAM;MACZ;MACAQ,MAAM,GAAGF,uBAAuB,CAACuP,eAAe,EAAE,mBAAmB,EAAE3iB,EAAE,EAAExE,IAAI,CAAC;MAChF,KAAK,MAAMoQ,MAAM,IAAI+W,eAAe,EAAE;QAClC/W,MAAM,CAAC4D,YAAY,CAAC9K,OAAO,CAACkN,KAAK,IAAI;UACjC0B,MAAM,CAACjT,IAAI,CAAC8R,gBAAgB,CAACP,KAAK,EAAE5R,EAAE,EAAExE,IAAI,CAAC,CAAC;QAClD,CAAC,CAAC;MACN;MACA8X,MAAM,CAACjT,IAAI,CAAC0iB,uBAAuB,CAAC;MACpC;MACA,OAAOC,aAAa,CAAC1P,MAAM,CAAC;IAChC,CAAC,CAAC,CACGR,IAAI,CAAC,MAAM;MACZ;MACAQ,MAAM,GAAG,EAAE;MACX,KAAK,MAAM1H,MAAM,IAAIgX,eAAe,EAAE;QAClC;QACA,IAAIhX,MAAM,CAACuD,WAAW,EAAE;UACpB,IAAIlU,OAAO,CAAC2Q,MAAM,CAACuD,WAAW,CAAC,EAAE;YAC7B,KAAK,MAAMA,WAAW,IAAIvD,MAAM,CAACuD,WAAW,EACxCmE,MAAM,CAACjT,IAAI,CAAC8R,gBAAgB,CAAChD,WAAW,EAAEnP,EAAE,EAAExE,IAAI,CAAC,CAAC;UAC5D,CAAC,MACI;YACD8X,MAAM,CAACjT,IAAI,CAAC8R,gBAAgB,CAACvG,MAAM,CAACuD,WAAW,EAAEnP,EAAE,EAAExE,IAAI,CAAC,CAAC;UAC/D;QACJ;MACJ;MACA8X,MAAM,CAACjT,IAAI,CAAC0iB,uBAAuB,CAAC;MACpC;MACA,OAAOC,aAAa,CAAC1P,MAAM,CAAC;IAChC,CAAC,CAAC,CACGR,IAAI,CAAC,MAAM;MACZ;MACA;MACA9S,EAAE,CAACV,OAAO,CAACoF,OAAO,CAACkH,MAAM,IAAKA,MAAM,CAAC6D,cAAc,GAAG,CAAC,CAAE,CAAC;MAC1D;MACA6D,MAAM,GAAGF,uBAAuB,CAACwP,eAAe,EAAE,kBAAkB,EAAE5iB,EAAE,EAAExE,IAAI,EAAE4W,cAAc,CAAC;MAC/FkB,MAAM,CAACjT,IAAI,CAAC0iB,uBAAuB,CAAC;MACpC;MACA,OAAOC,aAAa,CAAC1P,MAAM,CAAC;IAChC,CAAC,CAAC,CACGR,IAAI,CAAC,MAAM;MACZ;MACAQ,MAAM,GAAG,EAAE;MACX,KAAK,MAAM1B,KAAK,IAAI2O,mBAAmB,CAAC7O,IAAI,CAAC,CAAC,EAAE;QAC5C4B,MAAM,CAACjT,IAAI,CAAC8R,gBAAgB,CAACP,KAAK,EAAE5R,EAAE,EAAExE,IAAI,CAAC,CAAC;MAClD;MACA8X,MAAM,CAACjT,IAAI,CAAC0iB,uBAAuB,CAAC;MACpC,OAAOC,aAAa,CAAC1P,MAAM,CAAC;IAChC,CAAC;IACG;IAAA,CACCJ,KAAK,CAAC3V,GAAG,IAAIgL,mBAAmB,CAAChL,GAAG,EAAE,CAAC,CAAC,qCAAqC,CAAC,GAC7EA,GAAG,GACH+U,OAAO,CAACC,MAAM,CAAChV,GAAG,CAAC,CAAC;EAC9B;EACA,SAASglB,gBAAgBA,CAACviB,EAAE,EAAExE,IAAI,EAAE2iB,OAAO,EAAE;IACzC;IACA;IACAqC,WAAW,CACN9O,IAAI,CAAC,CAAC,CACNhN,OAAO,CAACkN,KAAK,IAAIQ,cAAc,CAAC,MAAMR,KAAK,CAAC5R,EAAE,EAAExE,IAAI,EAAE2iB,OAAO,CAAC,CAAC,CAAC;EACzE;EACA;AACJ;AACA;AACA;AACA;EACI,SAASmE,kBAAkBA,CAACL,UAAU,EAAEzmB,IAAI,EAAEynB,MAAM,EAAElmB,OAAO,EAAEwJ,IAAI,EAAE;IACjE;IACA,MAAMD,KAAK,GAAGmb,uBAAuB,CAACQ,UAAU,EAAEzmB,IAAI,CAAC;IACvD,IAAI8K,KAAK,EACL,OAAOA,KAAK;IAChB;IACA,MAAM4c,iBAAiB,GAAG1nB,IAAI,KAAKkF,yBAAyB;IAC5D,MAAMuC,KAAK,GAAG,CAAClJ,SAAS,GAAG,CAAC,CAAC,GAAGiJ,OAAO,CAACC,KAAK;IAC7C;IACA;IACA,IAAIggB,MAAM,EAAE;MACR;MACA;MACA,IAAIlmB,OAAO,IAAImmB,iBAAiB,EAC5Bpc,aAAa,CAAC/J,OAAO,CAACklB,UAAU,CAACvjB,QAAQ,EAAEjE,MAAM,CAAC;QAC9C+I,MAAM,EAAE0f,iBAAiB,IAAIjgB,KAAK,IAAIA,KAAK,CAACO;MAChD,CAAC,EAAE+C,IAAI,CAAC,CAAC,CAAC,KAEVO,aAAa,CAACzG,IAAI,CAAC4hB,UAAU,CAACvjB,QAAQ,EAAE6H,IAAI,CAAC;IACrD;IACA;IACA8N,YAAY,CAACrZ,KAAK,GAAGinB,UAAU;IAC/BC,YAAY,CAACD,UAAU,EAAEzmB,IAAI,EAAEynB,MAAM,EAAEC,iBAAiB,CAAC;IACzDf,WAAW,CAAC,CAAC;EACjB;EACA,IAAIgB,qBAAqB;EACzB;EACA,SAASC,cAAcA,CAAA,EAAG;IACtB;IACA,IAAID,qBAAqB,EACrB;IACJA,qBAAqB,GAAGrc,aAAa,CAAC3B,MAAM,CAAC,CAACnF,EAAE,EAAEqjB,KAAK,EAAEhc,IAAI,KAAK;MAC9D,IAAI,CAAC+M,MAAM,CAACkP,SAAS,EACjB;MACJ;MACA,MAAMrB,UAAU,GAAG7T,OAAO,CAACpO,EAAE,CAAC;MAC9B;MACA;MACA;MACA,MAAMgiB,cAAc,GAAGL,oBAAoB,CAACM,UAAU,CAAC;MACvD,IAAID,cAAc,EAAE;QAChBN,gBAAgB,CAACjnB,MAAM,CAACunB,cAAc,EAAE;UAAEjlB,OAAO,EAAE,IAAI;UAAEglB,KAAK,EAAE;QAAK,CAAC,CAAC,EAAEE,UAAU,CAAC,CAAC/O,KAAK,CAAC/X,IAAI,CAAC;QAChG;MACJ;MACAslB,eAAe,GAAGwB,UAAU;MAC5B,MAAMzmB,IAAI,GAAG6Y,YAAY,CAACrZ,KAAK;MAC/B;MACA,IAAIjB,SAAS,EAAE;QACXqJ,kBAAkB,CAACN,YAAY,CAACtH,IAAI,CAACkD,QAAQ,EAAE2I,IAAI,CAACtE,KAAK,CAAC,EAAEb,qBAAqB,CAAC,CAAC,CAAC;MACxF;MACA+S,QAAQ,CAACgN,UAAU,EAAEzmB,IAAI,CAAC,CACrB0X,KAAK,CAAE5M,KAAK,IAAK;QAClB,IAAIiC,mBAAmB,CAACjC,KAAK,EAAE,CAAC,CAAC,sCAAsC,CAAC,CAAC,qCAAqC,CAAC,EAAE;UAC7G,OAAOA,KAAK;QAChB;QACA,IAAIiC,mBAAmB,CAACjC,KAAK,EAAE,CAAC,CAAC,0CAA0C,CAAC,EAAE;UAC1E;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACAob,gBAAgB,CAACjnB,MAAM,CAAC+mB,gBAAgB,CAAClb,KAAK,CAACtG,EAAE,CAAC,EAAE;YAChD+hB,KAAK,EAAE;UACX,CAAC,CAAC,EAAEE;UACJ;UACA,CAAC,CACInP,IAAI,CAACqL,OAAO,IAAI;YACjB;YACA;YACA;YACA,IAAI5V,mBAAmB,CAAC4V,OAAO,EAAE,CAAC,CAAC,sCAC/B,EAAE,CAAC,sCAAsC,CAAC,IAC1C,CAAC9W,IAAI,CAACtE,KAAK,IACXsE,IAAI,CAACzC,IAAI,KAAK7D,cAAc,CAAC8D,GAAG,EAAE;cAClCiC,aAAa,CAACF,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;YAC/B;UACJ,CAAC,CAAC,CACGsM,KAAK,CAAC/X,IAAI,CAAC;UAChB;UACA,OAAOmX,OAAO,CAACC,MAAM,CAAC,CAAC;QAC3B;QACA;QACA,IAAIlL,IAAI,CAACtE,KAAK,EAAE;UACZ+D,aAAa,CAACF,EAAE,CAAC,CAACS,IAAI,CAACtE,KAAK,EAAE,KAAK,CAAC;QACxC;QACA;QACA,OAAOqf,YAAY,CAAC9b,KAAK,EAAE2b,UAAU,EAAEzmB,IAAI,CAAC;MAChD,CAAC,CAAC,CACGsX,IAAI,CAAEqL,OAAO,IAAK;QACnBA,OAAO,GACHA,OAAO,IACHmE,kBAAkB;QAClB;QACAL,UAAU,EAAEzmB,IAAI,EAAE,KAAK,CAAC;QAChC;QACA,IAAI2iB,OAAO,EAAE;UACT,IAAI9W,IAAI,CAACtE,KAAK;UACV;UACA;UACA,CAACwF,mBAAmB,CAAC4V,OAAO,EAAE,CAAC,CAAC,qCAAqC,CAAC,EAAE;YACxErX,aAAa,CAACF,EAAE,CAAC,CAACS,IAAI,CAACtE,KAAK,EAAE,KAAK,CAAC;UACxC,CAAC,MACI,IAAIsE,IAAI,CAACzC,IAAI,KAAK7D,cAAc,CAAC8D,GAAG,IACrC0D,mBAAmB,CAAC4V,OAAO,EAAE,CAAC,CAAC,sCAAsC,EAAE,CAAC,sCAAsC,CAAC,EAAE;YACjH;YACA;YACArX,aAAa,CAACF,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;UAC/B;QACJ;QACA2b,gBAAgB,CAACN,UAAU,EAAEzmB,IAAI,EAAE2iB,OAAO,CAAC;MAC/C,CAAC;MACG;MAAA,CACCjL,KAAK,CAAC/X,IAAI,CAAC;IACpB,CAAC,CAAC;EACN;EACA;EACA,IAAIooB,aAAa,GAAGjS,YAAY,CAAC,CAAC;EAClC,IAAIkS,cAAc,GAAGlS,YAAY,CAAC,CAAC;EACnC,IAAImS,KAAK;EACT;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAASrB,YAAYA,CAAC9b,KAAK,EAAEtG,EAAE,EAAExE,IAAI,EAAE;IACnC2mB,WAAW,CAAC7b,KAAK,CAAC;IAClB,MAAMoL,IAAI,GAAG8R,cAAc,CAAC9R,IAAI,CAAC,CAAC;IAClC,IAAIA,IAAI,CAAClT,MAAM,EAAE;MACbkT,IAAI,CAAChN,OAAO,CAAC8M,OAAO,IAAIA,OAAO,CAAClL,KAAK,EAAEtG,EAAE,EAAExE,IAAI,CAAC,CAAC;IACrD,CAAC,MACI;MACD,IAAKgC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAG;QACzCrC,IAAI,CAAC,yCAAyC,CAAC;MACnD;MACAM,OAAO,CAAC2K,KAAK,CAACA,KAAK,CAAC;IACxB;IACA;IACA,OAAOgM,OAAO,CAACC,MAAM,CAACjM,KAAK,CAAC;EAChC;EACA,SAASod,OAAOA,CAAA,EAAG;IACf,IAAID,KAAK,IAAIpP,YAAY,CAACrZ,KAAK,KAAK0F,yBAAyB,EACzD,OAAO4R,OAAO,CAAClE,OAAO,CAAC,CAAC;IAC5B,OAAO,IAAIkE,OAAO,CAAC,CAAClE,OAAO,EAAEmE,MAAM,KAAK;MACpCgR,aAAa,CAACrX,GAAG,CAAC,CAACkC,OAAO,EAAEmE,MAAM,CAAC,CAAC;IACxC,CAAC,CAAC;EACN;EACA,SAAS4P,WAAWA,CAAC5kB,GAAG,EAAE;IACtB,IAAI,CAACkmB,KAAK,EAAE;MACR;MACAA,KAAK,GAAG,CAAClmB,GAAG;MACZ6lB,cAAc,CAAC,CAAC;MAChBG,aAAa,CACR7R,IAAI,CAAC,CAAC,CACNhN,OAAO,CAAC,CAAC,CAAC0J,OAAO,EAAEmE,MAAM,CAAC,KAAMhV,GAAG,GAAGgV,MAAM,CAAChV,GAAG,CAAC,GAAG6Q,OAAO,CAAC,CAAE,CAAC;MACpEmV,aAAa,CAAC9R,KAAK,CAAC,CAAC;IACzB;IACA,OAAOlU,GAAG;EACd;EACA;EACA,SAAS2kB,YAAYA,CAACliB,EAAE,EAAExE,IAAI,EAAEynB,MAAM,EAAEC,iBAAiB,EAAE;IACvD,MAAM;MAAExC;IAAe,CAAC,GAAGvX,OAAO;IAClC,IAAI,CAACpP,SAAS,IAAI,CAAC2mB,cAAc,EAC7B,OAAOpO,OAAO,CAAClE,OAAO,CAAC,CAAC;IAC5B,MAAM/K,cAAc,GAAI,CAAC4f,MAAM,IAAI1f,sBAAsB,CAACT,YAAY,CAAC9C,EAAE,CAACtB,QAAQ,EAAE,CAAC,CAAC,CAAC,IAClF,CAACwkB,iBAAiB,IAAI,CAACD,MAAM,KAC1BjgB,OAAO,CAACC,KAAK,IACbD,OAAO,CAACC,KAAK,CAACO,MAAO,IACzB,IAAI;IACR,OAAO3J,QAAQ,CAAC,CAAC,CACZiZ,IAAI,CAAC,MAAM4N,cAAc,CAAC1gB,EAAE,EAAExE,IAAI,EAAE6H,cAAc,CAAC,CAAC,CACpDyP,IAAI,CAACxS,QAAQ,IAAIA,QAAQ,IAAIgC,gBAAgB,CAAChC,QAAQ,CAAC,CAAC,CACxD4S,KAAK,CAAC3V,GAAG,IAAI6kB,YAAY,CAAC7kB,GAAG,EAAEyC,EAAE,EAAExE,IAAI,CAAC,CAAC;EAClD;EACA,MAAMoL,EAAE,GAAI7D,KAAK,IAAK+D,aAAa,CAACF,EAAE,CAAC7D,KAAK,CAAC;EAC7C,IAAI4gB,OAAO;EACX,MAAMlB,aAAa,GAAG,IAAIzW,GAAG,CAAC,CAAC;EAC/B,MAAMoI,MAAM,GAAG;IACXC,YAAY;IACZiP,SAAS,EAAE,IAAI;IACfzW,QAAQ;IACRiB,WAAW;IACXiB,WAAW,EAAE5C,OAAO,CAAC4C,WAAW;IAChCoS,QAAQ;IACRjT,SAAS;IACTE,OAAO;IACPjF,OAAO;IACP9I,IAAI;IACJtD,OAAO;IACP6J,EAAE;IACF5B,IAAI,EAAEA,CAAA,KAAM4B,EAAE,CAAC,CAAC,CAAC,CAAC;IAClB7B,OAAO,EAAEA,CAAA,KAAM6B,EAAE,CAAC,CAAC,CAAC;IACpBqX,UAAU,EAAEqC,YAAY,CAACpU,GAAG;IAC5B0X,aAAa,EAAErD,mBAAmB,CAACrU,GAAG;IACtCgS,SAAS,EAAEsC,WAAW,CAACtU,GAAG;IAC1BoR,OAAO,EAAEkG,cAAc,CAACtX,GAAG;IAC3BwX,OAAO;IACPG,OAAOA,CAAC9I,GAAG,EAAE;MACT,MAAM3G,MAAM,GAAG,IAAI;MACnB2G,GAAG,CAAC7gB,SAAS,CAAC,YAAY,EAAE+c,UAAU,CAAC;MACvC8D,GAAG,CAAC7gB,SAAS,CAAC,YAAY,EAAE8f,UAAU,CAAC;MACvCe,GAAG,CAAC+I,MAAM,CAACC,gBAAgB,CAACC,OAAO,GAAG5P,MAAM;MAC5C1Z,MAAM,CAACsM,cAAc,CAAC+T,GAAG,CAAC+I,MAAM,CAACC,gBAAgB,EAAE,QAAQ,EAAE;QACzD9c,UAAU,EAAE,IAAI;QAChBxD,GAAG,EAAEA,CAAA,KAAMtK,KAAK,CAACkb,YAAY;MACjC,CAAC,CAAC;MACF;MACA;MACA;MACA,IAAIta,SAAS;MACT;MACA;MACA,CAAC4pB,OAAO,IACRtP,YAAY,CAACrZ,KAAK,KAAK0F,yBAAyB,EAAE;QAClD;QACAijB,OAAO,GAAG,IAAI;QACdtjB,IAAI,CAACyG,aAAa,CAAC9I,QAAQ,CAAC,CAACkV,KAAK,CAAC3V,GAAG,IAAI;UACtC,IAAKC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EACtCrC,IAAI,CAAC,4CAA4C,EAAEkC,GAAG,CAAC;QAC/D,CAAC,CAAC;MACN;MACA,MAAM0mB,aAAa,GAAG,CAAC,CAAC;MACxB,KAAK,MAAMlpB,GAAG,IAAI2F,yBAAyB,EAAE;QACzChG,MAAM,CAACsM,cAAc,CAACid,aAAa,EAAElpB,GAAG,EAAE;UACtC0I,GAAG,EAAEA,CAAA,KAAM4Q,YAAY,CAACrZ,KAAK,CAACD,GAAG,CAAC;UAClCkM,UAAU,EAAE;QAChB,CAAC,CAAC;MACN;MACA8T,GAAG,CAACvhB,OAAO,CAAC2X,SAAS,EAAEiD,MAAM,CAAC;MAC9B2G,GAAG,CAACvhB,OAAO,CAAC4X,gBAAgB,EAAExX,eAAe,CAACqqB,aAAa,CAAC,CAAC;MAC7DlJ,GAAG,CAACvhB,OAAO,CAAC6X,qBAAqB,EAAEgD,YAAY,CAAC;MAChD,MAAM6P,UAAU,GAAGnJ,GAAG,CAACoJ,OAAO;MAC9B1B,aAAa,CAACvW,GAAG,CAAC6O,GAAG,CAAC;MACtBA,GAAG,CAACoJ,OAAO,GAAG,YAAY;QACtB1B,aAAa,CAAC/e,MAAM,CAACqX,GAAG,CAAC;QACzB;QACA,IAAI0H,aAAa,CAACxJ,IAAI,GAAG,CAAC,EAAE;UACxB;UACAwH,eAAe,GAAG/f,yBAAyB;UAC3CyiB,qBAAqB,IAAIA,qBAAqB,CAAC,CAAC;UAChDA,qBAAqB,GAAG,IAAI;UAC5B9O,YAAY,CAACrZ,KAAK,GAAG0F,yBAAyB;UAC9CijB,OAAO,GAAG,KAAK;UACfF,KAAK,GAAG,KAAK;QACjB;QACAS,UAAU,CAAC,CAAC;MAChB,CAAC;MACD;MACA,IAAI,CAAE1mB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAK6X,qBAAqB,KAAKxb,SAAS,EAAE;QACjF+gB,WAAW,CAACC,GAAG,EAAE3G,MAAM,EAAEjI,OAAO,CAAC;MACrC;IACJ;EACJ,CAAC;EACD;EACA,SAAS6W,aAAaA,CAAC1P,MAAM,EAAE;IAC3B,OAAOA,MAAM,CAAC3D,MAAM,CAAC,CAAC6D,OAAO,EAAE5B,KAAK,KAAK4B,OAAO,CAACV,IAAI,CAAC,MAAMV,cAAc,CAACR,KAAK,CAAC,CAAC,EAAEU,OAAO,CAAClE,OAAO,CAAC,CAAC,CAAC;EAC1G;EACA,OAAOgG,MAAM;AACjB;AACA,SAASyO,sBAAsBA,CAAC7iB,EAAE,EAAExE,IAAI,EAAE;EACtC,MAAMknB,cAAc,GAAG,EAAE;EACzB,MAAMC,eAAe,GAAG,EAAE;EAC1B,MAAMC,eAAe,GAAG,EAAE;EAC1B,MAAMwB,GAAG,GAAG7c,IAAI,CAACC,GAAG,CAAChM,IAAI,CAAC8D,OAAO,CAACd,MAAM,EAAEwB,EAAE,CAACV,OAAO,CAACd,MAAM,CAAC;EAC5D,KAAK,IAAIuB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqkB,GAAG,EAAErkB,CAAC,EAAE,EAAE;IAC1B,MAAMskB,UAAU,GAAG7oB,IAAI,CAAC8D,OAAO,CAACS,CAAC,CAAC;IAClC,IAAIskB,UAAU,EAAE;MACZ,IAAIrkB,EAAE,CAACV,OAAO,CAACkP,IAAI,CAAC5C,MAAM,IAAIpM,iBAAiB,CAACoM,MAAM,EAAEyY,UAAU,CAAC,CAAC,EAChE1B,eAAe,CAACtiB,IAAI,CAACgkB,UAAU,CAAC,CAAC,KAEjC3B,cAAc,CAACriB,IAAI,CAACgkB,UAAU,CAAC;IACvC;IACA,MAAMC,QAAQ,GAAGtkB,EAAE,CAACV,OAAO,CAACS,CAAC,CAAC;IAC9B,IAAIukB,QAAQ,EAAE;MACV;MACA,IAAI,CAAC9oB,IAAI,CAAC8D,OAAO,CAACkP,IAAI,CAAC5C,MAAM,IAAIpM,iBAAiB,CAACoM,MAAM,EAAE0Y,QAAQ,CAAC,CAAC,EAAE;QACnE1B,eAAe,CAACviB,IAAI,CAACikB,QAAQ,CAAC;MAClC;IACJ;EACJ;EACA,OAAO,CAAC5B,cAAc,EAAEC,eAAe,EAAEC,eAAe,CAAC;AAC7D;;AAEA;AACA;AACA;AACA;AACA,SAAS2B,SAASA,CAAA,EAAG;EACjB,OAAOzrB,MAAM,CAACqY,SAAS,CAAC;AAC5B;AACA;AACA;AACA;AACA;AACA,SAASqT,QAAQA,CAACC,KAAK,EAAE;EACrB,OAAO3rB,MAAM,CAACsY,gBAAgB,CAAC;AACnC;AAEA,SAASpJ,qBAAqB,EAAEiP,UAAU,EAAE+C,UAAU,EAAEtZ,yBAAyB,IAAIgkB,cAAc,EAAExd,mBAAmB,EAAEiZ,YAAY,EAAE7T,mBAAmB,EAAE5E,oBAAoB,EAAEjB,gBAAgB,EAAE8B,mBAAmB,EAAEyL,iBAAiB,EAAE/C,eAAe,EAAEa,kBAAkB,EAAEG,mBAAmB,EAAElU,UAAU,EAAEqT,gBAAgB,EAAED,SAAS,EAAEE,qBAAqB,EAAEzS,cAAc,EAAEuV,OAAO,EAAEqQ,QAAQ,EAAED,SAAS,EAAErT,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}