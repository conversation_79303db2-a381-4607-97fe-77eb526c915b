{"ast": null, "code": "'use strict';\n\nimport AxiosError from '../core/AxiosError.js';\nimport utils from '../utils.js';\n\n/**\n * A `CanceledError` is an object that is thrown when an operation is canceled.\n *\n * @param {string=} message The message.\n * @param {Object=} config The config.\n * @param {Object=} request The request.\n *\n * @returns {CanceledError} The created error.\n */\nfunction CanceledError(message, config, request) {\n  // eslint-disable-next-line no-eq-null,eqeqeq\n  AxiosError.call(this, message == null ? 'canceled' : message, AxiosError.ERR_CANCELED, config, request);\n  this.name = 'CanceledError';\n}\nutils.inherits(CanceledError, AxiosError, {\n  __CANCEL__: true\n});\nexport default CanceledError;", "map": {"version": 3, "names": ["AxiosError", "utils", "CanceledError", "message", "config", "request", "call", "ERR_CANCELED", "name", "inherits", "__CANCEL__"], "sources": ["C:/Users/<USER>/Desktop/cs/pacong_py/frontend_py/node_modules/axios/lib/cancel/CanceledError.js"], "sourcesContent": ["'use strict';\n\nimport AxiosError from '../core/AxiosError.js';\nimport utils from '../utils.js';\n\n/**\n * A `CanceledError` is an object that is thrown when an operation is canceled.\n *\n * @param {string=} message The message.\n * @param {Object=} config The config.\n * @param {Object=} request The request.\n *\n * @returns {CanceledError} The created error.\n */\nfunction CanceledError(message, config, request) {\n  // eslint-disable-next-line no-eq-null,eqeqeq\n  AxiosError.call(this, message == null ? 'canceled' : message, AxiosError.ERR_CANCELED, config, request);\n  this.name = 'CanceledError';\n}\n\nutils.inherits(CanceledError, AxiosError, {\n  __CANCEL__: true\n});\n\nexport default CanceledError;\n"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,KAAK,MAAM,aAAa;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,OAAO,EAAEC,MAAM,EAAEC,OAAO,EAAE;EAC/C;EACAL,UAAU,CAACM,IAAI,CAAC,IAAI,EAAEH,OAAO,IAAI,IAAI,GAAG,UAAU,GAAGA,OAAO,EAAEH,UAAU,CAACO,YAAY,EAAEH,MAAM,EAAEC,OAAO,CAAC;EACvG,IAAI,CAACG,IAAI,GAAG,eAAe;AAC7B;AAEAP,KAAK,CAACQ,QAAQ,CAACP,aAAa,EAAEF,UAAU,EAAE;EACxCU,UAAU,EAAE;AACd,CAAC,CAAC;AAEF,eAAeR,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}