{"ast": null, "code": "import { createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"dashboard\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[0] || (_cache[0] = _createElementVNode(\"h1\", null, \"监控数据大屏\", -1 /* CACHED */)), _cache[1] || (_cache[1] = _createElementVNode(\"p\", null, \"这里是监控数据大屏的预留区域，用于记录靶场漏洞告警信息，功能正在开发中，敬请期待！\", -1 /* CACHED */)), _createCommentVNode(\" 监控数据大屏框架内容 \")]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_createCommentVNode"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Dashboard.vue"], "sourcesContent": ["<template>\r\n  <div class=\"dashboard\">\r\n    <h1>监控数据大屏</h1>\r\n    <p>这里是监控数据大屏的预留区域，用于记录靶场漏洞告警信息，功能正在开发中，敬请期待！</p>\r\n    <!-- 监控数据大屏框架内容 -->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'Dashboard',\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.dashboard {\r\n  padding: 20px;\r\n}\r\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAW;;uBAAtBC,mBAAA,CAIM,OAJNC,UAIM,G,0BAHJC,mBAAA,CAAe,YAAX,QAAM,qB,0BACVA,mBAAA,CAAgD,WAA7C,2CAAyC,qBAC5CC,mBAAA,gBAAmB,C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}