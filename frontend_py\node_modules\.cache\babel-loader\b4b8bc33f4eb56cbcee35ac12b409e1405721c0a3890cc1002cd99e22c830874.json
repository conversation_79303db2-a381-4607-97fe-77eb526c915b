{"ast": null, "code": "export default {\n  name: 'Playground'\n};", "map": {"version": 3, "names": ["name"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Playground.vue"], "sourcesContent": ["<template>\r\n  <div class=\"playground\">\r\n    <h1>实操靶场</h1>\r\n    <p>这里是实操靶场的预留区域，功能正在开发中，敬请期待！</p>\r\n    <!-- 靶场框架内容 -->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'Playground',\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.playground {\r\n  padding: 20px;\r\n}\r\n</style>"], "mappings": "AASA,eAAe;EACbA,IAAI,EAAE;AACR", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}