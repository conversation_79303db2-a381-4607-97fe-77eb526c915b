import os
from flask import Blueprint, request, jsonify, current_app
from models import db, PastActivity
from middleware.auth import token_required, authorize_roles
from utils.file_upload import upload_file
import json
from datetime import datetime

past_activities_bp = Blueprint('past_activities', __name__)

@past_activities_bp.route('/', methods=['GET'])
def get_past_activities():
    try:
        activities = PastActivity.query.order_by(PastActivity.activity_date.desc()).all()
        return jsonify([{
            'id': a.id,
            'title': a.title,
            'description': a.description,
            'images': a.get_images(),
            'activity_date': a.activity_date.isoformat() if a.activity_date else None,
            'created_at': a.created_at.isoformat() if a.created_at else None,
            'updated_at': a.updated_at.isoformat() if a.updated_at else None
        } for a in activities]), 200
    except Exception as e:
        current_app.logger.error(f"获取往期活动失败: {e}")
        return jsonify({'msg': '服务器错误，获取往期活动失败'}), 500

@past_activities_bp.route('/', methods=['POST'])
@token_required
@authorize_roles(['admin'])
def create_past_activity():
    title = request.form.get('title')
    description = request.form.get('description')
    activity_date_str = request.form.get('activityDate')

    if not title or not description or not activity_date_str:
        return jsonify({'msg': '标题、描述和活动日期是必填项'}), 400

    try:
        activity_date = datetime.strptime(activity_date_str, '%Y-%m-%d').date()
    except ValueError:
        return jsonify({'msg': '活动日期格式无效，请使用 YYYY-MM-DD 格式'}), 400

    image_urls = []
    if 'images' in request.files:
        files = request.files.getlist('images')
        for file in files:
            if file.filename != '':
                file_url, error = upload_file(file, 'past_activities', ['jpeg', 'jpg', 'png', 'gif'], 5)
                if error:
                    return jsonify({'msg': f'图片上传失败: {error}'}), 400
                image_urls.append({'url': file_url, 'alt': file.filename})

    try:
        new_activity = PastActivity(title=title, description=description, activity_date=activity_date)
        new_activity.set_images(image_urls)
        db.session.add(new_activity)
        db.session.commit()
        return jsonify({'msg': '往期活动创建成功', 'activity': {
            'id': new_activity.id,
            'title': new_activity.title,
            'description': new_activity.description,
            'images': new_activity.get_images(),
            'activityDate': new_activity.activity_date.isoformat(),
            'createdAt': new_activity.created_at.isoformat()
        }}), 201
    except Exception as e:
        current_app.logger.error(f"创建往期活动失败: {e}")
        return jsonify({'msg': '服务器错误，创建往期活动失败'}), 500

@past_activities_bp.route('/<int:activity_id>', methods=['PUT'])
@token_required
@authorize_roles(['admin'])
def update_past_activity(activity_id):
    title = request.form.get('title')
    description = request.form.get('description')
    activity_date_str = request.form.get('activityDate')

    try:
        activity = PastActivity.query.get(activity_id)
        if not activity:
            return jsonify({'msg': '往期活动未找到'}), 404
        
        if activity_date_str:
            try:
                activity.activity_date = datetime.strptime(activity_date_str, '%Y-%m-%d').date()
            except ValueError:
                return jsonify({'msg': '活动日期格式无效，请使用 YYYY-MM-DD 格式'}), 400

        # 处理图片更新
        image_urls = []
        if 'images' in request.files:
            files = request.files.getlist('images')
            for file in files:
                if file.filename != '':
                    file_url, error = upload_file(file, 'past_activities', ['jpeg', 'jpg', 'png', 'gif'], 5)
                    if error:
                        return jsonify({'msg': f'图片上传失败: {error}'}), 400
                    image_urls.append({'url': file_url, 'alt': file.filename})
            
            # 如果有新图片上传，则追加
            if image_urls:
                existing_images = activity.get_images()
                activity.set_images(existing_images + image_urls)
            else: # 如果前端发送了空文件，表示删除所有图片
                for img in activity.get_images():
                    filename = os.path.basename(img['url'])
                    img_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'past_activities', filename)
                    if os.path.exists(img_path):
                        os.remove(img_path)
                activity.set_images([])


        activity.title = title if title is not None else activity.title
        activity.description = description if description is not None else activity.description
        db.session.commit()
        return jsonify({'msg': '往期活动更新成功', 'activity': {
            'id': activity.id,
            'title': activity.title,
            'description': activity.description,
            'images': activity.get_images(),
            'activityDate': activity.activity_date.isoformat(),
            'updatedAt': activity.updated_at.isoformat()
        }}), 200
    except Exception as e:
        current_app.logger.error(f"更新往期活动失败: {e}")
        return jsonify({'msg': '服务器错误，更新往期活动失败'}), 500

@past_activities_bp.route('/<int:activity_id>', methods=['DELETE'])
@token_required
@authorize_roles(['admin'])
def delete_past_activity(activity_id):
    try:
        activity = PastActivity.query.get(activity_id)
        if not activity:
            return jsonify({'msg': '往期活动未找到'}), 404
        
        # 删除关联图片
        for img in activity.get_images():
            filename = os.path.basename(img['url'])
            image_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'past_activities', filename)
            if os.path.exists(image_path):
                os.remove(image_path)
            else:
                current_app.logger.warning(f"尝试删除不存在的文件: {image_path}")

        db.session.delete(activity)
        db.session.commit()
        return jsonify({'msg': '往期活动已删除'}), 200
    except Exception as e:
        current_app.logger.error(f"删除往期活动失败: {e}")
        return jsonify({'msg': '服务器错误，删除往期活动失败'}), 500

@past_activities_bp.route('/image/<int:activity_id>/<int:image_index>', methods=['DELETE'])
@token_required
@authorize_roles(['admin'])
def delete_past_activity_image(activity_id, image_index):
    try:
        activity = PastActivity.query.get(activity_id)
        if not activity:
            return jsonify({'msg': '往期活动未找到'}), 404

        images = activity.get_images()
        if image_index < 0 or image_index >= len(images):
            return jsonify({'msg': '图片索引无效'}), 404
        
        image_url = images[image_index]['url']
        
        # 从文件系统删除图片
        filename = os.path.basename(image_url)
        image_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'past_activities', filename)
        
        if os.path.exists(image_path):
            os.remove(image_path)
        else:
            current_app.logger.warning(f"尝试删除不存在的文件: {image_path}")

        # 从数据库中移除图片信息
        images.pop(image_index)
        activity.set_images(images)
        db.session.commit()

        return jsonify({'msg': '图片已删除'}), 200

    except Exception as e:
        current_app.logger.error(f"删除往期活动图片失败: {e}")
        return jsonify({'msg': '服务器错误，删除图片失败'}), 500
