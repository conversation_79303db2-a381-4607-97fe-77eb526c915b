{"ast": null, "code": "import { createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, normalizeClass as _normalizeClass } from \"vue\";\nconst _hoisted_1 = {\n  class: \"admin\"\n};\nconst _hoisted_2 = {\n  class: \"admin-header\"\n};\nconst _hoisted_3 = {\n  class: \"admin-actions\"\n};\nconst _hoisted_4 = {\n  class: \"admin-tabs\"\n};\nconst _hoisted_5 = [\"onClick\"];\nconst _hoisted_6 = {\n  class: \"admin-content\"\n};\nconst _hoisted_7 = {\n  key: 0,\n  class: \"tab-content\"\n};\nconst _hoisted_8 = {\n  class: \"content-header\"\n};\nconst _hoisted_9 = {\n  class: \"table-container\"\n};\nconst _hoisted_10 = {\n  class: \"admin-table\"\n};\nconst _hoisted_11 = [\"onClick\"];\nconst _hoisted_12 = [\"onClick\"];\nconst _hoisted_13 = {\n  key: 1,\n  class: \"tab-content\"\n};\nconst _hoisted_14 = {\n  class: \"content-header\"\n};\nconst _hoisted_15 = {\n  class: \"table-container\"\n};\nconst _hoisted_16 = {\n  class: \"admin-table\"\n};\nconst _hoisted_17 = [\"onClick\"];\nconst _hoisted_18 = [\"onClick\"];\nconst _hoisted_19 = {\n  key: 2,\n  class: \"tab-content\"\n};\nconst _hoisted_20 = {\n  class: \"content-header\"\n};\nconst _hoisted_21 = {\n  class: \"table-container\"\n};\nconst _hoisted_22 = {\n  class: \"admin-table\"\n};\nconst _hoisted_23 = [\"onClick\"];\nconst _hoisted_24 = [\"onClick\"];\nconst _hoisted_25 = {\n  key: 3,\n  class: \"tab-content\"\n};\nconst _hoisted_26 = {\n  class: \"content-header\"\n};\nconst _hoisted_27 = {\n  class: \"table-container\"\n};\nconst _hoisted_28 = {\n  class: \"admin-table\"\n};\nconst _hoisted_29 = [\"onClick\"];\nconst _hoisted_30 = [\"onClick\"];\nconst _hoisted_31 = {\n  key: 4,\n  class: \"tab-content\"\n};\nconst _hoisted_32 = {\n  class: \"content-header\"\n};\nconst _hoisted_33 = {\n  class: \"table-container\"\n};\nconst _hoisted_34 = {\n  class: \"admin-table\"\n};\nconst _hoisted_35 = [\"onClick\"];\nconst _hoisted_36 = [\"onClick\"];\nexport function render(_ctx, _cache) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_cache[6] || (_cache[6] = _createElementVNode(\"h1\", null, \"管理员后台\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"span\", null, \"欢迎，\" + _toDisplayString(_ctx.currentUser?.name || _ctx.currentUser?.username), 1 /* TEXT */), _createElementVNode(\"button\", {\n    onClick: _cache[0] || (_cache[0] = (...args) => _ctx.logout && _ctx.logout(...args)),\n    class: \"btn btn-secondary\"\n  }, \"退出登录\")])]), _createCommentVNode(\" 导航标签 \"), _createElementVNode(\"div\", _hoisted_4, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.tabs, tab => {\n    return _openBlock(), _createElementBlock(\"button\", {\n      key: tab.key,\n      onClick: $event => _ctx.activeTab = tab.key,\n      class: _normalizeClass(['tab-btn', {\n        active: _ctx.activeTab === tab.key\n      }])\n    }, _toDisplayString(tab.label), 11 /* TEXT, CLASS, PROPS */, _hoisted_5);\n  }), 128 /* KEYED_FRAGMENT */))]), _createCommentVNode(\" 内容区域 \"), _createElementVNode(\"div\", _hoisted_6, [_createCommentVNode(\" 用户管理 \"), _ctx.activeTab === 'users' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, [_cache[7] || (_cache[7] = _createElementVNode(\"h2\", null, \"用户管理\", -1 /* CACHED */)), _createElementVNode(\"button\", {\n    onClick: _cache[1] || (_cache[1] = $event => _ctx.showCreateUserModal = true),\n    class: \"btn btn-primary\"\n  }, \"添加用户\")]), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"table\", _hoisted_10, [_cache[8] || (_cache[8] = _createElementVNode(\"thead\", null, [_createElementVNode(\"tr\", null, [_createElementVNode(\"th\", null, \"ID\"), _createElementVNode(\"th\", null, \"用户名\"), _createElementVNode(\"th\", null, \"姓名\"), _createElementVNode(\"th\", null, \"学号\"), _createElementVNode(\"th\", null, \"角色\"), _createElementVNode(\"th\", null, \"创建时间\"), _createElementVNode(\"th\", null, \"操作\")])], -1 /* CACHED */)), _createElementVNode(\"tbody\", null, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.users, user => {\n    return _openBlock(), _createElementBlock(\"tr\", {\n      key: user.id\n    }, [_createElementVNode(\"td\", null, _toDisplayString(user.id), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(user.username), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(user.name || '-'), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(user.student_id || '-'), 1 /* TEXT */), _createElementVNode(\"td\", null, [_createElementVNode(\"span\", {\n      class: _normalizeClass(['role-badge', user.role])\n    }, _toDisplayString(user.role === 'admin' ? '管理员' : '普通用户'), 3 /* TEXT, CLASS */)]), _createElementVNode(\"td\", null, _toDisplayString(_ctx.formatDate(user.created_at)), 1 /* TEXT */), _createElementVNode(\"td\", null, [_createElementVNode(\"button\", {\n      onClick: $event => _ctx.editUser(user),\n      class: \"btn btn-sm btn-primary\"\n    }, \"编辑\", 8 /* PROPS */, _hoisted_11), user.id !== _ctx.currentUser?.id ? (_openBlock(), _createElementBlock(\"button\", {\n      key: 0,\n      onClick: $event => _ctx.deleteUser(user.id),\n      class: \"btn btn-sm btn-danger\"\n    }, \"删除\", 8 /* PROPS */, _hoisted_12)) : _createCommentVNode(\"v-if\", true)])]);\n  }), 128 /* KEYED_FRAGMENT */))])])])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 公告管理 \"), _ctx.activeTab === 'announcements' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, [_cache[9] || (_cache[9] = _createElementVNode(\"h2\", null, \"公告管理\", -1 /* CACHED */)), _createElementVNode(\"button\", {\n    onClick: _cache[2] || (_cache[2] = $event => _ctx.showCreateAnnouncementModal = true),\n    class: \"btn btn-primary\"\n  }, \"发布公告\")]), _createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"table\", _hoisted_16, [_cache[10] || (_cache[10] = _createElementVNode(\"thead\", null, [_createElementVNode(\"tr\", null, [_createElementVNode(\"th\", null, \"ID\"), _createElementVNode(\"th\", null, \"标题\"), _createElementVNode(\"th\", null, \"内容预览\"), _createElementVNode(\"th\", null, \"创建时间\"), _createElementVNode(\"th\", null, \"操作\")])], -1 /* CACHED */)), _createElementVNode(\"tbody\", null, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.announcements, announcement => {\n    return _openBlock(), _createElementBlock(\"tr\", {\n      key: announcement.id\n    }, [_createElementVNode(\"td\", null, _toDisplayString(announcement.id), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(announcement.title), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(_ctx.truncateText(announcement.content, 50)), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(_ctx.formatDate(announcement.created_at)), 1 /* TEXT */), _createElementVNode(\"td\", null, [_createElementVNode(\"button\", {\n      onClick: $event => _ctx.editAnnouncement(announcement),\n      class: \"btn btn-sm btn-primary\"\n    }, \"编辑\", 8 /* PROPS */, _hoisted_17), _createElementVNode(\"button\", {\n      onClick: $event => _ctx.deleteAnnouncement(announcement.id),\n      class: \"btn btn-sm btn-danger\"\n    }, \"删除\", 8 /* PROPS */, _hoisted_18)])]);\n  }), 128 /* KEYED_FRAGMENT */))])])])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 社团文化管理 \"), _ctx.activeTab === 'culture' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_19, [_createElementVNode(\"div\", _hoisted_20, [_cache[11] || (_cache[11] = _createElementVNode(\"h2\", null, \"社团文化管理\", -1 /* CACHED */)), _createElementVNode(\"button\", {\n    onClick: _cache[3] || (_cache[3] = $event => _ctx.showCreateCultureModal = true),\n    class: \"btn btn-primary\"\n  }, \"添加内容\")]), _createElementVNode(\"div\", _hoisted_21, [_createElementVNode(\"table\", _hoisted_22, [_cache[12] || (_cache[12] = _createElementVNode(\"thead\", null, [_createElementVNode(\"tr\", null, [_createElementVNode(\"th\", null, \"ID\"), _createElementVNode(\"th\", null, \"标题\"), _createElementVNode(\"th\", null, \"内容预览\"), _createElementVNode(\"th\", null, \"创建时间\"), _createElementVNode(\"th\", null, \"操作\")])], -1 /* CACHED */)), _createElementVNode(\"tbody\", null, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.clubCultures, culture => {\n    return _openBlock(), _createElementBlock(\"tr\", {\n      key: culture.id\n    }, [_createElementVNode(\"td\", null, _toDisplayString(culture.id), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(culture.title), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(_ctx.truncateText(culture.content, 50)), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(_ctx.formatDate(culture.created_at)), 1 /* TEXT */), _createElementVNode(\"td\", null, [_createElementVNode(\"button\", {\n      onClick: $event => _ctx.editCulture(culture),\n      class: \"btn btn-sm btn-primary\"\n    }, \"编辑\", 8 /* PROPS */, _hoisted_23), _createElementVNode(\"button\", {\n      onClick: $event => _ctx.deleteCulture(culture.id),\n      class: \"btn btn-sm btn-danger\"\n    }, \"删除\", 8 /* PROPS */, _hoisted_24)])]);\n  }), 128 /* KEYED_FRAGMENT */))])])])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 学习资源管理 \"), _ctx.activeTab === 'resources' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_25, [_createElementVNode(\"div\", _hoisted_26, [_cache[13] || (_cache[13] = _createElementVNode(\"h2\", null, \"学习资源管理\", -1 /* CACHED */)), _createElementVNode(\"button\", {\n    onClick: _cache[4] || (_cache[4] = $event => _ctx.showCreateResourceModal = true),\n    class: \"btn btn-primary\"\n  }, \"添加资源\")]), _createElementVNode(\"div\", _hoisted_27, [_createElementVNode(\"table\", _hoisted_28, [_cache[14] || (_cache[14] = _createElementVNode(\"thead\", null, [_createElementVNode(\"tr\", null, [_createElementVNode(\"th\", null, \"ID\"), _createElementVNode(\"th\", null, \"标题\"), _createElementVNode(\"th\", null, \"类型\"), _createElementVNode(\"th\", null, \"创建时间\"), _createElementVNode(\"th\", null, \"操作\")])], -1 /* CACHED */)), _createElementVNode(\"tbody\", null, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.learningResources, resource => {\n    return _openBlock(), _createElementBlock(\"tr\", {\n      key: resource.id\n    }, [_createElementVNode(\"td\", null, _toDisplayString(resource.id), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(resource.title), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(resource.file_path ? '文件' : '文本'), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(_ctx.formatDate(resource.created_at)), 1 /* TEXT */), _createElementVNode(\"td\", null, [_createElementVNode(\"button\", {\n      onClick: $event => _ctx.editResource(resource),\n      class: \"btn btn-sm btn-primary\"\n    }, \"编辑\", 8 /* PROPS */, _hoisted_29), _createElementVNode(\"button\", {\n      onClick: $event => _ctx.deleteResource(resource.id),\n      class: \"btn btn-sm btn-danger\"\n    }, \"删除\", 8 /* PROPS */, _hoisted_30)])]);\n  }), 128 /* KEYED_FRAGMENT */))])])])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 过往活动管理 \"), _ctx.activeTab === 'activities' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_31, [_createElementVNode(\"div\", _hoisted_32, [_cache[15] || (_cache[15] = _createElementVNode(\"h2\", null, \"过往活动管理\", -1 /* CACHED */)), _createElementVNode(\"button\", {\n    onClick: _cache[5] || (_cache[5] = $event => _ctx.showCreateActivityModal = true),\n    class: \"btn btn-primary\"\n  }, \"添加活动\")]), _createElementVNode(\"div\", _hoisted_33, [_createElementVNode(\"table\", _hoisted_34, [_cache[16] || (_cache[16] = _createElementVNode(\"thead\", null, [_createElementVNode(\"tr\", null, [_createElementVNode(\"th\", null, \"ID\"), _createElementVNode(\"th\", null, \"标题\"), _createElementVNode(\"th\", null, \"活动日期\"), _createElementVNode(\"th\", null, \"创建时间\"), _createElementVNode(\"th\", null, \"操作\")])], -1 /* CACHED */)), _createElementVNode(\"tbody\", null, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.pastActivities, activity => {\n    return _openBlock(), _createElementBlock(\"tr\", {\n      key: activity.id\n    }, [_createElementVNode(\"td\", null, _toDisplayString(activity.id), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(activity.title), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(_ctx.formatDate(activity.activity_date)), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(_ctx.formatDate(activity.created_at)), 1 /* TEXT */), _createElementVNode(\"td\", null, [_createElementVNode(\"button\", {\n      onClick: $event => _ctx.editActivity(activity),\n      class: \"btn btn-sm btn-primary\"\n    }, \"编辑\", 8 /* PROPS */, _hoisted_35), _createElementVNode(\"button\", {\n      onClick: $event => _ctx.deleteActivity(activity.id),\n      class: \"btn btn-sm btn-danger\"\n    }, \"删除\", 8 /* PROPS */, _hoisted_36)])]);\n  }), 128 /* KEYED_FRAGMENT */))])])])])) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" 模态框组件将在这里添加 \")]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_toDisplayString", "_ctx", "currentUser", "name", "username", "onClick", "_cache", "args", "logout", "_createCommentVNode", "_hoisted_4", "_Fragment", "_renderList", "tabs", "tab", "key", "$event", "activeTab", "_normalizeClass", "active", "label", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "showCreateUserModal", "_hoisted_9", "_hoisted_10", "users", "user", "id", "student_id", "role", "formatDate", "created_at", "editUser", "_hoisted_11", "deleteUser", "_hoisted_12", "_hoisted_13", "_hoisted_14", "showCreateAnnouncementModal", "_hoisted_15", "_hoisted_16", "announcements", "announcement", "title", "truncateText", "content", "editAnnouncement", "_hoisted_17", "deleteAnnouncement", "_hoisted_18", "_hoisted_19", "_hoisted_20", "showCreateCultureModal", "_hoisted_21", "_hoisted_22", "clubCultures", "culture", "editCulture", "_hoisted_23", "deleteCulture", "_hoisted_24", "_hoisted_25", "_hoisted_26", "showCreateResourceModal", "_hoisted_27", "_hoisted_28", "learningResources", "resource", "file_path", "editResource", "_hoisted_29", "deleteResource", "_hoisted_30", "_hoisted_31", "_hoisted_32", "showCreateActivityModal", "_hoisted_33", "_hoisted_34", "pastActivities", "activity", "activity_date", "editActivity", "_hoisted_35", "deleteActivity", "_hoisted_36"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Admin.vue"], "sourcesContent": ["<template>\r\n  <div class=\"admin\">\r\n    <div class=\"admin-header\">\r\n      <h1>管理员后台</h1>\r\n      <div class=\"admin-actions\">\r\n        <span>欢迎，{{ currentUser?.name || currentUser?.username }}</span>\r\n        <button @click=\"logout\" class=\"btn btn-secondary\">退出登录</button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 导航标签 -->\r\n    <div class=\"admin-tabs\">\r\n      <button\r\n        v-for=\"tab in tabs\"\r\n        :key=\"tab.key\"\r\n        @click=\"activeTab = tab.key\"\r\n        :class=\"['tab-btn', { active: activeTab === tab.key }]\"\r\n      >\r\n        {{ tab.label }}\r\n      </button>\r\n    </div>\r\n\r\n    <!-- 内容区域 -->\r\n    <div class=\"admin-content\">\r\n      <!-- 用户管理 -->\r\n      <div v-if=\"activeTab === 'users'\" class=\"tab-content\">\r\n        <div class=\"content-header\">\r\n          <h2>用户管理</h2>\r\n          <button @click=\"showCreateUserModal = true\" class=\"btn btn-primary\">添加用户</button>\r\n        </div>\r\n\r\n        <div class=\"table-container\">\r\n          <table class=\"admin-table\">\r\n            <thead>\r\n              <tr>\r\n                <th>ID</th>\r\n                <th>用户名</th>\r\n                <th>姓名</th>\r\n                <th>学号</th>\r\n                <th>角色</th>\r\n                <th>创建时间</th>\r\n                <th>操作</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              <tr v-for=\"user in users\" :key=\"user.id\">\r\n                <td>{{ user.id }}</td>\r\n                <td>{{ user.username }}</td>\r\n                <td>{{ user.name || '-' }}</td>\r\n                <td>{{ user.student_id || '-' }}</td>\r\n                <td>\r\n                  <span :class=\"['role-badge', user.role]\">\r\n                    {{ user.role === 'admin' ? '管理员' : '普通用户' }}\r\n                  </span>\r\n                </td>\r\n                <td>{{ formatDate(user.created_at) }}</td>\r\n                <td>\r\n                  <button @click=\"editUser(user)\" class=\"btn btn-sm btn-primary\">编辑</button>\r\n                  <button @click=\"deleteUser(user.id)\" class=\"btn btn-sm btn-danger\" v-if=\"user.id !== currentUser?.id\">删除</button>\r\n                </td>\r\n              </tr>\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 公告管理 -->\r\n      <div v-if=\"activeTab === 'announcements'\" class=\"tab-content\">\r\n        <div class=\"content-header\">\r\n          <h2>公告管理</h2>\r\n          <button @click=\"showCreateAnnouncementModal = true\" class=\"btn btn-primary\">发布公告</button>\r\n        </div>\r\n\r\n        <div class=\"table-container\">\r\n          <table class=\"admin-table\">\r\n            <thead>\r\n              <tr>\r\n                <th>ID</th>\r\n                <th>标题</th>\r\n                <th>内容预览</th>\r\n                <th>创建时间</th>\r\n                <th>操作</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              <tr v-for=\"announcement in announcements\" :key=\"announcement.id\">\r\n                <td>{{ announcement.id }}</td>\r\n                <td>{{ announcement.title }}</td>\r\n                <td>{{ truncateText(announcement.content, 50) }}</td>\r\n                <td>{{ formatDate(announcement.created_at) }}</td>\r\n                <td>\r\n                  <button @click=\"editAnnouncement(announcement)\" class=\"btn btn-sm btn-primary\">编辑</button>\r\n                  <button @click=\"deleteAnnouncement(announcement.id)\" class=\"btn btn-sm btn-danger\">删除</button>\r\n                </td>\r\n              </tr>\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 社团文化管理 -->\r\n      <div v-if=\"activeTab === 'culture'\" class=\"tab-content\">\r\n        <div class=\"content-header\">\r\n          <h2>社团文化管理</h2>\r\n          <button @click=\"showCreateCultureModal = true\" class=\"btn btn-primary\">添加内容</button>\r\n        </div>\r\n\r\n        <div class=\"table-container\">\r\n          <table class=\"admin-table\">\r\n            <thead>\r\n              <tr>\r\n                <th>ID</th>\r\n                <th>标题</th>\r\n                <th>内容预览</th>\r\n                <th>创建时间</th>\r\n                <th>操作</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              <tr v-for=\"culture in clubCultures\" :key=\"culture.id\">\r\n                <td>{{ culture.id }}</td>\r\n                <td>{{ culture.title }}</td>\r\n                <td>{{ truncateText(culture.content, 50) }}</td>\r\n                <td>{{ formatDate(culture.created_at) }}</td>\r\n                <td>\r\n                  <button @click=\"editCulture(culture)\" class=\"btn btn-sm btn-primary\">编辑</button>\r\n                  <button @click=\"deleteCulture(culture.id)\" class=\"btn btn-sm btn-danger\">删除</button>\r\n                </td>\r\n              </tr>\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 学习资源管理 -->\r\n      <div v-if=\"activeTab === 'resources'\" class=\"tab-content\">\r\n        <div class=\"content-header\">\r\n          <h2>学习资源管理</h2>\r\n          <button @click=\"showCreateResourceModal = true\" class=\"btn btn-primary\">添加资源</button>\r\n        </div>\r\n\r\n        <div class=\"table-container\">\r\n          <table class=\"admin-table\">\r\n            <thead>\r\n              <tr>\r\n                <th>ID</th>\r\n                <th>标题</th>\r\n                <th>类型</th>\r\n                <th>创建时间</th>\r\n                <th>操作</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              <tr v-for=\"resource in learningResources\" :key=\"resource.id\">\r\n                <td>{{ resource.id }}</td>\r\n                <td>{{ resource.title }}</td>\r\n                <td>{{ resource.file_path ? '文件' : '文本' }}</td>\r\n                <td>{{ formatDate(resource.created_at) }}</td>\r\n                <td>\r\n                  <button @click=\"editResource(resource)\" class=\"btn btn-sm btn-primary\">编辑</button>\r\n                  <button @click=\"deleteResource(resource.id)\" class=\"btn btn-sm btn-danger\">删除</button>\r\n                </td>\r\n              </tr>\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 过往活动管理 -->\r\n      <div v-if=\"activeTab === 'activities'\" class=\"tab-content\">\r\n        <div class=\"content-header\">\r\n          <h2>过往活动管理</h2>\r\n          <button @click=\"showCreateActivityModal = true\" class=\"btn btn-primary\">添加活动</button>\r\n        </div>\r\n\r\n        <div class=\"table-container\">\r\n          <table class=\"admin-table\">\r\n            <thead>\r\n              <tr>\r\n                <th>ID</th>\r\n                <th>标题</th>\r\n                <th>活动日期</th>\r\n                <th>创建时间</th>\r\n                <th>操作</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              <tr v-for=\"activity in pastActivities\" :key=\"activity.id\">\r\n                <td>{{ activity.id }}</td>\r\n                <td>{{ activity.title }}</td>\r\n                <td>{{ formatDate(activity.activity_date) }}</td>\r\n                <td>{{ formatDate(activity.created_at) }}</td>\r\n                <td>\r\n                  <button @click=\"editActivity(activity)\" class=\"btn btn-sm btn-primary\">编辑</button>\r\n                  <button @click=\"deleteActivity(activity.id)\" class=\"btn btn-sm btn-danger\">删除</button>\r\n                </td>\r\n              </tr>\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 模态框组件将在这里添加 -->\r\n  </div>\r\n</template>"], "mappings": ";;EACOA,KAAK,EAAC;AAAO;;EACXA,KAAK,EAAC;AAAc;;EAElBA,KAAK,EAAC;AAAe;;EAOvBA,KAAK,EAAC;AAAY;;;EAYlBA,KAAK,EAAC;AAAe;;;EAEUA,KAAK,EAAC;;;EACjCA,KAAK,EAAC;AAAgB;;EAKtBA,KAAK,EAAC;AAAiB;;EACnBA,KAAK,EAAC;AAAa;;;;;EAmCYA,KAAK,EAAC;;;EACzCA,KAAK,EAAC;AAAgB;;EAKtBA,KAAK,EAAC;AAAiB;;EACnBA,KAAK,EAAC;AAAa;;;;;EA2BMA,KAAK,EAAC;;;EACnCA,KAAK,EAAC;AAAgB;;EAKtBA,KAAK,EAAC;AAAiB;;EACnBA,KAAK,EAAC;AAAa;;;;;EA2BQA,KAAK,EAAC;;;EACrCA,KAAK,EAAC;AAAgB;;EAKtBA,KAAK,EAAC;AAAiB;;EACnBA,KAAK,EAAC;AAAa;;;;;EA2BSA,KAAK,EAAC;;;EACtCA,KAAK,EAAC;AAAgB;;EAKtBA,KAAK,EAAC;AAAiB;;EACnBA,KAAK,EAAC;AAAa;;;;uBA/KlCC,mBAAA,CA2MM,OA3MNC,UA2MM,GA1MJC,mBAAA,CAMM,OANNC,UAMM,G,0BALJD,mBAAA,CAAc,YAAV,OAAK,qBACTA,mBAAA,CAGM,OAHNE,UAGM,GAFJF,mBAAA,CAAgE,cAA1D,KAAG,GAAAG,gBAAA,CAAGC,IAAA,CAAAC,WAAW,EAAEC,IAAI,IAAIF,IAAA,CAAAC,WAAW,EAAEE,QAAQ,kBACtDP,mBAAA,CAA+D;IAAtDQ,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEN,IAAA,CAAAO,MAAA,IAAAP,IAAA,CAAAO,MAAA,IAAAD,IAAA,CAAM;IAAEb,KAAK,EAAC;KAAoB,MAAI,E,KAI1De,mBAAA,UAAa,EACbZ,mBAAA,CASM,OATNa,UASM,I,kBARJf,mBAAA,CAOSgB,SAAA,QAAAC,WAAA,CANOX,IAAA,CAAAY,IAAI,EAAXC,GAAG;yBADZnB,mBAAA,CAOS;MALNoB,GAAG,EAAED,GAAG,CAACC,GAAG;MACZV,OAAK,EAAAW,MAAA,IAAEf,IAAA,CAAAgB,SAAS,GAAGH,GAAG,CAACC,GAAG;MAC1BrB,KAAK,EAAAwB,eAAA;QAAAC,MAAA,EAAwBlB,IAAA,CAAAgB,SAAS,KAAKH,GAAG,CAACC;MAAG;wBAEhDD,GAAG,CAACM,KAAK,gCAAAC,UAAA;oCAIhBZ,mBAAA,UAAa,EACbZ,mBAAA,CAkLM,OAlLNyB,UAkLM,GAjLJb,mBAAA,UAAa,EACFR,IAAA,CAAAgB,SAAS,gB,cAApBtB,mBAAA,CAuCM,OAvCN4B,UAuCM,GAtCJ1B,mBAAA,CAGM,OAHN2B,UAGM,G,0BAFJ3B,mBAAA,CAAa,YAAT,MAAI,qBACRA,mBAAA,CAAiF;IAAxEQ,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAU,MAAA,IAAEf,IAAA,CAAAwB,mBAAmB;IAAS/B,KAAK,EAAC;KAAkB,MAAI,E,GAG1EG,mBAAA,CAgCM,OAhCN6B,UAgCM,GA/BJ7B,mBAAA,CA8BQ,SA9BR8B,WA8BQ,G,0BA7BN9B,mBAAA,CAUQ,gBATNA,mBAAA,CAQK,aAPHA,mBAAA,CAAW,YAAP,IAAE,GACNA,mBAAA,CAAY,YAAR,KAAG,GACPA,mBAAA,CAAW,YAAP,IAAE,GACNA,mBAAA,CAAW,YAAP,IAAE,GACNA,mBAAA,CAAW,YAAP,IAAE,GACNA,mBAAA,CAAa,YAAT,MAAI,GACRA,mBAAA,CAAW,YAAP,IAAE,E,uBAGVA,mBAAA,CAiBQ,iB,kBAhBNF,mBAAA,CAeKgB,SAAA,QAAAC,WAAA,CAfcX,IAAA,CAAA2B,KAAK,EAAbC,IAAI;yBAAflC,mBAAA,CAeK;MAfsBoB,GAAG,EAAEc,IAAI,CAACC;QACnCjC,mBAAA,CAAsB,YAAAG,gBAAA,CAAf6B,IAAI,CAACC,EAAE,kBACdjC,mBAAA,CAA4B,YAAAG,gBAAA,CAArB6B,IAAI,CAACzB,QAAQ,kBACpBP,mBAAA,CAA+B,YAAAG,gBAAA,CAAxB6B,IAAI,CAAC1B,IAAI,yBAChBN,mBAAA,CAAqC,YAAAG,gBAAA,CAA9B6B,IAAI,CAACE,UAAU,yBACtBlC,mBAAA,CAIK,aAHHA,mBAAA,CAEO;MAFAH,KAAK,EAAAwB,eAAA,gBAAiBW,IAAI,CAACG,IAAI;wBACjCH,IAAI,CAACG,IAAI,qD,GAGhBnC,mBAAA,CAA0C,YAAAG,gBAAA,CAAnCC,IAAA,CAAAgC,UAAU,CAACJ,IAAI,CAACK,UAAU,mBACjCrC,mBAAA,CAGK,aAFHA,mBAAA,CAA0E;MAAjEQ,OAAK,EAAAW,MAAA,IAAEf,IAAA,CAAAkC,QAAQ,CAACN,IAAI;MAAGnC,KAAK,EAAC;OAAyB,IAAE,iBAAA0C,WAAA,GACQP,IAAI,CAACC,EAAE,KAAK7B,IAAA,CAAAC,WAAW,EAAE4B,EAAE,I,cAApGnC,mBAAA,CAAiH;;MAAxGU,OAAK,EAAAW,MAAA,IAAEf,IAAA,CAAAoC,UAAU,CAACR,IAAI,CAACC,EAAE;MAAGpC,KAAK,EAAC;OAA2D,IAAE,iBAAA4C,WAAA,K;+EAQpH7B,mBAAA,UAAa,EACFR,IAAA,CAAAgB,SAAS,wB,cAApBtB,mBAAA,CA+BM,OA/BN4C,WA+BM,GA9BJ1C,mBAAA,CAGM,OAHN2C,WAGM,G,0BAFJ3C,mBAAA,CAAa,YAAT,MAAI,qBACRA,mBAAA,CAAyF;IAAhFQ,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAU,MAAA,IAAEf,IAAA,CAAAwC,2BAA2B;IAAS/C,KAAK,EAAC;KAAkB,MAAI,E,GAGlFG,mBAAA,CAwBM,OAxBN6C,WAwBM,GAvBJ7C,mBAAA,CAsBQ,SAtBR8C,WAsBQ,G,4BArBN9C,mBAAA,CAQQ,gBAPNA,mBAAA,CAMK,aALHA,mBAAA,CAAW,YAAP,IAAE,GACNA,mBAAA,CAAW,YAAP,IAAE,GACNA,mBAAA,CAAa,YAAT,MAAI,GACRA,mBAAA,CAAa,YAAT,MAAI,GACRA,mBAAA,CAAW,YAAP,IAAE,E,uBAGVA,mBAAA,CAWQ,iB,kBAVNF,mBAAA,CASKgB,SAAA,QAAAC,WAAA,CATsBX,IAAA,CAAA2C,aAAa,EAA7BC,YAAY;yBAAvBlD,mBAAA,CASK;MATsCoB,GAAG,EAAE8B,YAAY,CAACf;QAC3DjC,mBAAA,CAA8B,YAAAG,gBAAA,CAAvB6C,YAAY,CAACf,EAAE,kBACtBjC,mBAAA,CAAiC,YAAAG,gBAAA,CAA1B6C,YAAY,CAACC,KAAK,kBACzBjD,mBAAA,CAAqD,YAAAG,gBAAA,CAA9CC,IAAA,CAAA8C,YAAY,CAACF,YAAY,CAACG,OAAO,uBACxCnD,mBAAA,CAAkD,YAAAG,gBAAA,CAA3CC,IAAA,CAAAgC,UAAU,CAACY,YAAY,CAACX,UAAU,mBACzCrC,mBAAA,CAGK,aAFHA,mBAAA,CAA0F;MAAjFQ,OAAK,EAAAW,MAAA,IAAEf,IAAA,CAAAgD,gBAAgB,CAACJ,YAAY;MAAGnD,KAAK,EAAC;OAAyB,IAAE,iBAAAwD,WAAA,GACjFrD,mBAAA,CAA8F;MAArFQ,OAAK,EAAAW,MAAA,IAAEf,IAAA,CAAAkD,kBAAkB,CAACN,YAAY,CAACf,EAAE;MAAGpC,KAAK,EAAC;OAAwB,IAAE,iBAAA0D,WAAA,E;+EAQjG3C,mBAAA,YAAe,EACJR,IAAA,CAAAgB,SAAS,kB,cAApBtB,mBAAA,CA+BM,OA/BN0D,WA+BM,GA9BJxD,mBAAA,CAGM,OAHNyD,WAGM,G,4BAFJzD,mBAAA,CAAe,YAAX,QAAM,qBACVA,mBAAA,CAAoF;IAA3EQ,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAU,MAAA,IAAEf,IAAA,CAAAsD,sBAAsB;IAAS7D,KAAK,EAAC;KAAkB,MAAI,E,GAG7EG,mBAAA,CAwBM,OAxBN2D,WAwBM,GAvBJ3D,mBAAA,CAsBQ,SAtBR4D,WAsBQ,G,4BArBN5D,mBAAA,CAQQ,gBAPNA,mBAAA,CAMK,aALHA,mBAAA,CAAW,YAAP,IAAE,GACNA,mBAAA,CAAW,YAAP,IAAE,GACNA,mBAAA,CAAa,YAAT,MAAI,GACRA,mBAAA,CAAa,YAAT,MAAI,GACRA,mBAAA,CAAW,YAAP,IAAE,E,uBAGVA,mBAAA,CAWQ,iB,kBAVNF,mBAAA,CASKgB,SAAA,QAAAC,WAAA,CATiBX,IAAA,CAAAyD,YAAY,EAAvBC,OAAO;yBAAlBhE,mBAAA,CASK;MATgCoB,GAAG,EAAE4C,OAAO,CAAC7B;QAChDjC,mBAAA,CAAyB,YAAAG,gBAAA,CAAlB2D,OAAO,CAAC7B,EAAE,kBACjBjC,mBAAA,CAA4B,YAAAG,gBAAA,CAArB2D,OAAO,CAACb,KAAK,kBACpBjD,mBAAA,CAAgD,YAAAG,gBAAA,CAAzCC,IAAA,CAAA8C,YAAY,CAACY,OAAO,CAACX,OAAO,uBACnCnD,mBAAA,CAA6C,YAAAG,gBAAA,CAAtCC,IAAA,CAAAgC,UAAU,CAAC0B,OAAO,CAACzB,UAAU,mBACpCrC,mBAAA,CAGK,aAFHA,mBAAA,CAAgF;MAAvEQ,OAAK,EAAAW,MAAA,IAAEf,IAAA,CAAA2D,WAAW,CAACD,OAAO;MAAGjE,KAAK,EAAC;OAAyB,IAAE,iBAAAmE,WAAA,GACvEhE,mBAAA,CAAoF;MAA3EQ,OAAK,EAAAW,MAAA,IAAEf,IAAA,CAAA6D,aAAa,CAACH,OAAO,CAAC7B,EAAE;MAAGpC,KAAK,EAAC;OAAwB,IAAE,iBAAAqE,WAAA,E;+EAQvFtD,mBAAA,YAAe,EACJR,IAAA,CAAAgB,SAAS,oB,cAApBtB,mBAAA,CA+BM,OA/BNqE,WA+BM,GA9BJnE,mBAAA,CAGM,OAHNoE,WAGM,G,4BAFJpE,mBAAA,CAAe,YAAX,QAAM,qBACVA,mBAAA,CAAqF;IAA5EQ,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAU,MAAA,IAAEf,IAAA,CAAAiE,uBAAuB;IAASxE,KAAK,EAAC;KAAkB,MAAI,E,GAG9EG,mBAAA,CAwBM,OAxBNsE,WAwBM,GAvBJtE,mBAAA,CAsBQ,SAtBRuE,WAsBQ,G,4BArBNvE,mBAAA,CAQQ,gBAPNA,mBAAA,CAMK,aALHA,mBAAA,CAAW,YAAP,IAAE,GACNA,mBAAA,CAAW,YAAP,IAAE,GACNA,mBAAA,CAAW,YAAP,IAAE,GACNA,mBAAA,CAAa,YAAT,MAAI,GACRA,mBAAA,CAAW,YAAP,IAAE,E,uBAGVA,mBAAA,CAWQ,iB,kBAVNF,mBAAA,CASKgB,SAAA,QAAAC,WAAA,CATkBX,IAAA,CAAAoE,iBAAiB,EAA7BC,QAAQ;yBAAnB3E,mBAAA,CASK;MATsCoB,GAAG,EAAEuD,QAAQ,CAACxC;QACvDjC,mBAAA,CAA0B,YAAAG,gBAAA,CAAnBsE,QAAQ,CAACxC,EAAE,kBAClBjC,mBAAA,CAA6B,YAAAG,gBAAA,CAAtBsE,QAAQ,CAACxB,KAAK,kBACrBjD,mBAAA,CAA+C,YAAAG,gBAAA,CAAxCsE,QAAQ,CAACC,SAAS,gCACzB1E,mBAAA,CAA8C,YAAAG,gBAAA,CAAvCC,IAAA,CAAAgC,UAAU,CAACqC,QAAQ,CAACpC,UAAU,mBACrCrC,mBAAA,CAGK,aAFHA,mBAAA,CAAkF;MAAzEQ,OAAK,EAAAW,MAAA,IAAEf,IAAA,CAAAuE,YAAY,CAACF,QAAQ;MAAG5E,KAAK,EAAC;OAAyB,IAAE,iBAAA+E,WAAA,GACzE5E,mBAAA,CAAsF;MAA7EQ,OAAK,EAAAW,MAAA,IAAEf,IAAA,CAAAyE,cAAc,CAACJ,QAAQ,CAACxC,EAAE;MAAGpC,KAAK,EAAC;OAAwB,IAAE,iBAAAiF,WAAA,E;+EAQzFlE,mBAAA,YAAe,EACJR,IAAA,CAAAgB,SAAS,qB,cAApBtB,mBAAA,CA+BM,OA/BNiF,WA+BM,GA9BJ/E,mBAAA,CAGM,OAHNgF,WAGM,G,4BAFJhF,mBAAA,CAAe,YAAX,QAAM,qBACVA,mBAAA,CAAqF;IAA5EQ,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAU,MAAA,IAAEf,IAAA,CAAA6E,uBAAuB;IAASpF,KAAK,EAAC;KAAkB,MAAI,E,GAG9EG,mBAAA,CAwBM,OAxBNkF,WAwBM,GAvBJlF,mBAAA,CAsBQ,SAtBRmF,WAsBQ,G,4BArBNnF,mBAAA,CAQQ,gBAPNA,mBAAA,CAMK,aALHA,mBAAA,CAAW,YAAP,IAAE,GACNA,mBAAA,CAAW,YAAP,IAAE,GACNA,mBAAA,CAAa,YAAT,MAAI,GACRA,mBAAA,CAAa,YAAT,MAAI,GACRA,mBAAA,CAAW,YAAP,IAAE,E,uBAGVA,mBAAA,CAWQ,iB,kBAVNF,mBAAA,CASKgB,SAAA,QAAAC,WAAA,CATkBX,IAAA,CAAAgF,cAAc,EAA1BC,QAAQ;yBAAnBvF,mBAAA,CASK;MATmCoB,GAAG,EAAEmE,QAAQ,CAACpD;QACpDjC,mBAAA,CAA0B,YAAAG,gBAAA,CAAnBkF,QAAQ,CAACpD,EAAE,kBAClBjC,mBAAA,CAA6B,YAAAG,gBAAA,CAAtBkF,QAAQ,CAACpC,KAAK,kBACrBjD,mBAAA,CAAiD,YAAAG,gBAAA,CAA1CC,IAAA,CAAAgC,UAAU,CAACiD,QAAQ,CAACC,aAAa,mBACxCtF,mBAAA,CAA8C,YAAAG,gBAAA,CAAvCC,IAAA,CAAAgC,UAAU,CAACiD,QAAQ,CAAChD,UAAU,mBACrCrC,mBAAA,CAGK,aAFHA,mBAAA,CAAkF;MAAzEQ,OAAK,EAAAW,MAAA,IAAEf,IAAA,CAAAmF,YAAY,CAACF,QAAQ;MAAGxF,KAAK,EAAC;OAAyB,IAAE,iBAAA2F,WAAA,GACzExF,mBAAA,CAAsF;MAA7EQ,OAAK,EAAAW,MAAA,IAAEf,IAAA,CAAAqF,cAAc,CAACJ,QAAQ,CAACpD,EAAE;MAAGpC,KAAK,EAAC;OAAwB,IAAE,iBAAA6F,WAAA,E;iFAS3F9E,mBAAA,iBAAoB,C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}