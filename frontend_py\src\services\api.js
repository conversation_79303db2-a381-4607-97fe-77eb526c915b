import axios from 'axios'

// 创建 axios 实例
const api = axios.create({
  baseURL: process.env.NODE_ENV === 'production' ? '/api' : 'http://localhost:5000/api',
  withCredentials: true, // 发送 cookies
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 可以在这里添加 loading 状态
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    return response
  },
  error => {
    if (error.response) {
      // 处理不同的错误状态码
      switch (error.response.status) {
        case 401:
          // 未认证，跳转到登录页
          if (window.location.pathname !== '/login') {
            window.location.href = '/login'
          }
          break
        case 403:
          // 无权限
          console.error('无权限访问')
          break
        case 500:
          console.error('服务器内部错误')
          break
        default:
          console.error('请求失败:', error.response.data.msg || '未知错误')
      }
    } else if (error.request) {
      console.error('网络错误，请检查网络连接')
    } else {
      console.error('请求配置错误:', error.message)
    }
    return Promise.reject(error)
  }
)

// 认证相关 API
export const authAPI = {
  // 登录
  login: (credentials) => api.post('/auth/login', credentials),

  // 注册
  register: (userData) => api.post('/auth/register', userData),

  // 检查认证状态
  checkAuth: () => api.get('/auth/check-auth'),

  // 登出
  logout: () => api.post('/auth/logout')
}

// 用户相关 API
export const userAPI = {
  // 获取用户列表
  getUsers: () => api.get('/users'),
  
  // 获取用户详情
  getUser: (id) => api.get(`/users/${id}`),
  
  // 创建用户
  createUser: (userData) => api.post('/users', userData),
  
  // 更新用户
  updateUser: (id, userData) => api.put(`/users/${id}`, userData),
  
  // 删除用户
  deleteUser: (id) => api.delete(`/users/${id}`)
}

// 公告相关 API
export const announcementAPI = {
  getAnnouncements: () => api.get('/announcements'),
  getAnnouncement: (id) => api.get(`/announcements/${id}`),
  createAnnouncement: (data) => api.post('/announcements', data),
  updateAnnouncement: (id, data) => api.put(`/announcements/${id}`, data),
  deleteAnnouncement: (id) => api.delete(`/announcements/${id}`)
}

// 社团文化相关 API
export const clubCultureAPI = {
  getClubCultures: () => api.get('/club-culture'),
  getClubCulture: (id) => api.get(`/club-culture/${id}`),
  createClubCulture: (data) => api.post('/club-culture', data),
  updateClubCulture: (id, data) => api.put(`/club-culture/${id}`, data),
  deleteClubCulture: (id) => api.delete(`/club-culture/${id}`)
}

// 学习资源相关 API
export const learningResourceAPI = {
  getLearningResources: () => api.get('/learning-resources'),
  getLearningResource: (id) => api.get(`/learning-resources/${id}`),
  createLearningResource: (data) => api.post('/learning-resources', data),
  updateLearningResource: (id, data) => api.put(`/learning-resources/${id}`, data),
  deleteLearningResource: (id) => api.delete(`/learning-resources/${id}`)
}

// 过往活动相关 API
export const pastActivityAPI = {
  getPastActivities: () => api.get('/past-activities'),
  getPastActivity: (id) => api.get(`/past-activities/${id}`),
  createPastActivity: (data) => api.post('/past-activities', data),
  updatePastActivity: (id, data) => api.put(`/past-activities/${id}`, data),
  deletePastActivity: (id) => api.delete(`/past-activities/${id}`)
}

// 网络安全新闻相关 API
export const cyberSecurityNewsAPI = {
  getCyberSecurityNews: () => api.get('/cyber-security-news'),
  getCyberSecurityNewsItem: (id) => api.get(`/cyber-security-news/${id}`),
  createCyberSecurityNews: (data) => api.post('/cyber-security-news', data),
  updateCyberSecurityNews: (id, data) => api.put(`/cyber-security-news/${id}`, data),
  deleteCyberSecurityNews: (id) => api.delete(`/cyber-security-news/${id}`)
}

export default api
