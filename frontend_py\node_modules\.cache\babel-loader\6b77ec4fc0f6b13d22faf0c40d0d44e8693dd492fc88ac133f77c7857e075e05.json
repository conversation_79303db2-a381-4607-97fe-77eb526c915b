{"ast": null, "code": "import { createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, createStaticVNode as _createStaticVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"playground\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, _cache[0] || (_cache[0] = [_createStaticVNode(\"<div class=\\\"hero-section\\\" data-v-467009f5><h1 data-v-467009f5>🎮 实操靶场</h1><p class=\\\"hero-subtitle\\\" data-v-467009f5>提升网络安全技能的实战平台</p></div><div class=\\\"content-container\\\" data-v-467009f5><!-- 功能介绍 --><div class=\\\"section\\\" data-v-467009f5><h2 data-v-467009f5>靶场功能</h2><div class=\\\"features-grid\\\" data-v-467009f5><div class=\\\"feature-card\\\" data-v-467009f5><div class=\\\"feature-icon\\\" data-v-467009f5>🔍</div><h3 data-v-467009f5>漏洞扫描</h3><p data-v-467009f5>学习使用各种扫描工具发现系统漏洞</p></div><div class=\\\"feature-card\\\" data-v-467009f5><div class=\\\"feature-icon\\\" data-v-467009f5>🛡️</div><h3 data-v-467009f5>渗透测试</h3><p data-v-467009f5>在安全环境中练习渗透测试技术</p></div><div class=\\\"feature-card\\\" data-v-467009f5><div class=\\\"feature-icon\\\" data-v-467009f5>🔐</div><h3 data-v-467009f5>密码破解</h3><p data-v-467009f5>学习密码安全和破解防护技术</p></div><div class=\\\"feature-card\\\" data-v-467009f5><div class=\\\"feature-icon\\\" data-v-467009f5>🌐</div><h3 data-v-467009f5>Web安全</h3><p data-v-467009f5>掌握Web应用安全测试方法</p></div></div></div><!-- 开发状态 --><div class=\\\"section status-section\\\" data-v-467009f5><h2 data-v-467009f5>开发状态</h2><div class=\\\"status-card\\\" data-v-467009f5><div class=\\\"status-icon\\\" data-v-467009f5>🚧</div><h3 data-v-467009f5>功能开发中</h3><p data-v-467009f5>实操靶场功能正在紧张开发中，将为大家提供：</p><ul class=\\\"status-list\\\" data-v-467009f5><li data-v-467009f5>✅ 多种难度级别的挑战</li><li data-v-467009f5>✅ 实时的技能评估系统</li><li data-v-467009f5>✅ 详细的学习指导</li><li data-v-467009f5>✅ 团队协作练习环境</li><li data-v-467009f5>✅ 成就系统和排行榜</li></ul><p class=\\\"status-note\\\" data-v-467009f5>敬请期待正式上线！</p></div></div></div>\", 2)]));\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_cache"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Playground.vue"], "sourcesContent": ["<template>\r\n  <div class=\"playground\">\r\n    <div class=\"hero-section\">\r\n      <h1>🎮 实操靶场</h1>\r\n      <p class=\"hero-subtitle\">提升网络安全技能的实战平台</p>\r\n    </div>\r\n\r\n    <div class=\"content-container\">\r\n      <!-- 功能介绍 -->\r\n      <div class=\"section\">\r\n        <h2>靶场功能</h2>\r\n        <div class=\"features-grid\">\r\n          <div class=\"feature-card\">\r\n            <div class=\"feature-icon\">🔍</div>\r\n            <h3>漏洞扫描</h3>\r\n            <p>学习使用各种扫描工具发现系统漏洞</p>\r\n          </div>\r\n          <div class=\"feature-card\">\r\n            <div class=\"feature-icon\">🛡️</div>\r\n            <h3>渗透测试</h3>\r\n            <p>在安全环境中练习渗透测试技术</p>\r\n          </div>\r\n          <div class=\"feature-card\">\r\n            <div class=\"feature-icon\">🔐</div>\r\n            <h3>密码破解</h3>\r\n            <p>学习密码安全和破解防护技术</p>\r\n          </div>\r\n          <div class=\"feature-card\">\r\n            <div class=\"feature-icon\">🌐</div>\r\n            <h3>Web安全</h3>\r\n            <p>掌握Web应用安全测试方法</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 开发状态 -->\r\n      <div class=\"section status-section\">\r\n        <h2>开发状态</h2>\r\n        <div class=\"status-card\">\r\n          <div class=\"status-icon\">🚧</div>\r\n          <h3>功能开发中</h3>\r\n          <p>实操靶场功能正在紧张开发中，将为大家提供：</p>\r\n          <ul class=\"status-list\">\r\n            <li>✅ 多种难度级别的挑战</li>\r\n            <li>✅ 实时的技能评估系统</li>\r\n            <li>✅ 详细的学习指导</li>\r\n            <li>✅ 团队协作练习环境</li>\r\n            <li>✅ 成就系统和排行榜</li>\r\n          </ul>\r\n          <p class=\"status-note\">敬请期待正式上线！</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'Playground'\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.playground {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n}\r\n\r\n.hero-section {\r\n  text-align: center;\r\n  padding: 80px 20px;\r\n  color: white;\r\n}\r\n\r\n.hero-section h1 {\r\n  font-size: 3.5rem;\r\n  margin-bottom: 20px;\r\n  font-weight: bold;\r\n}\r\n\r\n.hero-subtitle {\r\n  font-size: 1.3rem;\r\n  opacity: 0.9;\r\n}\r\n\r\n.content-container {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 40px 20px;\r\n  background: white;\r\n  border-radius: 20px 20px 0 0;\r\n}\r\n\r\n.section {\r\n  margin-bottom: 50px;\r\n}\r\n\r\n.section h2 {\r\n  font-size: 2.2rem;\r\n  margin-bottom: 30px;\r\n  color: #333;\r\n  text-align: center;\r\n  border-bottom: 3px solid #42b983;\r\n  padding-bottom: 15px;\r\n}\r\n\r\n.features-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\r\n  gap: 30px;\r\n  margin-top: 30px;\r\n}\r\n\r\n.feature-card {\r\n  background: white;\r\n  padding: 30px;\r\n  border-radius: 15px;\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\r\n  text-align: center;\r\n  transition: transform 0.3s ease;\r\n  border: 2px solid transparent;\r\n}\r\n\r\n.feature-card:hover {\r\n  transform: translateY(-10px);\r\n  border-color: #42b983;\r\n}\r\n\r\n.feature-icon {\r\n  font-size: 3rem;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.feature-card h3 {\r\n  color: #333;\r\n  margin-bottom: 15px;\r\n  font-size: 1.4rem;\r\n}\r\n\r\n.feature-card p {\r\n  color: #666;\r\n  line-height: 1.6;\r\n}\r\n\r\n.status-section {\r\n  background: #f8f9fa;\r\n  padding: 40px;\r\n  border-radius: 15px;\r\n}\r\n\r\n.status-card {\r\n  text-align: center;\r\n  max-width: 600px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.status-icon {\r\n  font-size: 4rem;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.status-card h3 {\r\n  color: #333;\r\n  margin-bottom: 20px;\r\n  font-size: 1.8rem;\r\n}\r\n\r\n.status-card p {\r\n  color: #666;\r\n  line-height: 1.6;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.status-list {\r\n  text-align: left;\r\n  max-width: 400px;\r\n  margin: 20px auto;\r\n  color: #666;\r\n}\r\n\r\n.status-list li {\r\n  margin-bottom: 10px;\r\n  padding-left: 10px;\r\n}\r\n\r\n.status-note {\r\n  font-weight: bold;\r\n  color: #42b983;\r\n  font-size: 1.1rem;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .hero-section h1 {\r\n    font-size: 2.5rem;\r\n  }\r\n\r\n  .features-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n\r\n  .content-container {\r\n    padding: 20px 15px;\r\n  }\r\n}\r\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAY;;uBAAvBC,mBAAA,CAoDM,OApDNC,UAoDM,EAAAC,MAAA,QAAAA,MAAA,O", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}