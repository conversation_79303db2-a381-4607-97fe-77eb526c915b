{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  id: \"app\"\n};\nconst _hoisted_2 = {\n  class: \"main-content\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_Navigation = _resolveComponent(\"Navigation\");\n  const _component_router_view = _resolveComponent(\"router-view\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_Navigation), _createElementVNode(\"main\", _hoisted_2, [_createVNode(_component_router_view)])]);\n}", "map": {"version": 3, "names": ["id", "class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_Navigation", "_createElementVNode", "_hoisted_2", "_component_router_view"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\App.vue"], "sourcesContent": ["<template>\n  <div id=\"app\">\n    <Navigation />\n    <main class=\"main-content\">\n      <router-view/>\n    </main>\n  </div>\n</template>\n\n<script>\nimport Navigation from './components/Navigation.vue'\n\nexport default {\n  name: 'App',\n  components: {\n    Navigation\n  }\n}\n</script>\n\n<style>\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\n#app {\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  color: #2c3e50;\n  min-height: 100vh;\n  background-color: #f8f9fa;\n}\n\n.main-content {\n  min-height: calc(100vh - 70px);\n}\n\nbody {\n  margin: 0;\n  padding: 0;\n}\n</style>\n"], "mappings": ";;EACOA,EAAE,EAAC;AAAK;;EAELC,KAAK,EAAC;AAAc;;;;uBAF5BC,mBAAA,CAKM,OALNC,UAKM,GAJJC,YAAA,CAAcC,qBAAA,GACdC,mBAAA,CAEO,QAFPC,UAEO,GADLH,YAAA,CAAcI,sBAAA,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}