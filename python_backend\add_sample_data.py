#!/usr/bin/env python3
"""
添加示例数据到数据库
"""
import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from models import db, Announcement, ClubCulture, LearningResource, PastActivity, CyberSecurityNews
from datetime import datetime, date

def add_sample_data():
    with app.app_context():
        print("开始添加示例数据...")
        
        # 添加公告
        if not Announcement.query.first():
            announcements = [
                Announcement(
                    title="欢迎加入NIS社团！",
                    content="欢迎各位新同学加入NIS网络信息安全社团！我们将一起学习网络安全知识，参与各种实践活动。",
                    created_at=datetime.now()
                ),
                Announcement(
                    title="本周技术分享会",
                    content="本周五晚上7点，我们将举办技术分享会，主题是《Web安全基础》，欢迎大家参加！",
                    created_at=datetime.now()
                )
            ]
            for announcement in announcements:
                db.session.add(announcement)
            print("✅ 添加了示例公告")
        
        # 添加社团文化
        if not ClubCulture.query.first():
            culture = ClubCulture(
                title="NIS社团文化",
                content="""
                <h2>我们的使命</h2>
                <p>NIS网络信息安全社团致力于培养网络安全人才，提高同学们的信息安全意识。</p>
                
                <h2>社团价值观</h2>
                <ul>
                    <li>学习：持续学习新技术，保持技术敏感度</li>
                    <li>分享：乐于分享知识，帮助他人成长</li>
                    <li>实践：理论结合实践，动手解决问题</li>
                    <li>责任：负责任地使用技术，维护网络安全</li>
                </ul>
                
                <h2>活动形式</h2>
                <p>我们定期举办技术分享会、CTF竞赛、安全演练等活动。</p>
                """,
                images="[]",
                created_at=datetime.now()
            )
            db.session.add(culture)
            print("✅ 添加了社团文化内容")
        
        # 添加学习资源
        if not LearningResource.query.first():
            resources = [
                LearningResource(
                    title="网络安全入门指南",
                    content="这是一份适合初学者的网络安全学习指南，包含了基础概念、学习路径和推荐资源。",
                    file_path=None,
                    created_at=datetime.now()
                ),
                LearningResource(
                    title="Web安全基础教程",
                    content="深入浅出地介绍Web安全的基础知识，包括常见漏洞类型和防护方法。",
                    file_path=None,
                    created_at=datetime.now()
                ),
                LearningResource(
                    title="密码学基础",
                    content="介绍密码学的基本概念，包括对称加密、非对称加密、哈希函数等。",
                    file_path=None,
                    created_at=datetime.now()
                )
            ]
            for resource in resources:
                db.session.add(resource)
            print("✅ 添加了学习资源")
        
        # 添加过往活动
        if not PastActivity.query.first():
            activities = [
                PastActivity(
                    title="2024春季CTF竞赛",
                    description="我们举办了春季CTF竞赛，吸引了全校50多名同学参与，大家在比赛中学习了很多实用的安全技能。",
                    images="[]",
                    activity_date=date(2024, 3, 15),
                    created_at=datetime.now()
                ),
                PastActivity(
                    title="网络安全意识宣传周",
                    description="配合国家网络安全宣传周，我们在校园内开展了安全意识宣传活动，向同学们普及网络安全知识。",
                    images="[]",
                    activity_date=date(2024, 9, 10),
                    created_at=datetime.now()
                )
            ]
            for activity in activities:
                db.session.add(activity)
            print("✅ 添加了过往活动")
        
        # 添加网络安全新闻
        if not CyberSecurityNews.query.first():
            news = [
                CyberSecurityNews(
                    title="2024年网络安全趋势报告发布",
                    content="最新的网络安全趋势报告显示，AI驱动的攻击和防护技术将成为2024年的重点。",
                    source_url="https://example.com/news1",
                    published_date=date(2024, 1, 15),
                    created_at=datetime.now()
                ),
                CyberSecurityNews(
                    title="新型勒索软件威胁分析",
                    content="安全研究人员发现了一种新型勒索软件，具有更强的隐蔽性和破坏力。",
                    source_url="https://example.com/news2",
                    published_date=date(2024, 1, 10),
                    created_at=datetime.now()
                )
            ]
            for item in news:
                db.session.add(item)
            print("✅ 添加了网络安全新闻")
        
        # 提交所有更改
        db.session.commit()
        print("🎉 示例数据添加完成！")

if __name__ == "__main__":
    add_sample_data()
