{"ast": null, "code": "import { createElementVNode as _createElementVNode, vModelText as _vModelText, withDirectives as _withDirectives, withModifiers as _withModifiers, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"login\"\n};\nconst _hoisted_2 = {\n  class: \"form-group\"\n};\nconst _hoisted_3 = {\n  class: \"form-group\"\n};\nconst _hoisted_4 = {\n  key: 0,\n  class: \"error-message\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[6] || (_cache[6] = _createElementVNode(\"h1\", null, \"用户登录\", -1 /* CACHED */)), _createElementVNode(\"form\", {\n    onSubmit: _cache[2] || (_cache[2] = _withModifiers((...args) => $options.login && $options.login(...args), [\"prevent\"]))\n  }, [_createElementVNode(\"div\", _hoisted_2, [_cache[3] || (_cache[3] = _createElementVNode(\"label\", {\n    for: \"username\"\n  }, \"用户名:\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    id: \"username\",\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $data.username = $event),\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.username]])]), _createElementVNode(\"div\", _hoisted_3, [_cache[4] || (_cache[4] = _createElementVNode(\"label\", {\n    for: \"password\"\n  }, \"密码:\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"password\",\n    id: \"password\",\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $data.password = $event),\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.password]])]), _cache[5] || (_cache[5] = _createElementVNode(\"button\", {\n    type: \"submit\"\n  }, \"登录\", -1 /* CACHED */))], 32 /* NEED_HYDRATION */), $data.error ? (_openBlock(), _createElementBlock(\"p\", _hoisted_4, _toDisplayString($data.error), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "onSubmit", "_cache", "_withModifiers", "args", "$options", "login", "_hoisted_2", "for", "type", "id", "$data", "username", "$event", "required", "_hoisted_3", "password", "error", "_hoisted_4", "_toDisplayString"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Login.vue"], "sourcesContent": ["<template>\r\n  <div class=\"login\">\r\n    <h1>用户登录</h1>\r\n    <form @submit.prevent=\"login\">\r\n      <div class=\"form-group\">\r\n        <label for=\"username\">用户名:</label>\r\n        <input type=\"text\" id=\"username\" v-model=\"username\" required>\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"password\">密码:</label>\r\n        <input type=\"password\" id=\"password\" v-model=\"password\" required>\r\n      </div>\r\n      <button type=\"submit\">登录</button>\r\n    </form>\r\n    <p v-if=\"error\" class=\"error-message\">{{ error }}</p>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport axios from 'axios';\r\n\r\nexport default {\r\n  name: 'Login',\r\n  data() {\r\n    return {\r\n      username: '',\r\n      password: '',\r\n      error: null,\r\n    };\r\n  },\r\n  methods: {\r\n    async login() {\r\n      this.error = null; // 清除之前的错误信息\r\n\r\n      // 前端输入验证\r\n      if (!this.username) {\r\n        this.error = '用户名不能为空。';\r\n        return;\r\n      }\r\n      if (!this.password) {\r\n        this.error = '密码不能为空。';\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const res = await axios.post('http://localhost:5000/api/auth/login', {\r\n          username: this.username,\r\n          password: this.password,\r\n        });\r\n        // 登录成功后，token 会通过 HttpOnly cookie 自动设置，前端无需手动处理\r\n        this.$router.push('/'); // 示例：跳转到首页\r\n      } catch (err) {\r\n        this.error = err.response.data.msg || '登录失败，请检查用户名和密码。';\r\n        console.error(err);\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.login {\r\n  padding: 20px;\r\n  max-width: 400px;\r\n  margin: 50px auto;\r\n  border: 1px solid #ccc;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.form-group {\r\n  margin-bottom: 15px;\r\n  text-align: left;\r\n}\r\n\r\n.form-group label {\r\n  display: block;\r\n  margin-bottom: 5px;\r\n  font-weight: bold;\r\n}\r\n\r\n.form-group input[type=\"text\"],\r\n.form-group input[type=\"password\"] {\r\n  width: 100%;\r\n  padding: 10px;\r\n  border: 1px solid #ddd;\r\n  border-radius: 4px;\r\n  box-sizing: border-box; /* Ensures padding doesn't increase overall width */\r\n}\r\n\r\nbutton {\r\n  background-color: #42b983;\r\n  color: white;\r\n  padding: 10px 15px;\r\n  border: none;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  font-size: 16px;\r\n}\r\n\r\nbutton:hover {\r\n  background-color: #369f6e;\r\n}\r\n\r\n.error-message {\r\n  color: red;\r\n  margin-top: 10px;\r\n}\r\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAO;;EAGTA,KAAK,EAAC;AAAY;;EAIlBA,KAAK,EAAC;AAAY;;;EAMTA,KAAK,EAAC;;;uBAbxBC,mBAAA,CAcM,OAdNC,UAcM,G,0BAbJC,mBAAA,CAAa,YAAT,MAAI,qBACRA,mBAAA,CAUO;IAVAC,QAAM,EAAAC,MAAA,QAAAA,MAAA,MAAAC,cAAA,KAAAC,IAAA,KAAUC,QAAA,CAAAC,KAAA,IAAAD,QAAA,CAAAC,KAAA,IAAAF,IAAA,CAAK;MAC1BJ,mBAAA,CAGM,OAHNO,UAGM,G,0BAFJP,mBAAA,CAAkC;IAA3BQ,GAAG,EAAC;EAAU,GAAC,MAAI,qB,gBAC1BR,mBAAA,CAA6D;IAAtDS,IAAI,EAAC,MAAM;IAACC,EAAE,EAAC,UAAU;+DAAUC,KAAA,CAAAC,QAAQ,GAAAC,MAAA;IAAEC,QAAQ,EAAR;iDAAVH,KAAA,CAAAC,QAAQ,E,KAEpDZ,mBAAA,CAGM,OAHNe,UAGM,G,0BAFJf,mBAAA,CAAiC;IAA1BQ,GAAG,EAAC;EAAU,GAAC,KAAG,qB,gBACzBR,mBAAA,CAAiE;IAA1DS,IAAI,EAAC,UAAU;IAACC,EAAE,EAAC,UAAU;+DAAUC,KAAA,CAAAK,QAAQ,GAAAH,MAAA;IAAEC,QAAQ,EAAR;iDAAVH,KAAA,CAAAK,QAAQ,E,+BAExDhB,mBAAA,CAAiC;IAAzBS,IAAI,EAAC;EAAQ,GAAC,IAAE,oB,4BAEjBE,KAAA,CAAAM,KAAK,I,cAAdnB,mBAAA,CAAqD,KAArDoB,UAAqD,EAAAC,gBAAA,CAAZR,KAAA,CAAAM,KAAK,oB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}