<template>
  <div class="past-activities">
    <h1>往期活动回顾</h1>
    <p>这里展示社团过往活动的图片、视频、文字记录。</p>
    <div class="activities-list">
      <div v-for="activity in activities" :key="activity.id" class="activity-item">
        <h3>{{ activity.title }}</h3>
        <div v-html="purifyContent(activity.description)"></div>
      </div>
    </div>
  </div>
</template>

<script>
import DOMPurify from 'dompurify'; // 引入 DOMPurify

export default {
  name: 'PastActivities',
  data() {
    return {
      // 示例：往期活动列表，实际应从后端获取
      activities: [
        { id: 1, title: '2023 年网络安全周', description: '<p>回顾 2023 年网络安全周的**精彩瞬间**。</p><img src="invalid.png" onerror="alert(\'XSS in activity!\')">' },
        { id: 2, title: 'CTF 比赛回顾', description: '<p>社团成员在 CTF 比赛中的表现。</p>' },
      ],
    };
  },
  methods: {
    purifyContent(content) {
      return DOMPurify.sanitize(content);
    },
  },
}
</script>

<style scoped>
.past-activities {
  padding: 20px;
}
</style>