<template>
  <div class="past-activities">
    <h1>往期活动回顾</h1>
    <p>这里展示社团过往活动的图片、视频、文字记录。</p>

    <div v-if="loading" class="loading">
      <p>加载中...</p>
    </div>

    <div v-else-if="error" class="error">
      <p>{{ error }}</p>
      <button @click="loadActivities" class="btn btn-primary">重试</button>
    </div>

    <div v-else class="activities-list">
      <div v-if="activities.length === 0" class="no-data">
        <p>暂无往期活动记录</p>
      </div>
      <div v-else>
        <div v-for="activity in activities" :key="activity.id" class="activity-item">
          <h3>{{ activity.title }}</h3>
          <div class="activity-date">
            <strong>活动时间:</strong> {{ formatDate(activity.activity_date) }}
          </div>
          <div class="activity-description">
            <div v-html="purifyContent(activity.description)"></div>
          </div>
          <div class="activity-meta">
            <small>发布时间: {{ formatDate(activity.created_at) }}</small>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DOMPurify from 'dompurify'
import { pastActivityAPI } from '../services/api'

export default {
  name: 'PastActivities',
  data() {
    return {
      activities: [],
      loading: false,
      error: null
    }
  },
  async mounted() {
    await this.loadActivities()
  },
  methods: {
    async loadActivities() {
      this.loading = true
      this.error = null
      try {
        const response = await pastActivityAPI.getPastActivities()
        this.activities = response.data
      } catch (error) {
        console.error('加载往期活动失败:', error)
        this.error = '加载往期活动失败，请稍后重试'
      } finally {
        this.loading = false
      }
    },

    purifyContent(content) {
      return DOMPurify.sanitize(content)
    },

    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    }
  }
}
</script>

<style scoped>
.past-activities {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.loading, .error, .no-data {
  text-align: center;
  padding: 40px;
  color: #666;
}

.error {
  color: #e74c3c;
}

.activities-list {
  margin-top: 20px;
}

.activity-item {
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.activity-item h3 {
  color: #2c3e50;
  margin-bottom: 15px;
  border-bottom: 2px solid #e74c3c;
  padding-bottom: 10px;
}

.activity-date {
  color: #e74c3c;
  font-weight: bold;
  margin-bottom: 15px;
}

.activity-description {
  margin: 15px 0;
  line-height: 1.6;
}

.activity-meta {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #eee;
  color: #666;
}

.btn {
  display: inline-block;
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  text-decoration: none;
  cursor: pointer;
  font-size: 14px;
  background-color: #e74c3c;
  color: white;
}

.btn:hover {
  background-color: #c0392b;
}
</style>