{"ast": null, "code": "import { createApp } from 'vue';\nimport App from './App.vue';\nimport router from './router';\nconst app = createApp(App);\napp.use(router);\napp.mount('#app');", "map": {"version": 3, "names": ["createApp", "App", "router", "app", "use", "mount"], "sources": ["C:/Users/<USER>/Desktop/cs/pacong_py/frontend_py/src/main.js"], "sourcesContent": ["import { createApp } from 'vue'\nimport App from './App.vue'\nimport router from './router'\n\nconst app = createApp(App)\n\napp.use(router)\napp.mount('#app')\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,KAAK;AAC/B,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAE7B,MAAMC,GAAG,GAAGH,SAAS,CAACC,GAAG,CAAC;AAE1BE,GAAG,CAACC,GAAG,CAACF,MAAM,CAAC;AACfC,GAAG,CAACE,KAAK,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}