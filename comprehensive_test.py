#!/usr/bin/env python3
"""
NIS社团网站综合测试脚本
测试所有主要功能和API端点
"""
import requests
import json
import sys

BASE_URL = "http://localhost:5000"
API_BASE = f"{BASE_URL}/api"

def test_backend_health():
    """测试后端健康状态"""
    print("🔍 测试后端健康状态...")
    try:
        response = requests.get(BASE_URL, timeout=5)
        if response.status_code == 200:
            print("✅ 后端服务正常运行")
            return True
        else:
            print(f"❌ 后端服务异常，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到后端服务: {e}")
        return False

def test_login_api():
    """测试登录API"""
    print("\n🔍 测试登录API...")
    try:
        response = requests.post(
            f"{API_BASE}/auth/login",
            json={"username": "admin", "password": "Admin123!"},
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ 登录API正常工作")
            # 检查是否设置了cookie
            if 'Set-Cookie' in response.headers:
                print("✅ JWT Token cookie已设置")
            return response.cookies
        else:
            print(f"❌ 登录失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 登录API测试失败: {e}")
        return None

def test_auth_check(cookies):
    """测试认证检查API"""
    print("\n🔍 测试认证检查API...")
    try:
        response = requests.get(
            f"{API_BASE}/auth/check-auth",
            cookies=cookies,
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ 认证检查API正常工作")
            data = response.json()
            print(f"用户信息: {data.get('user', {}).get('username', 'N/A')}")
            return True
        else:
            print(f"❌ 认证检查失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 认证检查API测试失败: {e}")
        return False

def test_other_apis():
    """测试其他API端点"""
    print("\n🔍 测试其他API端点...")
    
    endpoints = [
        "/api/announcements",
        "/api/club-culture", 
        "/api/learning-resources",
        "/api/past-activities",
        "/api/cyber-security-news"
    ]
    
    results = {}
    for endpoint in endpoints:
        try:
            response = requests.get(f"{BASE_URL}{endpoint}", timeout=5)
            if response.status_code in [200, 401]:  # 200正常，401需要认证也是正常的
                results[endpoint] = "✅"
            else:
                results[endpoint] = f"❌ ({response.status_code})"
        except Exception as e:
            results[endpoint] = f"❌ (异常: {str(e)[:50]})"
    
    for endpoint, status in results.items():
        print(f"{status} {endpoint}")
    
    return all("✅" in status for status in results.values())

def main():
    """主测试函数"""
    print("🚀 开始NIS社团网站综合测试")
    print("=" * 50)
    
    # 测试后端健康状态
    if not test_backend_health():
        print("\n❌ 后端服务不可用，测试终止")
        sys.exit(1)
    
    # 测试登录API
    cookies = test_login_api()
    if not cookies:
        print("\n❌ 登录功能异常，测试终止")
        sys.exit(1)
    
    # 测试认证检查
    auth_ok = test_auth_check(cookies)
    
    # 测试其他API
    other_apis_ok = test_other_apis()
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"后端服务: ✅")
    print(f"登录功能: ✅")
    print(f"认证检查: {'✅' if auth_ok else '❌'}")
    print(f"其他API: {'✅' if other_apis_ok else '❌'}")
    
    if auth_ok and other_apis_ok:
        print("\n🎉 所有测试通过！系统运行正常")
        return 0
    else:
        print("\n⚠️  部分测试失败，请检查相关功能")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
