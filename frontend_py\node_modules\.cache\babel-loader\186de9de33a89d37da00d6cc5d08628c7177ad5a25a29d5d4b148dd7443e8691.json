{"ast": null, "code": "import { createElementVNode as _createElementVNode, vModelText as _vModelText, withDirectives as _withDirectives, toDisplayString as _toDisplayString, withModifiers as _withModifiers, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"login\"\n};\nconst _hoisted_2 = {\n  class: \"form-group\"\n};\nconst _hoisted_3 = {\n  class: \"form-group\"\n};\nconst _hoisted_4 = [\"disabled\"];\nconst _hoisted_5 = {\n  key: 0,\n  class: \"error-message\"\n};\nconst _hoisted_6 = {\n  class: \"register-link\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_router_link = _resolveComponent(\"router-link\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[8] || (_cache[8] = _createElementVNode(\"h1\", null, \"用户登录\", -1 /* CACHED */)), _createElementVNode(\"form\", {\n    onSubmit: _cache[2] || (_cache[2] = _withModifiers((...args) => $options.login && $options.login(...args), [\"prevent\"]))\n  }, [_createElementVNode(\"div\", _hoisted_2, [_cache[3] || (_cache[3] = _createElementVNode(\"label\", {\n    for: \"username\"\n  }, \"用户名:\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    id: \"username\",\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $data.username = $event),\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.username]])]), _createElementVNode(\"div\", _hoisted_3, [_cache[4] || (_cache[4] = _createElementVNode(\"label\", {\n    for: \"password\"\n  }, \"密码:\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"password\",\n    id: \"password\",\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $data.password = $event),\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.password]])]), _createElementVNode(\"button\", {\n    type: \"submit\",\n    disabled: $data.loading\n  }, _toDisplayString($data.loading ? '登录中...' : '登录'), 9 /* TEXT, PROPS */, _hoisted_4)], 32 /* NEED_HYDRATION */), $data.error ? (_openBlock(), _createElementBlock(\"p\", _hoisted_5, _toDisplayString($data.error), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"p\", null, [_cache[6] || (_cache[6] = _createTextVNode(\"还没有账户？\")), _createVNode(_component_router_link, {\n    to: \"/register\"\n  }, {\n    default: _withCtx(() => _cache[5] || (_cache[5] = [_createTextVNode(\"立即注册\")])),\n    _: 1 /* STABLE */,\n    __: [5]\n  })]), _cache[7] || (_cache[7] = _createElementVNode(\"p\", {\n    class: \"admin-hint\"\n  }, \"管理员账户：admin / Admin123!\", -1 /* CACHED */))])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "onSubmit", "_cache", "_withModifiers", "args", "$options", "login", "_hoisted_2", "for", "type", "id", "$data", "username", "$event", "required", "_hoisted_3", "password", "disabled", "loading", "_hoisted_4", "error", "_hoisted_5", "_toDisplayString", "_hoisted_6", "_createVNode", "_component_router_link", "to"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Login.vue"], "sourcesContent": ["<template>\r\n  <div class=\"login\">\r\n    <h1>用户登录</h1>\r\n    <form @submit.prevent=\"login\">\r\n      <div class=\"form-group\">\r\n        <label for=\"username\">用户名:</label>\r\n        <input type=\"text\" id=\"username\" v-model=\"username\" required>\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"password\">密码:</label>\r\n        <input type=\"password\" id=\"password\" v-model=\"password\" required>\r\n      </div>\r\n      <button type=\"submit\" :disabled=\"loading\">\r\n        {{ loading ? '登录中...' : '登录' }}\r\n      </button>\r\n    </form>\r\n    <p v-if=\"error\" class=\"error-message\">{{ error }}</p>\r\n\r\n    <div class=\"register-link\">\r\n      <p>还没有账户？<router-link to=\"/register\">立即注册</router-link></p>\r\n      <p class=\"admin-hint\">管理员账户：admin / Admin123!</p>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { authAPI } from '../services/api'\r\n\r\nexport default {\r\n  name: 'Login',\r\n  data() {\r\n    return {\r\n      username: '',\r\n      password: '',\r\n      error: null,\r\n      loading: false\r\n    }\r\n  },\r\n  methods: {\r\n    async login() {\r\n      this.error = null\r\n      this.loading = true\r\n\r\n      // 前端输入验证\r\n      if (!this.username) {\r\n        this.error = '用户名不能为空。'\r\n        this.loading = false\r\n        return\r\n      }\r\n      if (!this.password) {\r\n        this.error = '密码不能为空。'\r\n        this.loading = false\r\n        return\r\n      }\r\n\r\n      try {\r\n        const response = await authAPI.login({\r\n          username: this.username,\r\n          password: this.password\r\n        })\r\n\r\n        // 登录成功，检查用户角色并跳转到相应页面\r\n        const authResponse = await authAPI.checkAuth()\r\n        const userRole = authResponse.data.user.role\r\n\r\n        if (userRole === 'admin') {\r\n          this.$router.push('/admin')\r\n        } else {\r\n          this.$router.push('/dashboard')\r\n        }\r\n      } catch (err) {\r\n        this.error = err.response?.data?.msg || '登录失败，请检查用户名和密码。'\r\n        console.error(err)\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.login {\r\n  padding: 20px;\r\n  max-width: 400px;\r\n  margin: 50px auto;\r\n  border: 1px solid #ccc;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.form-group {\r\n  margin-bottom: 15px;\r\n  text-align: left;\r\n}\r\n\r\n.form-group label {\r\n  display: block;\r\n  margin-bottom: 5px;\r\n  font-weight: bold;\r\n}\r\n\r\n.form-group input[type=\"text\"],\r\n.form-group input[type=\"password\"] {\r\n  width: 100%;\r\n  padding: 10px;\r\n  border: 1px solid #ddd;\r\n  border-radius: 4px;\r\n  box-sizing: border-box; /* Ensures padding doesn't increase overall width */\r\n}\r\n\r\nbutton {\r\n  background-color: #42b983;\r\n  color: white;\r\n  padding: 10px 15px;\r\n  border: none;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  font-size: 16px;\r\n}\r\n\r\nbutton:hover:not(:disabled) {\r\n  background-color: #369f6e;\r\n}\r\n\r\nbutton:disabled {\r\n  background-color: #ccc;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.error-message {\r\n  color: red;\r\n  margin-top: 10px;\r\n}\r\n\r\n.register-link {\r\n  margin-top: 20px;\r\n  text-align: center;\r\n}\r\n\r\n.register-link a {\r\n  color: #42b983;\r\n  text-decoration: none;\r\n}\r\n\r\n.register-link a:hover {\r\n  text-decoration: underline;\r\n}\r\n\r\n.admin-hint {\r\n  font-size: 12px;\r\n  color: #666;\r\n  margin-top: 10px;\r\n}\r\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAO;;EAGTA,KAAK,EAAC;AAAY;;EAIlBA,KAAK,EAAC;AAAY;;;;EAQTA,KAAK,EAAC;;;EAEjBA,KAAK,EAAC;AAAe;;;uBAjB5BC,mBAAA,CAqBM,OArBNC,UAqBM,G,0BApBJC,mBAAA,CAAa,YAAT,MAAI,qBACRA,mBAAA,CAYO;IAZAC,QAAM,EAAAC,MAAA,QAAAA,MAAA,MAAAC,cAAA,KAAAC,IAAA,KAAUC,QAAA,CAAAC,KAAA,IAAAD,QAAA,CAAAC,KAAA,IAAAF,IAAA,CAAK;MAC1BJ,mBAAA,CAGM,OAHNO,UAGM,G,0BAFJP,mBAAA,CAAkC;IAA3BQ,GAAG,EAAC;EAAU,GAAC,MAAI,qB,gBAC1BR,mBAAA,CAA6D;IAAtDS,IAAI,EAAC,MAAM;IAACC,EAAE,EAAC,UAAU;+DAAUC,KAAA,CAAAC,QAAQ,GAAAC,MAAA;IAAEC,QAAQ,EAAR;iDAAVH,KAAA,CAAAC,QAAQ,E,KAEpDZ,mBAAA,CAGM,OAHNe,UAGM,G,0BAFJf,mBAAA,CAAiC;IAA1BQ,GAAG,EAAC;EAAU,GAAC,KAAG,qB,gBACzBR,mBAAA,CAAiE;IAA1DS,IAAI,EAAC,UAAU;IAACC,EAAE,EAAC,UAAU;+DAAUC,KAAA,CAAAK,QAAQ,GAAAH,MAAA;IAAEC,QAAQ,EAAR;iDAAVH,KAAA,CAAAK,QAAQ,E,KAExDhB,mBAAA,CAES;IAFDS,IAAI,EAAC,QAAQ;IAAEQ,QAAQ,EAAEN,KAAA,CAAAO;sBAC5BP,KAAA,CAAAO,OAAO,0CAAAC,UAAA,E,4BAGLR,KAAA,CAAAS,KAAK,I,cAAdtB,mBAAA,CAAqD,KAArDuB,UAAqD,EAAAC,gBAAA,CAAZX,KAAA,CAAAS,KAAK,oB,mCAE9CpB,mBAAA,CAGM,OAHNuB,UAGM,GAFJvB,mBAAA,CAA2D,Y,2CAAxD,QAAM,IAAAwB,YAAA,CAA8CC,sBAAA;IAAjCC,EAAE,EAAC;EAAW;sBAAC,MAAIxB,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;kCACzCF,mBAAA,CAAiD;IAA9CH,KAAK,EAAC;EAAY,GAAC,yBAAuB,oB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}