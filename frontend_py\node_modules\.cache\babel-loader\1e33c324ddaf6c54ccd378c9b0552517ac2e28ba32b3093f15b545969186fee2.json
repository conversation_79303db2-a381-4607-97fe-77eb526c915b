{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { authAPI } from '../services/api';\nexport default {\n  name: 'Login',\n  data() {\n    return {\n      username: '',\n      password: '',\n      error: null,\n      loading: false\n    };\n  },\n  methods: {\n    async login() {\n      this.error = null;\n      this.loading = true;\n\n      // 前端输入验证\n      if (!this.username) {\n        this.error = '用户名不能为空。';\n        this.loading = false;\n        return;\n      }\n      if (!this.password) {\n        this.error = '密码不能为空。';\n        this.loading = false;\n        return;\n      }\n      try {\n        console.log('开始登录请求...');\n        const loginResponse = await authAPI.login({\n          username: this.username,\n          password: this.password\n        });\n        console.log('登录响应:', loginResponse);\n\n        // 登录成功，检查用户角色并跳转到相应页面\n        const authResponse = await authAPI.checkAuth();\n        const userRole = authResponse.data.user.role;\n        if (userRole === 'admin') {\n          this.$router.push('/admin');\n        } else {\n          this.$router.push('/dashboard');\n        }\n      } catch (err) {\n        console.error('登录错误详情:', err);\n        if (err.response) {\n          console.error('响应状态:', err.response.status);\n          console.error('响应数据:', err.response.data);\n          this.error = err.response.data?.msg || '服务器错误';\n        } else if (err.request) {\n          console.error('请求错误:', err.request);\n          this.error = '无法连接到服务器，请检查网络连接';\n        } else {\n          console.error('其他错误:', err.message);\n          this.error = '登录失败：' + err.message;\n        }\n      } finally {\n        this.loading = false;\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["authAPI", "name", "data", "username", "password", "error", "loading", "methods", "login", "console", "log", "loginResponse", "authResponse", "checkAuth", "userRole", "user", "role", "$router", "push", "err", "response", "status", "msg", "request", "message"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Login.vue"], "sourcesContent": ["<template>\r\n  <div class=\"login\">\r\n    <h1>用户登录</h1>\r\n    <form @submit.prevent=\"login\">\r\n      <div class=\"form-group\">\r\n        <label for=\"username\">用户名:</label>\r\n        <input type=\"text\" id=\"username\" v-model=\"username\" required>\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"password\">密码:</label>\r\n        <input type=\"password\" id=\"password\" v-model=\"password\" required>\r\n      </div>\r\n      <button type=\"submit\" :disabled=\"loading\">\r\n        {{ loading ? '登录中...' : '登录' }}\r\n      </button>\r\n    </form>\r\n    <p v-if=\"error\" class=\"error-message\">{{ error }}</p>\r\n\r\n    <div class=\"register-link\">\r\n      <p>还没有账户？<router-link to=\"/register\">立即注册</router-link></p>\r\n      <p class=\"admin-hint\">管理员账户：admin / Admin123!</p>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { authAPI } from '../services/api'\r\n\r\nexport default {\r\n  name: 'Login',\r\n  data() {\r\n    return {\r\n      username: '',\r\n      password: '',\r\n      error: null,\r\n      loading: false\r\n    }\r\n  },\r\n  methods: {\r\n    async login() {\r\n      this.error = null\r\n      this.loading = true\r\n\r\n      // 前端输入验证\r\n      if (!this.username) {\r\n        this.error = '用户名不能为空。'\r\n        this.loading = false\r\n        return\r\n      }\r\n      if (!this.password) {\r\n        this.error = '密码不能为空。'\r\n        this.loading = false\r\n        return\r\n      }\r\n\r\n      try {\r\n        console.log('开始登录请求...')\r\n        const loginResponse = await authAPI.login({\r\n          username: this.username,\r\n          password: this.password\r\n        })\r\n        console.log('登录响应:', loginResponse)\r\n\r\n        // 登录成功，检查用户角色并跳转到相应页面\r\n        const authResponse = await authAPI.checkAuth()\r\n        const userRole = authResponse.data.user.role\r\n\r\n        if (userRole === 'admin') {\r\n          this.$router.push('/admin')\r\n        } else {\r\n          this.$router.push('/dashboard')\r\n        }\r\n      } catch (err) {\r\n        console.error('登录错误详情:', err)\r\n        if (err.response) {\r\n          console.error('响应状态:', err.response.status)\r\n          console.error('响应数据:', err.response.data)\r\n          this.error = err.response.data?.msg || '服务器错误'\r\n        } else if (err.request) {\r\n          console.error('请求错误:', err.request)\r\n          this.error = '无法连接到服务器，请检查网络连接'\r\n        } else {\r\n          console.error('其他错误:', err.message)\r\n          this.error = '登录失败：' + err.message\r\n        }\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.login {\r\n  padding: 20px;\r\n  max-width: 400px;\r\n  margin: 50px auto;\r\n  border: 1px solid #ccc;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.form-group {\r\n  margin-bottom: 15px;\r\n  text-align: left;\r\n}\r\n\r\n.form-group label {\r\n  display: block;\r\n  margin-bottom: 5px;\r\n  font-weight: bold;\r\n}\r\n\r\n.form-group input[type=\"text\"],\r\n.form-group input[type=\"password\"] {\r\n  width: 100%;\r\n  padding: 10px;\r\n  border: 1px solid #ddd;\r\n  border-radius: 4px;\r\n  box-sizing: border-box; /* Ensures padding doesn't increase overall width */\r\n}\r\n\r\nbutton {\r\n  background-color: #42b983;\r\n  color: white;\r\n  padding: 10px 15px;\r\n  border: none;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  font-size: 16px;\r\n}\r\n\r\nbutton:hover:not(:disabled) {\r\n  background-color: #369f6e;\r\n}\r\n\r\nbutton:disabled {\r\n  background-color: #ccc;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.error-message {\r\n  color: red;\r\n  margin-top: 10px;\r\n}\r\n\r\n.register-link {\r\n  margin-top: 20px;\r\n  text-align: center;\r\n}\r\n\r\n.register-link a {\r\n  color: #42b983;\r\n  text-decoration: none;\r\n}\r\n\r\n.register-link a:hover {\r\n  text-decoration: underline;\r\n}\r\n\r\n.admin-hint {\r\n  font-size: 12px;\r\n  color: #666;\r\n  margin-top: 10px;\r\n}\r\n</style>"], "mappings": ";AA0BA,SAASA,OAAM,QAAS,iBAAgB;AAExC,eAAe;EACbC,IAAI,EAAE,OAAO;EACbC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,IAAI;MACXC,OAAO,EAAE;IACX;EACF,CAAC;EACDC,OAAO,EAAE;IACP,MAAMC,KAAKA,CAAA,EAAG;MACZ,IAAI,CAACH,KAAI,GAAI,IAAG;MAChB,IAAI,CAACC,OAAM,GAAI,IAAG;;MAElB;MACA,IAAI,CAAC,IAAI,CAACH,QAAQ,EAAE;QAClB,IAAI,CAACE,KAAI,GAAI,UAAS;QACtB,IAAI,CAACC,OAAM,GAAI,KAAI;QACnB;MACF;MACA,IAAI,CAAC,IAAI,CAACF,QAAQ,EAAE;QAClB,IAAI,CAACC,KAAI,GAAI,SAAQ;QACrB,IAAI,CAACC,OAAM,GAAI,KAAI;QACnB;MACF;MAEA,IAAI;QACFG,OAAO,CAACC,GAAG,CAAC,WAAW;QACvB,MAAMC,aAAY,GAAI,MAAMX,OAAO,CAACQ,KAAK,CAAC;UACxCL,QAAQ,EAAE,IAAI,CAACA,QAAQ;UACvBC,QAAQ,EAAE,IAAI,CAACA;QACjB,CAAC;QACDK,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEC,aAAa;;QAElC;QACA,MAAMC,YAAW,GAAI,MAAMZ,OAAO,CAACa,SAAS,CAAC;QAC7C,MAAMC,QAAO,GAAIF,YAAY,CAACV,IAAI,CAACa,IAAI,CAACC,IAAG;QAE3C,IAAIF,QAAO,KAAM,OAAO,EAAE;UACxB,IAAI,CAACG,OAAO,CAACC,IAAI,CAAC,QAAQ;QAC5B,OAAO;UACL,IAAI,CAACD,OAAO,CAACC,IAAI,CAAC,YAAY;QAChC;MACF,EAAE,OAAOC,GAAG,EAAE;QACZV,OAAO,CAACJ,KAAK,CAAC,SAAS,EAAEc,GAAG;QAC5B,IAAIA,GAAG,CAACC,QAAQ,EAAE;UAChBX,OAAO,CAACJ,KAAK,CAAC,OAAO,EAAEc,GAAG,CAACC,QAAQ,CAACC,MAAM;UAC1CZ,OAAO,CAACJ,KAAK,CAAC,OAAO,EAAEc,GAAG,CAACC,QAAQ,CAAClB,IAAI;UACxC,IAAI,CAACG,KAAI,GAAIc,GAAG,CAACC,QAAQ,CAAClB,IAAI,EAAEoB,GAAE,IAAK,OAAM;QAC/C,OAAO,IAAIH,GAAG,CAACI,OAAO,EAAE;UACtBd,OAAO,CAACJ,KAAK,CAAC,OAAO,EAAEc,GAAG,CAACI,OAAO;UAClC,IAAI,CAAClB,KAAI,GAAI,kBAAiB;QAChC,OAAO;UACLI,OAAO,CAACJ,KAAK,CAAC,OAAO,EAAEc,GAAG,CAACK,OAAO;UAClC,IAAI,CAACnB,KAAI,GAAI,OAAM,GAAIc,GAAG,CAACK,OAAM;QACnC;MACF,UAAU;QACR,IAAI,CAAClB,OAAM,GAAI,KAAI;MACrB;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}