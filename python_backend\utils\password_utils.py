import bcrypt

def hash_password(password):
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

def check_password(password, hashed_password):
    return bcrypt.checkpw(password.encode('utf-8'), hashed_password.encode('utf-8'))

def is_weak_password(password):
    min_length = 8
    has_upper = any(c.isupper() for c in password)
    has_lower = any(c.islower() for c in password)
    has_digit = any(c.isdigit() for c in password)
    has_special = any(c in '!@#$%^&*(),.?":{}|<>' for c in password)
    common_weak_passwords = ['123456', 'password', 'admin', 'test', 'qwerty', '12345678', '123456789']

    if (len(password) < min_length or
        not has_upper or
        not has_lower or
        not has_digit or
        not has_special or
        password.lower() in common_weak_passwords):
        return True
    return False
