#!/usr/bin/env python3
import os
import sys

# 切换到python_backend目录
os.chdir('python_backend')

# 添加当前目录到Python路径
sys.path.insert(0, '.')

# 设置环境变量
os.environ['DATABASE_URL'] = 'sqlite:///nis_club.db'
os.environ['JWT_SECRET_KEY'] = 'supersecretjwtkey'
os.environ['SECRET_KEY'] = 'supersecretflaskkey'

try:
    from app import app
    
    print("=" * 60)
    print("启动NIS社团文化网站后端服务...")
    print("=" * 60)
    print("服务地址: http://localhost:5000")
    print("API文档: http://localhost:5000/api/auth/test")
    print("管理员账户: admin / Admin123!")
    print("=" * 60)
    
    # 在开发模式下运行
    app.run(debug=True, host='127.0.0.1', port=5000, use_reloader=False)
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    import traceback
    traceback.print_exc()
except Exception as e:
    print(f"❌ 启动错误: {e}")
    import traceback
    traceback.print_exc()
