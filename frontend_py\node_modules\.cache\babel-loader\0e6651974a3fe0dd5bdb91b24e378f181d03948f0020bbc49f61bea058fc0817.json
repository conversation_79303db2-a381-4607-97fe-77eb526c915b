{"ast": null, "code": "import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, renderList as _renderList, Fragment as _Fragment, createTextVNode as _createTextVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"past-activities\"\n};\nconst _hoisted_2 = {\n  key: 0,\n  class: \"loading\"\n};\nconst _hoisted_3 = {\n  key: 1,\n  class: \"error\"\n};\nconst _hoisted_4 = {\n  key: 2,\n  class: \"activities-list\"\n};\nconst _hoisted_5 = {\n  key: 0,\n  class: \"no-data\"\n};\nconst _hoisted_6 = {\n  key: 1\n};\nconst _hoisted_7 = {\n  class: \"activity-date\"\n};\nconst _hoisted_8 = {\n  class: \"activity-description\"\n};\nconst _hoisted_9 = [\"innerHTML\"];\nconst _hoisted_10 = {\n  class: \"activity-meta\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[4] || (_cache[4] = _createElementVNode(\"h1\", null, \"往期活动回顾\", -1 /* CACHED */)), _cache[5] || (_cache[5] = _createElementVNode(\"p\", null, \"这里展示社团过往活动的图片、视频、文字记录。\", -1 /* CACHED */)), $data.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, _cache[1] || (_cache[1] = [_createElementVNode(\"p\", null, \"加载中...\", -1 /* CACHED */)]))) : $data.error ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, [_createElementVNode(\"p\", null, _toDisplayString($data.error), 1 /* TEXT */), _createElementVNode(\"button\", {\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.loadActivities && $options.loadActivities(...args)),\n    class: \"btn btn-primary\"\n  }, \"重试\")])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [$data.activities.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, _cache[2] || (_cache[2] = [_createElementVNode(\"p\", null, \"暂无往期活动记录\", -1 /* CACHED */)]))) : (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.activities, activity => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: activity.id,\n      class: \"activity-item\"\n    }, [_createElementVNode(\"h3\", null, _toDisplayString(activity.title), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_7, [_cache[3] || (_cache[3] = _createElementVNode(\"strong\", null, \"活动时间:\", -1 /* CACHED */)), _createTextVNode(\" \" + _toDisplayString($options.formatDate(activity.activity_date)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"div\", {\n      innerHTML: $options.purifyContent(activity.description)\n    }, null, 8 /* PROPS */, _hoisted_9)]), _createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"small\", null, \"发布时间: \" + _toDisplayString($options.formatDate(activity.created_at)), 1 /* TEXT */)])]);\n  }), 128 /* KEYED_FRAGMENT */))]))]))]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "$data", "loading", "_hoisted_2", "_cache", "error", "_hoisted_3", "_toDisplayString", "onClick", "args", "$options", "loadActivities", "_hoisted_4", "activities", "length", "_hoisted_5", "_hoisted_6", "_Fragment", "_renderList", "activity", "key", "id", "title", "_hoisted_7", "formatDate", "activity_date", "_hoisted_8", "innerHTML", "purifyContent", "description", "_hoisted_10", "created_at"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\PastActivities.vue"], "sourcesContent": ["<template>\r\n  <div class=\"past-activities\">\r\n    <h1>往期活动回顾</h1>\r\n    <p>这里展示社团过往活动的图片、视频、文字记录。</p>\r\n\r\n    <div v-if=\"loading\" class=\"loading\">\r\n      <p>加载中...</p>\r\n    </div>\r\n\r\n    <div v-else-if=\"error\" class=\"error\">\r\n      <p>{{ error }}</p>\r\n      <button @click=\"loadActivities\" class=\"btn btn-primary\">重试</button>\r\n    </div>\r\n\r\n    <div v-else class=\"activities-list\">\r\n      <div v-if=\"activities.length === 0\" class=\"no-data\">\r\n        <p>暂无往期活动记录</p>\r\n      </div>\r\n      <div v-else>\r\n        <div v-for=\"activity in activities\" :key=\"activity.id\" class=\"activity-item\">\r\n          <h3>{{ activity.title }}</h3>\r\n          <div class=\"activity-date\">\r\n            <strong>活动时间:</strong> {{ formatDate(activity.activity_date) }}\r\n          </div>\r\n          <div class=\"activity-description\">\r\n            <div v-html=\"purifyContent(activity.description)\"></div>\r\n          </div>\r\n          <div class=\"activity-meta\">\r\n            <small>发布时间: {{ formatDate(activity.created_at) }}</small>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport DOMPurify from 'dompurify'\r\nimport { pastActivityAPI } from '../services/api'\r\n\r\nexport default {\r\n  name: 'PastActivities',\r\n  data() {\r\n    return {\r\n      activities: [],\r\n      loading: false,\r\n      error: null\r\n    }\r\n  },\r\n  async mounted() {\r\n    await this.loadActivities()\r\n  },\r\n  methods: {\r\n    async loadActivities() {\r\n      this.loading = true\r\n      this.error = null\r\n      try {\r\n        const response = await pastActivityAPI.getPastActivities()\r\n        this.activities = response.data\r\n      } catch (error) {\r\n        console.error('加载往期活动失败:', error)\r\n        this.error = '加载往期活动失败，请稍后重试'\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    purifyContent(content) {\r\n      return DOMPurify.sanitize(content)\r\n    },\r\n\r\n    formatDate(dateString) {\r\n      if (!dateString) return ''\r\n      const date = new Date(dateString)\r\n      return date.toLocaleDateString('zh-CN')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.past-activities {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 20px;\r\n}\r\n\r\n.loading, .error, .no-data {\r\n  text-align: center;\r\n  padding: 40px;\r\n  color: #666;\r\n}\r\n\r\n.error {\r\n  color: #e74c3c;\r\n}\r\n\r\n.activities-list {\r\n  margin-top: 20px;\r\n}\r\n\r\n.activity-item {\r\n  background: white;\r\n  border: 1px solid #ddd;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  margin-bottom: 20px;\r\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\r\n}\r\n\r\n.activity-item h3 {\r\n  color: #2c3e50;\r\n  margin-bottom: 15px;\r\n  border-bottom: 2px solid #e74c3c;\r\n  padding-bottom: 10px;\r\n}\r\n\r\n.activity-date {\r\n  color: #e74c3c;\r\n  font-weight: bold;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.activity-description {\r\n  margin: 15px 0;\r\n  line-height: 1.6;\r\n}\r\n\r\n.activity-meta {\r\n  margin-top: 15px;\r\n  padding-top: 15px;\r\n  border-top: 1px solid #eee;\r\n  color: #666;\r\n}\r\n\r\n.btn {\r\n  display: inline-block;\r\n  padding: 8px 16px;\r\n  border: none;\r\n  border-radius: 4px;\r\n  text-decoration: none;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  background-color: #e74c3c;\r\n  color: white;\r\n}\r\n\r\n.btn:hover {\r\n  background-color: #c0392b;\r\n}\r\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAiB;;;EAINA,KAAK,EAAC;;;;EAIHA,KAAK,EAAC;;;;EAKjBA,KAAK,EAAC;;;;EACoBA,KAAK,EAAC;;;;;;EAMjCA,KAAK,EAAC;AAAe;;EAGrBA,KAAK,EAAC;AAAsB;;;EAG5BA,KAAK,EAAC;AAAe;;uBA1BlCC,mBAAA,CAgCM,OAhCNC,UAgCM,G,0BA/BJC,mBAAA,CAAe,YAAX,QAAM,qB,0BACVA,mBAAA,CAA6B,WAA1B,wBAAsB,qBAEdC,KAAA,CAAAC,OAAO,I,cAAlBJ,mBAAA,CAEM,OAFNK,UAEM,EAAAC,MAAA,QAAAA,MAAA,OADJJ,mBAAA,CAAa,WAAV,QAAM,mB,MAGKC,KAAA,CAAAI,KAAK,I,cAArBP,mBAAA,CAGM,OAHNQ,UAGM,GAFJN,mBAAA,CAAkB,WAAAO,gBAAA,CAAZN,KAAA,CAAAI,KAAK,kBACXL,mBAAA,CAAmE;IAA1DQ,OAAK,EAAAJ,MAAA,QAAAA,MAAA,UAAAK,IAAA,KAAEC,QAAA,CAAAC,cAAA,IAAAD,QAAA,CAAAC,cAAA,IAAAF,IAAA,CAAc;IAAEZ,KAAK,EAAC;KAAkB,IAAE,E,oBAG5DC,mBAAA,CAkBM,OAlBNc,UAkBM,GAjBOX,KAAA,CAAAY,UAAU,CAACC,MAAM,U,cAA5BhB,mBAAA,CAEM,OAFNiB,UAEM,EAAAX,MAAA,QAAAA,MAAA,OADJJ,mBAAA,CAAe,WAAZ,UAAQ,mB,qBAEbF,mBAAA,CAaM,OAAAkB,UAAA,I,kBAZJlB,mBAAA,CAWMmB,SAAA,QAAAC,WAAA,CAXkBjB,KAAA,CAAAY,UAAU,EAAtBM,QAAQ;yBAApBrB,mBAAA,CAWM;MAX+BsB,GAAG,EAAED,QAAQ,CAACE,EAAE;MAAExB,KAAK,EAAC;QAC3DG,mBAAA,CAA6B,YAAAO,gBAAA,CAAtBY,QAAQ,CAACG,KAAK,kBACrBtB,mBAAA,CAEM,OAFNuB,UAEM,G,0BADJvB,mBAAA,CAAsB,gBAAd,OAAK,qB,iBAAS,GAAC,GAAAO,gBAAA,CAAGG,QAAA,CAAAc,UAAU,CAACL,QAAQ,CAACM,aAAa,kB,GAE7DzB,mBAAA,CAEM,OAFN0B,UAEM,GADJ1B,mBAAA,CAAwD;MAAnD2B,SAA4C,EAApCjB,QAAA,CAAAkB,aAAa,CAACT,QAAQ,CAACU,WAAW;2CAEjD7B,mBAAA,CAEM,OAFN8B,WAEM,GADJ9B,mBAAA,CAA0D,eAAnD,QAAM,GAAAO,gBAAA,CAAGG,QAAA,CAAAc,UAAU,CAACL,QAAQ,CAACY,UAAU,kB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}