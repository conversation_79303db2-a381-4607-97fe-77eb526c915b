{"ast": null, "code": "import { createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString } from \"vue\";\nconst _hoisted_1 = {\n  class: \"past-activities\"\n};\nconst _hoisted_2 = {\n  class: \"activities-list\"\n};\nconst _hoisted_3 = [\"innerHTML\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[0] || (_cache[0] = _createElementVNode(\"h1\", null, \"往期活动回顾\", -1 /* CACHED */)), _cache[1] || (_cache[1] = _createElementVNode(\"p\", null, \"这里展示社团过往活动的图片、视频、文字记录。\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_2, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.activities, activity => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: activity.id,\n      class: \"activity-item\"\n    }, [_createElementVNode(\"h3\", null, _toDisplayString(activity.title), 1 /* TEXT */), _createElementVNode(\"div\", {\n      innerHTML: $options.purifyContent(activity.description)\n    }, null, 8 /* PROPS */, _hoisted_3)]);\n  }), 128 /* KEYED_FRAGMENT */))])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_Fragment", "_renderList", "$data", "activities", "activity", "key", "id", "_toDisplayString", "title", "innerHTML", "$options", "purifyContent", "description"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\PastActivities.vue"], "sourcesContent": ["<template>\r\n  <div class=\"past-activities\">\r\n    <h1>往期活动回顾</h1>\r\n    <p>这里展示社团过往活动的图片、视频、文字记录。</p>\r\n    <div class=\"activities-list\">\r\n      <div v-for=\"activity in activities\" :key=\"activity.id\" class=\"activity-item\">\r\n        <h3>{{ activity.title }}</h3>\r\n        <div v-html=\"purifyContent(activity.description)\"></div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport DOMPurify from 'dompurify'; // 引入 DOMPurify\r\n\r\nexport default {\r\n  name: 'PastActivities',\r\n  data() {\r\n    return {\r\n      // 示例：往期活动列表，实际应从后端获取\r\n      activities: [\r\n        { id: 1, title: '2023 年网络安全周', description: '<p>回顾 2023 年网络安全周的**精彩瞬间**。</p><img src=\"invalid.png\" onerror=\"alert(\\'XSS in activity!\\')\">' },\r\n        { id: 2, title: 'CTF 比赛回顾', description: '<p>社团成员在 CTF 比赛中的表现。</p>' },\r\n      ],\r\n    };\r\n  },\r\n  methods: {\r\n    purifyContent(content) {\r\n      return DOMPurify.sanitize(content);\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.past-activities {\r\n  padding: 20px;\r\n}\r\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAiB;;EAGrBA,KAAK,EAAC;AAAiB;;;uBAH9BC,mBAAA,CASM,OATNC,UASM,G,0BARJC,mBAAA,CAAe,YAAX,QAAM,qB,0BACVA,mBAAA,CAA6B,WAA1B,wBAAsB,qBACzBA,mBAAA,CAKM,OALNC,UAKM,I,kBAJJH,mBAAA,CAGMI,SAAA,QAAAC,WAAA,CAHkBC,KAAA,CAAAC,UAAU,EAAtBC,QAAQ;yBAApBR,mBAAA,CAGM;MAH+BS,GAAG,EAAED,QAAQ,CAACE,EAAE;MAAEX,KAAK,EAAC;QAC3DG,mBAAA,CAA6B,YAAAS,gBAAA,CAAtBH,QAAQ,CAACI,KAAK,kBACrBV,mBAAA,CAAwD;MAAnDW,SAA4C,EAApCC,QAAA,CAAAC,aAAa,CAACP,QAAQ,CAACQ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}