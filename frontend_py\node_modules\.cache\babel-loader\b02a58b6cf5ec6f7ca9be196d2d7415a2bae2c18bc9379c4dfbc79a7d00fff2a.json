{"ast": null, "code": "import { createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, renderList as _renderList, Fragment as _Fragment, toDisplayString as _toDisplayString, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createStaticVNode as _createStaticVNode } from \"vue\";\nimport _imports_0 from '@/assets/logo.png';\nconst _hoisted_1 = {\n  class: \"club-culture\"\n};\nconst _hoisted_2 = {\n  class: \"section\"\n};\nconst _hoisted_3 = {\n  key: 0,\n  class: \"loading\"\n};\nconst _hoisted_4 = {\n  key: 1,\n  class: \"no-data\"\n};\nconst _hoisted_5 = {\n  key: 2,\n  class: \"culture-grid\"\n};\nconst _hoisted_6 = [\"innerHTML\"];\nconst _hoisted_7 = {\n  key: 0,\n  class: \"culture-images\"\n};\nconst _hoisted_8 = [\"src\", \"alt\"];\nconst _hoisted_9 = {\n  class: \"culture-date\"\n};\nconst _hoisted_10 = {\n  class: \"section join-section\"\n};\nconst _hoisted_11 = {\n  class: \"join-content\"\n};\nconst _hoisted_12 = {\n  class: \"join-actions\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_router_link = _resolveComponent(\"router-link\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[5] || (_cache[5] = _createElementVNode(\"div\", {\n    class: \"hero-section\"\n  }, [_createElementVNode(\"h1\", null, \"社团文化\"), _createElementVNode(\"p\", {\n    class: \"hero-subtitle\"\n  }, \"传承网络安全精神，培养技术人才\")], -1 /* CACHED */)), _createCommentVNode(\" 社团介绍 \"), _cache[6] || (_cache[6] = _createStaticVNode(\"<div class=\\\"section\\\" data-v-3dcdfbce><div class=\\\"intro-card\\\" data-v-3dcdfbce><div class=\\\"intro-content\\\" data-v-3dcdfbce><h2 data-v-3dcdfbce>网络信息安全社团</h2><div class=\\\"intro-details\\\" data-v-3dcdfbce><p data-v-3dcdfbce><strong data-v-3dcdfbce>学校：</strong>成都工业职业技术学院（金堂校区）</p><p data-v-3dcdfbce><strong data-v-3dcdfbce>社团群号：</strong>242050951</p><p data-v-3dcdfbce><strong data-v-3dcdfbce>抖音：</strong>21647629167</p><p data-v-3dcdfbce><strong data-v-3dcdfbce>主要活动：</strong>组网技术，网络攻防，安全科普</p></div></div><div class=\\\"intro-image\\\" data-v-3dcdfbce><img src=\\\"\" + _imports_0 + \"\\\" alt=\\\"NIS 社团标志\\\" data-v-3dcdfbce></div></div></div>\", 1)), _createCommentVNode(\" 社团文化内容 \"), _createElementVNode(\"div\", _hoisted_2, [_cache[0] || (_cache[0] = _createElementVNode(\"h2\", null, \"社团文化内容\", -1 /* CACHED */)), _ctx.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, \"加载中...\")) : _ctx.cultures.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, \"暂无文化内容\")) : (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.cultures, culture => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: culture.id,\n      class: \"culture-card\"\n    }, [_createElementVNode(\"h3\", null, _toDisplayString(culture.title), 1 /* TEXT */), _createElementVNode(\"div\", {\n      class: \"culture-content\",\n      innerHTML: _ctx.purifyContent(culture.content)\n    }, null, 8 /* PROPS */, _hoisted_6), culture.images && culture.images.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(culture.images, (image, index) => {\n      return _openBlock(), _createElementBlock(\"img\", {\n        key: index,\n        src: image,\n        alt: culture.title\n      }, null, 8 /* PROPS */, _hoisted_8);\n    }), 128 /* KEYED_FRAGMENT */))])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"p\", _hoisted_9, _toDisplayString(_ctx.formatDate(culture.created_at)), 1 /* TEXT */)]);\n  }), 128 /* KEYED_FRAGMENT */))]))]), _createCommentVNode(\" 社团价值观 \"), _cache[7] || (_cache[7] = _createStaticVNode(\"<div class=\\\"section values-section\\\" data-v-3dcdfbce><h2 data-v-3dcdfbce>社团价值观</h2><div class=\\\"values-grid\\\" data-v-3dcdfbce><div class=\\\"value-card\\\" data-v-3dcdfbce><div class=\\\"value-icon\\\" data-v-3dcdfbce>🛡️</div><h3 data-v-3dcdfbce>安全第一</h3><p data-v-3dcdfbce>始终将网络安全放在首位，保护数字世界的安全</p></div><div class=\\\"value-card\\\" data-v-3dcdfbce><div class=\\\"value-icon\\\" data-v-3dcdfbce>🤝</div><h3 data-v-3dcdfbce>团队协作</h3><p data-v-3dcdfbce>相互学习，共同进步，打造强大的技术团队</p></div><div class=\\\"value-card\\\" data-v-3dcdfbce><div class=\\\"value-icon\\\" data-v-3dcdfbce>💡</div><h3 data-v-3dcdfbce>创新思维</h3><p data-v-3dcdfbce>勇于探索新技术，创新解决方案</p></div><div class=\\\"value-card\\\" data-v-3dcdfbce><div class=\\\"value-icon\\\" data-v-3dcdfbce>📚</div><h3 data-v-3dcdfbce>持续学习</h3><p data-v-3dcdfbce>保持学习热情，跟上技术发展步伐</p></div></div></div>\", 1)), _createCommentVNode(\" 加入我们 \"), _createElementVNode(\"div\", _hoisted_10, [_cache[4] || (_cache[4] = _createElementVNode(\"h2\", null, \"加入我们\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_11, [_cache[3] || (_cache[3] = _createElementVNode(\"p\", null, \"如果你对网络安全感兴趣，想要学习相关技术，欢迎加入我们的大家庭！\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_12, [_createVNode(_component_router_link, {\n    to: \"/register\",\n    class: \"btn btn-primary\"\n  }, {\n    default: _withCtx(() => _cache[1] || (_cache[1] = [_createTextVNode(\"立即注册\")])),\n    _: 1 /* STABLE */,\n    __: [1]\n  }), _cache[2] || (_cache[2] = _createElementVNode(\"a\", {\n    href: \"https://qm.qq.com/cgi-bin/qm/qr?k=242050951\",\n    target: \"_blank\",\n    class: \"btn btn-secondary\"\n  }, \"加入QQ群\", -1 /* CACHED */))])])])]);\n}", "map": {"version": 3, "names": ["_imports_0", "class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_createCommentVNode", "_hoisted_2", "_ctx", "loading", "_hoisted_3", "cultures", "length", "_hoisted_4", "_hoisted_5", "_Fragment", "_renderList", "culture", "key", "id", "_toDisplayString", "title", "innerHTML", "purifyContent", "content", "images", "_hoisted_7", "image", "index", "src", "alt", "_hoisted_9", "formatDate", "created_at", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_createVNode", "_component_router_link", "to", "_cache", "href", "target"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\ClubCulture.vue"], "sourcesContent": ["<template>\r\n  <div class=\"club-culture\">\r\n    <div class=\"hero-section\">\r\n      <h1>社团文化</h1>\r\n      <p class=\"hero-subtitle\">传承网络安全精神，培养技术人才</p>\r\n    </div>\r\n\r\n    <!-- 社团介绍 -->\r\n    <div class=\"section\">\r\n      <div class=\"intro-card\">\r\n        <div class=\"intro-content\">\r\n          <h2>网络信息安全社团</h2>\r\n          <div class=\"intro-details\">\r\n            <p><strong>学校：</strong>成都工业职业技术学院（金堂校区）</p>\r\n            <p><strong>社团群号：</strong>242050951</p>\r\n            <p><strong>抖音：</strong>21647629167</p>\r\n            <p><strong>主要活动：</strong>组网技术，网络攻防，安全科普</p>\r\n          </div>\r\n        </div>\r\n        <div class=\"intro-image\">\r\n          <img src=\"@/assets/logo.png\" alt=\"NIS 社团标志\" />\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 社团文化内容 -->\r\n    <div class=\"section\">\r\n      <h2>社团文化内容</h2>\r\n      <div v-if=\"loading\" class=\"loading\">加载中...</div>\r\n      <div v-else-if=\"cultures.length === 0\" class=\"no-data\">暂无文化内容</div>\r\n      <div v-else class=\"culture-grid\">\r\n        <div v-for=\"culture in cultures\" :key=\"culture.id\" class=\"culture-card\">\r\n          <h3>{{ culture.title }}</h3>\r\n          <div class=\"culture-content\" v-html=\"purifyContent(culture.content)\"></div>\r\n          <div v-if=\"culture.images && culture.images.length > 0\" class=\"culture-images\">\r\n            <img v-for=\"(image, index) in culture.images\" :key=\"index\" :src=\"image\" :alt=\"culture.title\" />\r\n          </div>\r\n          <p class=\"culture-date\">{{ formatDate(culture.created_at) }}</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 社团价值观 -->\r\n    <div class=\"section values-section\">\r\n      <h2>社团价值观</h2>\r\n      <div class=\"values-grid\">\r\n        <div class=\"value-card\">\r\n          <div class=\"value-icon\">🛡️</div>\r\n          <h3>安全第一</h3>\r\n          <p>始终将网络安全放在首位，保护数字世界的安全</p>\r\n        </div>\r\n        <div class=\"value-card\">\r\n          <div class=\"value-icon\">🤝</div>\r\n          <h3>团队协作</h3>\r\n          <p>相互学习，共同进步，打造强大的技术团队</p>\r\n        </div>\r\n        <div class=\"value-card\">\r\n          <div class=\"value-icon\">💡</div>\r\n          <h3>创新思维</h3>\r\n          <p>勇于探索新技术，创新解决方案</p>\r\n        </div>\r\n        <div class=\"value-card\">\r\n          <div class=\"value-icon\">📚</div>\r\n          <h3>持续学习</h3>\r\n          <p>保持学习热情，跟上技术发展步伐</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 加入我们 -->\r\n    <div class=\"section join-section\">\r\n      <h2>加入我们</h2>\r\n      <div class=\"join-content\">\r\n        <p>如果你对网络安全感兴趣，想要学习相关技术，欢迎加入我们的大家庭！</p>\r\n        <div class=\"join-actions\">\r\n          <router-link to=\"/register\" class=\"btn btn-primary\">立即注册</router-link>\r\n          <a href=\"https://qm.qq.com/cgi-bin/qm/qr?k=242050951\" target=\"_blank\" class=\"btn btn-secondary\">加入QQ群</a>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport DOMPurify from 'dompurify'; // 引入 DOMPurify\r\n\r\nexport default {\r\n  name: 'ClubCulture',\r\n  data() {\r\n    return {\r\n      // 示例：社团文化介绍，实际应从后端获取\r\n      cultureContent: '<p>这里是NIS社团的**详细介绍**、宗旨、历史、吉祥物等信息。</p><p>我们致力于网络安全技术的学习与交流。</p><img src=\"invalid-image.jpg\" onerror=\"alert(\\'XSS in image!\\')\">',\r\n    };\r\n  },\r\n  computed: {\r\n    purifiedCultureContent() {\r\n      return DOMPurify.sanitize(this.cultureContent);\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.club-culture {\r\n  padding: 20px;\r\n}\r\nimg {\r\n  display: block;\r\n}\r\n</style>"], "mappings": ";OAoBeA,UAAuB;;EAnB/BC,KAAK,EAAC;AAAc;;EAyBlBA,KAAK,EAAC;AAAS;;;EAEEA,KAAK,EAAC;;;;EACaA,KAAK,EAAC;;;;EACjCA,KAAK,EAAC;;;;;EAI0CA,KAAK,EAAC;;;;EAG3DA,KAAK,EAAC;AAAc;;EAiCxBA,KAAK,EAAC;AAAsB;;EAE1BA,KAAK,EAAC;AAAc;;EAElBA,KAAK,EAAC;AAAc;;;uBAzE/BC,mBAAA,CA+EM,OA/ENC,UA+EM,G,0BA9EJC,mBAAA,CAGM;IAHDH,KAAK,EAAC;EAAc,IACvBG,mBAAA,CAAa,YAAT,MAAI,GACRA,mBAAA,CAA4C;IAAzCH,KAAK,EAAC;EAAe,GAAC,iBAAe,E,qBAG1CI,mBAAA,UAAa,E,8qBAkBbA,mBAAA,YAAe,EACfD,mBAAA,CAcM,OAdNE,UAcM,G,0BAbJF,mBAAA,CAAe,YAAX,QAAM,qBACCG,IAAA,CAAAC,OAAO,I,cAAlBN,mBAAA,CAAgD,OAAhDO,UAAgD,EAAZ,QAAM,KAC1BF,IAAA,CAAAG,QAAQ,CAACC,MAAM,U,cAA/BT,mBAAA,CAAmE,OAAnEU,UAAmE,EAAZ,QAAM,M,cAC7DV,mBAAA,CASM,OATNW,UASM,I,kBARJX,mBAAA,CAOMY,SAAA,QAAAC,WAAA,CAPiBR,IAAA,CAAAG,QAAQ,EAAnBM,OAAO;yBAAnBd,mBAAA,CAOM;MAP4Be,GAAG,EAAED,OAAO,CAACE,EAAE;MAAEjB,KAAK,EAAC;QACvDG,mBAAA,CAA4B,YAAAe,gBAAA,CAArBH,OAAO,CAACI,KAAK,kBACpBhB,mBAAA,CAA2E;MAAtEH,KAAK,EAAC,iBAAiB;MAACoB,SAAuC,EAA/Bd,IAAA,CAAAe,aAAa,CAACN,OAAO,CAACO,OAAO;yCACvDP,OAAO,CAACQ,MAAM,IAAIR,OAAO,CAACQ,MAAM,CAACb,MAAM,Q,cAAlDT,mBAAA,CAEM,OAFNuB,UAEM,I,kBADJvB,mBAAA,CAA+FY,SAAA,QAAAC,WAAA,CAAjEC,OAAO,CAACQ,MAAM,GAA/BE,KAAK,EAAEC,KAAK;2BAAzBzB,mBAAA,CAA+F;QAAhDe,GAAG,EAAEU,KAAK;QAAGC,GAAG,EAAEF,KAAK;QAAGG,GAAG,EAAEb,OAAO,CAACI;;2EAExFhB,mBAAA,CAAgE,KAAhE0B,UAAgE,EAAAX,gBAAA,CAArCZ,IAAA,CAAAwB,UAAU,CAACf,OAAO,CAACgB,UAAU,kB;uCAK9D3B,mBAAA,WAAc,E,+1BA2BdA,mBAAA,UAAa,EACbD,mBAAA,CASM,OATN6B,WASM,G,0BARJ7B,mBAAA,CAAa,YAAT,MAAI,qBACRA,mBAAA,CAMM,OANN8B,WAMM,G,0BALJ9B,mBAAA,CAAuC,WAApC,kCAAgC,qBACnCA,mBAAA,CAGM,OAHN+B,WAGM,GAFJC,YAAA,CAAsEC,sBAAA;IAAzDC,EAAE,EAAC,WAAW;IAACrC,KAAK,EAAC;;sBAAkB,MAAIsC,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;gCACxDnC,mBAAA,CAAyG;IAAtGoC,IAAI,EAAC,6CAA6C;IAACC,MAAM,EAAC,QAAQ;IAACxC,KAAK,EAAC;KAAoB,OAAK,oB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}