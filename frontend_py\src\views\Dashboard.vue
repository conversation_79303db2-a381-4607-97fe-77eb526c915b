<template>
  <div class="dashboard">
    <!-- 用户信息头部 -->
    <div class="dashboard-header">
      <div class="user-welcome">
        <h1>欢迎回来，{{ currentUser?.name || currentUser?.username }}！</h1>
        <p class="user-role">{{ currentUser?.role === 'admin' ? '管理员' : '社团成员' }}</p>
      </div>
      <div class="header-actions">
        <button @click="logout" class="btn btn-secondary">退出登录</button>
      </div>
    </div>

    <!-- 快速导航 -->
    <div class="quick-nav">
      <h2>快速导航</h2>
      <div class="nav-grid">
        <router-link to="/club-culture" class="nav-card">
          <div class="nav-icon">🏛️</div>
          <h3>社团文化</h3>
          <p>了解社团历史与文化</p>
        </router-link>
        <router-link to="/learning-resources" class="nav-card">
          <div class="nav-icon">📚</div>
          <h3>学习资源</h3>
          <p>获取学习材料和资源</p>
        </router-link>
        <router-link to="/past-activities" class="nav-card">
          <div class="nav-icon">🎯</div>
          <h3>过往活动</h3>
          <p>查看历史活动记录</p>
        </router-link>
        <router-link to="/cyber-security-news" class="nav-card">
          <div class="nav-icon">🔒</div>
          <h3>安全资讯</h3>
          <p>最新网络安全新闻</p>
        </router-link>
        <router-link to="/playground" class="nav-card">
          <div class="nav-icon">🎮</div>
          <h3>练习场</h3>
          <p>技能练习与挑战</p>
        </router-link>
        <router-link v-if="currentUser?.role === 'admin'" to="/admin" class="nav-card admin-card">
          <div class="nav-icon">⚙️</div>
          <h3>管理后台</h3>
          <p>系统管理与配置</p>
        </router-link>
      </div>
    </div>

    <!-- 最新公告 -->
    <div class="dashboard-section">
      <h2>最新公告</h2>
      <div class="announcements-container">
        <div v-if="loading" class="loading">加载中...</div>
        <div v-else-if="announcements.length === 0" class="no-data">暂无公告</div>
        <div v-else class="announcements-list">
          <div v-for="announcement in announcements.slice(0, 3)" :key="announcement.id" class="announcement-item">
            <h3>{{ announcement.title }}</h3>
            <p class="announcement-content" v-html="purifyContent(announcement.content)"></p>
            <p class="announcement-date">{{ formatDate(announcement.created_at) }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 监控数据大屏预览 -->
    <div class="dashboard-section">
      <h2>监控数据大屏</h2>
      <div class="monitoring-preview">
        <div class="monitoring-card">
          <h3>🚨 安全告警</h3>
          <div class="metric-value">{{ securityAlerts }}</div>
          <p>本月检测到的安全事件</p>
        </div>
        <div class="monitoring-card">
          <h3>🔍 漏洞扫描</h3>
          <div class="metric-value">{{ vulnerabilities }}</div>
          <p>发现的潜在漏洞</p>
        </div>
        <div class="monitoring-card">
          <h3>👥 在线用户</h3>
          <div class="metric-value">{{ onlineUsers }}</div>
          <p>当前活跃用户数</p>
        </div>
        <div class="monitoring-card">
          <h3>📊 系统状态</h3>
          <div class="metric-value status-good">正常</div>
          <p>系统运行状态</p>
        </div>
      </div>
      <div class="monitoring-note">
        <p>💡 完整的监控数据大屏功能正在开发中，将用于记录靶场漏洞告警信息，敬请期待！</p>
      </div>
    </div>

    <!-- 个人信息 -->
    <div class="dashboard-section">
      <h2>个人信息</h2>
      <div class="user-info-card">
        <div class="info-row">
          <span class="info-label">用户名：</span>
          <span class="info-value">{{ currentUser?.username }}</span>
        </div>
        <div class="info-row">
          <span class="info-label">姓名：</span>
          <span class="info-value">{{ currentUser?.name || '未设置' }}</span>
        </div>
        <div class="info-row">
          <span class="info-label">学号：</span>
          <span class="info-value">{{ currentUser?.student_id || '未设置' }}</span>
        </div>
        <div class="info-row">
          <span class="info-label">角色：</span>
          <span class="info-value">{{ currentUser?.role === 'admin' ? '管理员' : '普通用户' }}</span>
        </div>
        <div class="info-row">
          <span class="info-label">加入时间：</span>
          <span class="info-value">{{ formatDate(currentUser?.created_at) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DOMPurify from 'dompurify'
import { authAPI, announcementAPI } from '../services/api'

export default {
  name: 'Dashboard',
  data() {
    return {
      currentUser: null,
      announcements: [],
      loading: false,

      // 模拟监控数据
      securityAlerts: 12,
      vulnerabilities: 5,
      onlineUsers: 23,
    }
  },
  async mounted() {
    await this.loadCurrentUser()
    await this.loadAnnouncements()
    this.startMonitoringSimulation()
  },
  methods: {
    async loadCurrentUser() {
      try {
        const response = await authAPI.checkAuth()
        this.currentUser = response.data.user
      } catch (error) {
        console.error('获取用户信息失败:', error)
        this.$router.push('/login')
      }
    },

    async loadAnnouncements() {
      this.loading = true
      try {
        const response = await announcementAPI.getAnnouncements()
        this.announcements = response.data
      } catch (error) {
        console.error('加载公告失败:', error)
        // 如果API调用失败，显示示例数据
        this.announcements = [
          {
            id: 1,
            title: '社团招新公告',
            content: '<p>欢迎参加网络信息安全社团的招新活动！</p>',
            created_at: new Date().toISOString()
          }
        ]
      } finally {
        this.loading = false
      }
    },

    async logout() {
      try {
        await authAPI.logout()
        this.$router.push('/login')
      } catch (error) {
        console.error('登出失败:', error)
      }
    },

    purifyContent(content) {
      return DOMPurify.sanitize(content)
    },

    formatDate(dateString) {
      if (!dateString) return '未知'
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    },

    startMonitoringSimulation() {
      // 模拟监控数据变化
      setInterval(() => {
        this.securityAlerts = Math.floor(Math.random() * 20) + 10
        this.vulnerabilities = Math.floor(Math.random() * 10) + 3
        this.onlineUsers = Math.floor(Math.random() * 50) + 15
      }, 30000) // 每30秒更新一次
    }
  }
}
</script>

<style scoped>
.dashboard {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
}

.user-welcome h1 {
  margin: 0 0 5px 0;
  font-size: 2rem;
}

.user-role {
  margin: 0;
  opacity: 0.9;
  font-size: 1.1rem;
}

.header-actions .btn {
  padding: 10px 20px;
  border: 2px solid white;
  background: transparent;
  color: white;
  border-radius: 6px;
  text-decoration: none;
  font-weight: bold;
  transition: all 0.3s ease;
}

.header-actions .btn:hover {
  background: white;
  color: #667eea;
}

.dashboard-section {
  margin-bottom: 40px;
}

.dashboard-section h2 {
  font-size: 1.8rem;
  margin-bottom: 20px;
  color: #333;
  border-bottom: 3px solid #42b983;
  padding-bottom: 10px;
}

.quick-nav .nav-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.nav-card {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  text-decoration: none;
  color: inherit;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.nav-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
  border-color: #42b983;
}

.nav-card.admin-card {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: white;
}

.nav-card.admin-card:hover {
  border-color: #ff6b6b;
}

.nav-icon {
  font-size: 2.5rem;
  margin-bottom: 15px;
  text-align: center;
}

.nav-card h3 {
  margin: 0 0 10px 0;
  font-size: 1.3rem;
  text-align: center;
}

.nav-card p {
  margin: 0;
  text-align: center;
  opacity: 0.8;
}

.announcements-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.announcements-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.announcement-item {
  padding: 15px;
  border-left: 4px solid #42b983;
  background: #f8f9fa;
  border-radius: 0 8px 8px 0;
}

.announcement-item h3 {
  margin: 0 0 10px 0;
  color: #333;
}

.announcement-content {
  color: #666;
  line-height: 1.6;
  margin-bottom: 10px;
}

.announcement-date {
  color: #999;
  font-size: 0.9rem;
  text-align: right;
  margin: 0;
}

.monitoring-preview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.monitoring-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.monitoring-card h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 1.1rem;
}

.metric-value {
  font-size: 2.5rem;
  font-weight: bold;
  color: #42b983;
  margin-bottom: 10px;
}

.metric-value.status-good {
  color: #28a745;
  font-size: 1.5rem;
}

.monitoring-card p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.monitoring-note {
  background: #e3f2fd;
  padding: 15px;
  border-radius: 8px;
  border-left: 4px solid #2196f3;
}

.monitoring-note p {
  margin: 0;
  color: #1976d2;
}

.user-info-card {
  background: white;
  padding: 25px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  font-weight: bold;
  color: #333;
}

.info-value {
  color: #666;
}

.loading, .no-data {
  text-align: center;
  padding: 40px;
  color: #666;
}

@media (max-width: 768px) {
  .dashboard {
    padding: 10px;
  }

  .dashboard-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .dashboard-header h1 {
    font-size: 1.5rem;
  }

  .nav-grid {
    grid-template-columns: 1fr;
  }

  .monitoring-preview {
    grid-template-columns: repeat(2, 1fr);
  }

  .info-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
}
</style>