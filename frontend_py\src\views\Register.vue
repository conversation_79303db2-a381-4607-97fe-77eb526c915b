<template>
  <div class="register">
    <h1>用户注册</h1>
    <form @submit.prevent="register">
      <div class="form-group">
        <label for="username">用户名:</label>
        <input type="text" id="username" v-model="form.username" required>
      </div>
      <div class="form-group">
        <label for="password">密码:</label>
        <input type="password" id="password" v-model="form.password" required>
        <small>密码需包含大小写字母、数字和特殊字符，至少8位</small>
      </div>
      <div class="form-group">
        <label for="confirmPassword">确认密码:</label>
        <input type="password" id="confirmPassword" v-model="confirmPassword" required>
      </div>
      <div class="form-group">
        <label for="student_id">学号:</label>
        <input type="text" id="student_id" v-model="form.student_id">
      </div>
      <div class="form-group">
        <label for="name">姓名:</label>
        <input type="text" id="name" v-model="form.name">
      </div>
      <button type="submit" :disabled="loading">
        {{ loading ? '注册中...' : '注册' }}
      </button>
    </form>
    <p v-if="error" class="error-message">{{ error }}</p>
    <p v-if="success" class="success-message">{{ success }}</p>
    
    <div class="login-link">
      <p>已有账户？<router-link to="/login">立即登录</router-link></p>
    </div>
  </div>
</template>

<script>
import { authAPI } from '../services/api'

export default {
  name: 'Register',
  data() {
    return {
      form: {
        username: '',
        password: '',
        student_id: '',
        name: ''
      },
      confirmPassword: '',
      error: null,
      success: null,
      loading: false
    }
  },
  methods: {
    async register() {
      this.error = null
      this.success = null
      this.loading = true

      // 前端验证
      if (!this.form.username) {
        this.error = '用户名不能为空'
        this.loading = false
        return
      }
      if (!this.form.password) {
        this.error = '密码不能为空'
        this.loading = false
        return
      }
      if (this.form.password !== this.confirmPassword) {
        this.error = '两次输入的密码不一致'
        this.loading = false
        return
      }

      try {
        await authAPI.register(this.form)
        this.success = '注册成功！请登录'
        
        // 3秒后跳转到登录页
        setTimeout(() => {
          this.$router.push('/login')
        }, 3000)
      } catch (err) {
        this.error = err.response?.data?.msg || '注册失败，请稍后重试'
        console.error(err)
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.register {
  padding: 20px;
  max-width: 400px;
  margin: 50px auto;
  border: 1px solid #ccc;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 15px;
  text-align: left;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-sizing: border-box;
}

.form-group small {
  color: #666;
  font-size: 12px;
  margin-top: 5px;
  display: block;
}

button {
  background-color: #42b983;
  color: white;
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  width: 100%;
}

button:hover:not(:disabled) {
  background-color: #369f6e;
}

button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.error-message {
  color: red;
  margin-top: 10px;
}

.success-message {
  color: green;
  margin-top: 10px;
}

.login-link {
  margin-top: 20px;
  text-align: center;
}

.login-link a {
  color: #42b983;
  text-decoration: none;
}

.login-link a:hover {
  text-decoration: underline;
}
</style>
