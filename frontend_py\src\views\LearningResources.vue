<template>
  <div class="learning-resources">
    <h1>学习资源</h1>
    <p>这里提供与网络信息安全相关的学习资料，如组网技术、网络攻防，安全科普等。</p>

    <div v-if="loading" class="loading">
      <p>加载中...</p>
    </div>

    <div v-else-if="error" class="error">
      <p>{{ error }}</p>
      <button @click="loadResources" class="btn btn-primary">重试</button>
    </div>

    <div v-else class="resources-list">
      <div v-if="resources.length === 0" class="no-data">
        <p>暂无学习资源</p>
      </div>
      <div v-else>
        <div v-for="resource in resources" :key="resource.id" class="resource-item">
          <h3>{{ resource.title }}</h3>
          <div v-html="purifyContent(resource.content)"></div>
          <div v-if="resource.file_path" class="file-link">
            <a :href="resource.file_path" target="_blank" class="btn btn-sm btn-outline">下载文件</a>
          </div>
          <div class="resource-meta">
            <small>创建时间: {{ formatDate(resource.created_at) }}</small>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DOMPurify from 'dompurify'
import { learningResourceAPI } from '../services/api'

export default {
  name: 'LearningResources',
  data() {
    return {
      resources: [],
      loading: false,
      error: null
    }
  },
  async mounted() {
    await this.loadResources()
  },
  methods: {
    async loadResources() {
      this.loading = true
      this.error = null
      try {
        const response = await learningResourceAPI.getLearningResources()
        this.resources = response.data
      } catch (error) {
        console.error('加载学习资源失败:', error)
        this.error = '加载学习资源失败，请稍后重试'
      } finally {
        this.loading = false
      }
    },

    purifyContent(content) {
      return DOMPurify.sanitize(content)
    },

    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    }
  }
}
</script>

<style scoped>
.learning-resources {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.loading, .error, .no-data {
  text-align: center;
  padding: 40px;
  color: #666;
}

.error {
  color: #e74c3c;
}

.resources-list {
  margin-top: 20px;
}

.resource-item {
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.resource-item h3 {
  color: #2c3e50;
  margin-bottom: 15px;
  border-bottom: 2px solid #3498db;
  padding-bottom: 10px;
}

.file-link {
  margin: 15px 0;
}

.resource-meta {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #eee;
  color: #666;
}

.btn {
  display: inline-block;
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  text-decoration: none;
  cursor: pointer;
  font-size: 14px;
}

.btn-primary {
  background-color: #3498db;
  color: white;
}

.btn-primary:hover {
  background-color: #2980b9;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

.btn-outline {
  background-color: transparent;
  color: #3498db;
  border: 1px solid #3498db;
}

.btn-outline:hover {
  background-color: #3498db;
  color: white;
}
</style>