<template>
  <div class="learning-resources">
    <h1>学习资源</h1>
    <p>这里提供与网络信息安全相关的学习资料，如组网技术、网络攻防，安全科普等。</p>
    <div class="resources-list">
      <div v-for="resource in resources" :key="resource.id" class="resource-item">
        <h3>{{ resource.title }}</h3>
        <div v-html="purifyContent(resource.content)"></div>
      </div>
    </div>
  </div>
</template>

<script>
import DOMPurify from 'dompurify'; // 引入 DOMPurify

export default {
  name: 'LearningResources',
  data() {
    return {
      // 示例：学习资源列表，实际应从后端获取
      resources: [
        { id: 1, title: '网络攻防基础', content: '<p>学习网络攻防的**基础知识**。</p><img src="invalid.png" onerror="alert(\'XSS in resource!\')">' },
        { id: 2, title: 'Python 安全编程', content: '<p>Python 在安全领域的应用。</p>' },
      ],
    };
  },
  methods: {
    purifyContent(content) {
      return DOMPurify.sanitize(content);
    },
  },
}
</script>

<style scoped>
.learning-resources {
  padding: 20px;
}
</style>