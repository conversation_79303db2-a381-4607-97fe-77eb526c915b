{"ast": null, "code": "export default {\n  name: 'Admin'\n};", "map": {"version": 3, "names": ["name"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Admin.vue"], "sourcesContent": ["<template>\r\n  <div class=\"admin\">\r\n    <h1>管理员后台</h1>\r\n    <p>这里是管理员进行用户管理、内容发布等操作的界面。</p>\r\n    <!-- 这里将添加管理员功能，如用户导入、内容管理等 -->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'Admin',\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.admin {\r\n  padding: 20px;\r\n}\r\n</style>"], "mappings": "AASA,eAAe;EACbA,IAAI,EAAE;AACR", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}