{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, openBlock as _openBlock, createElement<PERSON>lock as _createElementBlock, renderList as _renderList, Fragment as _Fragment, toDisplayString as _toDisplayString, createStaticVNode as _createStaticVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"home\"\n};\nconst _hoisted_2 = {\n  class: \"hero-section\"\n};\nconst _hoisted_3 = {\n  class: \"hero-buttons\"\n};\nconst _hoisted_4 = {\n  class: \"section\"\n};\nconst _hoisted_5 = {\n  class: \"announcements\"\n};\nconst _hoisted_6 = {\n  key: 0,\n  class: \"loading\"\n};\nconst _hoisted_7 = {\n  key: 1,\n  class: \"no-data\"\n};\nconst _hoisted_8 = {\n  key: 2\n};\nconst _hoisted_9 = [\"innerHTML\"];\nconst _hoisted_10 = {\n  class: \"announcement-date\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_router_link = _resolveComponent(\"router-link\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 头部横幅 \"), _createElementVNode(\"div\", _hoisted_2, [_cache[2] || (_cache[2] = _createElementVNode(\"h1\", null, \"网络信息安全社团\", -1 /* CACHED */)), _cache[3] || (_cache[3] = _createElementVNode(\"p\", {\n    class: \"hero-subtitle\"\n  }, \"成都工业职业技术学院（金堂校区）\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_router_link, {\n    to: \"/login\",\n    class: \"btn btn-primary\"\n  }, {\n    default: _withCtx(() => _cache[0] || (_cache[0] = [_createTextVNode(\"立即登录\")])),\n    _: 1 /* STABLE */,\n    __: [0]\n  }), _createVNode(_component_router_link, {\n    to: \"/register\",\n    class: \"btn btn-secondary\"\n  }, {\n    default: _withCtx(() => _cache[1] || (_cache[1] = [_createTextVNode(\"注册账户\")])),\n    _: 1 /* STABLE */,\n    __: [1]\n  })])]), _createCommentVNode(\" 社团介绍 \"), _cache[5] || (_cache[5] = _createStaticVNode(\"<div class=\\\"section\\\" data-v-fae5bece><h2 data-v-fae5bece>社团介绍</h2><div class=\\\"intro-grid\\\" data-v-fae5bece><div class=\\\"intro-card\\\" data-v-fae5bece><h3 data-v-fae5bece>🛡️ 网络安全</h3><p data-v-fae5bece>学习网络安全知识，掌握防护技能</p></div><div class=\\\"intro-card\\\" data-v-fae5bece><h3 data-v-fae5bece>🔧 技术实践</h3><p data-v-fae5bece>动手实践，提升技术能力</p></div><div class=\\\"intro-card\\\" data-v-fae5bece><h3 data-v-fae5bece>👥 团队协作</h3><p data-v-fae5bece>与志同道合的伙伴一起成长</p></div></div></div>\", 1)), _createCommentVNode(\" 最新公告 \"), _createElementVNode(\"div\", _hoisted_4, [_cache[4] || (_cache[4] = _createElementVNode(\"h2\", null, \"最新公告\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_5, [_ctx.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, \"加载中...\")) : _ctx.announcements.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, \"暂无公告\")) : (_openBlock(), _createElementBlock(\"div\", _hoisted_8, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.announcements.slice(0, 3), announcement => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: announcement.id,\n      class: \"announcement-card\"\n    }, [_createElementVNode(\"h3\", null, _toDisplayString(announcement.title), 1 /* TEXT */), _createElementVNode(\"p\", {\n      class: \"announcement-content\",\n      innerHTML: _ctx.purifyContent(announcement.content)\n    }, null, 8 /* PROPS */, _hoisted_9), _createElementVNode(\"p\", _hoisted_10, _toDisplayString(_ctx.formatDate(announcement.created_at)), 1 /* TEXT */)]);\n  }), 128 /* KEYED_FRAGMENT */))]))])]), _createCommentVNode(\" 联系信息 \"), _cache[6] || (_cache[6] = _createStaticVNode(\"<div class=\\\"section contact-section\\\" data-v-fae5bece><h2 data-v-fae5bece>联系我们</h2><div class=\\\"contact-info\\\" data-v-fae5bece><p data-v-fae5bece><strong data-v-fae5bece>学校：</strong>成都工业职业技术学院（金堂校区）</p><p data-v-fae5bece><strong data-v-fae5bece>社团群号：</strong>242050951</p><p data-v-fae5bece><strong data-v-fae5bece>抖音：</strong>21647629167</p><p data-v-fae5bece><strong data-v-fae5bece>主要活动：</strong>组网技术，网络攻防，安全科普</p></div></div>\", 1))]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_createVNode", "_component_router_link", "to", "_cache", "_hoisted_4", "_hoisted_5", "_ctx", "loading", "_hoisted_6", "announcements", "length", "_hoisted_7", "_hoisted_8", "_Fragment", "_renderList", "slice", "announcement", "key", "id", "_toDisplayString", "title", "innerHTML", "purifyContent", "content", "_hoisted_10", "formatDate", "created_at"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Home.vue"], "sourcesContent": ["<template>\r\n  <div class=\"home\">\r\n    <!-- 头部横幅 -->\r\n    <div class=\"hero-section\">\r\n      <h1>网络信息安全社团</h1>\r\n      <p class=\"hero-subtitle\">成都工业职业技术学院（金堂校区）</p>\r\n      <div class=\"hero-buttons\">\r\n        <router-link to=\"/login\" class=\"btn btn-primary\">立即登录</router-link>\r\n        <router-link to=\"/register\" class=\"btn btn-secondary\">注册账户</router-link>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 社团介绍 -->\r\n    <div class=\"section\">\r\n      <h2>社团介绍</h2>\r\n      <div class=\"intro-grid\">\r\n        <div class=\"intro-card\">\r\n          <h3>🛡️ 网络安全</h3>\r\n          <p>学习网络安全知识，掌握防护技能</p>\r\n        </div>\r\n        <div class=\"intro-card\">\r\n          <h3>🔧 技术实践</h3>\r\n          <p>动手实践，提升技术能力</p>\r\n        </div>\r\n        <div class=\"intro-card\">\r\n          <h3>👥 团队协作</h3>\r\n          <p>与志同道合的伙伴一起成长</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 最新公告 -->\r\n    <div class=\"section\">\r\n      <h2>最新公告</h2>\r\n      <div class=\"announcements\">\r\n        <div v-if=\"loading\" class=\"loading\">加载中...</div>\r\n        <div v-else-if=\"announcements.length === 0\" class=\"no-data\">暂无公告</div>\r\n        <div v-else>\r\n          <div v-for=\"announcement in announcements.slice(0, 3)\" :key=\"announcement.id\" class=\"announcement-card\">\r\n            <h3>{{ announcement.title }}</h3>\r\n            <p class=\"announcement-content\" v-html=\"purifyContent(announcement.content)\"></p>\r\n            <p class=\"announcement-date\">{{ formatDate(announcement.created_at) }}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 联系信息 -->\r\n    <div class=\"section contact-section\">\r\n      <h2>联系我们</h2>\r\n      <div class=\"contact-info\">\r\n        <p><strong>学校：</strong>成都工业职业技术学院（金堂校区）</p>\r\n        <p><strong>社团群号：</strong>242050951</p>\r\n        <p><strong>抖音：</strong>21647629167</p>\r\n        <p><strong>主要活动：</strong>组网技术，网络攻防，安全科普</p>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport DOMPurify from 'dompurify'; // 引入 DOMPurify\r\n\r\nexport default {\r\n  name: 'Home',\r\n  data() {\r\n    return {\r\n      // 示例公告内容，实际应从后端获取\r\n      announcementContent: '<p>欢迎参加**网络信息安全社团**的招新活动！</p><script>alert(\"XSS Attack!\");<\\/script>',\r\n    };\r\n  },\r\n  computed: {\r\n    purifiedAnnouncementContent() {\r\n      // 使用 DOMPurify 清理公告内容\r\n      return DOMPurify.sanitize(this.announcementContent);\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.home {\r\n  padding: 20px;\r\n}\r\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAM;;EAEVA,KAAK,EAAC;AAAc;;EAGlBA,KAAK,EAAC;AAAc;;EA0BtBA,KAAK,EAAC;AAAS;;EAEbA,KAAK,EAAC;AAAe;;;EACJA,KAAK,EAAC;;;;EACkBA,KAAK,EAAC;;;;;;;EAK3CA,KAAK,EAAC;AAAmB;;;uBAxCtCC,mBAAA,CAwDM,OAxDNC,UAwDM,GAvDJC,mBAAA,UAAa,EACbC,mBAAA,CAOM,OAPNC,UAOM,G,0BANJD,mBAAA,CAAiB,YAAb,UAAQ,qB,0BACZA,mBAAA,CAA6C;IAA1CJ,KAAK,EAAC;EAAe,GAAC,kBAAgB,qBACzCI,mBAAA,CAGM,OAHNE,UAGM,GAFJC,YAAA,CAAmEC,sBAAA;IAAtDC,EAAE,EAAC,QAAQ;IAACT,KAAK,EAAC;;sBAAkB,MAAIU,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;MACrDH,YAAA,CAAwEC,sBAAA;IAA3DC,EAAE,EAAC,WAAW;IAACT,KAAK,EAAC;;sBAAoB,MAAIU,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;UAI9DP,mBAAA,UAAa,E,4gBAmBbA,mBAAA,UAAa,EACbC,mBAAA,CAaM,OAbNO,UAaM,G,0BAZJP,mBAAA,CAAa,YAAT,MAAI,qBACRA,mBAAA,CAUM,OAVNQ,UAUM,GATOC,IAAA,CAAAC,OAAO,I,cAAlBb,mBAAA,CAAgD,OAAhDc,UAAgD,EAAZ,QAAM,KAC1BF,IAAA,CAAAG,aAAa,CAACC,MAAM,U,cAApChB,mBAAA,CAAsE,OAAtEiB,UAAsE,EAAV,MAAI,M,cAChEjB,mBAAA,CAMM,OAAAkB,UAAA,I,kBALJlB,mBAAA,CAIMmB,SAAA,QAAAC,WAAA,CAJsBR,IAAA,CAAAG,aAAa,CAACM,KAAK,QAAnCC,YAAY;yBAAxBtB,mBAAA,CAIM;MAJkDuB,GAAG,EAAED,YAAY,CAACE,EAAE;MAAEzB,KAAK,EAAC;QAClFI,mBAAA,CAAiC,YAAAsB,gBAAA,CAA1BH,YAAY,CAACI,KAAK,kBACzBvB,mBAAA,CAAiF;MAA9EJ,KAAK,EAAC,sBAAsB;MAAC4B,SAA4C,EAApCf,IAAA,CAAAgB,aAAa,CAACN,YAAY,CAACO,OAAO;yCAC1E1B,mBAAA,CAA0E,KAA1E2B,WAA0E,EAAAL,gBAAA,CAA1Cb,IAAA,CAAAmB,UAAU,CAACT,YAAY,CAACU,UAAU,kB;yCAM1E9B,mBAAA,UAAa,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}