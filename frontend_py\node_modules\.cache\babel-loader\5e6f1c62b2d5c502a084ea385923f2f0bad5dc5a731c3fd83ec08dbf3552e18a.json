{"ast": null, "code": "export default {\n  name: 'App'\n};", "map": {"version": 3, "names": ["name"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\App.vue"], "sourcesContent": ["<template>\n  <div id=\"app\">\n    <nav>\n      <router-link to=\"/\">首页</router-link> |\n      <router-link to=\"/club-culture\">社团文化</router-link> |\n      <router-link to=\"/learning-resources\">学习资源</router-link> |\n      <router-link to=\"/past-activities\">往期活动回顾</router-link> |\n      <router-link to=\"/cyber-security-news\">网安资讯</router-link> |\n      <router-link to=\"/login\">登录</router-link> |\n      <router-link to=\"/admin\">管理员后台</router-link> |\n      <router-link to=\"/playground\">实操靶场</router-link> |\n      <router-link to=\"/dashboard\">监控数据大屏</router-link>\n    </nav>\n    <router-view/>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'App',\n}\n</script>\n\n<style>\n#app {\n  font-family: Avenir, Helvetica, Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  text-align: center;\n  color: #2c3e50;\n}\n\nnav {\n  padding: 30px;\n}\n\nnav a {\n  font-weight: bold;\n  color: #2c3e50;\n}\n\nnav a.router-link-exact-active {\n  color: #42b983;\n}\n</style>\n"], "mappings": "AAkBA,eAAe;EACbA,IAAI,EAAE;AACR", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}