{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*! @license DOMPurify 3.2.6 | (c) Cure<PERSON> and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.6/LICENSE */\n\nconst {\n  entries,\n  setPrototypeOf,\n  isFrozen,\n  getPrototypeOf,\n  getOwnPropertyDescriptor\n} = Object;\nlet {\n  freeze,\n  seal,\n  create\n} = Object; // eslint-disable-line import/no-mutable-exports\nlet {\n  apply,\n  construct\n} = typeof Reflect !== 'undefined' && Reflect;\nif (!freeze) {\n  freeze = function freeze(x) {\n    return x;\n  };\n}\nif (!seal) {\n  seal = function seal(x) {\n    return x;\n  };\n}\nif (!apply) {\n  apply = function apply(fun, thisValue, args) {\n    return fun.apply(thisValue, args);\n  };\n}\nif (!construct) {\n  construct = function construct(Func, args) {\n    return new Func(...args);\n  };\n}\nconst arrayForEach = unapply(Array.prototype.forEach);\nconst arrayLastIndexOf = unapply(Array.prototype.lastIndexOf);\nconst arrayPop = unapply(Array.prototype.pop);\nconst arrayPush = unapply(Array.prototype.push);\nconst arraySplice = unapply(Array.prototype.splice);\nconst stringToLowerCase = unapply(String.prototype.toLowerCase);\nconst stringToString = unapply(String.prototype.toString);\nconst stringMatch = unapply(String.prototype.match);\nconst stringReplace = unapply(String.prototype.replace);\nconst stringIndexOf = unapply(String.prototype.indexOf);\nconst stringTrim = unapply(String.prototype.trim);\nconst objectHasOwnProperty = unapply(Object.prototype.hasOwnProperty);\nconst regExpTest = unapply(RegExp.prototype.test);\nconst typeErrorCreate = unconstruct(TypeError);\n/**\n * Creates a new function that calls the given function with a specified thisArg and arguments.\n *\n * @param func - The function to be wrapped and called.\n * @returns A new function that calls the given function with a specified thisArg and arguments.\n */\nfunction unapply(func) {\n  return function (thisArg) {\n    if (thisArg instanceof RegExp) {\n      thisArg.lastIndex = 0;\n    }\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    return apply(func, thisArg, args);\n  };\n}\n/**\n * Creates a new function that constructs an instance of the given constructor function with the provided arguments.\n *\n * @param func - The constructor function to be wrapped and called.\n * @returns A new function that constructs an instance of the given constructor function with the provided arguments.\n */\nfunction unconstruct(func) {\n  return function () {\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    return construct(func, args);\n  };\n}\n/**\n * Add properties to a lookup table\n *\n * @param set - The set to which elements will be added.\n * @param array - The array containing elements to be added to the set.\n * @param transformCaseFunc - An optional function to transform the case of each element before adding to the set.\n * @returns The modified set with added elements.\n */\nfunction addToSet(set, array) {\n  let transformCaseFunc = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : stringToLowerCase;\n  if (setPrototypeOf) {\n    // Make 'in' and truthy checks like Boolean(set.constructor)\n    // independent of any properties defined on Object.prototype.\n    // Prevent prototype setters from intercepting set as a this value.\n    setPrototypeOf(set, null);\n  }\n  let l = array.length;\n  while (l--) {\n    let element = array[l];\n    if (typeof element === 'string') {\n      const lcElement = transformCaseFunc(element);\n      if (lcElement !== element) {\n        // Config presets (e.g. tags.js, attrs.js) are immutable.\n        if (!isFrozen(array)) {\n          array[l] = lcElement;\n        }\n        element = lcElement;\n      }\n    }\n    set[element] = true;\n  }\n  return set;\n}\n/**\n * Clean up an array to harden against CSPP\n *\n * @param array - The array to be cleaned.\n * @returns The cleaned version of the array\n */\nfunction cleanArray(array) {\n  for (let index = 0; index < array.length; index++) {\n    const isPropertyExist = objectHasOwnProperty(array, index);\n    if (!isPropertyExist) {\n      array[index] = null;\n    }\n  }\n  return array;\n}\n/**\n * Shallow clone an object\n *\n * @param object - The object to be cloned.\n * @returns A new object that copies the original.\n */\nfunction clone(object) {\n  const newObject = create(null);\n  for (const [property, value] of entries(object)) {\n    const isPropertyExist = objectHasOwnProperty(object, property);\n    if (isPropertyExist) {\n      if (Array.isArray(value)) {\n        newObject[property] = cleanArray(value);\n      } else if (value && typeof value === 'object' && value.constructor === Object) {\n        newObject[property] = clone(value);\n      } else {\n        newObject[property] = value;\n      }\n    }\n  }\n  return newObject;\n}\n/**\n * This method automatically checks if the prop is function or getter and behaves accordingly.\n *\n * @param object - The object to look up the getter function in its prototype chain.\n * @param prop - The property name for which to find the getter function.\n * @returns The getter function found in the prototype chain or a fallback function.\n */\nfunction lookupGetter(object, prop) {\n  while (object !== null) {\n    const desc = getOwnPropertyDescriptor(object, prop);\n    if (desc) {\n      if (desc.get) {\n        return unapply(desc.get);\n      }\n      if (typeof desc.value === 'function') {\n        return unapply(desc.value);\n      }\n    }\n    object = getPrototypeOf(object);\n  }\n  function fallbackValue() {\n    return null;\n  }\n  return fallbackValue;\n}\nconst html$1 = freeze(['a', 'abbr', 'acronym', 'address', 'area', 'article', 'aside', 'audio', 'b', 'bdi', 'bdo', 'big', 'blink', 'blockquote', 'body', 'br', 'button', 'canvas', 'caption', 'center', 'cite', 'code', 'col', 'colgroup', 'content', 'data', 'datalist', 'dd', 'decorator', 'del', 'details', 'dfn', 'dialog', 'dir', 'div', 'dl', 'dt', 'element', 'em', 'fieldset', 'figcaption', 'figure', 'font', 'footer', 'form', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'head', 'header', 'hgroup', 'hr', 'html', 'i', 'img', 'input', 'ins', 'kbd', 'label', 'legend', 'li', 'main', 'map', 'mark', 'marquee', 'menu', 'menuitem', 'meter', 'nav', 'nobr', 'ol', 'optgroup', 'option', 'output', 'p', 'picture', 'pre', 'progress', 'q', 'rp', 'rt', 'ruby', 's', 'samp', 'section', 'select', 'shadow', 'small', 'source', 'spacer', 'span', 'strike', 'strong', 'style', 'sub', 'summary', 'sup', 'table', 'tbody', 'td', 'template', 'textarea', 'tfoot', 'th', 'thead', 'time', 'tr', 'track', 'tt', 'u', 'ul', 'var', 'video', 'wbr']);\nconst svg$1 = freeze(['svg', 'a', 'altglyph', 'altglyphdef', 'altglyphitem', 'animatecolor', 'animatemotion', 'animatetransform', 'circle', 'clippath', 'defs', 'desc', 'ellipse', 'filter', 'font', 'g', 'glyph', 'glyphref', 'hkern', 'image', 'line', 'lineargradient', 'marker', 'mask', 'metadata', 'mpath', 'path', 'pattern', 'polygon', 'polyline', 'radialgradient', 'rect', 'stop', 'style', 'switch', 'symbol', 'text', 'textpath', 'title', 'tref', 'tspan', 'view', 'vkern']);\nconst svgFilters = freeze(['feBlend', 'feColorMatrix', 'feComponentTransfer', 'feComposite', 'feConvolveMatrix', 'feDiffuseLighting', 'feDisplacementMap', 'feDistantLight', 'feDropShadow', 'feFlood', 'feFuncA', 'feFuncB', 'feFuncG', 'feFuncR', 'feGaussianBlur', 'feImage', 'feMerge', 'feMergeNode', 'feMorphology', 'feOffset', 'fePointLight', 'feSpecularLighting', 'feSpotLight', 'feTile', 'feTurbulence']);\n// List of SVG elements that are disallowed by default.\n// We still need to know them so that we can do namespace\n// checks properly in case one wants to add them to\n// allow-list.\nconst svgDisallowed = freeze(['animate', 'color-profile', 'cursor', 'discard', 'font-face', 'font-face-format', 'font-face-name', 'font-face-src', 'font-face-uri', 'foreignobject', 'hatch', 'hatchpath', 'mesh', 'meshgradient', 'meshpatch', 'meshrow', 'missing-glyph', 'script', 'set', 'solidcolor', 'unknown', 'use']);\nconst mathMl$1 = freeze(['math', 'menclose', 'merror', 'mfenced', 'mfrac', 'mglyph', 'mi', 'mlabeledtr', 'mmultiscripts', 'mn', 'mo', 'mover', 'mpadded', 'mphantom', 'mroot', 'mrow', 'ms', 'mspace', 'msqrt', 'mstyle', 'msub', 'msup', 'msubsup', 'mtable', 'mtd', 'mtext', 'mtr', 'munder', 'munderover', 'mprescripts']);\n// Similarly to SVG, we want to know all MathML elements,\n// even those that we disallow by default.\nconst mathMlDisallowed = freeze(['maction', 'maligngroup', 'malignmark', 'mlongdiv', 'mscarries', 'mscarry', 'msgroup', 'mstack', 'msline', 'msrow', 'semantics', 'annotation', 'annotation-xml', 'mprescripts', 'none']);\nconst text = freeze(['#text']);\nconst html = freeze(['accept', 'action', 'align', 'alt', 'autocapitalize', 'autocomplete', 'autopictureinpicture', 'autoplay', 'background', 'bgcolor', 'border', 'capture', 'cellpadding', 'cellspacing', 'checked', 'cite', 'class', 'clear', 'color', 'cols', 'colspan', 'controls', 'controlslist', 'coords', 'crossorigin', 'datetime', 'decoding', 'default', 'dir', 'disabled', 'disablepictureinpicture', 'disableremoteplayback', 'download', 'draggable', 'enctype', 'enterkeyhint', 'face', 'for', 'headers', 'height', 'hidden', 'high', 'href', 'hreflang', 'id', 'inputmode', 'integrity', 'ismap', 'kind', 'label', 'lang', 'list', 'loading', 'loop', 'low', 'max', 'maxlength', 'media', 'method', 'min', 'minlength', 'multiple', 'muted', 'name', 'nonce', 'noshade', 'novalidate', 'nowrap', 'open', 'optimum', 'pattern', 'placeholder', 'playsinline', 'popover', 'popovertarget', 'popovertargetaction', 'poster', 'preload', 'pubdate', 'radiogroup', 'readonly', 'rel', 'required', 'rev', 'reversed', 'role', 'rows', 'rowspan', 'spellcheck', 'scope', 'selected', 'shape', 'size', 'sizes', 'span', 'srclang', 'start', 'src', 'srcset', 'step', 'style', 'summary', 'tabindex', 'title', 'translate', 'type', 'usemap', 'valign', 'value', 'width', 'wrap', 'xmlns', 'slot']);\nconst svg = freeze(['accent-height', 'accumulate', 'additive', 'alignment-baseline', 'amplitude', 'ascent', 'attributename', 'attributetype', 'azimuth', 'basefrequency', 'baseline-shift', 'begin', 'bias', 'by', 'class', 'clip', 'clippathunits', 'clip-path', 'clip-rule', 'color', 'color-interpolation', 'color-interpolation-filters', 'color-profile', 'color-rendering', 'cx', 'cy', 'd', 'dx', 'dy', 'diffuseconstant', 'direction', 'display', 'divisor', 'dur', 'edgemode', 'elevation', 'end', 'exponent', 'fill', 'fill-opacity', 'fill-rule', 'filter', 'filterunits', 'flood-color', 'flood-opacity', 'font-family', 'font-size', 'font-size-adjust', 'font-stretch', 'font-style', 'font-variant', 'font-weight', 'fx', 'fy', 'g1', 'g2', 'glyph-name', 'glyphref', 'gradientunits', 'gradienttransform', 'height', 'href', 'id', 'image-rendering', 'in', 'in2', 'intercept', 'k', 'k1', 'k2', 'k3', 'k4', 'kerning', 'keypoints', 'keysplines', 'keytimes', 'lang', 'lengthadjust', 'letter-spacing', 'kernelmatrix', 'kernelunitlength', 'lighting-color', 'local', 'marker-end', 'marker-mid', 'marker-start', 'markerheight', 'markerunits', 'markerwidth', 'maskcontentunits', 'maskunits', 'max', 'mask', 'media', 'method', 'mode', 'min', 'name', 'numoctaves', 'offset', 'operator', 'opacity', 'order', 'orient', 'orientation', 'origin', 'overflow', 'paint-order', 'path', 'pathlength', 'patterncontentunits', 'patterntransform', 'patternunits', 'points', 'preservealpha', 'preserveaspectratio', 'primitiveunits', 'r', 'rx', 'ry', 'radius', 'refx', 'refy', 'repeatcount', 'repeatdur', 'restart', 'result', 'rotate', 'scale', 'seed', 'shape-rendering', 'slope', 'specularconstant', 'specularexponent', 'spreadmethod', 'startoffset', 'stddeviation', 'stitchtiles', 'stop-color', 'stop-opacity', 'stroke-dasharray', 'stroke-dashoffset', 'stroke-linecap', 'stroke-linejoin', 'stroke-miterlimit', 'stroke-opacity', 'stroke', 'stroke-width', 'style', 'surfacescale', 'systemlanguage', 'tabindex', 'tablevalues', 'targetx', 'targety', 'transform', 'transform-origin', 'text-anchor', 'text-decoration', 'text-rendering', 'textlength', 'type', 'u1', 'u2', 'unicode', 'values', 'viewbox', 'visibility', 'version', 'vert-adv-y', 'vert-origin-x', 'vert-origin-y', 'width', 'word-spacing', 'wrap', 'writing-mode', 'xchannelselector', 'ychannelselector', 'x', 'x1', 'x2', 'xmlns', 'y', 'y1', 'y2', 'z', 'zoomandpan']);\nconst mathMl = freeze(['accent', 'accentunder', 'align', 'bevelled', 'close', 'columnsalign', 'columnlines', 'columnspan', 'denomalign', 'depth', 'dir', 'display', 'displaystyle', 'encoding', 'fence', 'frame', 'height', 'href', 'id', 'largeop', 'length', 'linethickness', 'lspace', 'lquote', 'mathbackground', 'mathcolor', 'mathsize', 'mathvariant', 'maxsize', 'minsize', 'movablelimits', 'notation', 'numalign', 'open', 'rowalign', 'rowlines', 'rowspacing', 'rowspan', 'rspace', 'rquote', 'scriptlevel', 'scriptminsize', 'scriptsizemultiplier', 'selection', 'separator', 'separators', 'stretchy', 'subscriptshift', 'supscriptshift', 'symmetric', 'voffset', 'width', 'xmlns']);\nconst xml = freeze(['xlink:href', 'xml:id', 'xlink:title', 'xml:space', 'xmlns:xlink']);\n\n// eslint-disable-next-line unicorn/better-regex\nconst MUSTACHE_EXPR = seal(/\\{\\{[\\w\\W]*|[\\w\\W]*\\}\\}/gm); // Specify template detection regex for SAFE_FOR_TEMPLATES mode\nconst ERB_EXPR = seal(/<%[\\w\\W]*|[\\w\\W]*%>/gm);\nconst TMPLIT_EXPR = seal(/\\$\\{[\\w\\W]*/gm); // eslint-disable-line unicorn/better-regex\nconst DATA_ATTR = seal(/^data-[\\-\\w.\\u00B7-\\uFFFF]+$/); // eslint-disable-line no-useless-escape\nconst ARIA_ATTR = seal(/^aria-[\\-\\w]+$/); // eslint-disable-line no-useless-escape\nconst IS_ALLOWED_URI = seal(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\\-]+(?:[^a-z+.\\-:]|$))/i // eslint-disable-line no-useless-escape\n);\nconst IS_SCRIPT_OR_DATA = seal(/^(?:\\w+script|data):/i);\nconst ATTR_WHITESPACE = seal(/[\\u0000-\\u0020\\u00A0\\u1680\\u180E\\u2000-\\u2029\\u205F\\u3000]/g // eslint-disable-line no-control-regex\n);\nconst DOCTYPE_NAME = seal(/^html$/i);\nconst CUSTOM_ELEMENT = seal(/^[a-z][.\\w]*(-[.\\w]+)+$/i);\nvar EXPRESSIONS = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  ARIA_ATTR: ARIA_ATTR,\n  ATTR_WHITESPACE: ATTR_WHITESPACE,\n  CUSTOM_ELEMENT: CUSTOM_ELEMENT,\n  DATA_ATTR: DATA_ATTR,\n  DOCTYPE_NAME: DOCTYPE_NAME,\n  ERB_EXPR: ERB_EXPR,\n  IS_ALLOWED_URI: IS_ALLOWED_URI,\n  IS_SCRIPT_OR_DATA: IS_SCRIPT_OR_DATA,\n  MUSTACHE_EXPR: MUSTACHE_EXPR,\n  TMPLIT_EXPR: TMPLIT_EXPR\n});\n\n/* eslint-disable @typescript-eslint/indent */\n// https://developer.mozilla.org/en-US/docs/Web/API/Node/nodeType\nconst NODE_TYPE = {\n  element: 1,\n  attribute: 2,\n  text: 3,\n  cdataSection: 4,\n  entityReference: 5,\n  // Deprecated\n  entityNode: 6,\n  // Deprecated\n  progressingInstruction: 7,\n  comment: 8,\n  document: 9,\n  documentType: 10,\n  documentFragment: 11,\n  notation: 12 // Deprecated\n};\nconst getGlobal = function getGlobal() {\n  return typeof window === 'undefined' ? null : window;\n};\n/**\n * Creates a no-op policy for internal use only.\n * Don't export this function outside this module!\n * @param trustedTypes The policy factory.\n * @param purifyHostElement The Script element used to load DOMPurify (to determine policy name suffix).\n * @return The policy created (or null, if Trusted Types\n * are not supported or creating the policy failed).\n */\nconst _createTrustedTypesPolicy = function _createTrustedTypesPolicy(trustedTypes, purifyHostElement) {\n  if (typeof trustedTypes !== 'object' || typeof trustedTypes.createPolicy !== 'function') {\n    return null;\n  }\n  // Allow the callers to control the unique policy name\n  // by adding a data-tt-policy-suffix to the script element with the DOMPurify.\n  // Policy creation with duplicate names throws in Trusted Types.\n  let suffix = null;\n  const ATTR_NAME = 'data-tt-policy-suffix';\n  if (purifyHostElement && purifyHostElement.hasAttribute(ATTR_NAME)) {\n    suffix = purifyHostElement.getAttribute(ATTR_NAME);\n  }\n  const policyName = 'dompurify' + (suffix ? '#' + suffix : '');\n  try {\n    return trustedTypes.createPolicy(policyName, {\n      createHTML(html) {\n        return html;\n      },\n      createScriptURL(scriptUrl) {\n        return scriptUrl;\n      }\n    });\n  } catch (_) {\n    // Policy creation failed (most likely another DOMPurify script has\n    // already run). Skip creating the policy, as this will only cause errors\n    // if TT are enforced.\n    console.warn('TrustedTypes policy ' + policyName + ' could not be created.');\n    return null;\n  }\n};\nconst _createHooksMap = function _createHooksMap() {\n  return {\n    afterSanitizeAttributes: [],\n    afterSanitizeElements: [],\n    afterSanitizeShadowDOM: [],\n    beforeSanitizeAttributes: [],\n    beforeSanitizeElements: [],\n    beforeSanitizeShadowDOM: [],\n    uponSanitizeAttribute: [],\n    uponSanitizeElement: [],\n    uponSanitizeShadowNode: []\n  };\n};\nfunction createDOMPurify() {\n  let window = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : getGlobal();\n  const DOMPurify = root => createDOMPurify(root);\n  DOMPurify.version = '3.2.6';\n  DOMPurify.removed = [];\n  if (!window || !window.document || window.document.nodeType !== NODE_TYPE.document || !window.Element) {\n    // Not running in a browser, provide a factory function\n    // so that you can pass your own Window\n    DOMPurify.isSupported = false;\n    return DOMPurify;\n  }\n  let {\n    document\n  } = window;\n  const originalDocument = document;\n  const currentScript = originalDocument.currentScript;\n  const {\n    DocumentFragment,\n    HTMLTemplateElement,\n    Node,\n    Element,\n    NodeFilter,\n    NamedNodeMap = window.NamedNodeMap || window.MozNamedAttrMap,\n    HTMLFormElement,\n    DOMParser,\n    trustedTypes\n  } = window;\n  const ElementPrototype = Element.prototype;\n  const cloneNode = lookupGetter(ElementPrototype, 'cloneNode');\n  const remove = lookupGetter(ElementPrototype, 'remove');\n  const getNextSibling = lookupGetter(ElementPrototype, 'nextSibling');\n  const getChildNodes = lookupGetter(ElementPrototype, 'childNodes');\n  const getParentNode = lookupGetter(ElementPrototype, 'parentNode');\n  // As per issue #47, the web-components registry is inherited by a\n  // new document created via createHTMLDocument. As per the spec\n  // (http://w3c.github.io/webcomponents/spec/custom/#creating-and-passing-registries)\n  // a new empty registry is used when creating a template contents owner\n  // document, so we use that as our parent document to ensure nothing\n  // is inherited.\n  if (typeof HTMLTemplateElement === 'function') {\n    const template = document.createElement('template');\n    if (template.content && template.content.ownerDocument) {\n      document = template.content.ownerDocument;\n    }\n  }\n  let trustedTypesPolicy;\n  let emptyHTML = '';\n  const {\n    implementation,\n    createNodeIterator,\n    createDocumentFragment,\n    getElementsByTagName\n  } = document;\n  const {\n    importNode\n  } = originalDocument;\n  let hooks = _createHooksMap();\n  /**\n   * Expose whether this browser supports running the full DOMPurify.\n   */\n  DOMPurify.isSupported = typeof entries === 'function' && typeof getParentNode === 'function' && implementation && implementation.createHTMLDocument !== undefined;\n  const {\n    MUSTACHE_EXPR,\n    ERB_EXPR,\n    TMPLIT_EXPR,\n    DATA_ATTR,\n    ARIA_ATTR,\n    IS_SCRIPT_OR_DATA,\n    ATTR_WHITESPACE,\n    CUSTOM_ELEMENT\n  } = EXPRESSIONS;\n  let {\n    IS_ALLOWED_URI: IS_ALLOWED_URI$1\n  } = EXPRESSIONS;\n  /**\n   * We consider the elements and attributes below to be safe. Ideally\n   * don't add any new ones but feel free to remove unwanted ones.\n   */\n  /* allowed element names */\n  let ALLOWED_TAGS = null;\n  const DEFAULT_ALLOWED_TAGS = addToSet({}, [...html$1, ...svg$1, ...svgFilters, ...mathMl$1, ...text]);\n  /* Allowed attribute names */\n  let ALLOWED_ATTR = null;\n  const DEFAULT_ALLOWED_ATTR = addToSet({}, [...html, ...svg, ...mathMl, ...xml]);\n  /*\n   * Configure how DOMPurify should handle custom elements and their attributes as well as customized built-in elements.\n   * @property {RegExp|Function|null} tagNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any custom elements)\n   * @property {RegExp|Function|null} attributeNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any attributes not on the allow list)\n   * @property {boolean} allowCustomizedBuiltInElements allow custom elements derived from built-ins if they pass CUSTOM_ELEMENT_HANDLING.tagNameCheck. Default: `false`.\n   */\n  let CUSTOM_ELEMENT_HANDLING = Object.seal(create(null, {\n    tagNameCheck: {\n      writable: true,\n      configurable: false,\n      enumerable: true,\n      value: null\n    },\n    attributeNameCheck: {\n      writable: true,\n      configurable: false,\n      enumerable: true,\n      value: null\n    },\n    allowCustomizedBuiltInElements: {\n      writable: true,\n      configurable: false,\n      enumerable: true,\n      value: false\n    }\n  }));\n  /* Explicitly forbidden tags (overrides ALLOWED_TAGS/ADD_TAGS) */\n  let FORBID_TAGS = null;\n  /* Explicitly forbidden attributes (overrides ALLOWED_ATTR/ADD_ATTR) */\n  let FORBID_ATTR = null;\n  /* Decide if ARIA attributes are okay */\n  let ALLOW_ARIA_ATTR = true;\n  /* Decide if custom data attributes are okay */\n  let ALLOW_DATA_ATTR = true;\n  /* Decide if unknown protocols are okay */\n  let ALLOW_UNKNOWN_PROTOCOLS = false;\n  /* Decide if self-closing tags in attributes are allowed.\n   * Usually removed due to a mXSS issue in jQuery 3.0 */\n  let ALLOW_SELF_CLOSE_IN_ATTR = true;\n  /* Output should be safe for common template engines.\n   * This means, DOMPurify removes data attributes, mustaches and ERB\n   */\n  let SAFE_FOR_TEMPLATES = false;\n  /* Output should be safe even for XML used within HTML and alike.\n   * This means, DOMPurify removes comments when containing risky content.\n   */\n  let SAFE_FOR_XML = true;\n  /* Decide if document with <html>... should be returned */\n  let WHOLE_DOCUMENT = false;\n  /* Track whether config is already set on this instance of DOMPurify. */\n  let SET_CONFIG = false;\n  /* Decide if all elements (e.g. style, script) must be children of\n   * document.body. By default, browsers might move them to document.head */\n  let FORCE_BODY = false;\n  /* Decide if a DOM `HTMLBodyElement` should be returned, instead of a html\n   * string (or a TrustedHTML object if Trusted Types are supported).\n   * If `WHOLE_DOCUMENT` is enabled a `HTMLHtmlElement` will be returned instead\n   */\n  let RETURN_DOM = false;\n  /* Decide if a DOM `DocumentFragment` should be returned, instead of a html\n   * string  (or a TrustedHTML object if Trusted Types are supported) */\n  let RETURN_DOM_FRAGMENT = false;\n  /* Try to return a Trusted Type object instead of a string, return a string in\n   * case Trusted Types are not supported  */\n  let RETURN_TRUSTED_TYPE = false;\n  /* Output should be free from DOM clobbering attacks?\n   * This sanitizes markups named with colliding, clobberable built-in DOM APIs.\n   */\n  let SANITIZE_DOM = true;\n  /* Achieve full DOM Clobbering protection by isolating the namespace of named\n   * properties and JS variables, mitigating attacks that abuse the HTML/DOM spec rules.\n   *\n   * HTML/DOM spec rules that enable DOM Clobbering:\n   *   - Named Access on Window (§7.3.3)\n   *   - DOM Tree Accessors (§3.1.5)\n   *   - Form Element Parent-Child Relations (§4.10.3)\n   *   - Iframe srcdoc / Nested WindowProxies (§4.8.5)\n   *   - HTMLCollection (§4.2.10.2)\n   *\n   * Namespace isolation is implemented by prefixing `id` and `name` attributes\n   * with a constant string, i.e., `user-content-`\n   */\n  let SANITIZE_NAMED_PROPS = false;\n  const SANITIZE_NAMED_PROPS_PREFIX = 'user-content-';\n  /* Keep element content when removing element? */\n  let KEEP_CONTENT = true;\n  /* If a `Node` is passed to sanitize(), then performs sanitization in-place instead\n   * of importing it into a new Document and returning a sanitized copy */\n  let IN_PLACE = false;\n  /* Allow usage of profiles like html, svg and mathMl */\n  let USE_PROFILES = {};\n  /* Tags to ignore content of when KEEP_CONTENT is true */\n  let FORBID_CONTENTS = null;\n  const DEFAULT_FORBID_CONTENTS = addToSet({}, ['annotation-xml', 'audio', 'colgroup', 'desc', 'foreignobject', 'head', 'iframe', 'math', 'mi', 'mn', 'mo', 'ms', 'mtext', 'noembed', 'noframes', 'noscript', 'plaintext', 'script', 'style', 'svg', 'template', 'thead', 'title', 'video', 'xmp']);\n  /* Tags that are safe for data: URIs */\n  let DATA_URI_TAGS = null;\n  const DEFAULT_DATA_URI_TAGS = addToSet({}, ['audio', 'video', 'img', 'source', 'image', 'track']);\n  /* Attributes safe for values like \"javascript:\" */\n  let URI_SAFE_ATTRIBUTES = null;\n  const DEFAULT_URI_SAFE_ATTRIBUTES = addToSet({}, ['alt', 'class', 'for', 'id', 'label', 'name', 'pattern', 'placeholder', 'role', 'summary', 'title', 'value', 'style', 'xmlns']);\n  const MATHML_NAMESPACE = 'http://www.w3.org/1998/Math/MathML';\n  const SVG_NAMESPACE = 'http://www.w3.org/2000/svg';\n  const HTML_NAMESPACE = 'http://www.w3.org/1999/xhtml';\n  /* Document namespace */\n  let NAMESPACE = HTML_NAMESPACE;\n  let IS_EMPTY_INPUT = false;\n  /* Allowed XHTML+XML namespaces */\n  let ALLOWED_NAMESPACES = null;\n  const DEFAULT_ALLOWED_NAMESPACES = addToSet({}, [MATHML_NAMESPACE, SVG_NAMESPACE, HTML_NAMESPACE], stringToString);\n  let MATHML_TEXT_INTEGRATION_POINTS = addToSet({}, ['mi', 'mo', 'mn', 'ms', 'mtext']);\n  let HTML_INTEGRATION_POINTS = addToSet({}, ['annotation-xml']);\n  // Certain elements are allowed in both SVG and HTML\n  // namespace. We need to specify them explicitly\n  // so that they don't get erroneously deleted from\n  // HTML namespace.\n  const COMMON_SVG_AND_HTML_ELEMENTS = addToSet({}, ['title', 'style', 'font', 'a', 'script']);\n  /* Parsing of strict XHTML documents */\n  let PARSER_MEDIA_TYPE = null;\n  const SUPPORTED_PARSER_MEDIA_TYPES = ['application/xhtml+xml', 'text/html'];\n  const DEFAULT_PARSER_MEDIA_TYPE = 'text/html';\n  let transformCaseFunc = null;\n  /* Keep a reference to config to pass to hooks */\n  let CONFIG = null;\n  /* Ideally, do not touch anything below this line */\n  /* ______________________________________________ */\n  const formElement = document.createElement('form');\n  const isRegexOrFunction = function isRegexOrFunction(testValue) {\n    return testValue instanceof RegExp || testValue instanceof Function;\n  };\n  /**\n   * _parseConfig\n   *\n   * @param cfg optional config literal\n   */\n  // eslint-disable-next-line complexity\n  const _parseConfig = function _parseConfig() {\n    let cfg = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    if (CONFIG && CONFIG === cfg) {\n      return;\n    }\n    /* Shield configuration object from tampering */\n    if (!cfg || typeof cfg !== 'object') {\n      cfg = {};\n    }\n    /* Shield configuration object from prototype pollution */\n    cfg = clone(cfg);\n    PARSER_MEDIA_TYPE =\n    // eslint-disable-next-line unicorn/prefer-includes\n    SUPPORTED_PARSER_MEDIA_TYPES.indexOf(cfg.PARSER_MEDIA_TYPE) === -1 ? DEFAULT_PARSER_MEDIA_TYPE : cfg.PARSER_MEDIA_TYPE;\n    // HTML tags and attributes are not case-sensitive, converting to lowercase. Keeping XHTML as is.\n    transformCaseFunc = PARSER_MEDIA_TYPE === 'application/xhtml+xml' ? stringToString : stringToLowerCase;\n    /* Set configuration parameters */\n    ALLOWED_TAGS = objectHasOwnProperty(cfg, 'ALLOWED_TAGS') ? addToSet({}, cfg.ALLOWED_TAGS, transformCaseFunc) : DEFAULT_ALLOWED_TAGS;\n    ALLOWED_ATTR = objectHasOwnProperty(cfg, 'ALLOWED_ATTR') ? addToSet({}, cfg.ALLOWED_ATTR, transformCaseFunc) : DEFAULT_ALLOWED_ATTR;\n    ALLOWED_NAMESPACES = objectHasOwnProperty(cfg, 'ALLOWED_NAMESPACES') ? addToSet({}, cfg.ALLOWED_NAMESPACES, stringToString) : DEFAULT_ALLOWED_NAMESPACES;\n    URI_SAFE_ATTRIBUTES = objectHasOwnProperty(cfg, 'ADD_URI_SAFE_ATTR') ? addToSet(clone(DEFAULT_URI_SAFE_ATTRIBUTES), cfg.ADD_URI_SAFE_ATTR, transformCaseFunc) : DEFAULT_URI_SAFE_ATTRIBUTES;\n    DATA_URI_TAGS = objectHasOwnProperty(cfg, 'ADD_DATA_URI_TAGS') ? addToSet(clone(DEFAULT_DATA_URI_TAGS), cfg.ADD_DATA_URI_TAGS, transformCaseFunc) : DEFAULT_DATA_URI_TAGS;\n    FORBID_CONTENTS = objectHasOwnProperty(cfg, 'FORBID_CONTENTS') ? addToSet({}, cfg.FORBID_CONTENTS, transformCaseFunc) : DEFAULT_FORBID_CONTENTS;\n    FORBID_TAGS = objectHasOwnProperty(cfg, 'FORBID_TAGS') ? addToSet({}, cfg.FORBID_TAGS, transformCaseFunc) : clone({});\n    FORBID_ATTR = objectHasOwnProperty(cfg, 'FORBID_ATTR') ? addToSet({}, cfg.FORBID_ATTR, transformCaseFunc) : clone({});\n    USE_PROFILES = objectHasOwnProperty(cfg, 'USE_PROFILES') ? cfg.USE_PROFILES : false;\n    ALLOW_ARIA_ATTR = cfg.ALLOW_ARIA_ATTR !== false; // Default true\n    ALLOW_DATA_ATTR = cfg.ALLOW_DATA_ATTR !== false; // Default true\n    ALLOW_UNKNOWN_PROTOCOLS = cfg.ALLOW_UNKNOWN_PROTOCOLS || false; // Default false\n    ALLOW_SELF_CLOSE_IN_ATTR = cfg.ALLOW_SELF_CLOSE_IN_ATTR !== false; // Default true\n    SAFE_FOR_TEMPLATES = cfg.SAFE_FOR_TEMPLATES || false; // Default false\n    SAFE_FOR_XML = cfg.SAFE_FOR_XML !== false; // Default true\n    WHOLE_DOCUMENT = cfg.WHOLE_DOCUMENT || false; // Default false\n    RETURN_DOM = cfg.RETURN_DOM || false; // Default false\n    RETURN_DOM_FRAGMENT = cfg.RETURN_DOM_FRAGMENT || false; // Default false\n    RETURN_TRUSTED_TYPE = cfg.RETURN_TRUSTED_TYPE || false; // Default false\n    FORCE_BODY = cfg.FORCE_BODY || false; // Default false\n    SANITIZE_DOM = cfg.SANITIZE_DOM !== false; // Default true\n    SANITIZE_NAMED_PROPS = cfg.SANITIZE_NAMED_PROPS || false; // Default false\n    KEEP_CONTENT = cfg.KEEP_CONTENT !== false; // Default true\n    IN_PLACE = cfg.IN_PLACE || false; // Default false\n    IS_ALLOWED_URI$1 = cfg.ALLOWED_URI_REGEXP || IS_ALLOWED_URI;\n    NAMESPACE = cfg.NAMESPACE || HTML_NAMESPACE;\n    MATHML_TEXT_INTEGRATION_POINTS = cfg.MATHML_TEXT_INTEGRATION_POINTS || MATHML_TEXT_INTEGRATION_POINTS;\n    HTML_INTEGRATION_POINTS = cfg.HTML_INTEGRATION_POINTS || HTML_INTEGRATION_POINTS;\n    CUSTOM_ELEMENT_HANDLING = cfg.CUSTOM_ELEMENT_HANDLING || {};\n    if (cfg.CUSTOM_ELEMENT_HANDLING && isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck)) {\n      CUSTOM_ELEMENT_HANDLING.tagNameCheck = cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck;\n    }\n    if (cfg.CUSTOM_ELEMENT_HANDLING && isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)) {\n      CUSTOM_ELEMENT_HANDLING.attributeNameCheck = cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck;\n    }\n    if (cfg.CUSTOM_ELEMENT_HANDLING && typeof cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements === 'boolean') {\n      CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements = cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements;\n    }\n    if (SAFE_FOR_TEMPLATES) {\n      ALLOW_DATA_ATTR = false;\n    }\n    if (RETURN_DOM_FRAGMENT) {\n      RETURN_DOM = true;\n    }\n    /* Parse profile info */\n    if (USE_PROFILES) {\n      ALLOWED_TAGS = addToSet({}, text);\n      ALLOWED_ATTR = [];\n      if (USE_PROFILES.html === true) {\n        addToSet(ALLOWED_TAGS, html$1);\n        addToSet(ALLOWED_ATTR, html);\n      }\n      if (USE_PROFILES.svg === true) {\n        addToSet(ALLOWED_TAGS, svg$1);\n        addToSet(ALLOWED_ATTR, svg);\n        addToSet(ALLOWED_ATTR, xml);\n      }\n      if (USE_PROFILES.svgFilters === true) {\n        addToSet(ALLOWED_TAGS, svgFilters);\n        addToSet(ALLOWED_ATTR, svg);\n        addToSet(ALLOWED_ATTR, xml);\n      }\n      if (USE_PROFILES.mathMl === true) {\n        addToSet(ALLOWED_TAGS, mathMl$1);\n        addToSet(ALLOWED_ATTR, mathMl);\n        addToSet(ALLOWED_ATTR, xml);\n      }\n    }\n    /* Merge configuration parameters */\n    if (cfg.ADD_TAGS) {\n      if (ALLOWED_TAGS === DEFAULT_ALLOWED_TAGS) {\n        ALLOWED_TAGS = clone(ALLOWED_TAGS);\n      }\n      addToSet(ALLOWED_TAGS, cfg.ADD_TAGS, transformCaseFunc);\n    }\n    if (cfg.ADD_ATTR) {\n      if (ALLOWED_ATTR === DEFAULT_ALLOWED_ATTR) {\n        ALLOWED_ATTR = clone(ALLOWED_ATTR);\n      }\n      addToSet(ALLOWED_ATTR, cfg.ADD_ATTR, transformCaseFunc);\n    }\n    if (cfg.ADD_URI_SAFE_ATTR) {\n      addToSet(URI_SAFE_ATTRIBUTES, cfg.ADD_URI_SAFE_ATTR, transformCaseFunc);\n    }\n    if (cfg.FORBID_CONTENTS) {\n      if (FORBID_CONTENTS === DEFAULT_FORBID_CONTENTS) {\n        FORBID_CONTENTS = clone(FORBID_CONTENTS);\n      }\n      addToSet(FORBID_CONTENTS, cfg.FORBID_CONTENTS, transformCaseFunc);\n    }\n    /* Add #text in case KEEP_CONTENT is set to true */\n    if (KEEP_CONTENT) {\n      ALLOWED_TAGS['#text'] = true;\n    }\n    /* Add html, head and body to ALLOWED_TAGS in case WHOLE_DOCUMENT is true */\n    if (WHOLE_DOCUMENT) {\n      addToSet(ALLOWED_TAGS, ['html', 'head', 'body']);\n    }\n    /* Add tbody to ALLOWED_TAGS in case tables are permitted, see #286, #365 */\n    if (ALLOWED_TAGS.table) {\n      addToSet(ALLOWED_TAGS, ['tbody']);\n      delete FORBID_TAGS.tbody;\n    }\n    if (cfg.TRUSTED_TYPES_POLICY) {\n      if (typeof cfg.TRUSTED_TYPES_POLICY.createHTML !== 'function') {\n        throw typeErrorCreate('TRUSTED_TYPES_POLICY configuration option must provide a \"createHTML\" hook.');\n      }\n      if (typeof cfg.TRUSTED_TYPES_POLICY.createScriptURL !== 'function') {\n        throw typeErrorCreate('TRUSTED_TYPES_POLICY configuration option must provide a \"createScriptURL\" hook.');\n      }\n      // Overwrite existing TrustedTypes policy.\n      trustedTypesPolicy = cfg.TRUSTED_TYPES_POLICY;\n      // Sign local variables required by `sanitize`.\n      emptyHTML = trustedTypesPolicy.createHTML('');\n    } else {\n      // Uninitialized policy, attempt to initialize the internal dompurify policy.\n      if (trustedTypesPolicy === undefined) {\n        trustedTypesPolicy = _createTrustedTypesPolicy(trustedTypes, currentScript);\n      }\n      // If creating the internal policy succeeded sign internal variables.\n      if (trustedTypesPolicy !== null && typeof emptyHTML === 'string') {\n        emptyHTML = trustedTypesPolicy.createHTML('');\n      }\n    }\n    // Prevent further manipulation of configuration.\n    // Not available in IE8, Safari 5, etc.\n    if (freeze) {\n      freeze(cfg);\n    }\n    CONFIG = cfg;\n  };\n  /* Keep track of all possible SVG and MathML tags\n   * so that we can perform the namespace checks\n   * correctly. */\n  const ALL_SVG_TAGS = addToSet({}, [...svg$1, ...svgFilters, ...svgDisallowed]);\n  const ALL_MATHML_TAGS = addToSet({}, [...mathMl$1, ...mathMlDisallowed]);\n  /**\n   * @param element a DOM element whose namespace is being checked\n   * @returns Return false if the element has a\n   *  namespace that a spec-compliant parser would never\n   *  return. Return true otherwise.\n   */\n  const _checkValidNamespace = function _checkValidNamespace(element) {\n    let parent = getParentNode(element);\n    // In JSDOM, if we're inside shadow DOM, then parentNode\n    // can be null. We just simulate parent in this case.\n    if (!parent || !parent.tagName) {\n      parent = {\n        namespaceURI: NAMESPACE,\n        tagName: 'template'\n      };\n    }\n    const tagName = stringToLowerCase(element.tagName);\n    const parentTagName = stringToLowerCase(parent.tagName);\n    if (!ALLOWED_NAMESPACES[element.namespaceURI]) {\n      return false;\n    }\n    if (element.namespaceURI === SVG_NAMESPACE) {\n      // The only way to switch from HTML namespace to SVG\n      // is via <svg>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'svg';\n      }\n      // The only way to switch from MathML to SVG is via`\n      // svg if parent is either <annotation-xml> or MathML\n      // text integration points.\n      if (parent.namespaceURI === MATHML_NAMESPACE) {\n        return tagName === 'svg' && (parentTagName === 'annotation-xml' || MATHML_TEXT_INTEGRATION_POINTS[parentTagName]);\n      }\n      // We only allow elements that are defined in SVG\n      // spec. All others are disallowed in SVG namespace.\n      return Boolean(ALL_SVG_TAGS[tagName]);\n    }\n    if (element.namespaceURI === MATHML_NAMESPACE) {\n      // The only way to switch from HTML namespace to MathML\n      // is via <math>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'math';\n      }\n      // The only way to switch from SVG to MathML is via\n      // <math> and HTML integration points\n      if (parent.namespaceURI === SVG_NAMESPACE) {\n        return tagName === 'math' && HTML_INTEGRATION_POINTS[parentTagName];\n      }\n      // We only allow elements that are defined in MathML\n      // spec. All others are disallowed in MathML namespace.\n      return Boolean(ALL_MATHML_TAGS[tagName]);\n    }\n    if (element.namespaceURI === HTML_NAMESPACE) {\n      // The only way to switch from SVG to HTML is via\n      // HTML integration points, and from MathML to HTML\n      // is via MathML text integration points\n      if (parent.namespaceURI === SVG_NAMESPACE && !HTML_INTEGRATION_POINTS[parentTagName]) {\n        return false;\n      }\n      if (parent.namespaceURI === MATHML_NAMESPACE && !MATHML_TEXT_INTEGRATION_POINTS[parentTagName]) {\n        return false;\n      }\n      // We disallow tags that are specific for MathML\n      // or SVG and should never appear in HTML namespace\n      return !ALL_MATHML_TAGS[tagName] && (COMMON_SVG_AND_HTML_ELEMENTS[tagName] || !ALL_SVG_TAGS[tagName]);\n    }\n    // For XHTML and XML documents that support custom namespaces\n    if (PARSER_MEDIA_TYPE === 'application/xhtml+xml' && ALLOWED_NAMESPACES[element.namespaceURI]) {\n      return true;\n    }\n    // The code should never reach this place (this means\n    // that the element somehow got namespace that is not\n    // HTML, SVG, MathML or allowed via ALLOWED_NAMESPACES).\n    // Return false just in case.\n    return false;\n  };\n  /**\n   * _forceRemove\n   *\n   * @param node a DOM node\n   */\n  const _forceRemove = function _forceRemove(node) {\n    arrayPush(DOMPurify.removed, {\n      element: node\n    });\n    try {\n      // eslint-disable-next-line unicorn/prefer-dom-node-remove\n      getParentNode(node).removeChild(node);\n    } catch (_) {\n      remove(node);\n    }\n  };\n  /**\n   * _removeAttribute\n   *\n   * @param name an Attribute name\n   * @param element a DOM node\n   */\n  const _removeAttribute = function _removeAttribute(name, element) {\n    try {\n      arrayPush(DOMPurify.removed, {\n        attribute: element.getAttributeNode(name),\n        from: element\n      });\n    } catch (_) {\n      arrayPush(DOMPurify.removed, {\n        attribute: null,\n        from: element\n      });\n    }\n    element.removeAttribute(name);\n    // We void attribute values for unremovable \"is\" attributes\n    if (name === 'is') {\n      if (RETURN_DOM || RETURN_DOM_FRAGMENT) {\n        try {\n          _forceRemove(element);\n        } catch (_) {}\n      } else {\n        try {\n          element.setAttribute(name, '');\n        } catch (_) {}\n      }\n    }\n  };\n  /**\n   * _initDocument\n   *\n   * @param dirty - a string of dirty markup\n   * @return a DOM, filled with the dirty markup\n   */\n  const _initDocument = function _initDocument(dirty) {\n    /* Create a HTML document */\n    let doc = null;\n    let leadingWhitespace = null;\n    if (FORCE_BODY) {\n      dirty = '<remove></remove>' + dirty;\n    } else {\n      /* If FORCE_BODY isn't used, leading whitespace needs to be preserved manually */\n      const matches = stringMatch(dirty, /^[\\r\\n\\t ]+/);\n      leadingWhitespace = matches && matches[0];\n    }\n    if (PARSER_MEDIA_TYPE === 'application/xhtml+xml' && NAMESPACE === HTML_NAMESPACE) {\n      // Root of XHTML doc must contain xmlns declaration (see https://www.w3.org/TR/xhtml1/normative.html#strict)\n      dirty = '<html xmlns=\"http://www.w3.org/1999/xhtml\"><head></head><body>' + dirty + '</body></html>';\n    }\n    const dirtyPayload = trustedTypesPolicy ? trustedTypesPolicy.createHTML(dirty) : dirty;\n    /*\n     * Use the DOMParser API by default, fallback later if needs be\n     * DOMParser not work for svg when has multiple root element.\n     */\n    if (NAMESPACE === HTML_NAMESPACE) {\n      try {\n        doc = new DOMParser().parseFromString(dirtyPayload, PARSER_MEDIA_TYPE);\n      } catch (_) {}\n    }\n    /* Use createHTMLDocument in case DOMParser is not available */\n    if (!doc || !doc.documentElement) {\n      doc = implementation.createDocument(NAMESPACE, 'template', null);\n      try {\n        doc.documentElement.innerHTML = IS_EMPTY_INPUT ? emptyHTML : dirtyPayload;\n      } catch (_) {\n        // Syntax error if dirtyPayload is invalid xml\n      }\n    }\n    const body = doc.body || doc.documentElement;\n    if (dirty && leadingWhitespace) {\n      body.insertBefore(document.createTextNode(leadingWhitespace), body.childNodes[0] || null);\n    }\n    /* Work on whole document or just its body */\n    if (NAMESPACE === HTML_NAMESPACE) {\n      return getElementsByTagName.call(doc, WHOLE_DOCUMENT ? 'html' : 'body')[0];\n    }\n    return WHOLE_DOCUMENT ? doc.documentElement : body;\n  };\n  /**\n   * Creates a NodeIterator object that you can use to traverse filtered lists of nodes or elements in a document.\n   *\n   * @param root The root element or node to start traversing on.\n   * @return The created NodeIterator\n   */\n  const _createNodeIterator = function _createNodeIterator(root) {\n    return createNodeIterator.call(root.ownerDocument || root, root,\n    // eslint-disable-next-line no-bitwise\n    NodeFilter.SHOW_ELEMENT | NodeFilter.SHOW_COMMENT | NodeFilter.SHOW_TEXT | NodeFilter.SHOW_PROCESSING_INSTRUCTION | NodeFilter.SHOW_CDATA_SECTION, null);\n  };\n  /**\n   * _isClobbered\n   *\n   * @param element element to check for clobbering attacks\n   * @return true if clobbered, false if safe\n   */\n  const _isClobbered = function _isClobbered(element) {\n    return element instanceof HTMLFormElement && (typeof element.nodeName !== 'string' || typeof element.textContent !== 'string' || typeof element.removeChild !== 'function' || !(element.attributes instanceof NamedNodeMap) || typeof element.removeAttribute !== 'function' || typeof element.setAttribute !== 'function' || typeof element.namespaceURI !== 'string' || typeof element.insertBefore !== 'function' || typeof element.hasChildNodes !== 'function');\n  };\n  /**\n   * Checks whether the given object is a DOM node.\n   *\n   * @param value object to check whether it's a DOM node\n   * @return true is object is a DOM node\n   */\n  const _isNode = function _isNode(value) {\n    return typeof Node === 'function' && value instanceof Node;\n  };\n  function _executeHooks(hooks, currentNode, data) {\n    arrayForEach(hooks, hook => {\n      hook.call(DOMPurify, currentNode, data, CONFIG);\n    });\n  }\n  /**\n   * _sanitizeElements\n   *\n   * @protect nodeName\n   * @protect textContent\n   * @protect removeChild\n   * @param currentNode to check for permission to exist\n   * @return true if node was killed, false if left alive\n   */\n  const _sanitizeElements = function _sanitizeElements(currentNode) {\n    let content = null;\n    /* Execute a hook if present */\n    _executeHooks(hooks.beforeSanitizeElements, currentNode, null);\n    /* Check if element is clobbered or can clobber */\n    if (_isClobbered(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n    /* Now let's check the element's type and name */\n    const tagName = transformCaseFunc(currentNode.nodeName);\n    /* Execute a hook if present */\n    _executeHooks(hooks.uponSanitizeElement, currentNode, {\n      tagName,\n      allowedTags: ALLOWED_TAGS\n    });\n    /* Detect mXSS attempts abusing namespace confusion */\n    if (SAFE_FOR_XML && currentNode.hasChildNodes() && !_isNode(currentNode.firstElementChild) && regExpTest(/<[/\\w!]/g, currentNode.innerHTML) && regExpTest(/<[/\\w!]/g, currentNode.textContent)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n    /* Remove any occurrence of processing instructions */\n    if (currentNode.nodeType === NODE_TYPE.progressingInstruction) {\n      _forceRemove(currentNode);\n      return true;\n    }\n    /* Remove any kind of possibly harmful comments */\n    if (SAFE_FOR_XML && currentNode.nodeType === NODE_TYPE.comment && regExpTest(/<[/\\w]/g, currentNode.data)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n    /* Remove element if anything forbids its presence */\n    if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n      /* Check if we have a custom element to handle */\n      if (!FORBID_TAGS[tagName] && _isBasicCustomElement(tagName)) {\n        if (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, tagName)) {\n          return false;\n        }\n        if (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(tagName)) {\n          return false;\n        }\n      }\n      /* Keep content except for bad-listed elements */\n      if (KEEP_CONTENT && !FORBID_CONTENTS[tagName]) {\n        const parentNode = getParentNode(currentNode) || currentNode.parentNode;\n        const childNodes = getChildNodes(currentNode) || currentNode.childNodes;\n        if (childNodes && parentNode) {\n          const childCount = childNodes.length;\n          for (let i = childCount - 1; i >= 0; --i) {\n            const childClone = cloneNode(childNodes[i], true);\n            childClone.__removalCount = (currentNode.__removalCount || 0) + 1;\n            parentNode.insertBefore(childClone, getNextSibling(currentNode));\n          }\n        }\n      }\n      _forceRemove(currentNode);\n      return true;\n    }\n    /* Check whether element has a valid namespace */\n    if (currentNode instanceof Element && !_checkValidNamespace(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n    /* Make sure that older browsers don't get fallback-tag mXSS */\n    if ((tagName === 'noscript' || tagName === 'noembed' || tagName === 'noframes') && regExpTest(/<\\/no(script|embed|frames)/i, currentNode.innerHTML)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n    /* Sanitize element content to be template-safe */\n    if (SAFE_FOR_TEMPLATES && currentNode.nodeType === NODE_TYPE.text) {\n      /* Get the element's text content */\n      content = currentNode.textContent;\n      arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], expr => {\n        content = stringReplace(content, expr, ' ');\n      });\n      if (currentNode.textContent !== content) {\n        arrayPush(DOMPurify.removed, {\n          element: currentNode.cloneNode()\n        });\n        currentNode.textContent = content;\n      }\n    }\n    /* Execute a hook if present */\n    _executeHooks(hooks.afterSanitizeElements, currentNode, null);\n    return false;\n  };\n  /**\n   * _isValidAttribute\n   *\n   * @param lcTag Lowercase tag name of containing element.\n   * @param lcName Lowercase attribute name.\n   * @param value Attribute value.\n   * @return Returns true if `value` is valid, otherwise false.\n   */\n  // eslint-disable-next-line complexity\n  const _isValidAttribute = function _isValidAttribute(lcTag, lcName, value) {\n    /* Make sure attribute cannot clobber */\n    if (SANITIZE_DOM && (lcName === 'id' || lcName === 'name') && (value in document || value in formElement)) {\n      return false;\n    }\n    /* Allow valid data-* attributes: At least one character after \"-\"\n        (https://html.spec.whatwg.org/multipage/dom.html#embedding-custom-non-visible-data-with-the-data-*-attributes)\n        XML-compatible (https://html.spec.whatwg.org/multipage/infrastructure.html#xml-compatible and http://www.w3.org/TR/xml/#d0e804)\n        We don't need to check the value; it's always URI safe. */\n    if (ALLOW_DATA_ATTR && !FORBID_ATTR[lcName] && regExpTest(DATA_ATTR, lcName)) ;else if (ALLOW_ARIA_ATTR && regExpTest(ARIA_ATTR, lcName)) ;else if (!ALLOWED_ATTR[lcName] || FORBID_ATTR[lcName]) {\n      if (\n      // First condition does a very basic check if a) it's basically a valid custom element tagname AND\n      // b) if the tagName passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n      // and c) if the attribute name passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.attributeNameCheck\n      _isBasicCustomElement(lcTag) && (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, lcTag) || CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(lcTag)) && (CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.attributeNameCheck, lcName) || CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.attributeNameCheck(lcName)) ||\n      // Alternative, second condition checks if it's an `is`-attribute, AND\n      // the value passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n      lcName === 'is' && CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements && (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, value) || CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(value))) ;else {\n        return false;\n      }\n      /* Check value is safe. First, is attr inert? If so, is safe */\n    } else if (URI_SAFE_ATTRIBUTES[lcName]) ;else if (regExpTest(IS_ALLOWED_URI$1, stringReplace(value, ATTR_WHITESPACE, ''))) ;else if ((lcName === 'src' || lcName === 'xlink:href' || lcName === 'href') && lcTag !== 'script' && stringIndexOf(value, 'data:') === 0 && DATA_URI_TAGS[lcTag]) ;else if (ALLOW_UNKNOWN_PROTOCOLS && !regExpTest(IS_SCRIPT_OR_DATA, stringReplace(value, ATTR_WHITESPACE, ''))) ;else if (value) {\n      return false;\n    } else ;\n    return true;\n  };\n  /**\n   * _isBasicCustomElement\n   * checks if at least one dash is included in tagName, and it's not the first char\n   * for more sophisticated checking see https://github.com/sindresorhus/validate-element-name\n   *\n   * @param tagName name of the tag of the node to sanitize\n   * @returns Returns true if the tag name meets the basic criteria for a custom element, otherwise false.\n   */\n  const _isBasicCustomElement = function _isBasicCustomElement(tagName) {\n    return tagName !== 'annotation-xml' && stringMatch(tagName, CUSTOM_ELEMENT);\n  };\n  /**\n   * _sanitizeAttributes\n   *\n   * @protect attributes\n   * @protect nodeName\n   * @protect removeAttribute\n   * @protect setAttribute\n   *\n   * @param currentNode to sanitize\n   */\n  const _sanitizeAttributes = function _sanitizeAttributes(currentNode) {\n    /* Execute a hook if present */\n    _executeHooks(hooks.beforeSanitizeAttributes, currentNode, null);\n    const {\n      attributes\n    } = currentNode;\n    /* Check if we have attributes; if not we might have a text node */\n    if (!attributes || _isClobbered(currentNode)) {\n      return;\n    }\n    const hookEvent = {\n      attrName: '',\n      attrValue: '',\n      keepAttr: true,\n      allowedAttributes: ALLOWED_ATTR,\n      forceKeepAttr: undefined\n    };\n    let l = attributes.length;\n    /* Go backwards over all attributes; safely remove bad ones */\n    while (l--) {\n      const attr = attributes[l];\n      const {\n        name,\n        namespaceURI,\n        value: attrValue\n      } = attr;\n      const lcName = transformCaseFunc(name);\n      const initValue = attrValue;\n      let value = name === 'value' ? initValue : stringTrim(initValue);\n      /* Execute a hook if present */\n      hookEvent.attrName = lcName;\n      hookEvent.attrValue = value;\n      hookEvent.keepAttr = true;\n      hookEvent.forceKeepAttr = undefined; // Allows developers to see this is a property they can set\n      _executeHooks(hooks.uponSanitizeAttribute, currentNode, hookEvent);\n      value = hookEvent.attrValue;\n      /* Full DOM Clobbering protection via namespace isolation,\n       * Prefix id and name attributes with `user-content-`\n       */\n      if (SANITIZE_NAMED_PROPS && (lcName === 'id' || lcName === 'name')) {\n        // Remove the attribute with this value\n        _removeAttribute(name, currentNode);\n        // Prefix the value and later re-create the attribute with the sanitized value\n        value = SANITIZE_NAMED_PROPS_PREFIX + value;\n      }\n      /* Work around a security issue with comments inside attributes */\n      if (SAFE_FOR_XML && regExpTest(/((--!?|])>)|<\\/(style|title)/i, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n      /* Did the hooks approve of the attribute? */\n      if (hookEvent.forceKeepAttr) {\n        continue;\n      }\n      /* Did the hooks approve of the attribute? */\n      if (!hookEvent.keepAttr) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n      /* Work around a security issue in jQuery 3.0 */\n      if (!ALLOW_SELF_CLOSE_IN_ATTR && regExpTest(/\\/>/i, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n      /* Sanitize attribute content to be template-safe */\n      if (SAFE_FOR_TEMPLATES) {\n        arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], expr => {\n          value = stringReplace(value, expr, ' ');\n        });\n      }\n      /* Is `value` valid for this attribute? */\n      const lcTag = transformCaseFunc(currentNode.nodeName);\n      if (!_isValidAttribute(lcTag, lcName, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n      /* Handle attributes that require Trusted Types */\n      if (trustedTypesPolicy && typeof trustedTypes === 'object' && typeof trustedTypes.getAttributeType === 'function') {\n        if (namespaceURI) ;else {\n          switch (trustedTypes.getAttributeType(lcTag, lcName)) {\n            case 'TrustedHTML':\n              {\n                value = trustedTypesPolicy.createHTML(value);\n                break;\n              }\n            case 'TrustedScriptURL':\n              {\n                value = trustedTypesPolicy.createScriptURL(value);\n                break;\n              }\n          }\n        }\n      }\n      /* Handle invalid data-* attribute set by try-catching it */\n      if (value !== initValue) {\n        try {\n          if (namespaceURI) {\n            currentNode.setAttributeNS(namespaceURI, name, value);\n          } else {\n            /* Fallback to setAttribute() for browser-unrecognized namespaces e.g. \"x-schema\". */\n            currentNode.setAttribute(name, value);\n          }\n          if (_isClobbered(currentNode)) {\n            _forceRemove(currentNode);\n          } else {\n            arrayPop(DOMPurify.removed);\n          }\n        } catch (_) {\n          _removeAttribute(name, currentNode);\n        }\n      }\n    }\n    /* Execute a hook if present */\n    _executeHooks(hooks.afterSanitizeAttributes, currentNode, null);\n  };\n  /**\n   * _sanitizeShadowDOM\n   *\n   * @param fragment to iterate over recursively\n   */\n  const _sanitizeShadowDOM = function _sanitizeShadowDOM(fragment) {\n    let shadowNode = null;\n    const shadowIterator = _createNodeIterator(fragment);\n    /* Execute a hook if present */\n    _executeHooks(hooks.beforeSanitizeShadowDOM, fragment, null);\n    while (shadowNode = shadowIterator.nextNode()) {\n      /* Execute a hook if present */\n      _executeHooks(hooks.uponSanitizeShadowNode, shadowNode, null);\n      /* Sanitize tags and elements */\n      _sanitizeElements(shadowNode);\n      /* Check attributes next */\n      _sanitizeAttributes(shadowNode);\n      /* Deep shadow DOM detected */\n      if (shadowNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(shadowNode.content);\n      }\n    }\n    /* Execute a hook if present */\n    _executeHooks(hooks.afterSanitizeShadowDOM, fragment, null);\n  };\n  // eslint-disable-next-line complexity\n  DOMPurify.sanitize = function (dirty) {\n    let cfg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    let body = null;\n    let importedNode = null;\n    let currentNode = null;\n    let returnNode = null;\n    /* Make sure we have a string to sanitize.\n      DO NOT return early, as this will return the wrong type if\n      the user has requested a DOM object rather than a string */\n    IS_EMPTY_INPUT = !dirty;\n    if (IS_EMPTY_INPUT) {\n      dirty = '<!-->';\n    }\n    /* Stringify, in case dirty is an object */\n    if (typeof dirty !== 'string' && !_isNode(dirty)) {\n      if (typeof dirty.toString === 'function') {\n        dirty = dirty.toString();\n        if (typeof dirty !== 'string') {\n          throw typeErrorCreate('dirty is not a string, aborting');\n        }\n      } else {\n        throw typeErrorCreate('toString is not a function');\n      }\n    }\n    /* Return dirty HTML if DOMPurify cannot run */\n    if (!DOMPurify.isSupported) {\n      return dirty;\n    }\n    /* Assign config vars */\n    if (!SET_CONFIG) {\n      _parseConfig(cfg);\n    }\n    /* Clean up removed elements */\n    DOMPurify.removed = [];\n    /* Check if dirty is correctly typed for IN_PLACE */\n    if (typeof dirty === 'string') {\n      IN_PLACE = false;\n    }\n    if (IN_PLACE) {\n      /* Do some early pre-sanitization to avoid unsafe root nodes */\n      if (dirty.nodeName) {\n        const tagName = transformCaseFunc(dirty.nodeName);\n        if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n          throw typeErrorCreate('root node is forbidden and cannot be sanitized in-place');\n        }\n      }\n    } else if (dirty instanceof Node) {\n      /* If dirty is a DOM element, append to an empty document to avoid\n         elements being stripped by the parser */\n      body = _initDocument('<!---->');\n      importedNode = body.ownerDocument.importNode(dirty, true);\n      if (importedNode.nodeType === NODE_TYPE.element && importedNode.nodeName === 'BODY') {\n        /* Node is already a body, use as is */\n        body = importedNode;\n      } else if (importedNode.nodeName === 'HTML') {\n        body = importedNode;\n      } else {\n        // eslint-disable-next-line unicorn/prefer-dom-node-append\n        body.appendChild(importedNode);\n      }\n    } else {\n      /* Exit directly if we have nothing to do */\n      if (!RETURN_DOM && !SAFE_FOR_TEMPLATES && !WHOLE_DOCUMENT &&\n      // eslint-disable-next-line unicorn/prefer-includes\n      dirty.indexOf('<') === -1) {\n        return trustedTypesPolicy && RETURN_TRUSTED_TYPE ? trustedTypesPolicy.createHTML(dirty) : dirty;\n      }\n      /* Initialize the document to work on */\n      body = _initDocument(dirty);\n      /* Check we have a DOM node from the data */\n      if (!body) {\n        return RETURN_DOM ? null : RETURN_TRUSTED_TYPE ? emptyHTML : '';\n      }\n    }\n    /* Remove first element node (ours) if FORCE_BODY is set */\n    if (body && FORCE_BODY) {\n      _forceRemove(body.firstChild);\n    }\n    /* Get node iterator */\n    const nodeIterator = _createNodeIterator(IN_PLACE ? dirty : body);\n    /* Now start iterating over the created document */\n    while (currentNode = nodeIterator.nextNode()) {\n      /* Sanitize tags and elements */\n      _sanitizeElements(currentNode);\n      /* Check attributes next */\n      _sanitizeAttributes(currentNode);\n      /* Shadow DOM detected, sanitize it */\n      if (currentNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(currentNode.content);\n      }\n    }\n    /* If we sanitized `dirty` in-place, return it. */\n    if (IN_PLACE) {\n      return dirty;\n    }\n    /* Return sanitized string or DOM */\n    if (RETURN_DOM) {\n      if (RETURN_DOM_FRAGMENT) {\n        returnNode = createDocumentFragment.call(body.ownerDocument);\n        while (body.firstChild) {\n          // eslint-disable-next-line unicorn/prefer-dom-node-append\n          returnNode.appendChild(body.firstChild);\n        }\n      } else {\n        returnNode = body;\n      }\n      if (ALLOWED_ATTR.shadowroot || ALLOWED_ATTR.shadowrootmode) {\n        /*\n          AdoptNode() is not used because internal state is not reset\n          (e.g. the past names map of a HTMLFormElement), this is safe\n          in theory but we would rather not risk another attack vector.\n          The state that is cloned by importNode() is explicitly defined\n          by the specs.\n        */\n        returnNode = importNode.call(originalDocument, returnNode, true);\n      }\n      return returnNode;\n    }\n    let serializedHTML = WHOLE_DOCUMENT ? body.outerHTML : body.innerHTML;\n    /* Serialize doctype if allowed */\n    if (WHOLE_DOCUMENT && ALLOWED_TAGS['!doctype'] && body.ownerDocument && body.ownerDocument.doctype && body.ownerDocument.doctype.name && regExpTest(DOCTYPE_NAME, body.ownerDocument.doctype.name)) {\n      serializedHTML = '<!DOCTYPE ' + body.ownerDocument.doctype.name + '>\\n' + serializedHTML;\n    }\n    /* Sanitize final string template-safe */\n    if (SAFE_FOR_TEMPLATES) {\n      arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], expr => {\n        serializedHTML = stringReplace(serializedHTML, expr, ' ');\n      });\n    }\n    return trustedTypesPolicy && RETURN_TRUSTED_TYPE ? trustedTypesPolicy.createHTML(serializedHTML) : serializedHTML;\n  };\n  DOMPurify.setConfig = function () {\n    let cfg = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    _parseConfig(cfg);\n    SET_CONFIG = true;\n  };\n  DOMPurify.clearConfig = function () {\n    CONFIG = null;\n    SET_CONFIG = false;\n  };\n  DOMPurify.isValidAttribute = function (tag, attr, value) {\n    /* Initialize shared config vars if necessary. */\n    if (!CONFIG) {\n      _parseConfig({});\n    }\n    const lcTag = transformCaseFunc(tag);\n    const lcName = transformCaseFunc(attr);\n    return _isValidAttribute(lcTag, lcName, value);\n  };\n  DOMPurify.addHook = function (entryPoint, hookFunction) {\n    if (typeof hookFunction !== 'function') {\n      return;\n    }\n    arrayPush(hooks[entryPoint], hookFunction);\n  };\n  DOMPurify.removeHook = function (entryPoint, hookFunction) {\n    if (hookFunction !== undefined) {\n      const index = arrayLastIndexOf(hooks[entryPoint], hookFunction);\n      return index === -1 ? undefined : arraySplice(hooks[entryPoint], index, 1)[0];\n    }\n    return arrayPop(hooks[entryPoint]);\n  };\n  DOMPurify.removeHooks = function (entryPoint) {\n    hooks[entryPoint] = [];\n  };\n  DOMPurify.removeAllHooks = function () {\n    hooks = _createHooksMap();\n  };\n  return DOMPurify;\n}\nvar purify = createDOMPurify();\nexport { purify as default };", "map": {"version": 3, "names": ["entries", "setPrototypeOf", "isFrozen", "getPrototypeOf", "getOwnPropertyDescriptor", "Object", "freeze", "seal", "create", "apply", "construct", "Reflect", "x", "fun", "thisValue", "args", "Func", "arrayForEach", "unapply", "Array", "prototype", "for<PERSON>ach", "arrayLastIndexOf", "lastIndexOf", "arrayPop", "pop", "arrayPush", "push", "arraySplice", "splice", "stringToLowerCase", "String", "toLowerCase", "stringToString", "toString", "stringMatch", "match", "stringReplace", "replace", "stringIndexOf", "indexOf", "stringTrim", "trim", "objectHasOwnProperty", "hasOwnProperty", "regExpTest", "RegExp", "test", "typeErrorCreate", "unconstruct", "TypeError", "func", "thisArg", "lastIndex", "_len", "arguments", "length", "_key", "_len2", "_key2", "addToSet", "set", "array", "transformCaseFunc", "undefined", "l", "element", "lcElement", "cleanArray", "index", "isPropertyExist", "clone", "object", "newObject", "property", "value", "isArray", "constructor", "lookupGetter", "prop", "desc", "get", "fallback<PERSON><PERSON><PERSON>", "html$1", "svg$1", "svgFilters", "svgDisallowed", "mathMl$1", "mathMlDisallowed", "text", "html", "svg", "mathMl", "xml", "MUSTACHE_EXPR", "ERB_EXPR", "TMPLIT_EXPR", "DATA_ATTR", "ARIA_ATTR", "IS_ALLOWED_URI", "IS_SCRIPT_OR_DATA", "ATTR_WHITESPACE", "DOCTYPE_NAME", "CUSTOM_ELEMENT", "NODE_TYPE", "attribute", "cdataSection", "entityReference", "entityNode", "progressingInstruction", "comment", "document", "documentType", "documentFragment", "notation", "getGlobal", "window", "_createTrustedTypesPolicy", "trustedTypes", "purifyHostElement", "createPolicy", "suffix", "ATTR_NAME", "hasAttribute", "getAttribute", "policyName", "createHTML", "createScriptURL", "scriptUrl", "_", "console", "warn", "_createHooksMap", "afterSanitizeAttributes", "afterSanitizeElements", "afterSanitizeShadowDOM", "beforeSanitizeAttributes", "beforeSanitizeElements", "beforeSanitizeShadowDOM", "uponSanitizeAttribute", "uponSanitizeElement", "uponSanitizeShadowNode", "createDOMPurify", "DOMPurify", "root", "version", "removed", "nodeType", "Element", "isSupported", "originalDocument", "currentScript", "DocumentFragment", "HTMLTemplateElement", "Node", "Node<PERSON><PERSON><PERSON>", "NamedNodeMap", "MozNamedAttrMap", "HTMLFormElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ElementPrototype", "cloneNode", "remove", "getNextSibling", "getChildNodes", "getParentNode", "template", "createElement", "content", "ownerDocument", "trustedTypesPolicy", "emptyHTML", "implementation", "createNodeIterator", "createDocumentFragment", "getElementsByTagName", "importNode", "hooks", "createHTMLDocument", "EXPRESSIONS", "IS_ALLOWED_URI$1", "ALLOWED_TAGS", "DEFAULT_ALLOWED_TAGS", "ALLOWED_ATTR", "DEFAULT_ALLOWED_ATTR", "CUSTOM_ELEMENT_HANDLING", "tagNameCheck", "writable", "configurable", "enumerable", "attributeNameCheck", "allowCustomizedBuiltInElements", "FORBID_TAGS", "FORBID_ATTR", "ALLOW_ARIA_ATTR", "ALLOW_DATA_ATTR", "ALLOW_UNKNOWN_PROTOCOLS", "ALLOW_SELF_CLOSE_IN_ATTR", "SAFE_FOR_TEMPLATES", "SAFE_FOR_XML", "WHOLE_DOCUMENT", "SET_CONFIG", "FORCE_BODY", "RETURN_DOM", "RETURN_DOM_FRAGMENT", "RETURN_TRUSTED_TYPE", "SANITIZE_DOM", "SANITIZE_NAMED_PROPS", "SANITIZE_NAMED_PROPS_PREFIX", "KEEP_CONTENT", "IN_PLACE", "USE_PROFILES", "FORBID_CONTENTS", "DEFAULT_FORBID_CONTENTS", "DATA_URI_TAGS", "DEFAULT_DATA_URI_TAGS", "URI_SAFE_ATTRIBUTES", "DEFAULT_URI_SAFE_ATTRIBUTES", "MATHML_NAMESPACE", "SVG_NAMESPACE", "HTML_NAMESPACE", "NAMESPACE", "IS_EMPTY_INPUT", "ALLOWED_NAMESPACES", "DEFAULT_ALLOWED_NAMESPACES", "MATHML_TEXT_INTEGRATION_POINTS", "HTML_INTEGRATION_POINTS", "COMMON_SVG_AND_HTML_ELEMENTS", "PARSER_MEDIA_TYPE", "SUPPORTED_PARSER_MEDIA_TYPES", "DEFAULT_PARSER_MEDIA_TYPE", "CONFIG", "formElement", "isRegexOrFunction", "testValue", "Function", "_parseConfig", "cfg", "ADD_URI_SAFE_ATTR", "ADD_DATA_URI_TAGS", "ALLOWED_URI_REGEXP", "ADD_TAGS", "ADD_ATTR", "table", "tbody", "TRUSTED_TYPES_POLICY", "ALL_SVG_TAGS", "ALL_MATHML_TAGS", "_checkValidNamespace", "parent", "tagName", "namespaceURI", "parentTagName", "Boolean", "_forceRemove", "node", "<PERSON><PERSON><PERSON><PERSON>", "_removeAttribute", "name", "getAttributeNode", "from", "removeAttribute", "setAttribute", "_initDocument", "dirty", "doc", "leadingWhitespace", "matches", "dirtyPayload", "parseFromString", "documentElement", "createDocument", "innerHTML", "body", "insertBefore", "createTextNode", "childNodes", "call", "_createNodeIterator", "SHOW_ELEMENT", "SHOW_COMMENT", "SHOW_TEXT", "SHOW_PROCESSING_INSTRUCTION", "SHOW_CDATA_SECTION", "_isClobbered", "nodeName", "textContent", "attributes", "hasChildNodes", "_isNode", "_executeHooks", "currentNode", "data", "hook", "_sanitizeElements", "allowedTags", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "_isBasicCustomElement", "parentNode", "childCount", "i", "child<PERSON>lone", "__removalCount", "expr", "_isValidAttribute", "lcTag", "lcName", "_sanitizeAttributes", "hookEvent", "attrName", "attrValue", "keepAttr", "allowedAttributes", "forceKeepAttr", "attr", "initValue", "getAttributeType", "setAttributeNS", "_sanitizeShadowDOM", "fragment", "shadowNode", "shadowIterator", "nextNode", "sanitize", "importedNode", "returnNode", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "nodeIterator", "shadowroot", "shadowrootmode", "serializedHTML", "outerHTML", "doctype", "setConfig", "clearConfig", "isValidAttribute", "tag", "addHook", "entryPoint", "hookFunction", "removeH<PERSON>", "removeHooks", "removeAllHooks", "purify"], "sources": ["../src/utils.ts", "../src/tags.ts", "../src/attrs.ts", "../src/regexp.ts", "../src/purify.ts"], "sourcesContent": ["const {\n  entries,\n  setPrototypeOf,\n  isFrozen,\n  getPrototypeOf,\n  getOwnPropertyDescriptor,\n} = Object;\n\nlet { freeze, seal, create } = Object; // eslint-disable-line import/no-mutable-exports\nlet { apply, construct } = typeof Reflect !== 'undefined' && Reflect;\n\nif (!freeze) {\n  freeze = function (x) {\n    return x;\n  };\n}\n\nif (!seal) {\n  seal = function (x) {\n    return x;\n  };\n}\n\nif (!apply) {\n  apply = function (fun, thisValue, args) {\n    return fun.apply(thisValue, args);\n  };\n}\n\nif (!construct) {\n  construct = function (Func, args) {\n    return new Func(...args);\n  };\n}\n\nconst arrayForEach = unapply(Array.prototype.forEach);\nconst arrayIndexOf = unapply(Array.prototype.indexOf);\nconst arrayLastIndexOf = unapply(Array.prototype.lastIndexOf);\nconst arrayPop = unapply(Array.prototype.pop);\nconst arrayPush = unapply(Array.prototype.push);\nconst arraySlice = unapply(Array.prototype.slice);\nconst arraySplice = unapply(Array.prototype.splice);\n\nconst stringToLowerCase = unapply(String.prototype.toLowerCase);\nconst stringToString = unapply(String.prototype.toString);\nconst stringMatch = unapply(String.prototype.match);\nconst stringReplace = unapply(String.prototype.replace);\nconst stringIndexOf = unapply(String.prototype.indexOf);\nconst stringTrim = unapply(String.prototype.trim);\n\nconst objectHasOwnProperty = unapply(Object.prototype.hasOwnProperty);\n\nconst regExpTest = unapply(RegExp.prototype.test);\n\nconst typeErrorCreate = unconstruct(TypeError);\n\n/**\n * Creates a new function that calls the given function with a specified thisArg and arguments.\n *\n * @param func - The function to be wrapped and called.\n * @returns A new function that calls the given function with a specified thisArg and arguments.\n */\nfunction unapply<T>(\n  func: (thisArg: any, ...args: any[]) => T\n): (thisArg: any, ...args: any[]) => T {\n  return (thisArg: any, ...args: any[]): T => {\n    if (thisArg instanceof RegExp) {\n      thisArg.lastIndex = 0;\n    }\n\n    return apply(func, thisArg, args);\n  };\n}\n\n/**\n * Creates a new function that constructs an instance of the given constructor function with the provided arguments.\n *\n * @param func - The constructor function to be wrapped and called.\n * @returns A new function that constructs an instance of the given constructor function with the provided arguments.\n */\nfunction unconstruct<T>(func: (...args: any[]) => T): (...args: any[]) => T {\n  return (...args: any[]): T => construct(func, args);\n}\n\n/**\n * Add properties to a lookup table\n *\n * @param set - The set to which elements will be added.\n * @param array - The array containing elements to be added to the set.\n * @param transformCaseFunc - An optional function to transform the case of each element before adding to the set.\n * @returns The modified set with added elements.\n */\nfunction addToSet(\n  set: Record<string, any>,\n  array: readonly any[],\n  transformCaseFunc: ReturnType<typeof unapply<string>> = stringToLowerCase\n): Record<string, any> {\n  if (setPrototypeOf) {\n    // Make 'in' and truthy checks like Boolean(set.constructor)\n    // independent of any properties defined on Object.prototype.\n    // Prevent prototype setters from intercepting set as a this value.\n    setPrototypeOf(set, null);\n  }\n\n  let l = array.length;\n  while (l--) {\n    let element = array[l];\n    if (typeof element === 'string') {\n      const lcElement = transformCaseFunc(element);\n      if (lcElement !== element) {\n        // Config presets (e.g. tags.js, attrs.js) are immutable.\n        if (!isFrozen(array)) {\n          (array as any[])[l] = lcElement;\n        }\n\n        element = lcElement;\n      }\n    }\n\n    set[element] = true;\n  }\n\n  return set;\n}\n\n/**\n * Clean up an array to harden against CSPP\n *\n * @param array - The array to be cleaned.\n * @returns The cleaned version of the array\n */\nfunction cleanArray<T>(array: T[]): Array<T | null> {\n  for (let index = 0; index < array.length; index++) {\n    const isPropertyExist = objectHasOwnProperty(array, index);\n\n    if (!isPropertyExist) {\n      array[index] = null;\n    }\n  }\n\n  return array;\n}\n\n/**\n * Shallow clone an object\n *\n * @param object - The object to be cloned.\n * @returns A new object that copies the original.\n */\nfunction clone<T extends Record<string, any>>(object: T): T {\n  const newObject = create(null);\n\n  for (const [property, value] of entries(object)) {\n    const isPropertyExist = objectHasOwnProperty(object, property);\n\n    if (isPropertyExist) {\n      if (Array.isArray(value)) {\n        newObject[property] = cleanArray(value);\n      } else if (\n        value &&\n        typeof value === 'object' &&\n        value.constructor === Object\n      ) {\n        newObject[property] = clone(value);\n      } else {\n        newObject[property] = value;\n      }\n    }\n  }\n\n  return newObject;\n}\n\n/**\n * This method automatically checks if the prop is function or getter and behaves accordingly.\n *\n * @param object - The object to look up the getter function in its prototype chain.\n * @param prop - The property name for which to find the getter function.\n * @returns The getter function found in the prototype chain or a fallback function.\n */\nfunction lookupGetter<T extends Record<string, any>>(\n  object: T,\n  prop: string\n): ReturnType<typeof unapply<any>> | (() => null) {\n  while (object !== null) {\n    const desc = getOwnPropertyDescriptor(object, prop);\n\n    if (desc) {\n      if (desc.get) {\n        return unapply(desc.get);\n      }\n\n      if (typeof desc.value === 'function') {\n        return unapply(desc.value);\n      }\n    }\n\n    object = getPrototypeOf(object);\n  }\n\n  function fallbackValue(): null {\n    return null;\n  }\n\n  return fallbackValue;\n}\n\nexport {\n  // Array\n  arrayForEach,\n  arrayIndexOf,\n  arrayLastIndexOf,\n  arrayPop,\n  arrayPush,\n  arraySlice,\n  arraySplice,\n  // Object\n  entries,\n  freeze,\n  getPrototypeOf,\n  getOwnPropertyDescriptor,\n  isFrozen,\n  setPrototypeOf,\n  seal,\n  clone,\n  create,\n  objectHasOwnProperty,\n  // RegExp\n  regExpTest,\n  // String\n  stringIndexOf,\n  stringMatch,\n  stringReplace,\n  stringToLowerCase,\n  stringToString,\n  stringTrim,\n  // Errors\n  typeErrorCreate,\n  // Other\n  lookupGetter,\n  addToSet,\n  // Reflect\n  unapply,\n  unconstruct,\n};\n", "import { freeze } from './utils.js';\n\nexport const html = freeze([\n  'a',\n  'abbr',\n  'acronym',\n  'address',\n  'area',\n  'article',\n  'aside',\n  'audio',\n  'b',\n  'bdi',\n  'bdo',\n  'big',\n  'blink',\n  'blockquote',\n  'body',\n  'br',\n  'button',\n  'canvas',\n  'caption',\n  'center',\n  'cite',\n  'code',\n  'col',\n  'colgroup',\n  'content',\n  'data',\n  'datalist',\n  'dd',\n  'decorator',\n  'del',\n  'details',\n  'dfn',\n  'dialog',\n  'dir',\n  'div',\n  'dl',\n  'dt',\n  'element',\n  'em',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'font',\n  'footer',\n  'form',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'head',\n  'header',\n  'hgroup',\n  'hr',\n  'html',\n  'i',\n  'img',\n  'input',\n  'ins',\n  'kbd',\n  'label',\n  'legend',\n  'li',\n  'main',\n  'map',\n  'mark',\n  'marquee',\n  'menu',\n  'menuitem',\n  'meter',\n  'nav',\n  'nobr',\n  'ol',\n  'optgroup',\n  'option',\n  'output',\n  'p',\n  'picture',\n  'pre',\n  'progress',\n  'q',\n  'rp',\n  'rt',\n  'ruby',\n  's',\n  'samp',\n  'section',\n  'select',\n  'shadow',\n  'small',\n  'source',\n  'spacer',\n  'span',\n  'strike',\n  'strong',\n  'style',\n  'sub',\n  'summary',\n  'sup',\n  'table',\n  'tbody',\n  'td',\n  'template',\n  'textarea',\n  'tfoot',\n  'th',\n  'thead',\n  'time',\n  'tr',\n  'track',\n  'tt',\n  'u',\n  'ul',\n  'var',\n  'video',\n  'wbr',\n] as const);\n\nexport const svg = freeze([\n  'svg',\n  'a',\n  'altglyph',\n  'altglyphdef',\n  'altglyphitem',\n  'animatecolor',\n  'animatemotion',\n  'animatetransform',\n  'circle',\n  'clippath',\n  'defs',\n  'desc',\n  'ellipse',\n  'filter',\n  'font',\n  'g',\n  'glyph',\n  'glyphref',\n  'hkern',\n  'image',\n  'line',\n  'lineargradient',\n  'marker',\n  'mask',\n  'metadata',\n  'mpath',\n  'path',\n  'pattern',\n  'polygon',\n  'polyline',\n  'radialgradient',\n  'rect',\n  'stop',\n  'style',\n  'switch',\n  'symbol',\n  'text',\n  'textpath',\n  'title',\n  'tref',\n  'tspan',\n  'view',\n  'vkern',\n] as const);\n\nexport const svgFilters = freeze([\n  'feBlend',\n  'feColorMatrix',\n  'feComponentTransfer',\n  'feComposite',\n  'feConvolveMatrix',\n  'feDiffuseLighting',\n  'feDisplacementMap',\n  'feDistantLight',\n  'feDropShadow',\n  'feFlood',\n  'feFuncA',\n  'feFuncB',\n  'feFuncG',\n  'feFuncR',\n  'feGaussianBlur',\n  'feImage',\n  'feMerge',\n  'feMergeNode',\n  'feMorphology',\n  'feOffset',\n  'fePointLight',\n  'feSpecularLighting',\n  'feSpotLight',\n  'feTile',\n  'feTurbulence',\n] as const);\n\n// List of SVG elements that are disallowed by default.\n// We still need to know them so that we can do namespace\n// checks properly in case one wants to add them to\n// allow-list.\nexport const svgDisallowed = freeze([\n  'animate',\n  'color-profile',\n  'cursor',\n  'discard',\n  'font-face',\n  'font-face-format',\n  'font-face-name',\n  'font-face-src',\n  'font-face-uri',\n  'foreignobject',\n  'hatch',\n  'hatchpath',\n  'mesh',\n  'meshgradient',\n  'meshpatch',\n  'meshrow',\n  'missing-glyph',\n  'script',\n  'set',\n  'solidcolor',\n  'unknown',\n  'use',\n] as const);\n\nexport const mathMl = freeze([\n  'math',\n  'menclose',\n  'merror',\n  'mfenced',\n  'mfrac',\n  'mglyph',\n  'mi',\n  'mlabeledtr',\n  'mmultiscripts',\n  'mn',\n  'mo',\n  'mover',\n  'mpadded',\n  'mphantom',\n  'mroot',\n  'mrow',\n  'ms',\n  'mspace',\n  'msqrt',\n  'mstyle',\n  'msub',\n  'msup',\n  'msubsup',\n  'mtable',\n  'mtd',\n  'mtext',\n  'mtr',\n  'munder',\n  'munderover',\n  'mprescripts',\n] as const);\n\n// Similarly to SVG, we want to know all MathML elements,\n// even those that we disallow by default.\nexport const mathMlDisallowed = freeze([\n  'maction',\n  'maligngroup',\n  'malignmark',\n  'mlongdiv',\n  'mscarries',\n  'mscarry',\n  'msgroup',\n  'mstack',\n  'msline',\n  'msrow',\n  'semantics',\n  'annotation',\n  'annotation-xml',\n  'mprescripts',\n  'none',\n] as const);\n\nexport const text = freeze(['#text'] as const);\n", "import { freeze } from './utils.js';\n\nexport const html = freeze([\n  'accept',\n  'action',\n  'align',\n  'alt',\n  'autocapitalize',\n  'autocomplete',\n  'autopictureinpicture',\n  'autoplay',\n  'background',\n  'bgcolor',\n  'border',\n  'capture',\n  'cellpadding',\n  'cellspacing',\n  'checked',\n  'cite',\n  'class',\n  'clear',\n  'color',\n  'cols',\n  'colspan',\n  'controls',\n  'controlslist',\n  'coords',\n  'crossorigin',\n  'datetime',\n  'decoding',\n  'default',\n  'dir',\n  'disabled',\n  'disablepictureinpicture',\n  'disableremoteplayback',\n  'download',\n  'draggable',\n  'enctype',\n  'enterkeyhint',\n  'face',\n  'for',\n  'headers',\n  'height',\n  'hidden',\n  'high',\n  'href',\n  'hreflang',\n  'id',\n  'inputmode',\n  'integrity',\n  'ismap',\n  'kind',\n  'label',\n  'lang',\n  'list',\n  'loading',\n  'loop',\n  'low',\n  'max',\n  'maxlength',\n  'media',\n  'method',\n  'min',\n  'minlength',\n  'multiple',\n  'muted',\n  'name',\n  'nonce',\n  'noshade',\n  'novalidate',\n  'nowrap',\n  'open',\n  'optimum',\n  'pattern',\n  'placeholder',\n  'playsinline',\n  'popover',\n  'popovertarget',\n  'popovertargetaction',\n  'poster',\n  'preload',\n  'pubdate',\n  'radiogroup',\n  'readonly',\n  'rel',\n  'required',\n  'rev',\n  'reversed',\n  'role',\n  'rows',\n  'rowspan',\n  'spellcheck',\n  'scope',\n  'selected',\n  'shape',\n  'size',\n  'sizes',\n  'span',\n  'srclang',\n  'start',\n  'src',\n  'srcset',\n  'step',\n  'style',\n  'summary',\n  'tabindex',\n  'title',\n  'translate',\n  'type',\n  'usemap',\n  'valign',\n  'value',\n  'width',\n  'wrap',\n  'xmlns',\n  'slot',\n] as const);\n\nexport const svg = freeze([\n  'accent-height',\n  'accumulate',\n  'additive',\n  'alignment-baseline',\n  'amplitude',\n  'ascent',\n  'attributename',\n  'attributetype',\n  'azimuth',\n  'basefrequency',\n  'baseline-shift',\n  'begin',\n  'bias',\n  'by',\n  'class',\n  'clip',\n  'clippathunits',\n  'clip-path',\n  'clip-rule',\n  'color',\n  'color-interpolation',\n  'color-interpolation-filters',\n  'color-profile',\n  'color-rendering',\n  'cx',\n  'cy',\n  'd',\n  'dx',\n  'dy',\n  'diffuseconstant',\n  'direction',\n  'display',\n  'divisor',\n  'dur',\n  'edgemode',\n  'elevation',\n  'end',\n  'exponent',\n  'fill',\n  'fill-opacity',\n  'fill-rule',\n  'filter',\n  'filterunits',\n  'flood-color',\n  'flood-opacity',\n  'font-family',\n  'font-size',\n  'font-size-adjust',\n  'font-stretch',\n  'font-style',\n  'font-variant',\n  'font-weight',\n  'fx',\n  'fy',\n  'g1',\n  'g2',\n  'glyph-name',\n  'glyphref',\n  'gradientunits',\n  'gradienttransform',\n  'height',\n  'href',\n  'id',\n  'image-rendering',\n  'in',\n  'in2',\n  'intercept',\n  'k',\n  'k1',\n  'k2',\n  'k3',\n  'k4',\n  'kerning',\n  'keypoints',\n  'keysplines',\n  'keytimes',\n  'lang',\n  'lengthadjust',\n  'letter-spacing',\n  'kernelmatrix',\n  'kernelunitlength',\n  'lighting-color',\n  'local',\n  'marker-end',\n  'marker-mid',\n  'marker-start',\n  'markerheight',\n  'markerunits',\n  'markerwidth',\n  'maskcontentunits',\n  'maskunits',\n  'max',\n  'mask',\n  'media',\n  'method',\n  'mode',\n  'min',\n  'name',\n  'numoctaves',\n  'offset',\n  'operator',\n  'opacity',\n  'order',\n  'orient',\n  'orientation',\n  'origin',\n  'overflow',\n  'paint-order',\n  'path',\n  'pathlength',\n  'patterncontentunits',\n  'patterntransform',\n  'patternunits',\n  'points',\n  'preservealpha',\n  'preserveaspectratio',\n  'primitiveunits',\n  'r',\n  'rx',\n  'ry',\n  'radius',\n  'refx',\n  'refy',\n  'repeatcount',\n  'repeatdur',\n  'restart',\n  'result',\n  'rotate',\n  'scale',\n  'seed',\n  'shape-rendering',\n  'slope',\n  'specularconstant',\n  'specularexponent',\n  'spreadmethod',\n  'startoffset',\n  'stddeviation',\n  'stitchtiles',\n  'stop-color',\n  'stop-opacity',\n  'stroke-dasharray',\n  'stroke-dashoffset',\n  'stroke-linecap',\n  'stroke-linejoin',\n  'stroke-miterlimit',\n  'stroke-opacity',\n  'stroke',\n  'stroke-width',\n  'style',\n  'surfacescale',\n  'systemlanguage',\n  'tabindex',\n  'tablevalues',\n  'targetx',\n  'targety',\n  'transform',\n  'transform-origin',\n  'text-anchor',\n  'text-decoration',\n  'text-rendering',\n  'textlength',\n  'type',\n  'u1',\n  'u2',\n  'unicode',\n  'values',\n  'viewbox',\n  'visibility',\n  'version',\n  'vert-adv-y',\n  'vert-origin-x',\n  'vert-origin-y',\n  'width',\n  'word-spacing',\n  'wrap',\n  'writing-mode',\n  'xchannelselector',\n  'ychannelselector',\n  'x',\n  'x1',\n  'x2',\n  'xmlns',\n  'y',\n  'y1',\n  'y2',\n  'z',\n  'zoomandpan',\n] as const);\n\nexport const mathMl = freeze([\n  'accent',\n  'accentunder',\n  'align',\n  'bevelled',\n  'close',\n  'columnsalign',\n  'columnlines',\n  'columnspan',\n  'denomalign',\n  'depth',\n  'dir',\n  'display',\n  'displaystyle',\n  'encoding',\n  'fence',\n  'frame',\n  'height',\n  'href',\n  'id',\n  'largeop',\n  'length',\n  'linethickness',\n  'lspace',\n  'lquote',\n  'mathbackground',\n  'mathcolor',\n  'mathsize',\n  'mathvariant',\n  'maxsize',\n  'minsize',\n  'movablelimits',\n  'notation',\n  'numalign',\n  'open',\n  'rowalign',\n  'rowlines',\n  'rowspacing',\n  'rowspan',\n  'rspace',\n  'rquote',\n  'scriptlevel',\n  'scriptminsize',\n  'scriptsizemultiplier',\n  'selection',\n  'separator',\n  'separators',\n  'stretchy',\n  'subscriptshift',\n  'supscriptshift',\n  'symmetric',\n  'voffset',\n  'width',\n  'xmlns',\n]);\n\nexport const xml = freeze([\n  'xlink:href',\n  'xml:id',\n  'xlink:title',\n  'xml:space',\n  'xmlns:xlink',\n] as const);\n", "import { seal } from './utils.js';\n\n// eslint-disable-next-line unicorn/better-regex\nexport const MUSTACHE_EXPR = seal(/\\{\\{[\\w\\W]*|[\\w\\W]*\\}\\}/gm); // Specify template detection regex for SAFE_FOR_TEMPLATES mode\nexport const ERB_EXPR = seal(/<%[\\w\\W]*|[\\w\\W]*%>/gm);\nexport const TMPLIT_EXPR = seal(/\\$\\{[\\w\\W]*/gm); // eslint-disable-line unicorn/better-regex\nexport const DATA_ATTR = seal(/^data-[\\-\\w.\\u00B7-\\uFFFF]+$/); // eslint-disable-line no-useless-escape\nexport const ARIA_ATTR = seal(/^aria-[\\-\\w]+$/); // eslint-disable-line no-useless-escape\nexport const IS_ALLOWED_URI = seal(\n  /^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\\-]+(?:[^a-z+.\\-:]|$))/i // eslint-disable-line no-useless-escape\n);\nexport const IS_SCRIPT_OR_DATA = seal(/^(?:\\w+script|data):/i);\nexport const ATTR_WHITESPACE = seal(\n  /[\\u0000-\\u0020\\u00A0\\u1680\\u180E\\u2000-\\u2029\\u205F\\u3000]/g // eslint-disable-line no-control-regex\n);\nexport const DOCTYPE_NAME = seal(/^html$/i);\nexport const CUSTOM_ELEMENT = seal(/^[a-z][.\\w]*(-[.\\w]+)+$/i);\n", "/* eslint-disable @typescript-eslint/indent */\n\nimport type { TrustedHTML, TrustedTypesWindow } from 'trusted-types/lib';\nimport type { Config, UseProfilesConfig } from './config';\nimport * as TAGS from './tags.js';\nimport * as ATTRS from './attrs.js';\nimport * as EXPRESSIONS from './regexp.js';\nimport {\n  addToSet,\n  clone,\n  entries,\n  freeze,\n  arrayForEach,\n  arrayLastIndexOf,\n  arrayPop,\n  arrayPush,\n  arraySplice,\n  stringMatch,\n  stringReplace,\n  stringToLowerCase,\n  stringToString,\n  stringIndexOf,\n  stringTrim,\n  regExpTest,\n  typeErrorCreate,\n  lookupGetter,\n  create,\n  objectHasOwnProperty,\n} from './utils.js';\n\nexport type { Config } from './config';\n\ndeclare const VERSION: string;\n\n// https://developer.mozilla.org/en-US/docs/Web/API/Node/nodeType\nconst NODE_TYPE = {\n  element: 1,\n  attribute: 2,\n  text: 3,\n  cdataSection: 4,\n  entityReference: 5, // Deprecated\n  entityNode: 6, // Deprecated\n  progressingInstruction: 7,\n  comment: 8,\n  document: 9,\n  documentType: 10,\n  documentFragment: 11,\n  notation: 12, // Deprecated\n};\n\nconst getGlobal = function (): WindowLike {\n  return typeof window === 'undefined' ? null : window;\n};\n\n/**\n * Creates a no-op policy for internal use only.\n * Don't export this function outside this module!\n * @param trustedTypes The policy factory.\n * @param purifyHostElement The Script element used to load DOMPurify (to determine policy name suffix).\n * @return The policy created (or null, if Trusted Types\n * are not supported or creating the policy failed).\n */\nconst _createTrustedTypesPolicy = function (\n  trustedTypes: TrustedTypePolicyFactory,\n  purifyHostElement: HTMLScriptElement\n) {\n  if (\n    typeof trustedTypes !== 'object' ||\n    typeof trustedTypes.createPolicy !== 'function'\n  ) {\n    return null;\n  }\n\n  // Allow the callers to control the unique policy name\n  // by adding a data-tt-policy-suffix to the script element with the DOMPurify.\n  // Policy creation with duplicate names throws in Trusted Types.\n  let suffix = null;\n  const ATTR_NAME = 'data-tt-policy-suffix';\n  if (purifyHostElement && purifyHostElement.hasAttribute(ATTR_NAME)) {\n    suffix = purifyHostElement.getAttribute(ATTR_NAME);\n  }\n\n  const policyName = 'dompurify' + (suffix ? '#' + suffix : '');\n\n  try {\n    return trustedTypes.createPolicy(policyName, {\n      createHTML(html) {\n        return html;\n      },\n      createScriptURL(scriptUrl) {\n        return scriptUrl;\n      },\n    });\n  } catch (_) {\n    // Policy creation failed (most likely another DOMPurify script has\n    // already run). Skip creating the policy, as this will only cause errors\n    // if TT are enforced.\n    console.warn(\n      'TrustedTypes policy ' + policyName + ' could not be created.'\n    );\n    return null;\n  }\n};\n\nconst _createHooksMap = function (): HooksMap {\n  return {\n    afterSanitizeAttributes: [],\n    afterSanitizeElements: [],\n    afterSanitizeShadowDOM: [],\n    beforeSanitizeAttributes: [],\n    beforeSanitizeElements: [],\n    beforeSanitizeShadowDOM: [],\n    uponSanitizeAttribute: [],\n    uponSanitizeElement: [],\n    uponSanitizeShadowNode: [],\n  };\n};\n\nfunction createDOMPurify(window: WindowLike = getGlobal()): DOMPurify {\n  const DOMPurify: DOMPurify = (root: WindowLike) => createDOMPurify(root);\n\n  DOMPurify.version = VERSION;\n\n  DOMPurify.removed = [];\n\n  if (\n    !window ||\n    !window.document ||\n    window.document.nodeType !== NODE_TYPE.document ||\n    !window.Element\n  ) {\n    // Not running in a browser, provide a factory function\n    // so that you can pass your own Window\n    DOMPurify.isSupported = false;\n\n    return DOMPurify;\n  }\n\n  let { document } = window;\n\n  const originalDocument = document;\n  const currentScript: HTMLScriptElement =\n    originalDocument.currentScript as HTMLScriptElement;\n  const {\n    DocumentFragment,\n    HTMLTemplateElement,\n    Node,\n    Element,\n    NodeFilter,\n    NamedNodeMap = window.NamedNodeMap || (window as any).MozNamedAttrMap,\n    HTMLFormElement,\n    DOMParser,\n    trustedTypes,\n  } = window;\n\n  const ElementPrototype = Element.prototype;\n\n  const cloneNode = lookupGetter(ElementPrototype, 'cloneNode');\n  const remove = lookupGetter(ElementPrototype, 'remove');\n  const getNextSibling = lookupGetter(ElementPrototype, 'nextSibling');\n  const getChildNodes = lookupGetter(ElementPrototype, 'childNodes');\n  const getParentNode = lookupGetter(ElementPrototype, 'parentNode');\n\n  // As per issue #47, the web-components registry is inherited by a\n  // new document created via createHTMLDocument. As per the spec\n  // (http://w3c.github.io/webcomponents/spec/custom/#creating-and-passing-registries)\n  // a new empty registry is used when creating a template contents owner\n  // document, so we use that as our parent document to ensure nothing\n  // is inherited.\n  if (typeof HTMLTemplateElement === 'function') {\n    const template = document.createElement('template');\n    if (template.content && template.content.ownerDocument) {\n      document = template.content.ownerDocument;\n    }\n  }\n\n  let trustedTypesPolicy;\n  let emptyHTML = '';\n\n  const {\n    implementation,\n    createNodeIterator,\n    createDocumentFragment,\n    getElementsByTagName,\n  } = document;\n  const { importNode } = originalDocument;\n\n  let hooks = _createHooksMap();\n\n  /**\n   * Expose whether this browser supports running the full DOMPurify.\n   */\n  DOMPurify.isSupported =\n    typeof entries === 'function' &&\n    typeof getParentNode === 'function' &&\n    implementation &&\n    implementation.createHTMLDocument !== undefined;\n\n  const {\n    MUSTACHE_EXPR,\n    ERB_EXPR,\n    TMPLIT_EXPR,\n    DATA_ATTR,\n    ARIA_ATTR,\n    IS_SCRIPT_OR_DATA,\n    ATTR_WHITESPACE,\n    CUSTOM_ELEMENT,\n  } = EXPRESSIONS;\n\n  let { IS_ALLOWED_URI } = EXPRESSIONS;\n\n  /**\n   * We consider the elements and attributes below to be safe. Ideally\n   * don't add any new ones but feel free to remove unwanted ones.\n   */\n\n  /* allowed element names */\n  let ALLOWED_TAGS = null;\n  const DEFAULT_ALLOWED_TAGS = addToSet({}, [\n    ...TAGS.html,\n    ...TAGS.svg,\n    ...TAGS.svgFilters,\n    ...TAGS.mathMl,\n    ...TAGS.text,\n  ]);\n\n  /* Allowed attribute names */\n  let ALLOWED_ATTR = null;\n  const DEFAULT_ALLOWED_ATTR = addToSet({}, [\n    ...ATTRS.html,\n    ...ATTRS.svg,\n    ...ATTRS.mathMl,\n    ...ATTRS.xml,\n  ]);\n\n  /*\n   * Configure how DOMPurify should handle custom elements and their attributes as well as customized built-in elements.\n   * @property {RegExp|Function|null} tagNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any custom elements)\n   * @property {RegExp|Function|null} attributeNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any attributes not on the allow list)\n   * @property {boolean} allowCustomizedBuiltInElements allow custom elements derived from built-ins if they pass CUSTOM_ELEMENT_HANDLING.tagNameCheck. Default: `false`.\n   */\n  let CUSTOM_ELEMENT_HANDLING = Object.seal(\n    create(null, {\n      tagNameCheck: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: null,\n      },\n      attributeNameCheck: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: null,\n      },\n      allowCustomizedBuiltInElements: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: false,\n      },\n    })\n  );\n\n  /* Explicitly forbidden tags (overrides ALLOWED_TAGS/ADD_TAGS) */\n  let FORBID_TAGS = null;\n\n  /* Explicitly forbidden attributes (overrides ALLOWED_ATTR/ADD_ATTR) */\n  let FORBID_ATTR = null;\n\n  /* Decide if ARIA attributes are okay */\n  let ALLOW_ARIA_ATTR = true;\n\n  /* Decide if custom data attributes are okay */\n  let ALLOW_DATA_ATTR = true;\n\n  /* Decide if unknown protocols are okay */\n  let ALLOW_UNKNOWN_PROTOCOLS = false;\n\n  /* Decide if self-closing tags in attributes are allowed.\n   * Usually removed due to a mXSS issue in jQuery 3.0 */\n  let ALLOW_SELF_CLOSE_IN_ATTR = true;\n\n  /* Output should be safe for common template engines.\n   * This means, DOMPurify removes data attributes, mustaches and ERB\n   */\n  let SAFE_FOR_TEMPLATES = false;\n\n  /* Output should be safe even for XML used within HTML and alike.\n   * This means, DOMPurify removes comments when containing risky content.\n   */\n  let SAFE_FOR_XML = true;\n\n  /* Decide if document with <html>... should be returned */\n  let WHOLE_DOCUMENT = false;\n\n  /* Track whether config is already set on this instance of DOMPurify. */\n  let SET_CONFIG = false;\n\n  /* Decide if all elements (e.g. style, script) must be children of\n   * document.body. By default, browsers might move them to document.head */\n  let FORCE_BODY = false;\n\n  /* Decide if a DOM `HTMLBodyElement` should be returned, instead of a html\n   * string (or a TrustedHTML object if Trusted Types are supported).\n   * If `WHOLE_DOCUMENT` is enabled a `HTMLHtmlElement` will be returned instead\n   */\n  let RETURN_DOM = false;\n\n  /* Decide if a DOM `DocumentFragment` should be returned, instead of a html\n   * string  (or a TrustedHTML object if Trusted Types are supported) */\n  let RETURN_DOM_FRAGMENT = false;\n\n  /* Try to return a Trusted Type object instead of a string, return a string in\n   * case Trusted Types are not supported  */\n  let RETURN_TRUSTED_TYPE = false;\n\n  /* Output should be free from DOM clobbering attacks?\n   * This sanitizes markups named with colliding, clobberable built-in DOM APIs.\n   */\n  let SANITIZE_DOM = true;\n\n  /* Achieve full DOM Clobbering protection by isolating the namespace of named\n   * properties and JS variables, mitigating attacks that abuse the HTML/DOM spec rules.\n   *\n   * HTML/DOM spec rules that enable DOM Clobbering:\n   *   - Named Access on Window (§7.3.3)\n   *   - DOM Tree Accessors (§3.1.5)\n   *   - Form Element Parent-Child Relations (§4.10.3)\n   *   - Iframe srcdoc / Nested WindowProxies (§4.8.5)\n   *   - HTMLCollection (§4.2.10.2)\n   *\n   * Namespace isolation is implemented by prefixing `id` and `name` attributes\n   * with a constant string, i.e., `user-content-`\n   */\n  let SANITIZE_NAMED_PROPS = false;\n  const SANITIZE_NAMED_PROPS_PREFIX = 'user-content-';\n\n  /* Keep element content when removing element? */\n  let KEEP_CONTENT = true;\n\n  /* If a `Node` is passed to sanitize(), then performs sanitization in-place instead\n   * of importing it into a new Document and returning a sanitized copy */\n  let IN_PLACE = false;\n\n  /* Allow usage of profiles like html, svg and mathMl */\n  let USE_PROFILES: UseProfilesConfig | false = {};\n\n  /* Tags to ignore content of when KEEP_CONTENT is true */\n  let FORBID_CONTENTS = null;\n  const DEFAULT_FORBID_CONTENTS = addToSet({}, [\n    'annotation-xml',\n    'audio',\n    'colgroup',\n    'desc',\n    'foreignobject',\n    'head',\n    'iframe',\n    'math',\n    'mi',\n    'mn',\n    'mo',\n    'ms',\n    'mtext',\n    'noembed',\n    'noframes',\n    'noscript',\n    'plaintext',\n    'script',\n    'style',\n    'svg',\n    'template',\n    'thead',\n    'title',\n    'video',\n    'xmp',\n  ]);\n\n  /* Tags that are safe for data: URIs */\n  let DATA_URI_TAGS = null;\n  const DEFAULT_DATA_URI_TAGS = addToSet({}, [\n    'audio',\n    'video',\n    'img',\n    'source',\n    'image',\n    'track',\n  ]);\n\n  /* Attributes safe for values like \"javascript:\" */\n  let URI_SAFE_ATTRIBUTES = null;\n  const DEFAULT_URI_SAFE_ATTRIBUTES = addToSet({}, [\n    'alt',\n    'class',\n    'for',\n    'id',\n    'label',\n    'name',\n    'pattern',\n    'placeholder',\n    'role',\n    'summary',\n    'title',\n    'value',\n    'style',\n    'xmlns',\n  ]);\n\n  const MATHML_NAMESPACE = 'http://www.w3.org/1998/Math/MathML';\n  const SVG_NAMESPACE = 'http://www.w3.org/2000/svg';\n  const HTML_NAMESPACE = 'http://www.w3.org/1999/xhtml';\n  /* Document namespace */\n  let NAMESPACE = HTML_NAMESPACE;\n  let IS_EMPTY_INPUT = false;\n\n  /* Allowed XHTML+XML namespaces */\n  let ALLOWED_NAMESPACES = null;\n  const DEFAULT_ALLOWED_NAMESPACES = addToSet(\n    {},\n    [MATHML_NAMESPACE, SVG_NAMESPACE, HTML_NAMESPACE],\n    stringToString\n  );\n\n  let MATHML_TEXT_INTEGRATION_POINTS = addToSet({}, [\n    'mi',\n    'mo',\n    'mn',\n    'ms',\n    'mtext',\n  ]);\n\n  let HTML_INTEGRATION_POINTS = addToSet({}, ['annotation-xml']);\n\n  // Certain elements are allowed in both SVG and HTML\n  // namespace. We need to specify them explicitly\n  // so that they don't get erroneously deleted from\n  // HTML namespace.\n  const COMMON_SVG_AND_HTML_ELEMENTS = addToSet({}, [\n    'title',\n    'style',\n    'font',\n    'a',\n    'script',\n  ]);\n\n  /* Parsing of strict XHTML documents */\n  let PARSER_MEDIA_TYPE: null | DOMParserSupportedType = null;\n  const SUPPORTED_PARSER_MEDIA_TYPES = ['application/xhtml+xml', 'text/html'];\n  const DEFAULT_PARSER_MEDIA_TYPE = 'text/html';\n  let transformCaseFunc: null | Parameters<typeof addToSet>[2] = null;\n\n  /* Keep a reference to config to pass to hooks */\n  let CONFIG: Config | null = null;\n\n  /* Ideally, do not touch anything below this line */\n  /* ______________________________________________ */\n\n  const formElement = document.createElement('form');\n\n  const isRegexOrFunction = function (\n    testValue: unknown\n  ): testValue is Function | RegExp {\n    return testValue instanceof RegExp || testValue instanceof Function;\n  };\n\n  /**\n   * _parseConfig\n   *\n   * @param cfg optional config literal\n   */\n  // eslint-disable-next-line complexity\n  const _parseConfig = function (cfg: Config = {}): void {\n    if (CONFIG && CONFIG === cfg) {\n      return;\n    }\n\n    /* Shield configuration object from tampering */\n    if (!cfg || typeof cfg !== 'object') {\n      cfg = {};\n    }\n\n    /* Shield configuration object from prototype pollution */\n    cfg = clone(cfg);\n\n    PARSER_MEDIA_TYPE =\n      // eslint-disable-next-line unicorn/prefer-includes\n      SUPPORTED_PARSER_MEDIA_TYPES.indexOf(cfg.PARSER_MEDIA_TYPE) === -1\n        ? DEFAULT_PARSER_MEDIA_TYPE\n        : cfg.PARSER_MEDIA_TYPE;\n\n    // HTML tags and attributes are not case-sensitive, converting to lowercase. Keeping XHTML as is.\n    transformCaseFunc =\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml'\n        ? stringToString\n        : stringToLowerCase;\n\n    /* Set configuration parameters */\n    ALLOWED_TAGS = objectHasOwnProperty(cfg, 'ALLOWED_TAGS')\n      ? addToSet({}, cfg.ALLOWED_TAGS, transformCaseFunc)\n      : DEFAULT_ALLOWED_TAGS;\n    ALLOWED_ATTR = objectHasOwnProperty(cfg, 'ALLOWED_ATTR')\n      ? addToSet({}, cfg.ALLOWED_ATTR, transformCaseFunc)\n      : DEFAULT_ALLOWED_ATTR;\n    ALLOWED_NAMESPACES = objectHasOwnProperty(cfg, 'ALLOWED_NAMESPACES')\n      ? addToSet({}, cfg.ALLOWED_NAMESPACES, stringToString)\n      : DEFAULT_ALLOWED_NAMESPACES;\n    URI_SAFE_ATTRIBUTES = objectHasOwnProperty(cfg, 'ADD_URI_SAFE_ATTR')\n      ? addToSet(\n          clone(DEFAULT_URI_SAFE_ATTRIBUTES),\n          cfg.ADD_URI_SAFE_ATTR,\n          transformCaseFunc\n        )\n      : DEFAULT_URI_SAFE_ATTRIBUTES;\n    DATA_URI_TAGS = objectHasOwnProperty(cfg, 'ADD_DATA_URI_TAGS')\n      ? addToSet(\n          clone(DEFAULT_DATA_URI_TAGS),\n          cfg.ADD_DATA_URI_TAGS,\n          transformCaseFunc\n        )\n      : DEFAULT_DATA_URI_TAGS;\n    FORBID_CONTENTS = objectHasOwnProperty(cfg, 'FORBID_CONTENTS')\n      ? addToSet({}, cfg.FORBID_CONTENTS, transformCaseFunc)\n      : DEFAULT_FORBID_CONTENTS;\n    FORBID_TAGS = objectHasOwnProperty(cfg, 'FORBID_TAGS')\n      ? addToSet({}, cfg.FORBID_TAGS, transformCaseFunc)\n      : clone({});\n    FORBID_ATTR = objectHasOwnProperty(cfg, 'FORBID_ATTR')\n      ? addToSet({}, cfg.FORBID_ATTR, transformCaseFunc)\n      : clone({});\n    USE_PROFILES = objectHasOwnProperty(cfg, 'USE_PROFILES')\n      ? cfg.USE_PROFILES\n      : false;\n    ALLOW_ARIA_ATTR = cfg.ALLOW_ARIA_ATTR !== false; // Default true\n    ALLOW_DATA_ATTR = cfg.ALLOW_DATA_ATTR !== false; // Default true\n    ALLOW_UNKNOWN_PROTOCOLS = cfg.ALLOW_UNKNOWN_PROTOCOLS || false; // Default false\n    ALLOW_SELF_CLOSE_IN_ATTR = cfg.ALLOW_SELF_CLOSE_IN_ATTR !== false; // Default true\n    SAFE_FOR_TEMPLATES = cfg.SAFE_FOR_TEMPLATES || false; // Default false\n    SAFE_FOR_XML = cfg.SAFE_FOR_XML !== false; // Default true\n    WHOLE_DOCUMENT = cfg.WHOLE_DOCUMENT || false; // Default false\n    RETURN_DOM = cfg.RETURN_DOM || false; // Default false\n    RETURN_DOM_FRAGMENT = cfg.RETURN_DOM_FRAGMENT || false; // Default false\n    RETURN_TRUSTED_TYPE = cfg.RETURN_TRUSTED_TYPE || false; // Default false\n    FORCE_BODY = cfg.FORCE_BODY || false; // Default false\n    SANITIZE_DOM = cfg.SANITIZE_DOM !== false; // Default true\n    SANITIZE_NAMED_PROPS = cfg.SANITIZE_NAMED_PROPS || false; // Default false\n    KEEP_CONTENT = cfg.KEEP_CONTENT !== false; // Default true\n    IN_PLACE = cfg.IN_PLACE || false; // Default false\n    IS_ALLOWED_URI = cfg.ALLOWED_URI_REGEXP || EXPRESSIONS.IS_ALLOWED_URI;\n    NAMESPACE = cfg.NAMESPACE || HTML_NAMESPACE;\n    MATHML_TEXT_INTEGRATION_POINTS =\n      cfg.MATHML_TEXT_INTEGRATION_POINTS || MATHML_TEXT_INTEGRATION_POINTS;\n    HTML_INTEGRATION_POINTS =\n      cfg.HTML_INTEGRATION_POINTS || HTML_INTEGRATION_POINTS;\n\n    CUSTOM_ELEMENT_HANDLING = cfg.CUSTOM_ELEMENT_HANDLING || {};\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck)\n    ) {\n      CUSTOM_ELEMENT_HANDLING.tagNameCheck =\n        cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck;\n    }\n\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)\n    ) {\n      CUSTOM_ELEMENT_HANDLING.attributeNameCheck =\n        cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck;\n    }\n\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      typeof cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements ===\n        'boolean'\n    ) {\n      CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements =\n        cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements;\n    }\n\n    if (SAFE_FOR_TEMPLATES) {\n      ALLOW_DATA_ATTR = false;\n    }\n\n    if (RETURN_DOM_FRAGMENT) {\n      RETURN_DOM = true;\n    }\n\n    /* Parse profile info */\n    if (USE_PROFILES) {\n      ALLOWED_TAGS = addToSet({}, TAGS.text);\n      ALLOWED_ATTR = [];\n      if (USE_PROFILES.html === true) {\n        addToSet(ALLOWED_TAGS, TAGS.html);\n        addToSet(ALLOWED_ATTR, ATTRS.html);\n      }\n\n      if (USE_PROFILES.svg === true) {\n        addToSet(ALLOWED_TAGS, TAGS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n\n      if (USE_PROFILES.svgFilters === true) {\n        addToSet(ALLOWED_TAGS, TAGS.svgFilters);\n        addToSet(ALLOWED_ATTR, ATTRS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n\n      if (USE_PROFILES.mathMl === true) {\n        addToSet(ALLOWED_TAGS, TAGS.mathMl);\n        addToSet(ALLOWED_ATTR, ATTRS.mathMl);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n    }\n\n    /* Merge configuration parameters */\n    if (cfg.ADD_TAGS) {\n      if (ALLOWED_TAGS === DEFAULT_ALLOWED_TAGS) {\n        ALLOWED_TAGS = clone(ALLOWED_TAGS);\n      }\n\n      addToSet(ALLOWED_TAGS, cfg.ADD_TAGS, transformCaseFunc);\n    }\n\n    if (cfg.ADD_ATTR) {\n      if (ALLOWED_ATTR === DEFAULT_ALLOWED_ATTR) {\n        ALLOWED_ATTR = clone(ALLOWED_ATTR);\n      }\n\n      addToSet(ALLOWED_ATTR, cfg.ADD_ATTR, transformCaseFunc);\n    }\n\n    if (cfg.ADD_URI_SAFE_ATTR) {\n      addToSet(URI_SAFE_ATTRIBUTES, cfg.ADD_URI_SAFE_ATTR, transformCaseFunc);\n    }\n\n    if (cfg.FORBID_CONTENTS) {\n      if (FORBID_CONTENTS === DEFAULT_FORBID_CONTENTS) {\n        FORBID_CONTENTS = clone(FORBID_CONTENTS);\n      }\n\n      addToSet(FORBID_CONTENTS, cfg.FORBID_CONTENTS, transformCaseFunc);\n    }\n\n    /* Add #text in case KEEP_CONTENT is set to true */\n    if (KEEP_CONTENT) {\n      ALLOWED_TAGS['#text'] = true;\n    }\n\n    /* Add html, head and body to ALLOWED_TAGS in case WHOLE_DOCUMENT is true */\n    if (WHOLE_DOCUMENT) {\n      addToSet(ALLOWED_TAGS, ['html', 'head', 'body']);\n    }\n\n    /* Add tbody to ALLOWED_TAGS in case tables are permitted, see #286, #365 */\n    if (ALLOWED_TAGS.table) {\n      addToSet(ALLOWED_TAGS, ['tbody']);\n      delete FORBID_TAGS.tbody;\n    }\n\n    if (cfg.TRUSTED_TYPES_POLICY) {\n      if (typeof cfg.TRUSTED_TYPES_POLICY.createHTML !== 'function') {\n        throw typeErrorCreate(\n          'TRUSTED_TYPES_POLICY configuration option must provide a \"createHTML\" hook.'\n        );\n      }\n\n      if (typeof cfg.TRUSTED_TYPES_POLICY.createScriptURL !== 'function') {\n        throw typeErrorCreate(\n          'TRUSTED_TYPES_POLICY configuration option must provide a \"createScriptURL\" hook.'\n        );\n      }\n\n      // Overwrite existing TrustedTypes policy.\n      trustedTypesPolicy = cfg.TRUSTED_TYPES_POLICY;\n\n      // Sign local variables required by `sanitize`.\n      emptyHTML = trustedTypesPolicy.createHTML('');\n    } else {\n      // Uninitialized policy, attempt to initialize the internal dompurify policy.\n      if (trustedTypesPolicy === undefined) {\n        trustedTypesPolicy = _createTrustedTypesPolicy(\n          trustedTypes,\n          currentScript\n        );\n      }\n\n      // If creating the internal policy succeeded sign internal variables.\n      if (trustedTypesPolicy !== null && typeof emptyHTML === 'string') {\n        emptyHTML = trustedTypesPolicy.createHTML('');\n      }\n    }\n\n    // Prevent further manipulation of configuration.\n    // Not available in IE8, Safari 5, etc.\n    if (freeze) {\n      freeze(cfg);\n    }\n\n    CONFIG = cfg;\n  };\n\n  /* Keep track of all possible SVG and MathML tags\n   * so that we can perform the namespace checks\n   * correctly. */\n  const ALL_SVG_TAGS = addToSet({}, [\n    ...TAGS.svg,\n    ...TAGS.svgFilters,\n    ...TAGS.svgDisallowed,\n  ]);\n  const ALL_MATHML_TAGS = addToSet({}, [\n    ...TAGS.mathMl,\n    ...TAGS.mathMlDisallowed,\n  ]);\n\n  /**\n   * @param element a DOM element whose namespace is being checked\n   * @returns Return false if the element has a\n   *  namespace that a spec-compliant parser would never\n   *  return. Return true otherwise.\n   */\n  const _checkValidNamespace = function (element: Element): boolean {\n    let parent = getParentNode(element);\n\n    // In JSDOM, if we're inside shadow DOM, then parentNode\n    // can be null. We just simulate parent in this case.\n    if (!parent || !parent.tagName) {\n      parent = {\n        namespaceURI: NAMESPACE,\n        tagName: 'template',\n      };\n    }\n\n    const tagName = stringToLowerCase(element.tagName);\n    const parentTagName = stringToLowerCase(parent.tagName);\n\n    if (!ALLOWED_NAMESPACES[element.namespaceURI]) {\n      return false;\n    }\n\n    if (element.namespaceURI === SVG_NAMESPACE) {\n      // The only way to switch from HTML namespace to SVG\n      // is via <svg>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'svg';\n      }\n\n      // The only way to switch from MathML to SVG is via`\n      // svg if parent is either <annotation-xml> or MathML\n      // text integration points.\n      if (parent.namespaceURI === MATHML_NAMESPACE) {\n        return (\n          tagName === 'svg' &&\n          (parentTagName === 'annotation-xml' ||\n            MATHML_TEXT_INTEGRATION_POINTS[parentTagName])\n        );\n      }\n\n      // We only allow elements that are defined in SVG\n      // spec. All others are disallowed in SVG namespace.\n      return Boolean(ALL_SVG_TAGS[tagName]);\n    }\n\n    if (element.namespaceURI === MATHML_NAMESPACE) {\n      // The only way to switch from HTML namespace to MathML\n      // is via <math>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'math';\n      }\n\n      // The only way to switch from SVG to MathML is via\n      // <math> and HTML integration points\n      if (parent.namespaceURI === SVG_NAMESPACE) {\n        return tagName === 'math' && HTML_INTEGRATION_POINTS[parentTagName];\n      }\n\n      // We only allow elements that are defined in MathML\n      // spec. All others are disallowed in MathML namespace.\n      return Boolean(ALL_MATHML_TAGS[tagName]);\n    }\n\n    if (element.namespaceURI === HTML_NAMESPACE) {\n      // The only way to switch from SVG to HTML is via\n      // HTML integration points, and from MathML to HTML\n      // is via MathML text integration points\n      if (\n        parent.namespaceURI === SVG_NAMESPACE &&\n        !HTML_INTEGRATION_POINTS[parentTagName]\n      ) {\n        return false;\n      }\n\n      if (\n        parent.namespaceURI === MATHML_NAMESPACE &&\n        !MATHML_TEXT_INTEGRATION_POINTS[parentTagName]\n      ) {\n        return false;\n      }\n\n      // We disallow tags that are specific for MathML\n      // or SVG and should never appear in HTML namespace\n      return (\n        !ALL_MATHML_TAGS[tagName] &&\n        (COMMON_SVG_AND_HTML_ELEMENTS[tagName] || !ALL_SVG_TAGS[tagName])\n      );\n    }\n\n    // For XHTML and XML documents that support custom namespaces\n    if (\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml' &&\n      ALLOWED_NAMESPACES[element.namespaceURI]\n    ) {\n      return true;\n    }\n\n    // The code should never reach this place (this means\n    // that the element somehow got namespace that is not\n    // HTML, SVG, MathML or allowed via ALLOWED_NAMESPACES).\n    // Return false just in case.\n    return false;\n  };\n\n  /**\n   * _forceRemove\n   *\n   * @param node a DOM node\n   */\n  const _forceRemove = function (node: Node): void {\n    arrayPush(DOMPurify.removed, { element: node });\n\n    try {\n      // eslint-disable-next-line unicorn/prefer-dom-node-remove\n      getParentNode(node).removeChild(node);\n    } catch (_) {\n      remove(node);\n    }\n  };\n\n  /**\n   * _removeAttribute\n   *\n   * @param name an Attribute name\n   * @param element a DOM node\n   */\n  const _removeAttribute = function (name: string, element: Element): void {\n    try {\n      arrayPush(DOMPurify.removed, {\n        attribute: element.getAttributeNode(name),\n        from: element,\n      });\n    } catch (_) {\n      arrayPush(DOMPurify.removed, {\n        attribute: null,\n        from: element,\n      });\n    }\n\n    element.removeAttribute(name);\n\n    // We void attribute values for unremovable \"is\" attributes\n    if (name === 'is') {\n      if (RETURN_DOM || RETURN_DOM_FRAGMENT) {\n        try {\n          _forceRemove(element);\n        } catch (_) {}\n      } else {\n        try {\n          element.setAttribute(name, '');\n        } catch (_) {}\n      }\n    }\n  };\n\n  /**\n   * _initDocument\n   *\n   * @param dirty - a string of dirty markup\n   * @return a DOM, filled with the dirty markup\n   */\n  const _initDocument = function (dirty: string): Document {\n    /* Create a HTML document */\n    let doc = null;\n    let leadingWhitespace = null;\n\n    if (FORCE_BODY) {\n      dirty = '<remove></remove>' + dirty;\n    } else {\n      /* If FORCE_BODY isn't used, leading whitespace needs to be preserved manually */\n      const matches = stringMatch(dirty, /^[\\r\\n\\t ]+/);\n      leadingWhitespace = matches && matches[0];\n    }\n\n    if (\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml' &&\n      NAMESPACE === HTML_NAMESPACE\n    ) {\n      // Root of XHTML doc must contain xmlns declaration (see https://www.w3.org/TR/xhtml1/normative.html#strict)\n      dirty =\n        '<html xmlns=\"http://www.w3.org/1999/xhtml\"><head></head><body>' +\n        dirty +\n        '</body></html>';\n    }\n\n    const dirtyPayload = trustedTypesPolicy\n      ? trustedTypesPolicy.createHTML(dirty)\n      : dirty;\n    /*\n     * Use the DOMParser API by default, fallback later if needs be\n     * DOMParser not work for svg when has multiple root element.\n     */\n    if (NAMESPACE === HTML_NAMESPACE) {\n      try {\n        doc = new DOMParser().parseFromString(dirtyPayload, PARSER_MEDIA_TYPE);\n      } catch (_) {}\n    }\n\n    /* Use createHTMLDocument in case DOMParser is not available */\n    if (!doc || !doc.documentElement) {\n      doc = implementation.createDocument(NAMESPACE, 'template', null);\n      try {\n        doc.documentElement.innerHTML = IS_EMPTY_INPUT\n          ? emptyHTML\n          : dirtyPayload;\n      } catch (_) {\n        // Syntax error if dirtyPayload is invalid xml\n      }\n    }\n\n    const body = doc.body || doc.documentElement;\n\n    if (dirty && leadingWhitespace) {\n      body.insertBefore(\n        document.createTextNode(leadingWhitespace),\n        body.childNodes[0] || null\n      );\n    }\n\n    /* Work on whole document or just its body */\n    if (NAMESPACE === HTML_NAMESPACE) {\n      return getElementsByTagName.call(\n        doc,\n        WHOLE_DOCUMENT ? 'html' : 'body'\n      )[0];\n    }\n\n    return WHOLE_DOCUMENT ? doc.documentElement : body;\n  };\n\n  /**\n   * Creates a NodeIterator object that you can use to traverse filtered lists of nodes or elements in a document.\n   *\n   * @param root The root element or node to start traversing on.\n   * @return The created NodeIterator\n   */\n  const _createNodeIterator = function (root: Node): NodeIterator {\n    return createNodeIterator.call(\n      root.ownerDocument || root,\n      root,\n      // eslint-disable-next-line no-bitwise\n      NodeFilter.SHOW_ELEMENT |\n        NodeFilter.SHOW_COMMENT |\n        NodeFilter.SHOW_TEXT |\n        NodeFilter.SHOW_PROCESSING_INSTRUCTION |\n        NodeFilter.SHOW_CDATA_SECTION,\n      null\n    );\n  };\n\n  /**\n   * _isClobbered\n   *\n   * @param element element to check for clobbering attacks\n   * @return true if clobbered, false if safe\n   */\n  const _isClobbered = function (element: Element): boolean {\n    return (\n      element instanceof HTMLFormElement &&\n      (typeof element.nodeName !== 'string' ||\n        typeof element.textContent !== 'string' ||\n        typeof element.removeChild !== 'function' ||\n        !(element.attributes instanceof NamedNodeMap) ||\n        typeof element.removeAttribute !== 'function' ||\n        typeof element.setAttribute !== 'function' ||\n        typeof element.namespaceURI !== 'string' ||\n        typeof element.insertBefore !== 'function' ||\n        typeof element.hasChildNodes !== 'function')\n    );\n  };\n\n  /**\n   * Checks whether the given object is a DOM node.\n   *\n   * @param value object to check whether it's a DOM node\n   * @return true is object is a DOM node\n   */\n  const _isNode = function (value: unknown): value is Node {\n    return typeof Node === 'function' && value instanceof Node;\n  };\n\n  function _executeHooks<\n    T extends\n      | NodeHook\n      | ElementHook\n      | DocumentFragmentHook\n      | UponSanitizeElementHook\n      | UponSanitizeAttributeHook\n  >(hooks: T[], currentNode: Parameters<T>[0], data: Parameters<T>[1]): void {\n    arrayForEach(hooks, (hook) => {\n      hook.call(DOMPurify, currentNode, data, CONFIG);\n    });\n  }\n\n  /**\n   * _sanitizeElements\n   *\n   * @protect nodeName\n   * @protect textContent\n   * @protect removeChild\n   * @param currentNode to check for permission to exist\n   * @return true if node was killed, false if left alive\n   */\n  const _sanitizeElements = function (currentNode: any): boolean {\n    let content = null;\n\n    /* Execute a hook if present */\n    _executeHooks(hooks.beforeSanitizeElements, currentNode, null);\n\n    /* Check if element is clobbered or can clobber */\n    if (_isClobbered(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Now let's check the element's type and name */\n    const tagName = transformCaseFunc(currentNode.nodeName);\n\n    /* Execute a hook if present */\n    _executeHooks(hooks.uponSanitizeElement, currentNode, {\n      tagName,\n      allowedTags: ALLOWED_TAGS,\n    });\n\n    /* Detect mXSS attempts abusing namespace confusion */\n    if (\n      SAFE_FOR_XML &&\n      currentNode.hasChildNodes() &&\n      !_isNode(currentNode.firstElementChild) &&\n      regExpTest(/<[/\\w!]/g, currentNode.innerHTML) &&\n      regExpTest(/<[/\\w!]/g, currentNode.textContent)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove any occurrence of processing instructions */\n    if (currentNode.nodeType === NODE_TYPE.progressingInstruction) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove any kind of possibly harmful comments */\n    if (\n      SAFE_FOR_XML &&\n      currentNode.nodeType === NODE_TYPE.comment &&\n      regExpTest(/<[/\\w]/g, currentNode.data)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove element if anything forbids its presence */\n    if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n      /* Check if we have a custom element to handle */\n      if (!FORBID_TAGS[tagName] && _isBasicCustomElement(tagName)) {\n        if (\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n          regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, tagName)\n        ) {\n          return false;\n        }\n\n        if (\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck(tagName)\n        ) {\n          return false;\n        }\n      }\n\n      /* Keep content except for bad-listed elements */\n      if (KEEP_CONTENT && !FORBID_CONTENTS[tagName]) {\n        const parentNode = getParentNode(currentNode) || currentNode.parentNode;\n        const childNodes = getChildNodes(currentNode) || currentNode.childNodes;\n\n        if (childNodes && parentNode) {\n          const childCount = childNodes.length;\n\n          for (let i = childCount - 1; i >= 0; --i) {\n            const childClone = cloneNode(childNodes[i], true);\n            childClone.__removalCount = (currentNode.__removalCount || 0) + 1;\n            parentNode.insertBefore(childClone, getNextSibling(currentNode));\n          }\n        }\n      }\n\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Check whether element has a valid namespace */\n    if (currentNode instanceof Element && !_checkValidNamespace(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Make sure that older browsers don't get fallback-tag mXSS */\n    if (\n      (tagName === 'noscript' ||\n        tagName === 'noembed' ||\n        tagName === 'noframes') &&\n      regExpTest(/<\\/no(script|embed|frames)/i, currentNode.innerHTML)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Sanitize element content to be template-safe */\n    if (SAFE_FOR_TEMPLATES && currentNode.nodeType === NODE_TYPE.text) {\n      /* Get the element's text content */\n      content = currentNode.textContent;\n\n      arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], (expr) => {\n        content = stringReplace(content, expr, ' ');\n      });\n\n      if (currentNode.textContent !== content) {\n        arrayPush(DOMPurify.removed, { element: currentNode.cloneNode() });\n        currentNode.textContent = content;\n      }\n    }\n\n    /* Execute a hook if present */\n    _executeHooks(hooks.afterSanitizeElements, currentNode, null);\n\n    return false;\n  };\n\n  /**\n   * _isValidAttribute\n   *\n   * @param lcTag Lowercase tag name of containing element.\n   * @param lcName Lowercase attribute name.\n   * @param value Attribute value.\n   * @return Returns true if `value` is valid, otherwise false.\n   */\n  // eslint-disable-next-line complexity\n  const _isValidAttribute = function (\n    lcTag: string,\n    lcName: string,\n    value: string\n  ): boolean {\n    /* Make sure attribute cannot clobber */\n    if (\n      SANITIZE_DOM &&\n      (lcName === 'id' || lcName === 'name') &&\n      (value in document || value in formElement)\n    ) {\n      return false;\n    }\n\n    /* Allow valid data-* attributes: At least one character after \"-\"\n        (https://html.spec.whatwg.org/multipage/dom.html#embedding-custom-non-visible-data-with-the-data-*-attributes)\n        XML-compatible (https://html.spec.whatwg.org/multipage/infrastructure.html#xml-compatible and http://www.w3.org/TR/xml/#d0e804)\n        We don't need to check the value; it's always URI safe. */\n    if (\n      ALLOW_DATA_ATTR &&\n      !FORBID_ATTR[lcName] &&\n      regExpTest(DATA_ATTR, lcName)\n    ) {\n      // This attribute is safe\n    } else if (ALLOW_ARIA_ATTR && regExpTest(ARIA_ATTR, lcName)) {\n      // This attribute is safe\n      /* Otherwise, check the name is permitted */\n    } else if (!ALLOWED_ATTR[lcName] || FORBID_ATTR[lcName]) {\n      if (\n        // First condition does a very basic check if a) it's basically a valid custom element tagname AND\n        // b) if the tagName passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n        // and c) if the attribute name passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.attributeNameCheck\n        (_isBasicCustomElement(lcTag) &&\n          ((CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, lcTag)) ||\n            (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.tagNameCheck(lcTag))) &&\n          ((CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.attributeNameCheck, lcName)) ||\n            (CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.attributeNameCheck(lcName)))) ||\n        // Alternative, second condition checks if it's an `is`-attribute, AND\n        // the value passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n        (lcName === 'is' &&\n          CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements &&\n          ((CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, value)) ||\n            (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.tagNameCheck(value))))\n      ) {\n        // If user has supplied a regexp or function in CUSTOM_ELEMENT_HANDLING.tagNameCheck, we need to also allow derived custom elements using the same tagName test.\n        // Additionally, we need to allow attributes passing the CUSTOM_ELEMENT_HANDLING.attributeNameCheck user has configured, as custom elements can define these at their own discretion.\n      } else {\n        return false;\n      }\n      /* Check value is safe. First, is attr inert? If so, is safe */\n    } else if (URI_SAFE_ATTRIBUTES[lcName]) {\n      // This attribute is safe\n      /* Check no script, data or unknown possibly unsafe URI\n        unless we know URI values are safe for that attribute */\n    } else if (\n      regExpTest(IS_ALLOWED_URI, stringReplace(value, ATTR_WHITESPACE, ''))\n    ) {\n      // This attribute is safe\n      /* Keep image data URIs alive if src/xlink:href is allowed */\n      /* Further prevent gadget XSS for dynamically built script tags */\n    } else if (\n      (lcName === 'src' || lcName === 'xlink:href' || lcName === 'href') &&\n      lcTag !== 'script' &&\n      stringIndexOf(value, 'data:') === 0 &&\n      DATA_URI_TAGS[lcTag]\n    ) {\n      // This attribute is safe\n      /* Allow unknown protocols: This provides support for links that\n        are handled by protocol handlers which may be unknown ahead of\n        time, e.g. fb:, spotify: */\n    } else if (\n      ALLOW_UNKNOWN_PROTOCOLS &&\n      !regExpTest(IS_SCRIPT_OR_DATA, stringReplace(value, ATTR_WHITESPACE, ''))\n    ) {\n      // This attribute is safe\n      /* Check for binary attributes */\n    } else if (value) {\n      return false;\n    } else {\n      // Binary attributes are safe at this point\n      /* Anything else, presume unsafe, do not add it back */\n    }\n\n    return true;\n  };\n\n  /**\n   * _isBasicCustomElement\n   * checks if at least one dash is included in tagName, and it's not the first char\n   * for more sophisticated checking see https://github.com/sindresorhus/validate-element-name\n   *\n   * @param tagName name of the tag of the node to sanitize\n   * @returns Returns true if the tag name meets the basic criteria for a custom element, otherwise false.\n   */\n  const _isBasicCustomElement = function (tagName: string): RegExpMatchArray {\n    return tagName !== 'annotation-xml' && stringMatch(tagName, CUSTOM_ELEMENT);\n  };\n\n  /**\n   * _sanitizeAttributes\n   *\n   * @protect attributes\n   * @protect nodeName\n   * @protect removeAttribute\n   * @protect setAttribute\n   *\n   * @param currentNode to sanitize\n   */\n  const _sanitizeAttributes = function (currentNode: Element): void {\n    /* Execute a hook if present */\n    _executeHooks(hooks.beforeSanitizeAttributes, currentNode, null);\n\n    const { attributes } = currentNode;\n\n    /* Check if we have attributes; if not we might have a text node */\n    if (!attributes || _isClobbered(currentNode)) {\n      return;\n    }\n\n    const hookEvent = {\n      attrName: '',\n      attrValue: '',\n      keepAttr: true,\n      allowedAttributes: ALLOWED_ATTR,\n      forceKeepAttr: undefined,\n    };\n    let l = attributes.length;\n\n    /* Go backwards over all attributes; safely remove bad ones */\n    while (l--) {\n      const attr = attributes[l];\n      const { name, namespaceURI, value: attrValue } = attr;\n      const lcName = transformCaseFunc(name);\n\n      const initValue = attrValue;\n      let value = name === 'value' ? initValue : stringTrim(initValue);\n\n      /* Execute a hook if present */\n      hookEvent.attrName = lcName;\n      hookEvent.attrValue = value;\n      hookEvent.keepAttr = true;\n      hookEvent.forceKeepAttr = undefined; // Allows developers to see this is a property they can set\n      _executeHooks(hooks.uponSanitizeAttribute, currentNode, hookEvent);\n      value = hookEvent.attrValue;\n\n      /* Full DOM Clobbering protection via namespace isolation,\n       * Prefix id and name attributes with `user-content-`\n       */\n      if (SANITIZE_NAMED_PROPS && (lcName === 'id' || lcName === 'name')) {\n        // Remove the attribute with this value\n        _removeAttribute(name, currentNode);\n\n        // Prefix the value and later re-create the attribute with the sanitized value\n        value = SANITIZE_NAMED_PROPS_PREFIX + value;\n      }\n\n      /* Work around a security issue with comments inside attributes */\n      if (SAFE_FOR_XML && regExpTest(/((--!?|])>)|<\\/(style|title)/i, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Did the hooks approve of the attribute? */\n      if (hookEvent.forceKeepAttr) {\n        continue;\n      }\n\n      /* Did the hooks approve of the attribute? */\n      if (!hookEvent.keepAttr) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Work around a security issue in jQuery 3.0 */\n      if (!ALLOW_SELF_CLOSE_IN_ATTR && regExpTest(/\\/>/i, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Sanitize attribute content to be template-safe */\n      if (SAFE_FOR_TEMPLATES) {\n        arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], (expr) => {\n          value = stringReplace(value, expr, ' ');\n        });\n      }\n\n      /* Is `value` valid for this attribute? */\n      const lcTag = transformCaseFunc(currentNode.nodeName);\n      if (!_isValidAttribute(lcTag, lcName, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Handle attributes that require Trusted Types */\n      if (\n        trustedTypesPolicy &&\n        typeof trustedTypes === 'object' &&\n        typeof trustedTypes.getAttributeType === 'function'\n      ) {\n        if (namespaceURI) {\n          /* Namespaces are not yet supported, see https://bugs.chromium.org/p/chromium/issues/detail?id=1305293 */\n        } else {\n          switch (trustedTypes.getAttributeType(lcTag, lcName)) {\n            case 'TrustedHTML': {\n              value = trustedTypesPolicy.createHTML(value);\n              break;\n            }\n\n            case 'TrustedScriptURL': {\n              value = trustedTypesPolicy.createScriptURL(value);\n              break;\n            }\n\n            default: {\n              break;\n            }\n          }\n        }\n      }\n\n      /* Handle invalid data-* attribute set by try-catching it */\n      if (value !== initValue) {\n        try {\n          if (namespaceURI) {\n            currentNode.setAttributeNS(namespaceURI, name, value);\n          } else {\n            /* Fallback to setAttribute() for browser-unrecognized namespaces e.g. \"x-schema\". */\n            currentNode.setAttribute(name, value);\n          }\n\n          if (_isClobbered(currentNode)) {\n            _forceRemove(currentNode);\n          } else {\n            arrayPop(DOMPurify.removed);\n          }\n        } catch (_) {\n          _removeAttribute(name, currentNode);\n        }\n      }\n    }\n\n    /* Execute a hook if present */\n    _executeHooks(hooks.afterSanitizeAttributes, currentNode, null);\n  };\n\n  /**\n   * _sanitizeShadowDOM\n   *\n   * @param fragment to iterate over recursively\n   */\n  const _sanitizeShadowDOM = function (fragment: DocumentFragment): void {\n    let shadowNode = null;\n    const shadowIterator = _createNodeIterator(fragment);\n\n    /* Execute a hook if present */\n    _executeHooks(hooks.beforeSanitizeShadowDOM, fragment, null);\n\n    while ((shadowNode = shadowIterator.nextNode())) {\n      /* Execute a hook if present */\n      _executeHooks(hooks.uponSanitizeShadowNode, shadowNode, null);\n\n      /* Sanitize tags and elements */\n      _sanitizeElements(shadowNode);\n\n      /* Check attributes next */\n      _sanitizeAttributes(shadowNode);\n\n      /* Deep shadow DOM detected */\n      if (shadowNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(shadowNode.content);\n      }\n    }\n\n    /* Execute a hook if present */\n    _executeHooks(hooks.afterSanitizeShadowDOM, fragment, null);\n  };\n\n  // eslint-disable-next-line complexity\n  DOMPurify.sanitize = function (dirty, cfg = {}) {\n    let body = null;\n    let importedNode = null;\n    let currentNode = null;\n    let returnNode = null;\n    /* Make sure we have a string to sanitize.\n      DO NOT return early, as this will return the wrong type if\n      the user has requested a DOM object rather than a string */\n    IS_EMPTY_INPUT = !dirty;\n    if (IS_EMPTY_INPUT) {\n      dirty = '<!-->';\n    }\n\n    /* Stringify, in case dirty is an object */\n    if (typeof dirty !== 'string' && !_isNode(dirty)) {\n      if (typeof dirty.toString === 'function') {\n        dirty = dirty.toString();\n        if (typeof dirty !== 'string') {\n          throw typeErrorCreate('dirty is not a string, aborting');\n        }\n      } else {\n        throw typeErrorCreate('toString is not a function');\n      }\n    }\n\n    /* Return dirty HTML if DOMPurify cannot run */\n    if (!DOMPurify.isSupported) {\n      return dirty;\n    }\n\n    /* Assign config vars */\n    if (!SET_CONFIG) {\n      _parseConfig(cfg);\n    }\n\n    /* Clean up removed elements */\n    DOMPurify.removed = [];\n\n    /* Check if dirty is correctly typed for IN_PLACE */\n    if (typeof dirty === 'string') {\n      IN_PLACE = false;\n    }\n\n    if (IN_PLACE) {\n      /* Do some early pre-sanitization to avoid unsafe root nodes */\n      if ((dirty as Node).nodeName) {\n        const tagName = transformCaseFunc((dirty as Node).nodeName);\n        if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n          throw typeErrorCreate(\n            'root node is forbidden and cannot be sanitized in-place'\n          );\n        }\n      }\n    } else if (dirty instanceof Node) {\n      /* If dirty is a DOM element, append to an empty document to avoid\n         elements being stripped by the parser */\n      body = _initDocument('<!---->');\n      importedNode = body.ownerDocument.importNode(dirty, true);\n      if (\n        importedNode.nodeType === NODE_TYPE.element &&\n        importedNode.nodeName === 'BODY'\n      ) {\n        /* Node is already a body, use as is */\n        body = importedNode;\n      } else if (importedNode.nodeName === 'HTML') {\n        body = importedNode;\n      } else {\n        // eslint-disable-next-line unicorn/prefer-dom-node-append\n        body.appendChild(importedNode);\n      }\n    } else {\n      /* Exit directly if we have nothing to do */\n      if (\n        !RETURN_DOM &&\n        !SAFE_FOR_TEMPLATES &&\n        !WHOLE_DOCUMENT &&\n        // eslint-disable-next-line unicorn/prefer-includes\n        dirty.indexOf('<') === -1\n      ) {\n        return trustedTypesPolicy && RETURN_TRUSTED_TYPE\n          ? trustedTypesPolicy.createHTML(dirty)\n          : dirty;\n      }\n\n      /* Initialize the document to work on */\n      body = _initDocument(dirty);\n\n      /* Check we have a DOM node from the data */\n      if (!body) {\n        return RETURN_DOM ? null : RETURN_TRUSTED_TYPE ? emptyHTML : '';\n      }\n    }\n\n    /* Remove first element node (ours) if FORCE_BODY is set */\n    if (body && FORCE_BODY) {\n      _forceRemove(body.firstChild);\n    }\n\n    /* Get node iterator */\n    const nodeIterator = _createNodeIterator(IN_PLACE ? dirty : body);\n\n    /* Now start iterating over the created document */\n    while ((currentNode = nodeIterator.nextNode())) {\n      /* Sanitize tags and elements */\n      _sanitizeElements(currentNode);\n\n      /* Check attributes next */\n      _sanitizeAttributes(currentNode);\n\n      /* Shadow DOM detected, sanitize it */\n      if (currentNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(currentNode.content);\n      }\n    }\n\n    /* If we sanitized `dirty` in-place, return it. */\n    if (IN_PLACE) {\n      return dirty;\n    }\n\n    /* Return sanitized string or DOM */\n    if (RETURN_DOM) {\n      if (RETURN_DOM_FRAGMENT) {\n        returnNode = createDocumentFragment.call(body.ownerDocument);\n\n        while (body.firstChild) {\n          // eslint-disable-next-line unicorn/prefer-dom-node-append\n          returnNode.appendChild(body.firstChild);\n        }\n      } else {\n        returnNode = body;\n      }\n\n      if (ALLOWED_ATTR.shadowroot || ALLOWED_ATTR.shadowrootmode) {\n        /*\n          AdoptNode() is not used because internal state is not reset\n          (e.g. the past names map of a HTMLFormElement), this is safe\n          in theory but we would rather not risk another attack vector.\n          The state that is cloned by importNode() is explicitly defined\n          by the specs.\n        */\n        returnNode = importNode.call(originalDocument, returnNode, true);\n      }\n\n      return returnNode;\n    }\n\n    let serializedHTML = WHOLE_DOCUMENT ? body.outerHTML : body.innerHTML;\n\n    /* Serialize doctype if allowed */\n    if (\n      WHOLE_DOCUMENT &&\n      ALLOWED_TAGS['!doctype'] &&\n      body.ownerDocument &&\n      body.ownerDocument.doctype &&\n      body.ownerDocument.doctype.name &&\n      regExpTest(EXPRESSIONS.DOCTYPE_NAME, body.ownerDocument.doctype.name)\n    ) {\n      serializedHTML =\n        '<!DOCTYPE ' + body.ownerDocument.doctype.name + '>\\n' + serializedHTML;\n    }\n\n    /* Sanitize final string template-safe */\n    if (SAFE_FOR_TEMPLATES) {\n      arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], (expr) => {\n        serializedHTML = stringReplace(serializedHTML, expr, ' ');\n      });\n    }\n\n    return trustedTypesPolicy && RETURN_TRUSTED_TYPE\n      ? trustedTypesPolicy.createHTML(serializedHTML)\n      : serializedHTML;\n  };\n\n  DOMPurify.setConfig = function (cfg = {}) {\n    _parseConfig(cfg);\n    SET_CONFIG = true;\n  };\n\n  DOMPurify.clearConfig = function () {\n    CONFIG = null;\n    SET_CONFIG = false;\n  };\n\n  DOMPurify.isValidAttribute = function (tag, attr, value) {\n    /* Initialize shared config vars if necessary. */\n    if (!CONFIG) {\n      _parseConfig({});\n    }\n\n    const lcTag = transformCaseFunc(tag);\n    const lcName = transformCaseFunc(attr);\n    return _isValidAttribute(lcTag, lcName, value);\n  };\n\n  DOMPurify.addHook = function (entryPoint, hookFunction) {\n    if (typeof hookFunction !== 'function') {\n      return;\n    }\n\n    arrayPush(hooks[entryPoint], hookFunction);\n  };\n\n  DOMPurify.removeHook = function (entryPoint, hookFunction) {\n    if (hookFunction !== undefined) {\n      const index = arrayLastIndexOf(hooks[entryPoint], hookFunction);\n\n      return index === -1\n        ? undefined\n        : arraySplice(hooks[entryPoint], index, 1)[0];\n    }\n\n    return arrayPop(hooks[entryPoint]);\n  };\n\n  DOMPurify.removeHooks = function (entryPoint) {\n    hooks[entryPoint] = [];\n  };\n\n  DOMPurify.removeAllHooks = function () {\n    hooks = _createHooksMap();\n  };\n\n  return DOMPurify;\n}\n\nexport default createDOMPurify();\n\nexport interface DOMPurify {\n  /**\n   * Creates a DOMPurify instance using the given window-like object. Defaults to `window`.\n   */\n  (root?: WindowLike): DOMPurify;\n\n  /**\n   * Version label, exposed for easier checks\n   * if DOMPurify is up to date or not\n   */\n  version: string;\n\n  /**\n   * Array of elements that DOMPurify removed during sanitation.\n   * Empty if nothing was removed.\n   */\n  removed: Array<RemovedElement | RemovedAttribute>;\n\n  /**\n   * Expose whether this browser supports running the full DOMPurify.\n   */\n  isSupported: boolean;\n\n  /**\n   * Set the configuration once.\n   *\n   * @param cfg configuration object\n   */\n  setConfig(cfg?: Config): void;\n\n  /**\n   * Removes the configuration.\n   */\n  clearConfig(): void;\n\n  /**\n   * Provides core sanitation functionality.\n   *\n   * @param dirty string or DOM node\n   * @param cfg object\n   * @returns Sanitized TrustedHTML.\n   */\n  sanitize(\n    dirty: string | Node,\n    cfg: Config & { RETURN_TRUSTED_TYPE: true }\n  ): TrustedHTML;\n\n  /**\n   * Provides core sanitation functionality.\n   *\n   * @param dirty DOM node\n   * @param cfg object\n   * @returns Sanitized DOM node.\n   */\n  sanitize(dirty: Node, cfg: Config & { IN_PLACE: true }): Node;\n\n  /**\n   * Provides core sanitation functionality.\n   *\n   * @param dirty string or DOM node\n   * @param cfg object\n   * @returns Sanitized DOM node.\n   */\n  sanitize(dirty: string | Node, cfg: Config & { RETURN_DOM: true }): Node;\n\n  /**\n   * Provides core sanitation functionality.\n   *\n   * @param dirty string or DOM node\n   * @param cfg object\n   * @returns Sanitized document fragment.\n   */\n  sanitize(\n    dirty: string | Node,\n    cfg: Config & { RETURN_DOM_FRAGMENT: true }\n  ): DocumentFragment;\n\n  /**\n   * Provides core sanitation functionality.\n   *\n   * @param dirty string or DOM node\n   * @param cfg object\n   * @returns Sanitized string.\n   */\n  sanitize(dirty: string | Node, cfg?: Config): string;\n\n  /**\n   * Checks if an attribute value is valid.\n   * Uses last set config, if any. Otherwise, uses config defaults.\n   *\n   * @param tag Tag name of containing element.\n   * @param attr Attribute name.\n   * @param value Attribute value.\n   * @returns Returns true if `value` is valid. Otherwise, returns false.\n   */\n  isValidAttribute(tag: string, attr: string, value: string): boolean;\n\n  /**\n   * Adds a DOMPurify hook.\n   *\n   * @param entryPoint entry point for the hook to add\n   * @param hookFunction function to execute\n   */\n  addHook(entryPoint: BasicHookName, hookFunction: NodeHook): void;\n\n  /**\n   * Adds a DOMPurify hook.\n   *\n   * @param entryPoint entry point for the hook to add\n   * @param hookFunction function to execute\n   */\n  addHook(entryPoint: ElementHookName, hookFunction: ElementHook): void;\n\n  /**\n   * Adds a DOMPurify hook.\n   *\n   * @param entryPoint entry point for the hook to add\n   * @param hookFunction function to execute\n   */\n  addHook(\n    entryPoint: DocumentFragmentHookName,\n    hookFunction: DocumentFragmentHook\n  ): void;\n\n  /**\n   * Adds a DOMPurify hook.\n   *\n   * @param entryPoint entry point for the hook to add\n   * @param hookFunction function to execute\n   */\n  addHook(\n    entryPoint: 'uponSanitizeElement',\n    hookFunction: UponSanitizeElementHook\n  ): void;\n\n  /**\n   * Adds a DOMPurify hook.\n   *\n   * @param entryPoint entry point for the hook to add\n   * @param hookFunction function to execute\n   */\n  addHook(\n    entryPoint: 'uponSanitizeAttribute',\n    hookFunction: UponSanitizeAttributeHook\n  ): void;\n\n  /**\n   * Remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if hook not specified)\n   *\n   * @param entryPoint entry point for the hook to remove\n   * @param hookFunction optional specific hook to remove\n   * @returns removed hook\n   */\n  removeHook(\n    entryPoint: BasicHookName,\n    hookFunction?: NodeHook\n  ): NodeHook | undefined;\n\n  /**\n   * Remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if hook not specified)\n   *\n   * @param entryPoint entry point for the hook to remove\n   * @param hookFunction optional specific hook to remove\n   * @returns removed hook\n   */\n  removeHook(\n    entryPoint: ElementHookName,\n    hookFunction?: ElementHook\n  ): ElementHook | undefined;\n\n  /**\n   * Remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if hook not specified)\n   *\n   * @param entryPoint entry point for the hook to remove\n   * @param hookFunction optional specific hook to remove\n   * @returns removed hook\n   */\n  removeHook(\n    entryPoint: DocumentFragmentHookName,\n    hookFunction?: DocumentFragmentHook\n  ): DocumentFragmentHook | undefined;\n\n  /**\n   * Remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if hook not specified)\n   *\n   * @param entryPoint entry point for the hook to remove\n   * @param hookFunction optional specific hook to remove\n   * @returns removed hook\n   */\n  removeHook(\n    entryPoint: 'uponSanitizeElement',\n    hookFunction?: UponSanitizeElementHook\n  ): UponSanitizeElementHook | undefined;\n\n  /**\n   * Remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if hook not specified)\n   *\n   * @param entryPoint entry point for the hook to remove\n   * @param hookFunction optional specific hook to remove\n   * @returns removed hook\n   */\n  removeHook(\n    entryPoint: 'uponSanitizeAttribute',\n    hookFunction?: UponSanitizeAttributeHook\n  ): UponSanitizeAttributeHook | undefined;\n\n  /**\n   * Removes all DOMPurify hooks at a given entryPoint\n   *\n   * @param entryPoint entry point for the hooks to remove\n   */\n  removeHooks(entryPoint: HookName): void;\n\n  /**\n   * Removes all DOMPurify hooks.\n   */\n  removeAllHooks(): void;\n}\n\n/**\n * An element removed by DOMPurify.\n */\nexport interface RemovedElement {\n  /**\n   * The element that was removed.\n   */\n  element: Node;\n}\n\n/**\n * An element removed by DOMPurify.\n */\nexport interface RemovedAttribute {\n  /**\n   * The attribute that was removed.\n   */\n  attribute: Attr | null;\n\n  /**\n   * The element that the attribute was removed.\n   */\n  from: Node;\n}\n\ntype BasicHookName =\n  | 'beforeSanitizeElements'\n  | 'afterSanitizeElements'\n  | 'uponSanitizeShadowNode';\ntype ElementHookName = 'beforeSanitizeAttributes' | 'afterSanitizeAttributes';\ntype DocumentFragmentHookName =\n  | 'beforeSanitizeShadowDOM'\n  | 'afterSanitizeShadowDOM';\ntype UponSanitizeElementHookName = 'uponSanitizeElement';\ntype UponSanitizeAttributeHookName = 'uponSanitizeAttribute';\n\ninterface HooksMap {\n  beforeSanitizeElements: NodeHook[];\n  afterSanitizeElements: NodeHook[];\n  beforeSanitizeShadowDOM: DocumentFragmentHook[];\n  uponSanitizeShadowNode: NodeHook[];\n  afterSanitizeShadowDOM: DocumentFragmentHook[];\n  beforeSanitizeAttributes: ElementHook[];\n  afterSanitizeAttributes: ElementHook[];\n  uponSanitizeElement: UponSanitizeElementHook[];\n  uponSanitizeAttribute: UponSanitizeAttributeHook[];\n}\n\nexport type HookName =\n  | BasicHookName\n  | ElementHookName\n  | DocumentFragmentHookName\n  | UponSanitizeElementHookName\n  | UponSanitizeAttributeHookName;\n\nexport type NodeHook = (\n  this: DOMPurify,\n  currentNode: Node,\n  hookEvent: null,\n  config: Config\n) => void;\n\nexport type ElementHook = (\n  this: DOMPurify,\n  currentNode: Element,\n  hookEvent: null,\n  config: Config\n) => void;\n\nexport type DocumentFragmentHook = (\n  this: DOMPurify,\n  currentNode: DocumentFragment,\n  hookEvent: null,\n  config: Config\n) => void;\n\nexport type UponSanitizeElementHook = (\n  this: DOMPurify,\n  currentNode: Node,\n  hookEvent: UponSanitizeElementHookEvent,\n  config: Config\n) => void;\n\nexport type UponSanitizeAttributeHook = (\n  this: DOMPurify,\n  currentNode: Element,\n  hookEvent: UponSanitizeAttributeHookEvent,\n  config: Config\n) => void;\n\nexport interface UponSanitizeElementHookEvent {\n  tagName: string;\n  allowedTags: Record<string, boolean>;\n}\n\nexport interface UponSanitizeAttributeHookEvent {\n  attrName: string;\n  attrValue: string;\n  keepAttr: boolean;\n  allowedAttributes: Record<string, boolean>;\n  forceKeepAttr: boolean | undefined;\n}\n\n/**\n * A `Window`-like object containing the properties and types that DOMPurify requires.\n */\nexport type WindowLike = Pick<\n  typeof globalThis,\n  | 'DocumentFragment'\n  | 'HTMLTemplateElement'\n  | 'Node'\n  | 'Element'\n  | 'NodeFilter'\n  | 'NamedNodeMap'\n  | 'HTMLFormElement'\n  | 'DOMParser'\n> & {\n  document?: Document;\n  MozNamedAttrMap?: typeof window.NamedNodeMap;\n} & Pick<TrustedTypesWindow, 'trustedTypes'>;\n"], "mappings": ";;;AAAA,MAAM;EACJA,OAAO;EACPC,cAAc;EACdC,QAAQ;EACRC,cAAc;EACdC;AACD,IAAGC,MAAM;AAEV,IAAI;EAAEC,MAAM;EAAEC,IAAI;EAAEC;AAAM,CAAE,GAAGH,MAAM,CAAC;AACtC,IAAI;EAAEI,KAAK;EAAEC;AAAW,IAAG,OAAOC,OAAO,KAAK,WAAW,IAAIA,OAAO;AAEpE,IAAI,CAACL,MAAM,EAAE;EACXA,MAAM,GAAG,SAAAA,MAAUA,CAAAM,CAAC;IAClB,OAAOA,CAAC;GACT;AACH;AAEA,IAAI,CAACL,IAAI,EAAE;EACTA,IAAI,GAAG,SAAAA,IAAUA,CAAAK,CAAC;IAChB,OAAOA,CAAC;GACT;AACH;AAEA,IAAI,CAACH,KAAK,EAAE;EACVA,KAAK,GAAG,SAAAA,KAAUA,CAAAI,GAAG,EAAEC,SAAS,EAAEC,IAAI;IACpC,OAAOF,GAAG,CAACJ,KAAK,CAACK,SAAS,EAAEC,IAAI,CAAC;GAClC;AACH;AAEA,IAAI,CAACL,SAAS,EAAE;EACdA,SAAS,GAAG,SAAAA,UAAUM,IAAI,EAAED,IAAI;IAC9B,OAAO,IAAIC,IAAI,CAAC,GAAGD,IAAI,CAAC;GACzB;AACH;AAEA,MAAME,YAAY,GAAGC,OAAO,CAACC,KAAK,CAACC,SAAS,CAACC,OAAO,CAAC;AAErD,MAAMC,gBAAgB,GAAGJ,OAAO,CAACC,KAAK,CAACC,SAAS,CAACG,WAAW,CAAC;AAC7D,MAAMC,QAAQ,GAAGN,OAAO,CAACC,KAAK,CAACC,SAAS,CAACK,GAAG,CAAC;AAC7C,MAAMC,SAAS,GAAGR,OAAO,CAACC,KAAK,CAACC,SAAS,CAACO,IAAI,CAAC;AAE/C,MAAMC,WAAW,GAAGV,OAAO,CAACC,KAAK,CAACC,SAAS,CAACS,MAAM,CAAC;AAEnD,MAAMC,iBAAiB,GAAGZ,OAAO,CAACa,MAAM,CAACX,SAAS,CAACY,WAAW,CAAC;AAC/D,MAAMC,cAAc,GAAGf,OAAO,CAACa,MAAM,CAACX,SAAS,CAACc,QAAQ,CAAC;AACzD,MAAMC,WAAW,GAAGjB,OAAO,CAACa,MAAM,CAACX,SAAS,CAACgB,KAAK,CAAC;AACnD,MAAMC,aAAa,GAAGnB,OAAO,CAACa,MAAM,CAACX,SAAS,CAACkB,OAAO,CAAC;AACvD,MAAMC,aAAa,GAAGrB,OAAO,CAACa,MAAM,CAACX,SAAS,CAACoB,OAAO,CAAC;AACvD,MAAMC,UAAU,GAAGvB,OAAO,CAACa,MAAM,CAACX,SAAS,CAACsB,IAAI,CAAC;AAEjD,MAAMC,oBAAoB,GAAGzB,OAAO,CAACb,MAAM,CAACe,SAAS,CAACwB,cAAc,CAAC;AAErE,MAAMC,UAAU,GAAG3B,OAAO,CAAC4B,MAAM,CAAC1B,SAAS,CAAC2B,IAAI,CAAC;AAEjD,MAAMC,eAAe,GAAGC,WAAW,CAACC,SAAS,CAAC;AAE9C;;;;;AAKG;AACH,SAAShC,OAAOA,CACdiC,IAAyC;EAEzC,OAAO,UAACC,OAAY,EAAuB;IACzC,IAAIA,OAAO,YAAYN,MAAM,EAAE;MAC7BM,OAAO,CAACC,SAAS,GAAG,CAAC;IACvB;IAAC,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAHsBzC,IAAW,OAAAI,KAAA,CAAAmC,IAAA,OAAAA,IAAA,WAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;MAAX1C,IAAW,CAAA0C,IAAA,QAAAF,SAAA,CAAAE,IAAA;IAAA;IAKlC,OAAOhD,KAAK,CAAC0C,IAAI,EAAEC,OAAO,EAAErC,IAAI,CAAC;GAClC;AACH;AAEA;;;;;AAKG;AACH,SAASkC,WAAWA,CAAIE,IAA2B;EACjD,OAAO;IAAA,SAAAO,KAAA,GAAAH,SAAA,CAAAC,MAAA,EAAIzC,IAAW,OAAAI,KAAA,CAAAuC,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAX5C,IAAW,CAAA4C,KAAA,IAAAJ,SAAA,CAAAI,KAAA;IAAA;IAAA,OAAQjD,SAAS,CAACyC,IAAI,EAAEpC,IAAI,CAAC;EAAA;AACrD;AAEA;;;;;;;AAOG;AACH,SAAS6C,QAAQA,CACfC,GAAwB,EACxBC,KAAqB,EACoD;EAAA,IAAzEC,iBAAA,GAAAR,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAS,SAAA,GAAAT,SAAA,MAAwDzB,iBAAiB;EAEzE,IAAI7B,cAAc,EAAE;IAClB;IACA;IACA;IACAA,cAAc,CAAC4D,GAAG,EAAE,IAAI,CAAC;EAC3B;EAEA,IAAII,CAAC,GAAGH,KAAK,CAACN,MAAM;EACpB,OAAOS,CAAC,EAAE,EAAE;IACV,IAAIC,OAAO,GAAGJ,KAAK,CAACG,CAAC,CAAC;IACtB,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;MAC/B,MAAMC,SAAS,GAAGJ,iBAAiB,CAACG,OAAO,CAAC;MAC5C,IAAIC,SAAS,KAAKD,OAAO,EAAE;QACzB;QACA,IAAI,CAAChE,QAAQ,CAAC4D,KAAK,CAAC,EAAE;UACnBA,KAAe,CAACG,CAAC,CAAC,GAAGE,SAAS;QACjC;QAEAD,OAAO,GAAGC,SAAS;MACrB;IACF;IAEAN,GAAG,CAACK,OAAO,CAAC,GAAG,IAAI;EACrB;EAEA,OAAOL,GAAG;AACZ;AAEA;;;;;AAKG;AACH,SAASO,UAAUA,CAAIN,KAAU;EAC/B,KAAK,IAAIO,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGP,KAAK,CAACN,MAAM,EAAEa,KAAK,EAAE,EAAE;IACjD,MAAMC,eAAe,GAAG3B,oBAAoB,CAACmB,KAAK,EAAEO,KAAK,CAAC;IAE1D,IAAI,CAACC,eAAe,EAAE;MACpBR,KAAK,CAACO,KAAK,CAAC,GAAG,IAAI;IACrB;EACF;EAEA,OAAOP,KAAK;AACd;AAEA;;;;;AAKG;AACH,SAASS,KAAKA,CAAgCC,MAAS;EACrD,MAAMC,SAAS,GAAGjE,MAAM,CAAC,IAAI,CAAC;EAE9B,KAAK,MAAM,CAACkE,QAAQ,EAAEC,KAAK,CAAC,IAAI3E,OAAO,CAACwE,MAAM,CAAC,EAAE;IAC/C,MAAMF,eAAe,GAAG3B,oBAAoB,CAAC6B,MAAM,EAAEE,QAAQ,CAAC;IAE9D,IAAIJ,eAAe,EAAE;MACnB,IAAInD,KAAK,CAACyD,OAAO,CAACD,KAAK,CAAC,EAAE;QACxBF,SAAS,CAACC,QAAQ,CAAC,GAAGN,UAAU,CAACO,KAAK,CAAC;MACzC,CAAC,MAAM,IACLA,KAAK,IACL,OAAOA,KAAK,KAAK,QAAQ,IACzBA,KAAK,CAACE,WAAW,KAAKxE,MAAM,EAC5B;QACAoE,SAAS,CAACC,QAAQ,CAAC,GAAGH,KAAK,CAACI,KAAK,CAAC;MACpC,CAAC,MAAM;QACLF,SAAS,CAACC,QAAQ,CAAC,GAAGC,KAAK;MAC7B;IACF;EACF;EAEA,OAAOF,SAAS;AAClB;AAEA;;;;;;AAMG;AACH,SAASK,YAAYA,CACnBN,MAAS,EACTO,IAAY;EAEZ,OAAOP,MAAM,KAAK,IAAI,EAAE;IACtB,MAAMQ,IAAI,GAAG5E,wBAAwB,CAACoE,MAAM,EAAEO,IAAI,CAAC;IAEnD,IAAIC,IAAI,EAAE;MACR,IAAIA,IAAI,CAACC,GAAG,EAAE;QACZ,OAAO/D,OAAO,CAAC8D,IAAI,CAACC,GAAG,CAAC;MAC1B;MAEA,IAAI,OAAOD,IAAI,CAACL,KAAK,KAAK,UAAU,EAAE;QACpC,OAAOzD,OAAO,CAAC8D,IAAI,CAACL,KAAK,CAAC;MAC5B;IACF;IAEAH,MAAM,GAAGrE,cAAc,CAACqE,MAAM,CAAC;EACjC;EAEA,SAASU,aAAaA,CAAA;IACpB,OAAO,IAAI;EACb;EAEA,OAAOA,aAAa;AACtB;AC3MO,MAAMC,MAAI,GAAG7E,MAAM,CAAC,CACzB,GAAG,EACH,MAAM,EACN,SAAS,EACT,SAAS,EACT,MAAM,EACN,SAAS,EACT,OAAO,EACP,OAAO,EACP,GAAG,EACH,KAAK,EACL,KAAK,EACL,KAAK,EACL,OAAO,EACP,YAAY,EACZ,MAAM,EACN,IAAI,EACJ,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,MAAM,EACN,MAAM,EACN,KAAK,EACL,UAAU,EACV,SAAS,EACT,MAAM,EACN,UAAU,EACV,IAAI,EACJ,WAAW,EACX,KAAK,EACL,SAAS,EACT,KAAK,EACL,QAAQ,EACR,KAAK,EACL,KAAK,EACL,IAAI,EACJ,IAAI,EACJ,SAAS,EACT,IAAI,EACJ,UAAU,EACV,YAAY,EACZ,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,MAAM,EACN,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,IAAI,EACJ,MAAM,EACN,GAAG,EACH,KAAK,EACL,OAAO,EACP,KAAK,EACL,KAAK,EACL,OAAO,EACP,QAAQ,EACR,IAAI,EACJ,MAAM,EACN,KAAK,EACL,MAAM,EACN,SAAS,EACT,MAAM,EACN,UAAU,EACV,OAAO,EACP,KAAK,EACL,MAAM,EACN,IAAI,EACJ,UAAU,EACV,QAAQ,EACR,QAAQ,EACR,GAAG,EACH,SAAS,EACT,KAAK,EACL,UAAU,EACV,GAAG,EACH,IAAI,EACJ,IAAI,EACJ,MAAM,EACN,GAAG,EACH,MAAM,EACN,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,KAAK,EACL,SAAS,EACT,KAAK,EACL,OAAO,EACP,OAAO,EACP,IAAI,EACJ,UAAU,EACV,UAAU,EACV,OAAO,EACP,IAAI,EACJ,OAAO,EACP,MAAM,EACN,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,KAAK,EACL,OAAO,EACP,KAAK,CACG,CAAC;AAEJ,MAAM8E,KAAG,GAAG9E,MAAM,CAAC,CACxB,KAAK,EACL,GAAG,EACH,UAAU,EACV,aAAa,EACb,cAAc,EACd,cAAc,EACd,eAAe,EACf,kBAAkB,EAClB,QAAQ,EACR,UAAU,EACV,MAAM,EACN,MAAM,EACN,SAAS,EACT,QAAQ,EACR,MAAM,EACN,GAAG,EACH,OAAO,EACP,UAAU,EACV,OAAO,EACP,OAAO,EACP,MAAM,EACN,gBAAgB,EAChB,QAAQ,EACR,MAAM,EACN,UAAU,EACV,OAAO,EACP,MAAM,EACN,SAAS,EACT,SAAS,EACT,UAAU,EACV,gBAAgB,EAChB,MAAM,EACN,MAAM,EACN,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,UAAU,EACV,OAAO,EACP,MAAM,EACN,OAAO,EACP,MAAM,EACN,OAAO,CACC,CAAC;AAEJ,MAAM+E,UAAU,GAAG/E,MAAM,CAAC,CAC/B,SAAS,EACT,eAAe,EACf,qBAAqB,EACrB,aAAa,EACb,kBAAkB,EAClB,mBAAmB,EACnB,mBAAmB,EACnB,gBAAgB,EAChB,cAAc,EACd,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,gBAAgB,EAChB,SAAS,EACT,SAAS,EACT,aAAa,EACb,cAAc,EACd,UAAU,EACV,cAAc,EACd,oBAAoB,EACpB,aAAa,EACb,QAAQ,EACR,cAAc,CACN,CAAC;AAEX;AACA;AACA;AACA;AACO,MAAMgF,aAAa,GAAGhF,MAAM,CAAC,CAClC,SAAS,EACT,eAAe,EACf,QAAQ,EACR,SAAS,EACT,WAAW,EACX,kBAAkB,EAClB,gBAAgB,EAChB,eAAe,EACf,eAAe,EACf,eAAe,EACf,OAAO,EACP,WAAW,EACX,MAAM,EACN,cAAc,EACd,WAAW,EACX,SAAS,EACT,eAAe,EACf,QAAQ,EACR,KAAK,EACL,YAAY,EACZ,SAAS,EACT,KAAK,CACG,CAAC;AAEJ,MAAMiF,QAAM,GAAGjF,MAAM,CAAC,CAC3B,MAAM,EACN,UAAU,EACV,QAAQ,EACR,SAAS,EACT,OAAO,EACP,QAAQ,EACR,IAAI,EACJ,YAAY,EACZ,eAAe,EACf,IAAI,EACJ,IAAI,EACJ,OAAO,EACP,SAAS,EACT,UAAU,EACV,OAAO,EACP,MAAM,EACN,IAAI,EACJ,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,MAAM,EACN,MAAM,EACN,SAAS,EACT,QAAQ,EACR,KAAK,EACL,OAAO,EACP,KAAK,EACL,QAAQ,EACR,YAAY,EACZ,aAAa,CACL,CAAC;AAEX;AACA;AACO,MAAMkF,gBAAgB,GAAGlF,MAAM,CAAC,CACrC,SAAS,EACT,aAAa,EACb,YAAY,EACZ,UAAU,EACV,WAAW,EACX,SAAS,EACT,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,WAAW,EACX,YAAY,EACZ,gBAAgB,EAChB,aAAa,EACb,MAAM,CACE,CAAC;AAEJ,MAAMmF,IAAI,GAAGnF,MAAM,CAAC,CAAC,OAAO,CAAU,CAAC;ACpRvC,MAAMoF,IAAI,GAAGpF,MAAM,CAAC,CACzB,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,KAAK,EACL,gBAAgB,EAChB,cAAc,EACd,sBAAsB,EACtB,UAAU,EACV,YAAY,EACZ,SAAS,EACT,QAAQ,EACR,SAAS,EACT,aAAa,EACb,aAAa,EACb,SAAS,EACT,MAAM,EACN,OAAO,EACP,OAAO,EACP,OAAO,EACP,MAAM,EACN,SAAS,EACT,UAAU,EACV,cAAc,EACd,QAAQ,EACR,aAAa,EACb,UAAU,EACV,UAAU,EACV,SAAS,EACT,KAAK,EACL,UAAU,EACV,yBAAyB,EACzB,uBAAuB,EACvB,UAAU,EACV,WAAW,EACX,SAAS,EACT,cAAc,EACd,MAAM,EACN,KAAK,EACL,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,MAAM,EACN,UAAU,EACV,IAAI,EACJ,WAAW,EACX,WAAW,EACX,OAAO,EACP,MAAM,EACN,OAAO,EACP,MAAM,EACN,MAAM,EACN,SAAS,EACT,MAAM,EACN,KAAK,EACL,KAAK,EACL,WAAW,EACX,OAAO,EACP,QAAQ,EACR,KAAK,EACL,WAAW,EACX,UAAU,EACV,OAAO,EACP,MAAM,EACN,OAAO,EACP,SAAS,EACT,YAAY,EACZ,QAAQ,EACR,MAAM,EACN,SAAS,EACT,SAAS,EACT,aAAa,EACb,aAAa,EACb,SAAS,EACT,eAAe,EACf,qBAAqB,EACrB,QAAQ,EACR,SAAS,EACT,SAAS,EACT,YAAY,EACZ,UAAU,EACV,KAAK,EACL,UAAU,EACV,KAAK,EACL,UAAU,EACV,MAAM,EACN,MAAM,EACN,SAAS,EACT,YAAY,EACZ,OAAO,EACP,UAAU,EACV,OAAO,EACP,MAAM,EACN,OAAO,EACP,MAAM,EACN,SAAS,EACT,OAAO,EACP,KAAK,EACL,QAAQ,EACR,MAAM,EACN,OAAO,EACP,SAAS,EACT,UAAU,EACV,OAAO,EACP,WAAW,EACX,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,OAAO,EACP,MAAM,EACN,OAAO,EACP,MAAM,CACE,CAAC;AAEJ,MAAMqF,GAAG,GAAGrF,MAAM,CAAC,CACxB,eAAe,EACf,YAAY,EACZ,UAAU,EACV,oBAAoB,EACpB,WAAW,EACX,QAAQ,EACR,eAAe,EACf,eAAe,EACf,SAAS,EACT,eAAe,EACf,gBAAgB,EAChB,OAAO,EACP,MAAM,EACN,IAAI,EACJ,OAAO,EACP,MAAM,EACN,eAAe,EACf,WAAW,EACX,WAAW,EACX,OAAO,EACP,qBAAqB,EACrB,6BAA6B,EAC7B,eAAe,EACf,iBAAiB,EACjB,IAAI,EACJ,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,IAAI,EACJ,iBAAiB,EACjB,WAAW,EACX,SAAS,EACT,SAAS,EACT,KAAK,EACL,UAAU,EACV,WAAW,EACX,KAAK,EACL,UAAU,EACV,MAAM,EACN,cAAc,EACd,WAAW,EACX,QAAQ,EACR,aAAa,EACb,aAAa,EACb,eAAe,EACf,aAAa,EACb,WAAW,EACX,kBAAkB,EAClB,cAAc,EACd,YAAY,EACZ,cAAc,EACd,aAAa,EACb,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,YAAY,EACZ,UAAU,EACV,eAAe,EACf,mBAAmB,EACnB,QAAQ,EACR,MAAM,EACN,IAAI,EACJ,iBAAiB,EACjB,IAAI,EACJ,KAAK,EACL,WAAW,EACX,GAAG,EACH,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,SAAS,EACT,WAAW,EACX,YAAY,EACZ,UAAU,EACV,MAAM,EACN,cAAc,EACd,gBAAgB,EAChB,cAAc,EACd,kBAAkB,EAClB,gBAAgB,EAChB,OAAO,EACP,YAAY,EACZ,YAAY,EACZ,cAAc,EACd,cAAc,EACd,aAAa,EACb,aAAa,EACb,kBAAkB,EAClB,WAAW,EACX,KAAK,EACL,MAAM,EACN,OAAO,EACP,QAAQ,EACR,MAAM,EACN,KAAK,EACL,MAAM,EACN,YAAY,EACZ,QAAQ,EACR,UAAU,EACV,SAAS,EACT,OAAO,EACP,QAAQ,EACR,aAAa,EACb,QAAQ,EACR,UAAU,EACV,aAAa,EACb,MAAM,EACN,YAAY,EACZ,qBAAqB,EACrB,kBAAkB,EAClB,cAAc,EACd,QAAQ,EACR,eAAe,EACf,qBAAqB,EACrB,gBAAgB,EAChB,GAAG,EACH,IAAI,EACJ,IAAI,EACJ,QAAQ,EACR,MAAM,EACN,MAAM,EACN,aAAa,EACb,WAAW,EACX,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,MAAM,EACN,iBAAiB,EACjB,OAAO,EACP,kBAAkB,EAClB,kBAAkB,EAClB,cAAc,EACd,aAAa,EACb,cAAc,EACd,aAAa,EACb,YAAY,EACZ,cAAc,EACd,kBAAkB,EAClB,mBAAmB,EACnB,gBAAgB,EAChB,iBAAiB,EACjB,mBAAmB,EACnB,gBAAgB,EAChB,QAAQ,EACR,cAAc,EACd,OAAO,EACP,cAAc,EACd,gBAAgB,EAChB,UAAU,EACV,aAAa,EACb,SAAS,EACT,SAAS,EACT,WAAW,EACX,kBAAkB,EAClB,aAAa,EACb,iBAAiB,EACjB,gBAAgB,EAChB,YAAY,EACZ,MAAM,EACN,IAAI,EACJ,IAAI,EACJ,SAAS,EACT,QAAQ,EACR,SAAS,EACT,YAAY,EACZ,SAAS,EACT,YAAY,EACZ,eAAe,EACf,eAAe,EACf,OAAO,EACP,cAAc,EACd,MAAM,EACN,cAAc,EACd,kBAAkB,EAClB,kBAAkB,EAClB,GAAG,EACH,IAAI,EACJ,IAAI,EACJ,OAAO,EACP,GAAG,EACH,IAAI,EACJ,IAAI,EACJ,GAAG,EACH,YAAY,CACJ,CAAC;AAEJ,MAAMsF,MAAM,GAAGtF,MAAM,CAAC,CAC3B,QAAQ,EACR,aAAa,EACb,OAAO,EACP,UAAU,EACV,OAAO,EACP,cAAc,EACd,aAAa,EACb,YAAY,EACZ,YAAY,EACZ,OAAO,EACP,KAAK,EACL,SAAS,EACT,cAAc,EACd,UAAU,EACV,OAAO,EACP,OAAO,EACP,QAAQ,EACR,MAAM,EACN,IAAI,EACJ,SAAS,EACT,QAAQ,EACR,eAAe,EACf,QAAQ,EACR,QAAQ,EACR,gBAAgB,EAChB,WAAW,EACX,UAAU,EACV,aAAa,EACb,SAAS,EACT,SAAS,EACT,eAAe,EACf,UAAU,EACV,UAAU,EACV,MAAM,EACN,UAAU,EACV,UAAU,EACV,YAAY,EACZ,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,aAAa,EACb,eAAe,EACf,sBAAsB,EACtB,WAAW,EACX,WAAW,EACX,YAAY,EACZ,UAAU,EACV,gBAAgB,EAChB,gBAAgB,EAChB,WAAW,EACX,SAAS,EACT,OAAO,EACP,OAAO,CACR,CAAC;AAEK,MAAMuF,GAAG,GAAGvF,MAAM,CAAC,CACxB,YAAY,EACZ,QAAQ,EACR,aAAa,EACb,WAAW,EACX,aAAa,CACL,CAAC;;AChXX;AACO,MAAMwF,aAAa,GAAGvF,IAAI,CAAC,2BAA2B,CAAC,CAAC;AACxD,MAAMwF,QAAQ,GAAGxF,IAAI,CAAC,uBAAuB,CAAC;AAC9C,MAAMyF,WAAW,GAAGzF,IAAI,CAAC,eAAe,CAAC,CAAC;AAC1C,MAAM0F,SAAS,GAAG1F,IAAI,CAAC,8BAA8B,CAAC,CAAC;AACvD,MAAM2F,SAAS,GAAG3F,IAAI,CAAC,gBAAgB,CAAC,CAAC;AACzC,MAAM4F,cAAc,GAAG5F,IAAI,CAChC,kGAAkG;CACnG;AACM,MAAM6F,iBAAiB,GAAG7F,IAAI,CAAC,uBAAuB,CAAC;AACvD,MAAM8F,eAAe,GAAG9F,IAAI,CACjC,6DAA6D;CAC9D;AACM,MAAM+F,YAAY,GAAG/F,IAAI,CAAC,SAAS,CAAC;AACpC,MAAMgG,cAAc,GAAGhG,IAAI,CAAC,0BAA0B,CAAC;;;;;;;;;;;;;;;AChB9D;AAkCA;AACA,MAAMiG,SAAS,GAAG;EAChBtC,OAAO,EAAE,CAAC;EACVuC,SAAS,EAAE,CAAC;EACZhB,IAAI,EAAE,CAAC;EACPiB,YAAY,EAAE,CAAC;EACfC,eAAe,EAAE,CAAC;EAAE;EACpBC,UAAU,EAAE,CAAC;EAAE;EACfC,sBAAsB,EAAE,CAAC;EACzBC,OAAO,EAAE,CAAC;EACVC,QAAQ,EAAE,CAAC;EACXC,YAAY,EAAE,EAAE;EAChBC,gBAAgB,EAAE,EAAE;EACpBC,QAAQ,EAAE,EAAE;CACb;AAED,MAAMC,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAG;EAChB,OAAO,OAAOC,MAAM,KAAK,WAAW,GAAG,IAAI,GAAGA,MAAM;AACtD,CAAC;AAED;;;;;;;AAOG;AACH,MAAMC,yBAAyB,GAAG,SAA5BA,yBAAyBA,CAC7BC,YAAsC,EACtCC,iBAAoC;EAEpC,IACE,OAAOD,YAAY,KAAK,QAAQ,IAChC,OAAOA,YAAY,CAACE,YAAY,KAAK,UAAU,EAC/C;IACA,OAAO,IAAI;EACb;EAEA;EACA;EACA;EACA,IAAIC,MAAM,GAAG,IAAI;EACjB,MAAMC,SAAS,GAAG,uBAAuB;EACzC,IAAIH,iBAAiB,IAAIA,iBAAiB,CAACI,YAAY,CAACD,SAAS,CAAC,EAAE;IAClED,MAAM,GAAGF,iBAAiB,CAACK,YAAY,CAACF,SAAS,CAAC;EACpD;EAEA,MAAMG,UAAU,GAAG,WAAW,IAAIJ,MAAM,GAAG,GAAG,GAAGA,MAAM,GAAG,EAAE,CAAC;EAE7D,IAAI;IACF,OAAOH,YAAY,CAACE,YAAY,CAACK,UAAU,EAAE;MAC3CC,UAAUA,CAACpC,IAAI;QACb,OAAOA,IAAI;OACZ;MACDqC,eAAeA,CAACC,SAAS;QACvB,OAAOA,SAAS;MAClB;IACD,EAAC;GACH,CAAC,OAAOC,CAAC,EAAE;IACV;IACA;IACA;IACAC,OAAO,CAACC,IAAI,CACV,sBAAsB,GAAGN,UAAU,GAAG,wBAAwB,CAC/D;IACD,OAAO,IAAI;EACb;AACF,CAAC;AAED,MAAMO,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAG;EACtB,OAAO;IACLC,uBAAuB,EAAE,EAAE;IAC3BC,qBAAqB,EAAE,EAAE;IACzBC,sBAAsB,EAAE,EAAE;IAC1BC,wBAAwB,EAAE,EAAE;IAC5BC,sBAAsB,EAAE,EAAE;IAC1BC,uBAAuB,EAAE,EAAE;IAC3BC,qBAAqB,EAAE,EAAE;IACzBC,mBAAmB,EAAE,EAAE;IACvBC,sBAAsB,EAAE;GACzB;AACH,CAAC;AAED,SAASC,eAAeA,CAAA,EAAiC;EAAA,IAAhC1B,MAAqB,GAAA7D,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAS,SAAA,GAAAT,SAAA,MAAA4D,SAAS,EAAE;EACvD,MAAM4B,SAAS,GAAeC,IAAgB,IAAKF,eAAe,CAACE,IAAI,CAAC;EAExED,SAAS,CAACE,OAAO,GAAG,OAAO;EAE3BF,SAAS,CAACG,OAAO,GAAG,EAAE;EAEtB,IACE,CAAC9B,MAAM,IACP,CAACA,MAAM,CAACL,QAAQ,IAChBK,MAAM,CAACL,QAAQ,CAACoC,QAAQ,KAAK3C,SAAS,CAACO,QAAQ,IAC/C,CAACK,MAAM,CAACgC,OAAO,EACf;IACA;IACA;IACAL,SAAS,CAACM,WAAW,GAAG,KAAK;IAE7B,OAAON,SAAS;EAClB;EAEA,IAAI;IAAEhC;EAAU,IAAGK,MAAM;EAEzB,MAAMkC,gBAAgB,GAAGvC,QAAQ;EACjC,MAAMwC,aAAa,GACjBD,gBAAgB,CAACC,aAAkC;EACrD,MAAM;IACJC,gBAAgB;IAChBC,mBAAmB;IACnBC,IAAI;IACJN,OAAO;IACPO,UAAU;IACVC,YAAY,GAAGxC,MAAM,CAACwC,YAAY,IAAKxC,MAAc,CAACyC,eAAe;IACrEC,eAAe;IACfC,SAAS;IACTzC;EACD,IAAGF,MAAM;EAEV,MAAM4C,gBAAgB,GAAGZ,OAAO,CAAChI,SAAS;EAE1C,MAAM6I,SAAS,GAAGnF,YAAY,CAACkF,gBAAgB,EAAE,WAAW,CAAC;EAC7D,MAAME,MAAM,GAAGpF,YAAY,CAACkF,gBAAgB,EAAE,QAAQ,CAAC;EACvD,MAAMG,cAAc,GAAGrF,YAAY,CAACkF,gBAAgB,EAAE,aAAa,CAAC;EACpE,MAAMI,aAAa,GAAGtF,YAAY,CAACkF,gBAAgB,EAAE,YAAY,CAAC;EAClE,MAAMK,aAAa,GAAGvF,YAAY,CAACkF,gBAAgB,EAAE,YAAY,CAAC;EAElE;EACA;EACA;EACA;EACA;EACA;EACA,IAAI,OAAOP,mBAAmB,KAAK,UAAU,EAAE;IAC7C,MAAMa,QAAQ,GAAGvD,QAAQ,CAACwD,aAAa,CAAC,UAAU,CAAC;IACnD,IAAID,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACE,OAAO,CAACC,aAAa,EAAE;MACtD1D,QAAQ,GAAGuD,QAAQ,CAACE,OAAO,CAACC,aAAa;IAC3C;EACF;EAEA,IAAIC,kBAAkB;EACtB,IAAIC,SAAS,GAAG,EAAE;EAElB,MAAM;IACJC,cAAc;IACdC,kBAAkB;IAClBC,sBAAsB;IACtBC;EAAoB,CACrB,GAAGhE,QAAQ;EACZ,MAAM;IAAEiE;EAAY,IAAG1B,gBAAgB;EAEvC,IAAI2B,KAAK,GAAG7C,eAAe,EAAE;EAE7B;;AAEG;EACHW,SAAS,CAACM,WAAW,GACnB,OAAOrJ,OAAO,KAAK,UAAU,IAC7B,OAAOqK,aAAa,KAAK,UAAU,IACnCO,cAAc,IACdA,cAAc,CAACM,kBAAkB,KAAKlH,SAAS;EAEjD,MAAM;IACJ8B,aAAa;IACbC,QAAQ;IACRC,WAAW;IACXC,SAAS;IACTC,SAAS;IACTE,iBAAiB;IACjBC,eAAe;IACfE;EACD,IAAG4E,WAAW;EAEf,IAAI;IAAEhF,cAAA,EAAAiF;EAAgB,IAAGD,WAAW;EAEpC;;;AAGG;EAEH;EACA,IAAIE,YAAY,GAAG,IAAI;EACvB,MAAMC,oBAAoB,GAAG1H,QAAQ,CAAC,EAAE,EAAE,CACxC,GAAGuB,MAAS,EACZ,GAAGC,KAAQ,EACX,GAAGC,UAAe,EAClB,GAAGE,QAAW,EACd,GAAGE,IAAS,CACb,CAAC;EAEF;EACA,IAAI8F,YAAY,GAAG,IAAI;EACvB,MAAMC,oBAAoB,GAAG5H,QAAQ,CAAC,EAAE,EAAE,CACxC,GAAG8B,IAAU,EACb,GAAGC,GAAS,EACZ,GAAGC,MAAY,EACf,GAAGC,GAAS,CACb,CAAC;EAEF;;;;;AAKG;EACH,IAAI4F,uBAAuB,GAAGpL,MAAM,CAACE,IAAI,CACvCC,MAAM,CAAC,IAAI,EAAE;IACXkL,YAAY,EAAE;MACZC,QAAQ,EAAE,IAAI;MACdC,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE,IAAI;MAChBlH,KAAK,EAAE;KACR;IACDmH,kBAAkB,EAAE;MAClBH,QAAQ,EAAE,IAAI;MACdC,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE,IAAI;MAChBlH,KAAK,EAAE;KACR;IACDoH,8BAA8B,EAAE;MAC9BJ,QAAQ,EAAE,IAAI;MACdC,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE,IAAI;MAChBlH,KAAK,EAAE;IACR;EACF,EAAC,CACH;EAED;EACA,IAAIqH,WAAW,GAAG,IAAI;EAEtB;EACA,IAAIC,WAAW,GAAG,IAAI;EAEtB;EACA,IAAIC,eAAe,GAAG,IAAI;EAE1B;EACA,IAAIC,eAAe,GAAG,IAAI;EAE1B;EACA,IAAIC,uBAAuB,GAAG,KAAK;EAEnC;AACuD;EACvD,IAAIC,wBAAwB,GAAG,IAAI;EAEnC;;AAEG;EACH,IAAIC,kBAAkB,GAAG,KAAK;EAE9B;;AAEG;EACH,IAAIC,YAAY,GAAG,IAAI;EAEvB;EACA,IAAIC,cAAc,GAAG,KAAK;EAE1B;EACA,IAAIC,UAAU,GAAG,KAAK;EAEtB;AAC0E;EAC1E,IAAIC,UAAU,GAAG,KAAK;EAEtB;;;AAGG;EACH,IAAIC,UAAU,GAAG,KAAK;EAEtB;AACsE;EACtE,IAAIC,mBAAmB,GAAG,KAAK;EAE/B;AAC2C;EAC3C,IAAIC,mBAAmB,GAAG,KAAK;EAE/B;;AAEG;EACH,IAAIC,YAAY,GAAG,IAAI;EAEvB;;;;;;;;;;;;AAYG;EACH,IAAIC,oBAAoB,GAAG,KAAK;EAChC,MAAMC,2BAA2B,GAAG,eAAe;EAEnD;EACA,IAAIC,YAAY,GAAG,IAAI;EAEvB;AACwE;EACxE,IAAIC,QAAQ,GAAG,KAAK;EAEpB;EACA,IAAIC,YAAY,GAA8B,EAAE;EAEhD;EACA,IAAIC,eAAe,GAAG,IAAI;EAC1B,MAAMC,uBAAuB,GAAGzJ,QAAQ,CAAC,EAAE,EAAE,CAC3C,gBAAgB,EAChB,OAAO,EACP,UAAU,EACV,MAAM,EACN,eAAe,EACf,MAAM,EACN,QAAQ,EACR,MAAM,EACN,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,OAAO,EACP,SAAS,EACT,UAAU,EACV,UAAU,EACV,WAAW,EACX,QAAQ,EACR,OAAO,EACP,KAAK,EACL,UAAU,EACV,OAAO,EACP,OAAO,EACP,OAAO,EACP,KAAK,CACN,CAAC;EAEF;EACA,IAAI0J,aAAa,GAAG,IAAI;EACxB,MAAMC,qBAAqB,GAAG3J,QAAQ,CAAC,EAAE,EAAE,CACzC,OAAO,EACP,OAAO,EACP,KAAK,EACL,QAAQ,EACR,OAAO,EACP,OAAO,CACR,CAAC;EAEF;EACA,IAAI4J,mBAAmB,GAAG,IAAI;EAC9B,MAAMC,2BAA2B,GAAG7J,QAAQ,CAAC,EAAE,EAAE,CAC/C,KAAK,EACL,OAAO,EACP,KAAK,EACL,IAAI,EACJ,OAAO,EACP,MAAM,EACN,SAAS,EACT,aAAa,EACb,MAAM,EACN,SAAS,EACT,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,CACR,CAAC;EAEF,MAAM8J,gBAAgB,GAAG,oCAAoC;EAC7D,MAAMC,aAAa,GAAG,4BAA4B;EAClD,MAAMC,cAAc,GAAG,8BAA8B;EACrD;EACA,IAAIC,SAAS,GAAGD,cAAc;EAC9B,IAAIE,cAAc,GAAG,KAAK;EAE1B;EACA,IAAIC,kBAAkB,GAAG,IAAI;EAC7B,MAAMC,0BAA0B,GAAGpK,QAAQ,CACzC,EAAE,EACF,CAAC8J,gBAAgB,EAAEC,aAAa,EAAEC,cAAc,CAAC,EACjD3L,cAAc,CACf;EAED,IAAIgM,8BAA8B,GAAGrK,QAAQ,CAAC,EAAE,EAAE,CAChD,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,OAAO,CACR,CAAC;EAEF,IAAIsK,uBAAuB,GAAGtK,QAAQ,CAAC,EAAE,EAAE,CAAC,gBAAgB,CAAC,CAAC;EAE9D;EACA;EACA;EACA;EACA,MAAMuK,4BAA4B,GAAGvK,QAAQ,CAAC,EAAE,EAAE,CAChD,OAAO,EACP,OAAO,EACP,MAAM,EACN,GAAG,EACH,QAAQ,CACT,CAAC;EAEF;EACA,IAAIwK,iBAAiB,GAAkC,IAAI;EAC3D,MAAMC,4BAA4B,GAAG,CAAC,uBAAuB,EAAE,WAAW,CAAC;EAC3E,MAAMC,yBAAyB,GAAG,WAAW;EAC7C,IAAIvK,iBAAiB,GAA0C,IAAI;EAEnE;EACA,IAAIwK,MAAM,GAAkB,IAAI;EAEhC;EACA;EAEA,MAAMC,WAAW,GAAGzH,QAAQ,CAACwD,aAAa,CAAC,MAAM,CAAC;EAElD,MAAMkE,iBAAiB,GAAG,SAApBA,iBAAiBA,CACrBC,SAAkB;IAElB,OAAOA,SAAS,YAAY5L,MAAM,IAAI4L,SAAS,YAAYC,QAAQ;GACpE;EAED;;;;AAIG;EACH;EACA,MAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAA6B;IAAA,IAAhBC,GAAA,GAAAtL,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAS,SAAA,GAAAT,SAAA,MAAc,EAAE;IAC7C,IAAIgL,MAAM,IAAIA,MAAM,KAAKM,GAAG,EAAE;MAC5B;IACF;IAEA;IACA,IAAI,CAACA,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;MACnCA,GAAG,GAAG,EAAE;IACV;IAEA;IACAA,GAAG,GAAGtK,KAAK,CAACsK,GAAG,CAAC;IAEhBT,iBAAiB;IACf;IACAC,4BAA4B,CAAC7L,OAAO,CAACqM,GAAG,CAACT,iBAAiB,CAAC,KAAK,CAAC,CAAC,GAC9DE,yBAAyB,GACzBO,GAAG,CAACT,iBAAiB;IAE3B;IACArK,iBAAiB,GACfqK,iBAAiB,KAAK,uBAAuB,GACzCnM,cAAc,GACdH,iBAAiB;IAEvB;IACAuJ,YAAY,GAAG1I,oBAAoB,CAACkM,GAAG,EAAE,cAAc,CAAC,GACpDjL,QAAQ,CAAC,EAAE,EAAEiL,GAAG,CAACxD,YAAY,EAAEtH,iBAAiB,CAAC,GACjDuH,oBAAoB;IACxBC,YAAY,GAAG5I,oBAAoB,CAACkM,GAAG,EAAE,cAAc,CAAC,GACpDjL,QAAQ,CAAC,EAAE,EAAEiL,GAAG,CAACtD,YAAY,EAAExH,iBAAiB,CAAC,GACjDyH,oBAAoB;IACxBuC,kBAAkB,GAAGpL,oBAAoB,CAACkM,GAAG,EAAE,oBAAoB,CAAC,GAChEjL,QAAQ,CAAC,EAAE,EAAEiL,GAAG,CAACd,kBAAkB,EAAE9L,cAAc,CAAC,GACpD+L,0BAA0B;IAC9BR,mBAAmB,GAAG7K,oBAAoB,CAACkM,GAAG,EAAE,mBAAmB,CAAC,GAChEjL,QAAQ,CACNW,KAAK,CAACkJ,2BAA2B,CAAC,EAClCoB,GAAG,CAACC,iBAAiB,EACrB/K,iBAAiB,CAClB,GACD0J,2BAA2B;IAC/BH,aAAa,GAAG3K,oBAAoB,CAACkM,GAAG,EAAE,mBAAmB,CAAC,GAC1DjL,QAAQ,CACNW,KAAK,CAACgJ,qBAAqB,CAAC,EAC5BsB,GAAG,CAACE,iBAAiB,EACrBhL,iBAAiB,CAClB,GACDwJ,qBAAqB;IACzBH,eAAe,GAAGzK,oBAAoB,CAACkM,GAAG,EAAE,iBAAiB,CAAC,GAC1DjL,QAAQ,CAAC,EAAE,EAAEiL,GAAG,CAACzB,eAAe,EAAErJ,iBAAiB,CAAC,GACpDsJ,uBAAuB;IAC3BrB,WAAW,GAAGrJ,oBAAoB,CAACkM,GAAG,EAAE,aAAa,CAAC,GAClDjL,QAAQ,CAAC,EAAE,EAAEiL,GAAG,CAAC7C,WAAW,EAAEjI,iBAAiB,CAAC,GAChDQ,KAAK,CAAC,EAAE,CAAC;IACb0H,WAAW,GAAGtJ,oBAAoB,CAACkM,GAAG,EAAE,aAAa,CAAC,GAClDjL,QAAQ,CAAC,EAAE,EAAEiL,GAAG,CAAC5C,WAAW,EAAElI,iBAAiB,CAAC,GAChDQ,KAAK,CAAC,EAAE,CAAC;IACb4I,YAAY,GAAGxK,oBAAoB,CAACkM,GAAG,EAAE,cAAc,CAAC,GACpDA,GAAG,CAAC1B,YAAY,GAChB,KAAK;IACTjB,eAAe,GAAG2C,GAAG,CAAC3C,eAAe,KAAK,KAAK,CAAC;IAChDC,eAAe,GAAG0C,GAAG,CAAC1C,eAAe,KAAK,KAAK,CAAC;IAChDC,uBAAuB,GAAGyC,GAAG,CAACzC,uBAAuB,IAAI,KAAK,CAAC;IAC/DC,wBAAwB,GAAGwC,GAAG,CAACxC,wBAAwB,KAAK,KAAK,CAAC;IAClEC,kBAAkB,GAAGuC,GAAG,CAACvC,kBAAkB,IAAI,KAAK,CAAC;IACrDC,YAAY,GAAGsC,GAAG,CAACtC,YAAY,KAAK,KAAK,CAAC;IAC1CC,cAAc,GAAGqC,GAAG,CAACrC,cAAc,IAAI,KAAK,CAAC;IAC7CG,UAAU,GAAGkC,GAAG,CAAClC,UAAU,IAAI,KAAK,CAAC;IACrCC,mBAAmB,GAAGiC,GAAG,CAACjC,mBAAmB,IAAI,KAAK,CAAC;IACvDC,mBAAmB,GAAGgC,GAAG,CAAChC,mBAAmB,IAAI,KAAK,CAAC;IACvDH,UAAU,GAAGmC,GAAG,CAACnC,UAAU,IAAI,KAAK,CAAC;IACrCI,YAAY,GAAG+B,GAAG,CAAC/B,YAAY,KAAK,KAAK,CAAC;IAC1CC,oBAAoB,GAAG8B,GAAG,CAAC9B,oBAAoB,IAAI,KAAK,CAAC;IACzDE,YAAY,GAAG4B,GAAG,CAAC5B,YAAY,KAAK,KAAK,CAAC;IAC1CC,QAAQ,GAAG2B,GAAG,CAAC3B,QAAQ,IAAI,KAAK,CAAC;IACjC9B,gBAAc,GAAGyD,GAAG,CAACG,kBAAkB,IAAI7I,cAA0B;IACrE0H,SAAS,GAAGgB,GAAG,CAAChB,SAAS,IAAID,cAAc;IAC3CK,8BAA8B,GAC5BY,GAAG,CAACZ,8BAA8B,IAAIA,8BAA8B;IACtEC,uBAAuB,GACrBW,GAAG,CAACX,uBAAuB,IAAIA,uBAAuB;IAExDzC,uBAAuB,GAAGoD,GAAG,CAACpD,uBAAuB,IAAI,EAAE;IAC3D,IACEoD,GAAG,CAACpD,uBAAuB,IAC3BgD,iBAAiB,CAACI,GAAG,CAACpD,uBAAuB,CAACC,YAAY,CAAC,EAC3D;MACAD,uBAAuB,CAACC,YAAY,GAClCmD,GAAG,CAACpD,uBAAuB,CAACC,YAAY;IAC5C;IAEA,IACEmD,GAAG,CAACpD,uBAAuB,IAC3BgD,iBAAiB,CAACI,GAAG,CAACpD,uBAAuB,CAACK,kBAAkB,CAAC,EACjE;MACAL,uBAAuB,CAACK,kBAAkB,GACxC+C,GAAG,CAACpD,uBAAuB,CAACK,kBAAkB;IAClD;IAEA,IACE+C,GAAG,CAACpD,uBAAuB,IAC3B,OAAOoD,GAAG,CAACpD,uBAAuB,CAACM,8BAA8B,KAC/D,SAAS,EACX;MACAN,uBAAuB,CAACM,8BAA8B,GACpD8C,GAAG,CAACpD,uBAAuB,CAACM,8BAA8B;IAC9D;IAEA,IAAIO,kBAAkB,EAAE;MACtBH,eAAe,GAAG,KAAK;IACzB;IAEA,IAAIS,mBAAmB,EAAE;MACvBD,UAAU,GAAG,IAAI;IACnB;IAEA;IACA,IAAIQ,YAAY,EAAE;MAChB9B,YAAY,GAAGzH,QAAQ,CAAC,EAAE,EAAE6B,IAAS,CAAC;MACtC8F,YAAY,GAAG,EAAE;MACjB,IAAI4B,YAAY,CAACzH,IAAI,KAAK,IAAI,EAAE;QAC9B9B,QAAQ,CAACyH,YAAY,EAAElG,MAAS,CAAC;QACjCvB,QAAQ,CAAC2H,YAAY,EAAE7F,IAAU,CAAC;MACpC;MAEA,IAAIyH,YAAY,CAACxH,GAAG,KAAK,IAAI,EAAE;QAC7B/B,QAAQ,CAACyH,YAAY,EAAEjG,KAAQ,CAAC;QAChCxB,QAAQ,CAAC2H,YAAY,EAAE5F,GAAS,CAAC;QACjC/B,QAAQ,CAAC2H,YAAY,EAAE1F,GAAS,CAAC;MACnC;MAEA,IAAIsH,YAAY,CAAC9H,UAAU,KAAK,IAAI,EAAE;QACpCzB,QAAQ,CAACyH,YAAY,EAAEhG,UAAe,CAAC;QACvCzB,QAAQ,CAAC2H,YAAY,EAAE5F,GAAS,CAAC;QACjC/B,QAAQ,CAAC2H,YAAY,EAAE1F,GAAS,CAAC;MACnC;MAEA,IAAIsH,YAAY,CAACvH,MAAM,KAAK,IAAI,EAAE;QAChChC,QAAQ,CAACyH,YAAY,EAAE9F,QAAW,CAAC;QACnC3B,QAAQ,CAAC2H,YAAY,EAAE3F,MAAY,CAAC;QACpChC,QAAQ,CAAC2H,YAAY,EAAE1F,GAAS,CAAC;MACnC;IACF;IAEA;IACA,IAAIgJ,GAAG,CAACI,QAAQ,EAAE;MAChB,IAAI5D,YAAY,KAAKC,oBAAoB,EAAE;QACzCD,YAAY,GAAG9G,KAAK,CAAC8G,YAAY,CAAC;MACpC;MAEAzH,QAAQ,CAACyH,YAAY,EAAEwD,GAAG,CAACI,QAAQ,EAAElL,iBAAiB,CAAC;IACzD;IAEA,IAAI8K,GAAG,CAACK,QAAQ,EAAE;MAChB,IAAI3D,YAAY,KAAKC,oBAAoB,EAAE;QACzCD,YAAY,GAAGhH,KAAK,CAACgH,YAAY,CAAC;MACpC;MAEA3H,QAAQ,CAAC2H,YAAY,EAAEsD,GAAG,CAACK,QAAQ,EAAEnL,iBAAiB,CAAC;IACzD;IAEA,IAAI8K,GAAG,CAACC,iBAAiB,EAAE;MACzBlL,QAAQ,CAAC4J,mBAAmB,EAAEqB,GAAG,CAACC,iBAAiB,EAAE/K,iBAAiB,CAAC;IACzE;IAEA,IAAI8K,GAAG,CAACzB,eAAe,EAAE;MACvB,IAAIA,eAAe,KAAKC,uBAAuB,EAAE;QAC/CD,eAAe,GAAG7I,KAAK,CAAC6I,eAAe,CAAC;MAC1C;MAEAxJ,QAAQ,CAACwJ,eAAe,EAAEyB,GAAG,CAACzB,eAAe,EAAErJ,iBAAiB,CAAC;IACnE;IAEA;IACA,IAAIkJ,YAAY,EAAE;MAChB5B,YAAY,CAAC,OAAO,CAAC,GAAG,IAAI;IAC9B;IAEA;IACA,IAAImB,cAAc,EAAE;MAClB5I,QAAQ,CAACyH,YAAY,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;IAClD;IAEA;IACA,IAAIA,YAAY,CAAC8D,KAAK,EAAE;MACtBvL,QAAQ,CAACyH,YAAY,EAAE,CAAC,OAAO,CAAC,CAAC;MACjC,OAAOW,WAAW,CAACoD,KAAK;IAC1B;IAEA,IAAIP,GAAG,CAACQ,oBAAoB,EAAE;MAC5B,IAAI,OAAOR,GAAG,CAACQ,oBAAoB,CAACvH,UAAU,KAAK,UAAU,EAAE;QAC7D,MAAM9E,eAAe,CACnB,6EAA6E,CAC9E;MACH;MAEA,IAAI,OAAO6L,GAAG,CAACQ,oBAAoB,CAACtH,eAAe,KAAK,UAAU,EAAE;QAClE,MAAM/E,eAAe,CACnB,kFAAkF,CACnF;MACH;MAEA;MACA0H,kBAAkB,GAAGmE,GAAG,CAACQ,oBAAoB;MAE7C;MACA1E,SAAS,GAAGD,kBAAkB,CAAC5C,UAAU,CAAC,EAAE,CAAC;IAC/C,CAAC,MAAM;MACL;MACA,IAAI4C,kBAAkB,KAAK1G,SAAS,EAAE;QACpC0G,kBAAkB,GAAGrD,yBAAyB,CAC5CC,YAAY,EACZiC,aAAa,CACd;MACH;MAEA;MACA,IAAImB,kBAAkB,KAAK,IAAI,IAAI,OAAOC,SAAS,KAAK,QAAQ,EAAE;QAChEA,SAAS,GAAGD,kBAAkB,CAAC5C,UAAU,CAAC,EAAE,CAAC;MAC/C;IACF;IAEA;IACA;IACA,IAAIxH,MAAM,EAAE;MACVA,MAAM,CAACuO,GAAG,CAAC;IACb;IAEAN,MAAM,GAAGM,GAAG;GACb;EAED;;AAEgB;EAChB,MAAMS,YAAY,GAAG1L,QAAQ,CAAC,EAAE,EAAE,CAChC,GAAGwB,KAAQ,EACX,GAAGC,UAAe,EAClB,GAAGC,aAAkB,CACtB,CAAC;EACF,MAAMiK,eAAe,GAAG3L,QAAQ,CAAC,EAAE,EAAE,CACnC,GAAG2B,QAAW,EACd,GAAGC,gBAAqB,CACzB,CAAC;EAEF;;;;;AAKG;EACH,MAAMgK,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAatL,OAAgB;IACrD,IAAIuL,MAAM,GAAGpF,aAAa,CAACnG,OAAO,CAAC;IAEnC;IACA;IACA,IAAI,CAACuL,MAAM,IAAI,CAACA,MAAM,CAACC,OAAO,EAAE;MAC9BD,MAAM,GAAG;QACPE,YAAY,EAAE9B,SAAS;QACvB6B,OAAO,EAAE;OACV;IACH;IAEA,MAAMA,OAAO,GAAG5N,iBAAiB,CAACoC,OAAO,CAACwL,OAAO,CAAC;IAClD,MAAME,aAAa,GAAG9N,iBAAiB,CAAC2N,MAAM,CAACC,OAAO,CAAC;IAEvD,IAAI,CAAC3B,kBAAkB,CAAC7J,OAAO,CAACyL,YAAY,CAAC,EAAE;MAC7C,OAAO,KAAK;IACd;IAEA,IAAIzL,OAAO,CAACyL,YAAY,KAAKhC,aAAa,EAAE;MAC1C;MACA;MACA;MACA,IAAI8B,MAAM,CAACE,YAAY,KAAK/B,cAAc,EAAE;QAC1C,OAAO8B,OAAO,KAAK,KAAK;MAC1B;MAEA;MACA;MACA;MACA,IAAID,MAAM,CAACE,YAAY,KAAKjC,gBAAgB,EAAE;QAC5C,OACEgC,OAAO,KAAK,KAAK,KAChBE,aAAa,KAAK,gBAAgB,IACjC3B,8BAA8B,CAAC2B,aAAa,CAAC,CAAC;MAEpD;MAEA;MACA;MACA,OAAOC,OAAO,CAACP,YAAY,CAACI,OAAO,CAAC,CAAC;IACvC;IAEA,IAAIxL,OAAO,CAACyL,YAAY,KAAKjC,gBAAgB,EAAE;MAC7C;MACA;MACA;MACA,IAAI+B,MAAM,CAACE,YAAY,KAAK/B,cAAc,EAAE;QAC1C,OAAO8B,OAAO,KAAK,MAAM;MAC3B;MAEA;MACA;MACA,IAAID,MAAM,CAACE,YAAY,KAAKhC,aAAa,EAAE;QACzC,OAAO+B,OAAO,KAAK,MAAM,IAAIxB,uBAAuB,CAAC0B,aAAa,CAAC;MACrE;MAEA;MACA;MACA,OAAOC,OAAO,CAACN,eAAe,CAACG,OAAO,CAAC,CAAC;IAC1C;IAEA,IAAIxL,OAAO,CAACyL,YAAY,KAAK/B,cAAc,EAAE;MAC3C;MACA;MACA;MACA,IACE6B,MAAM,CAACE,YAAY,KAAKhC,aAAa,IACrC,CAACO,uBAAuB,CAAC0B,aAAa,CAAC,EACvC;QACA,OAAO,KAAK;MACd;MAEA,IACEH,MAAM,CAACE,YAAY,KAAKjC,gBAAgB,IACxC,CAACO,8BAA8B,CAAC2B,aAAa,CAAC,EAC9C;QACA,OAAO,KAAK;MACd;MAEA;MACA;MACA,OACE,CAACL,eAAe,CAACG,OAAO,CAAC,KACxBvB,4BAA4B,CAACuB,OAAO,CAAC,IAAI,CAACJ,YAAY,CAACI,OAAO,CAAC,CAAC;IAErE;IAEA;IACA,IACEtB,iBAAiB,KAAK,uBAAuB,IAC7CL,kBAAkB,CAAC7J,OAAO,CAACyL,YAAY,CAAC,EACxC;MACA,OAAO,IAAI;IACb;IAEA;IACA;IACA;IACA;IACA,OAAO,KAAK;GACb;EAED;;;;AAIG;EACH,MAAMG,YAAY,GAAG,SAAfA,YAAYA,CAAaC,IAAU;IACvCrO,SAAS,CAACqH,SAAS,CAACG,OAAO,EAAE;MAAEhF,OAAO,EAAE6L;IAAM,EAAC;IAE/C,IAAI;MACF;MACA1F,aAAa,CAAC0F,IAAI,CAAC,CAACC,WAAW,CAACD,IAAI,CAAC;KACtC,CAAC,OAAO9H,CAAC,EAAE;MACViC,MAAM,CAAC6F,IAAI,CAAC;IACd;GACD;EAED;;;;;AAKG;EACH,MAAME,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAaC,IAAY,EAAEhM,OAAgB;IAC/D,IAAI;MACFxC,SAAS,CAACqH,SAAS,CAACG,OAAO,EAAE;QAC3BzC,SAAS,EAAEvC,OAAO,CAACiM,gBAAgB,CAACD,IAAI,CAAC;QACzCE,IAAI,EAAElM;MACP,EAAC;KACH,CAAC,OAAO+D,CAAC,EAAE;MACVvG,SAAS,CAACqH,SAAS,CAACG,OAAO,EAAE;QAC3BzC,SAAS,EAAE,IAAI;QACf2J,IAAI,EAAElM;MACP,EAAC;IACJ;IAEAA,OAAO,CAACmM,eAAe,CAACH,IAAI,CAAC;IAE7B;IACA,IAAIA,IAAI,KAAK,IAAI,EAAE;MACjB,IAAIvD,UAAU,IAAIC,mBAAmB,EAAE;QACrC,IAAI;UACFkD,YAAY,CAAC5L,OAAO,CAAC;QACvB,CAAC,CAAC,OAAO+D,CAAC,EAAE;MACd,CAAC,MAAM;QACL,IAAI;UACF/D,OAAO,CAACoM,YAAY,CAACJ,IAAI,EAAE,EAAE,CAAC;QAChC,CAAC,CAAC,OAAOjI,CAAC,EAAE;MACd;IACF;GACD;EAED;;;;;AAKG;EACH,MAAMsI,aAAa,GAAG,SAAhBA,aAAaA,CAAaC,KAAa;IAC3C;IACA,IAAIC,GAAG,GAAG,IAAI;IACd,IAAIC,iBAAiB,GAAG,IAAI;IAE5B,IAAIhE,UAAU,EAAE;MACd8D,KAAK,GAAG,mBAAmB,GAAGA,KAAK;IACrC,CAAC,MAAM;MACL;MACA,MAAMG,OAAO,GAAGxO,WAAW,CAACqO,KAAK,EAAE,aAAa,CAAC;MACjDE,iBAAiB,GAAGC,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC;IAC3C;IAEA,IACEvC,iBAAiB,KAAK,uBAAuB,IAC7CP,SAAS,KAAKD,cAAc,EAC5B;MACA;MACA4C,KAAK,GACH,gEAAgE,GAChEA,KAAK,GACL,gBAAgB;IACpB;IAEA,MAAMI,YAAY,GAAGlG,kBAAkB,GACnCA,kBAAkB,CAAC5C,UAAU,CAAC0I,KAAK,CAAC,GACpCA,KAAK;IACT;;;AAGG;IACH,IAAI3C,SAAS,KAAKD,cAAc,EAAE;MAChC,IAAI;QACF6C,GAAG,GAAG,IAAI1G,SAAS,EAAE,CAAC8G,eAAe,CAACD,YAAY,EAAExC,iBAAiB,CAAC;MACxE,CAAC,CAAC,OAAOnG,CAAC,EAAE;IACd;IAEA;IACA,IAAI,CAACwI,GAAG,IAAI,CAACA,GAAG,CAACK,eAAe,EAAE;MAChCL,GAAG,GAAG7F,cAAc,CAACmG,cAAc,CAAClD,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC;MAChE,IAAI;QACF4C,GAAG,CAACK,eAAe,CAACE,SAAS,GAAGlD,cAAc,GAC1CnD,SAAS,GACTiG,YAAY;OACjB,CAAC,OAAO3I,CAAC,EAAE;QACV;MAAA;IAEJ;IAEA,MAAMgJ,IAAI,GAAGR,GAAG,CAACQ,IAAI,IAAIR,GAAG,CAACK,eAAe;IAE5C,IAAIN,KAAK,IAAIE,iBAAiB,EAAE;MAC9BO,IAAI,CAACC,YAAY,CACfnK,QAAQ,CAACoK,cAAc,CAACT,iBAAiB,CAAC,EAC1CO,IAAI,CAACG,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,CAC3B;IACH;IAEA;IACA,IAAIvD,SAAS,KAAKD,cAAc,EAAE;MAChC,OAAO7C,oBAAoB,CAACsG,IAAI,CAC9BZ,GAAG,EACHjE,cAAc,GAAG,MAAM,GAAG,MAAM,CACjC,CAAC,CAAC,CAAC;IACN;IAEA,OAAOA,cAAc,GAAGiE,GAAG,CAACK,eAAe,GAAGG,IAAI;GACnD;EAED;;;;;AAKG;EACH,MAAMK,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAatI,IAAU;IAC9C,OAAO6B,kBAAkB,CAACwG,IAAI,CAC5BrI,IAAI,CAACyB,aAAa,IAAIzB,IAAI,EAC1BA,IAAI;IACJ;IACAW,UAAU,CAAC4H,YAAY,GACrB5H,UAAU,CAAC6H,YAAY,GACvB7H,UAAU,CAAC8H,SAAS,GACpB9H,UAAU,CAAC+H,2BAA2B,GACtC/H,UAAU,CAACgI,kBAAkB,EAC/B,IAAI,CACL;GACF;EAED;;;;;AAKG;EACH,MAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAa1N,OAAgB;IAC7C,OACEA,OAAO,YAAY4F,eAAe,KACjC,OAAO5F,OAAO,CAAC2N,QAAQ,KAAK,QAAQ,IACnC,OAAO3N,OAAO,CAAC4N,WAAW,KAAK,QAAQ,IACvC,OAAO5N,OAAO,CAAC8L,WAAW,KAAK,UAAU,IACzC,EAAE9L,OAAO,CAAC6N,UAAU,YAAYnI,YAAY,CAAC,IAC7C,OAAO1F,OAAO,CAACmM,eAAe,KAAK,UAAU,IAC7C,OAAOnM,OAAO,CAACoM,YAAY,KAAK,UAAU,IAC1C,OAAOpM,OAAO,CAACyL,YAAY,KAAK,QAAQ,IACxC,OAAOzL,OAAO,CAACgN,YAAY,KAAK,UAAU,IAC1C,OAAOhN,OAAO,CAAC8N,aAAa,KAAK,UAAU,CAAC;GAEjD;EAED;;;;;AAKG;EACH,MAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAatN,KAAc;IACtC,OAAO,OAAO+E,IAAI,KAAK,UAAU,IAAI/E,KAAK,YAAY+E,IAAI;GAC3D;EAED,SAASwI,aAAaA,CAOpBjH,KAAU,EAAEkH,WAA6B,EAAEC,IAAsB;IACjEnR,YAAY,CAACgK,KAAK,EAAGoH,IAAI,IAAI;MAC3BA,IAAI,CAAChB,IAAI,CAACtI,SAAS,EAAEoJ,WAAW,EAAEC,IAAI,EAAE7D,MAAM,CAAC;IACjD,CAAC,CAAC;EACJ;EAEA;;;;;;;;AAQG;EACH,MAAM+D,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAaH,WAAgB;IAClD,IAAI3H,OAAO,GAAG,IAAI;IAElB;IACA0H,aAAa,CAACjH,KAAK,CAACxC,sBAAsB,EAAE0J,WAAW,EAAE,IAAI,CAAC;IAE9D;IACA,IAAIP,YAAY,CAACO,WAAW,CAAC,EAAE;MAC7BrC,YAAY,CAACqC,WAAW,CAAC;MACzB,OAAO,IAAI;IACb;IAEA;IACA,MAAMzC,OAAO,GAAG3L,iBAAiB,CAACoO,WAAW,CAACN,QAAQ,CAAC;IAEvD;IACAK,aAAa,CAACjH,KAAK,CAACrC,mBAAmB,EAAEuJ,WAAW,EAAE;MACpDzC,OAAO;MACP6C,WAAW,EAAElH;IACd,EAAC;IAEF;IACA,IACEkB,YAAY,IACZ4F,WAAW,CAACH,aAAa,EAAE,IAC3B,CAACC,OAAO,CAACE,WAAW,CAACK,iBAAiB,CAAC,IACvC3P,UAAU,CAAC,UAAU,EAAEsP,WAAW,CAACnB,SAAS,CAAC,IAC7CnO,UAAU,CAAC,UAAU,EAAEsP,WAAW,CAACL,WAAW,CAAC,EAC/C;MACAhC,YAAY,CAACqC,WAAW,CAAC;MACzB,OAAO,IAAI;IACb;IAEA;IACA,IAAIA,WAAW,CAAChJ,QAAQ,KAAK3C,SAAS,CAACK,sBAAsB,EAAE;MAC7DiJ,YAAY,CAACqC,WAAW,CAAC;MACzB,OAAO,IAAI;IACb;IAEA;IACA,IACE5F,YAAY,IACZ4F,WAAW,CAAChJ,QAAQ,KAAK3C,SAAS,CAACM,OAAO,IAC1CjE,UAAU,CAAC,SAAS,EAAEsP,WAAW,CAACC,IAAI,CAAC,EACvC;MACAtC,YAAY,CAACqC,WAAW,CAAC;MACzB,OAAO,IAAI;IACb;IAEA;IACA,IAAI,CAAC9G,YAAY,CAACqE,OAAO,CAAC,IAAI1D,WAAW,CAAC0D,OAAO,CAAC,EAAE;MAClD;MACA,IAAI,CAAC1D,WAAW,CAAC0D,OAAO,CAAC,IAAI+C,qBAAqB,CAAC/C,OAAO,CAAC,EAAE;QAC3D,IACEjE,uBAAuB,CAACC,YAAY,YAAY5I,MAAM,IACtDD,UAAU,CAAC4I,uBAAuB,CAACC,YAAY,EAAEgE,OAAO,CAAC,EACzD;UACA,OAAO,KAAK;QACd;QAEA,IACEjE,uBAAuB,CAACC,YAAY,YAAYiD,QAAQ,IACxDlD,uBAAuB,CAACC,YAAY,CAACgE,OAAO,CAAC,EAC7C;UACA,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAIzC,YAAY,IAAI,CAACG,eAAe,CAACsC,OAAO,CAAC,EAAE;QAC7C,MAAMgD,UAAU,GAAGrI,aAAa,CAAC8H,WAAW,CAAC,IAAIA,WAAW,CAACO,UAAU;QACvE,MAAMtB,UAAU,GAAGhH,aAAa,CAAC+H,WAAW,CAAC,IAAIA,WAAW,CAACf,UAAU;QAEvE,IAAIA,UAAU,IAAIsB,UAAU,EAAE;UAC5B,MAAMC,UAAU,GAAGvB,UAAU,CAAC5N,MAAM;UAEpC,KAAK,IAAIoP,CAAC,GAAGD,UAAU,GAAG,CAAC,EAAEC,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;YACxC,MAAMC,UAAU,GAAG5I,SAAS,CAACmH,UAAU,CAACwB,CAAC,CAAC,EAAE,IAAI,CAAC;YACjDC,UAAU,CAACC,cAAc,GAAG,CAACX,WAAW,CAACW,cAAc,IAAI,CAAC,IAAI,CAAC;YACjEJ,UAAU,CAACxB,YAAY,CAAC2B,UAAU,EAAE1I,cAAc,CAACgI,WAAW,CAAC,CAAC;UAClE;QACF;MACF;MAEArC,YAAY,CAACqC,WAAW,CAAC;MACzB,OAAO,IAAI;IACb;IAEA;IACA,IAAIA,WAAW,YAAY/I,OAAO,IAAI,CAACoG,oBAAoB,CAAC2C,WAAW,CAAC,EAAE;MACxErC,YAAY,CAACqC,WAAW,CAAC;MACzB,OAAO,IAAI;IACb;IAEA;IACA,IACE,CAACzC,OAAO,KAAK,UAAU,IACrBA,OAAO,KAAK,SAAS,IACrBA,OAAO,KAAK,UAAU,KACxB7M,UAAU,CAAC,6BAA6B,EAAEsP,WAAW,CAACnB,SAAS,CAAC,EAChE;MACAlB,YAAY,CAACqC,WAAW,CAAC;MACzB,OAAO,IAAI;IACb;IAEA;IACA,IAAI7F,kBAAkB,IAAI6F,WAAW,CAAChJ,QAAQ,KAAK3C,SAAS,CAACf,IAAI,EAAE;MACjE;MACA+E,OAAO,GAAG2H,WAAW,CAACL,WAAW;MAEjC7Q,YAAY,CAAC,CAAC6E,aAAa,EAAEC,QAAQ,EAAEC,WAAW,CAAC,EAAG+M,IAAI,IAAI;QAC5DvI,OAAO,GAAGnI,aAAa,CAACmI,OAAO,EAAEuI,IAAI,EAAE,GAAG,CAAC;MAC7C,CAAC,CAAC;MAEF,IAAIZ,WAAW,CAACL,WAAW,KAAKtH,OAAO,EAAE;QACvC9I,SAAS,CAACqH,SAAS,CAACG,OAAO,EAAE;UAAEhF,OAAO,EAAEiO,WAAW,CAAClI,SAAS;QAAE,CAAE,CAAC;QAClEkI,WAAW,CAACL,WAAW,GAAGtH,OAAO;MACnC;IACF;IAEA;IACA0H,aAAa,CAACjH,KAAK,CAAC3C,qBAAqB,EAAE6J,WAAW,EAAE,IAAI,CAAC;IAE7D,OAAO,KAAK;GACb;EAED;;;;;;;AAOG;EACH;EACA,MAAMa,iBAAiB,GAAG,SAApBA,iBAAiBA,CACrBC,KAAa,EACbC,MAAc,EACdvO,KAAa;IAEb;IACA,IACEmI,YAAY,KACXoG,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,MAAM,CAAC,KACrCvO,KAAK,IAAIoC,QAAQ,IAAIpC,KAAK,IAAI6J,WAAW,CAAC,EAC3C;MACA,OAAO,KAAK;IACd;IAEA;;;AAG8D;IAC9D,IACErC,eAAe,IACf,CAACF,WAAW,CAACiH,MAAM,CAAC,IACpBrQ,UAAU,CAACoD,SAAS,EAAEiN,MAAM,CAAC,EAC7B,CAED,KAAM,IAAIhH,eAAe,IAAIrJ,UAAU,CAACqD,SAAS,EAAEgN,MAAM,CAAC,EAAE,CAG5D,KAAM,IAAI,CAAC3H,YAAY,CAAC2H,MAAM,CAAC,IAAIjH,WAAW,CAACiH,MAAM,CAAC,EAAE;MACvD;MACE;MACA;MACA;MACCT,qBAAqB,CAACQ,KAAK,CAAC,KACzBxH,uBAAuB,CAACC,YAAY,YAAY5I,MAAM,IACtDD,UAAU,CAAC4I,uBAAuB,CAACC,YAAY,EAAEuH,KAAK,CAAC,IACtDxH,uBAAuB,CAACC,YAAY,YAAYiD,QAAQ,IACvDlD,uBAAuB,CAACC,YAAY,CAACuH,KAAK,CAAE,CAAC,KAC/CxH,uBAAuB,CAACK,kBAAkB,YAAYhJ,MAAM,IAC5DD,UAAU,CAAC4I,uBAAuB,CAACK,kBAAkB,EAAEoH,MAAM,CAAC,IAC7DzH,uBAAuB,CAACK,kBAAkB,YAAY6C,QAAQ,IAC7DlD,uBAAuB,CAACK,kBAAkB,CAACoH,MAAM,CAAE,CAAC;MAC1D;MACA;MACCA,MAAM,KAAK,IAAI,IACdzH,uBAAuB,CAACM,8BAA8B,KACpDN,uBAAuB,CAACC,YAAY,YAAY5I,MAAM,IACtDD,UAAU,CAAC4I,uBAAuB,CAACC,YAAY,EAAE/G,KAAK,CAAC,IACtD8G,uBAAuB,CAACC,YAAY,YAAYiD,QAAQ,IACvDlD,uBAAuB,CAACC,YAAY,CAAC/G,KAAK,CAAE,CAAE,EACpD,CAGD,KAAM;QACL,OAAO,KAAK;MACd;MACA;IACF,CAAC,MAAM,IAAI6I,mBAAmB,CAAC0F,MAAM,CAAC,EAAE,CAIvC,KAAM,IACLrQ,UAAU,CAACuI,gBAAc,EAAE/I,aAAa,CAACsC,KAAK,EAAE0B,eAAe,EAAE,EAAE,CAAC,CAAC,EACrE,CAID,KAAM,IACL,CAAC6M,MAAM,KAAK,KAAK,IAAIA,MAAM,KAAK,YAAY,IAAIA,MAAM,KAAK,MAAM,KACjED,KAAK,KAAK,QAAQ,IAClB1Q,aAAa,CAACoC,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,IACnC2I,aAAa,CAAC2F,KAAK,CAAC,EACpB,CAKD,KAAM,IACL7G,uBAAuB,IACvB,CAACvJ,UAAU,CAACuD,iBAAiB,EAAE/D,aAAa,CAACsC,KAAK,EAAE0B,eAAe,EAAE,EAAE,CAAC,CAAC,EACzE,CAGD,KAAM,IAAI1B,KAAK,EAAE;MAChB,OAAO,KAAK;IACd,CAAC,MAAM;IAKP,OAAO,IAAI;GACZ;EAED;;;;;;;AAOG;EACH,MAAM8N,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAa/C,OAAe;IACrD,OAAOA,OAAO,KAAK,gBAAgB,IAAIvN,WAAW,CAACuN,OAAO,EAAEnJ,cAAc,CAAC;GAC5E;EAED;;;;;;;;;AASG;EACH,MAAM4M,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAahB,WAAoB;IACxD;IACAD,aAAa,CAACjH,KAAK,CAACzC,wBAAwB,EAAE2J,WAAW,EAAE,IAAI,CAAC;IAEhE,MAAM;MAAEJ;IAAY,IAAGI,WAAW;IAElC;IACA,IAAI,CAACJ,UAAU,IAAIH,YAAY,CAACO,WAAW,CAAC,EAAE;MAC5C;IACF;IAEA,MAAMiB,SAAS,GAAG;MAChBC,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,IAAI;MACdC,iBAAiB,EAAEjI,YAAY;MAC/BkI,aAAa,EAAEzP;KAChB;IACD,IAAIC,CAAC,GAAG8N,UAAU,CAACvO,MAAM;IAEzB;IACA,OAAOS,CAAC,EAAE,EAAE;MACV,MAAMyP,IAAI,GAAG3B,UAAU,CAAC9N,CAAC,CAAC;MAC1B,MAAM;QAAEiM,IAAI;QAAEP,YAAY;QAAEhL,KAAK,EAAE2O;MAAS,CAAE,GAAGI,IAAI;MACrD,MAAMR,MAAM,GAAGnP,iBAAiB,CAACmM,IAAI,CAAC;MAEtC,MAAMyD,SAAS,GAAGL,SAAS;MAC3B,IAAI3O,KAAK,GAAGuL,IAAI,KAAK,OAAO,GAAGyD,SAAS,GAAGlR,UAAU,CAACkR,SAAS,CAAC;MAEhE;MACAP,SAAS,CAACC,QAAQ,GAAGH,MAAM;MAC3BE,SAAS,CAACE,SAAS,GAAG3O,KAAK;MAC3ByO,SAAS,CAACG,QAAQ,GAAG,IAAI;MACzBH,SAAS,CAACK,aAAa,GAAGzP,SAAS,CAAC;MACpCkO,aAAa,CAACjH,KAAK,CAACtC,qBAAqB,EAAEwJ,WAAW,EAAEiB,SAAS,CAAC;MAClEzO,KAAK,GAAGyO,SAAS,CAACE,SAAS;MAE3B;;AAEG;MACH,IAAIvG,oBAAoB,KAAKmG,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,MAAM,CAAC,EAAE;QAClE;QACAjD,gBAAgB,CAACC,IAAI,EAAEiC,WAAW,CAAC;QAEnC;QACAxN,KAAK,GAAGqI,2BAA2B,GAAGrI,KAAK;MAC7C;MAEA;MACA,IAAI4H,YAAY,IAAI1J,UAAU,CAAC,+BAA+B,EAAE8B,KAAK,CAAC,EAAE;QACtEsL,gBAAgB,CAACC,IAAI,EAAEiC,WAAW,CAAC;QACnC;MACF;MAEA;MACA,IAAIiB,SAAS,CAACK,aAAa,EAAE;QAC3B;MACF;MAEA;MACA,IAAI,CAACL,SAAS,CAACG,QAAQ,EAAE;QACvBtD,gBAAgB,CAACC,IAAI,EAAEiC,WAAW,CAAC;QACnC;MACF;MAEA;MACA,IAAI,CAAC9F,wBAAwB,IAAIxJ,UAAU,CAAC,MAAM,EAAE8B,KAAK,CAAC,EAAE;QAC1DsL,gBAAgB,CAACC,IAAI,EAAEiC,WAAW,CAAC;QACnC;MACF;MAEA;MACA,IAAI7F,kBAAkB,EAAE;QACtBrL,YAAY,CAAC,CAAC6E,aAAa,EAAEC,QAAQ,EAAEC,WAAW,CAAC,EAAG+M,IAAI,IAAI;UAC5DpO,KAAK,GAAGtC,aAAa,CAACsC,KAAK,EAAEoO,IAAI,EAAE,GAAG,CAAC;QACzC,CAAC,CAAC;MACJ;MAEA;MACA,MAAME,KAAK,GAAGlP,iBAAiB,CAACoO,WAAW,CAACN,QAAQ,CAAC;MACrD,IAAI,CAACmB,iBAAiB,CAACC,KAAK,EAAEC,MAAM,EAAEvO,KAAK,CAAC,EAAE;QAC5CsL,gBAAgB,CAACC,IAAI,EAAEiC,WAAW,CAAC;QACnC;MACF;MAEA;MACA,IACEzH,kBAAkB,IAClB,OAAOpD,YAAY,KAAK,QAAQ,IAChC,OAAOA,YAAY,CAACsM,gBAAgB,KAAK,UAAU,EACnD;QACA,IAAIjE,YAAY,EAAE,CAEjB,KAAM;UACL,QAAQrI,YAAY,CAACsM,gBAAgB,CAACX,KAAK,EAAEC,MAAM,CAAC;YAClD,KAAK,aAAa;cAAE;gBAClBvO,KAAK,GAAG+F,kBAAkB,CAAC5C,UAAU,CAACnD,KAAK,CAAC;gBAC5C;cACF;YAEA,KAAK,kBAAkB;cAAE;gBACvBA,KAAK,GAAG+F,kBAAkB,CAAC3C,eAAe,CAACpD,KAAK,CAAC;gBACjD;cACF;UAKF;QACF;MACF;MAEA;MACA,IAAIA,KAAK,KAAKgP,SAAS,EAAE;QACvB,IAAI;UACF,IAAIhE,YAAY,EAAE;YAChBwC,WAAW,CAAC0B,cAAc,CAAClE,YAAY,EAAEO,IAAI,EAAEvL,KAAK,CAAC;UACvD,CAAC,MAAM;YACL;YACAwN,WAAW,CAAC7B,YAAY,CAACJ,IAAI,EAAEvL,KAAK,CAAC;UACvC;UAEA,IAAIiN,YAAY,CAACO,WAAW,CAAC,EAAE;YAC7BrC,YAAY,CAACqC,WAAW,CAAC;UAC3B,CAAC,MAAM;YACL3Q,QAAQ,CAACuH,SAAS,CAACG,OAAO,CAAC;UAC7B;SACD,CAAC,OAAOjB,CAAC,EAAE;UACVgI,gBAAgB,CAACC,IAAI,EAAEiC,WAAW,CAAC;QACrC;MACF;IACF;IAEA;IACAD,aAAa,CAACjH,KAAK,CAAC5C,uBAAuB,EAAE8J,WAAW,EAAE,IAAI,CAAC;GAChE;EAED;;;;AAIG;EACH,MAAM2B,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAaC,QAA0B;IAC7D,IAAIC,UAAU,GAAG,IAAI;IACrB,MAAMC,cAAc,GAAG3C,mBAAmB,CAACyC,QAAQ,CAAC;IAEpD;IACA7B,aAAa,CAACjH,KAAK,CAACvC,uBAAuB,EAAEqL,QAAQ,EAAE,IAAI,CAAC;IAE5D,OAAQC,UAAU,GAAGC,cAAc,CAACC,QAAQ,EAAE,EAAG;MAC/C;MACAhC,aAAa,CAACjH,KAAK,CAACpC,sBAAsB,EAAEmL,UAAU,EAAE,IAAI,CAAC;MAE7D;MACA1B,iBAAiB,CAAC0B,UAAU,CAAC;MAE7B;MACAb,mBAAmB,CAACa,UAAU,CAAC;MAE/B;MACA,IAAIA,UAAU,CAACxJ,OAAO,YAAYhB,gBAAgB,EAAE;QAClDsK,kBAAkB,CAACE,UAAU,CAACxJ,OAAO,CAAC;MACxC;IACF;IAEA;IACA0H,aAAa,CAACjH,KAAK,CAAC1C,sBAAsB,EAAEwL,QAAQ,EAAE,IAAI,CAAC;GAC5D;EAED;EACAhL,SAAS,CAACoL,QAAQ,GAAG,UAAU3D,KAAK,EAAU;IAAA,IAAR3B,GAAG,GAAAtL,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAS,SAAA,GAAAT,SAAA,MAAG,EAAE;IAC5C,IAAI0N,IAAI,GAAG,IAAI;IACf,IAAImD,YAAY,GAAG,IAAI;IACvB,IAAIjC,WAAW,GAAG,IAAI;IACtB,IAAIkC,UAAU,GAAG,IAAI;IACrB;;AAE6D;IAC7DvG,cAAc,GAAG,CAAC0C,KAAK;IACvB,IAAI1C,cAAc,EAAE;MAClB0C,KAAK,GAAG,OAAO;IACjB;IAEA;IACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAACyB,OAAO,CAACzB,KAAK,CAAC,EAAE;MAChD,IAAI,OAAOA,KAAK,CAACtO,QAAQ,KAAK,UAAU,EAAE;QACxCsO,KAAK,GAAGA,KAAK,CAACtO,QAAQ,EAAE;QACxB,IAAI,OAAOsO,KAAK,KAAK,QAAQ,EAAE;UAC7B,MAAMxN,eAAe,CAAC,iCAAiC,CAAC;QAC1D;MACF,CAAC,MAAM;QACL,MAAMA,eAAe,CAAC,4BAA4B,CAAC;MACrD;IACF;IAEA;IACA,IAAI,CAAC+F,SAAS,CAACM,WAAW,EAAE;MAC1B,OAAOmH,KAAK;IACd;IAEA;IACA,IAAI,CAAC/D,UAAU,EAAE;MACfmC,YAAY,CAACC,GAAG,CAAC;IACnB;IAEA;IACA9F,SAAS,CAACG,OAAO,GAAG,EAAE;IAEtB;IACA,IAAI,OAAOsH,KAAK,KAAK,QAAQ,EAAE;MAC7BtD,QAAQ,GAAG,KAAK;IAClB;IAEA,IAAIA,QAAQ,EAAE;MACZ;MACA,IAAKsD,KAAc,CAACqB,QAAQ,EAAE;QAC5B,MAAMnC,OAAO,GAAG3L,iBAAiB,CAAEyM,KAAc,CAACqB,QAAQ,CAAC;QAC3D,IAAI,CAACxG,YAAY,CAACqE,OAAO,CAAC,IAAI1D,WAAW,CAAC0D,OAAO,CAAC,EAAE;UAClD,MAAM1M,eAAe,CACnB,yDAAyD,CAC1D;QACH;MACF;IACF,CAAC,MAAM,IAAIwN,KAAK,YAAY9G,IAAI,EAAE;MAChC;AAC2C;MAC3CuH,IAAI,GAAGV,aAAa,CAAC,SAAS,CAAC;MAC/B6D,YAAY,GAAGnD,IAAI,CAACxG,aAAa,CAACO,UAAU,CAACwF,KAAK,EAAE,IAAI,CAAC;MACzD,IACE4D,YAAY,CAACjL,QAAQ,KAAK3C,SAAS,CAACtC,OAAO,IAC3CkQ,YAAY,CAACvC,QAAQ,KAAK,MAAM,EAChC;QACA;QACAZ,IAAI,GAAGmD,YAAY;MACrB,CAAC,MAAM,IAAIA,YAAY,CAACvC,QAAQ,KAAK,MAAM,EAAE;QAC3CZ,IAAI,GAAGmD,YAAY;MACrB,CAAC,MAAM;QACL;QACAnD,IAAI,CAACqD,WAAW,CAACF,YAAY,CAAC;MAChC;IACF,CAAC,MAAM;MACL;MACA,IACE,CAACzH,UAAU,IACX,CAACL,kBAAkB,IACnB,CAACE,cAAc;MACf;MACAgE,KAAK,CAAChO,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EACzB;QACA,OAAOkI,kBAAkB,IAAImC,mBAAmB,GAC5CnC,kBAAkB,CAAC5C,UAAU,CAAC0I,KAAK,CAAC,GACpCA,KAAK;MACX;MAEA;MACAS,IAAI,GAAGV,aAAa,CAACC,KAAK,CAAC;MAE3B;MACA,IAAI,CAACS,IAAI,EAAE;QACT,OAAOtE,UAAU,GAAG,IAAI,GAAGE,mBAAmB,GAAGlC,SAAS,GAAG,EAAE;MACjE;IACF;IAEA;IACA,IAAIsG,IAAI,IAAIvE,UAAU,EAAE;MACtBoD,YAAY,CAACmB,IAAI,CAACsD,UAAU,CAAC;IAC/B;IAEA;IACA,MAAMC,YAAY,GAAGlD,mBAAmB,CAACpE,QAAQ,GAAGsD,KAAK,GAAGS,IAAI,CAAC;IAEjE;IACA,OAAQkB,WAAW,GAAGqC,YAAY,CAACN,QAAQ,EAAE,EAAG;MAC9C;MACA5B,iBAAiB,CAACH,WAAW,CAAC;MAE9B;MACAgB,mBAAmB,CAAChB,WAAW,CAAC;MAEhC;MACA,IAAIA,WAAW,CAAC3H,OAAO,YAAYhB,gBAAgB,EAAE;QACnDsK,kBAAkB,CAAC3B,WAAW,CAAC3H,OAAO,CAAC;MACzC;IACF;IAEA;IACA,IAAI0C,QAAQ,EAAE;MACZ,OAAOsD,KAAK;IACd;IAEA;IACA,IAAI7D,UAAU,EAAE;MACd,IAAIC,mBAAmB,EAAE;QACvByH,UAAU,GAAGvJ,sBAAsB,CAACuG,IAAI,CAACJ,IAAI,CAACxG,aAAa,CAAC;QAE5D,OAAOwG,IAAI,CAACsD,UAAU,EAAE;UACtB;UACAF,UAAU,CAACC,WAAW,CAACrD,IAAI,CAACsD,UAAU,CAAC;QACzC;MACF,CAAC,MAAM;QACLF,UAAU,GAAGpD,IAAI;MACnB;MAEA,IAAI1F,YAAY,CAACkJ,UAAU,IAAIlJ,YAAY,CAACmJ,cAAc,EAAE;QAC1D;;;;;;AAME;QACFL,UAAU,GAAGrJ,UAAU,CAACqG,IAAI,CAAC/H,gBAAgB,EAAE+K,UAAU,EAAE,IAAI,CAAC;MAClE;MAEA,OAAOA,UAAU;IACnB;IAEA,IAAIM,cAAc,GAAGnI,cAAc,GAAGyE,IAAI,CAAC2D,SAAS,GAAG3D,IAAI,CAACD,SAAS;IAErE;IACA,IACExE,cAAc,IACdnB,YAAY,CAAC,UAAU,CAAC,IACxB4F,IAAI,CAACxG,aAAa,IAClBwG,IAAI,CAACxG,aAAa,CAACoK,OAAO,IAC1B5D,IAAI,CAACxG,aAAa,CAACoK,OAAO,CAAC3E,IAAI,IAC/BrN,UAAU,CAACyD,YAAwB,EAAE2K,IAAI,CAACxG,aAAa,CAACoK,OAAO,CAAC3E,IAAI,CAAC,EACrE;MACAyE,cAAc,GACZ,YAAY,GAAG1D,IAAI,CAACxG,aAAa,CAACoK,OAAO,CAAC3E,IAAI,GAAG,KAAK,GAAGyE,cAAc;IAC3E;IAEA;IACA,IAAIrI,kBAAkB,EAAE;MACtBrL,YAAY,CAAC,CAAC6E,aAAa,EAAEC,QAAQ,EAAEC,WAAW,CAAC,EAAG+M,IAAI,IAAI;QAC5D4B,cAAc,GAAGtS,aAAa,CAACsS,cAAc,EAAE5B,IAAI,EAAE,GAAG,CAAC;MAC3D,CAAC,CAAC;IACJ;IAEA,OAAOrI,kBAAkB,IAAImC,mBAAmB,GAC5CnC,kBAAkB,CAAC5C,UAAU,CAAC6M,cAAc,CAAC,GAC7CA,cAAc;GACnB;EAED5L,SAAS,CAAC+L,SAAS,GAAG,YAAkB;IAAA,IAARjG,GAAG,GAAAtL,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAS,SAAA,GAAAT,SAAA,MAAG,EAAE;IACtCqL,YAAY,CAACC,GAAG,CAAC;IACjBpC,UAAU,GAAG,IAAI;GAClB;EAED1D,SAAS,CAACgM,WAAW,GAAG;IACtBxG,MAAM,GAAG,IAAI;IACb9B,UAAU,GAAG,KAAK;GACnB;EAED1D,SAAS,CAACiM,gBAAgB,GAAG,UAAUC,GAAG,EAAEvB,IAAI,EAAE/O,KAAK;IACrD;IACA,IAAI,CAAC4J,MAAM,EAAE;MACXK,YAAY,CAAC,EAAE,CAAC;IAClB;IAEA,MAAMqE,KAAK,GAAGlP,iBAAiB,CAACkR,GAAG,CAAC;IACpC,MAAM/B,MAAM,GAAGnP,iBAAiB,CAAC2P,IAAI,CAAC;IACtC,OAAOV,iBAAiB,CAACC,KAAK,EAAEC,MAAM,EAAEvO,KAAK,CAAC;GAC/C;EAEDoE,SAAS,CAACmM,OAAO,GAAG,UAAUC,UAAU,EAAEC,YAAY;IACpD,IAAI,OAAOA,YAAY,KAAK,UAAU,EAAE;MACtC;IACF;IAEA1T,SAAS,CAACuJ,KAAK,CAACkK,UAAU,CAAC,EAAEC,YAAY,CAAC;GAC3C;EAEDrM,SAAS,CAACsM,UAAU,GAAG,UAAUF,UAAU,EAAEC,YAAY;IACvD,IAAIA,YAAY,KAAKpR,SAAS,EAAE;MAC9B,MAAMK,KAAK,GAAG/C,gBAAgB,CAAC2J,KAAK,CAACkK,UAAU,CAAC,EAAEC,YAAY,CAAC;MAE/D,OAAO/Q,KAAK,KAAK,CAAC,CAAC,GACfL,SAAS,GACTpC,WAAW,CAACqJ,KAAK,CAACkK,UAAU,CAAC,EAAE9Q,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACjD;IAEA,OAAO7C,QAAQ,CAACyJ,KAAK,CAACkK,UAAU,CAAC,CAAC;GACnC;EAEDpM,SAAS,CAACuM,WAAW,GAAG,UAAUH,UAAU;IAC1ClK,KAAK,CAACkK,UAAU,CAAC,GAAG,EAAE;GACvB;EAEDpM,SAAS,CAACwM,cAAc,GAAG;IACzBtK,KAAK,GAAG7C,eAAe,EAAE;GAC1B;EAED,OAAOW,SAAS;AAClB;AAEA,IAAAyM,MAAA,GAAe1M,eAAe,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}