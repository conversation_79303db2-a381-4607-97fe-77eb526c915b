{"ast": null, "code": "import { createRouter, createWebHistory } from 'vue-router';\nimport { authAPI } from '../services/api';\nimport Home from '../views/Home.vue';\nimport ClubCulture from '../views/ClubCulture.vue';\nimport LearningResources from '../views/LearningResources.vue';\nimport PastActivities from '../views/PastActivities.vue';\nimport CyberSecurityNews from '../views/CyberSecurityNews.vue';\nimport Login from '../views/Login.vue';\nimport Register from '../views/Register.vue';\nimport Admin from '../views/Admin.vue';\nimport Playground from '../views/Playground.vue';\nimport Dashboard from '../views/Dashboard.vue';\nimport Test from '../views/Test.vue';\nconst routes = [{\n  path: '/',\n  name: 'Home',\n  component: Home\n}, {\n  path: '/club-culture',\n  name: 'ClubCulture',\n  component: ClubCulture\n}, {\n  path: '/learning-resources',\n  name: 'LearningResources',\n  component: LearningResources\n}, {\n  path: '/past-activities',\n  name: 'PastActivities',\n  component: PastActivities\n}, {\n  path: '/cyber-security-news',\n  name: 'CyberSecurityNews',\n  component: CyberSecurityNews\n}, {\n  path: '/login',\n  name: 'Login',\n  component: Login\n}, {\n  path: '/register',\n  name: 'Register',\n  component: Register\n}, {\n  path: '/admin',\n  name: 'Admin',\n  component: Admin,\n  meta: {\n    requiresAuth: true,\n    authorize: ['admin']\n  } // 需要认证且是管理员\n}, {\n  path: '/playground',\n  name: 'Playground',\n  component: Playground,\n  meta: {\n    requiresAuth: true\n  } // 需要认证\n}, {\n  path: '/dashboard',\n  name: 'Dashboard',\n  component: Dashboard,\n  meta: {\n    requiresAuth: true\n  } // 需要认证\n}, {\n  path: '/test',\n  name: 'Test',\n  component: Test\n}];\nconst router = createRouter({\n  history: createWebHistory(process.env.BASE_URL),\n  routes\n});\nrouter.beforeEach(async (to, from, next) => {\n  const publicPages = ['/login', '/register', '/', '/club-culture', '/learning-resources', '/past-activities', '/cyber-security-news', '/test'];\n  const authRequired = !publicPages.includes(to.path);\n  if (authRequired) {\n    try {\n      // 使用 API 服务检查认证状态\n      const response = await authAPI.checkAuth();\n      if (response.status === 200) {\n        // 认证成功，获取用户角色并检查权限\n        const userRole = response.data.user.role;\n        if (to.meta.authorize && !to.meta.authorize.includes(userRole)) {\n          return next('/'); // 无权限，重定向到首页\n        }\n      }\n    } catch (error) {\n      if (error.response) {\n        if (error.response.status === 401) {\n          // 未认证，重定向到登录页\n          return next('/login');\n        } else if (error.response.status === 403) {\n          // 未授权，重定向到首页\n          return next('/');\n        }\n      } else {\n        console.error('认证检查失败:', error);\n        // 网络错误或后端不可达，也重定向到登录页\n        return next('/login');\n      }\n    }\n  }\n  next(); // 继续导航\n});\nexport default router;", "map": {"version": 3, "names": ["createRouter", "createWebHistory", "authAPI", "Home", "ClubCulture", "LearningResources", "PastActivities", "CyberSecurityNews", "<PERSON><PERSON>", "Register", "Admin", "Playground", "Dashboard", "Test", "routes", "path", "name", "component", "meta", "requiresAuth", "authorize", "router", "history", "process", "env", "BASE_URL", "beforeEach", "to", "from", "next", "publicPages", "authRequired", "includes", "response", "checkAuth", "status", "userRole", "data", "user", "role", "error", "console"], "sources": ["C:/Users/<USER>/Desktop/cs/pacong_py/frontend_py/src/router/index.js"], "sourcesContent": ["import { createRouter, createWebHistory } from 'vue-router'\r\nimport { authAPI } from '../services/api'\r\nimport Home from '../views/Home.vue'\r\nimport ClubCulture from '../views/ClubCulture.vue'\r\nimport LearningResources from '../views/LearningResources.vue'\r\nimport PastActivities from '../views/PastActivities.vue'\r\nimport CyberSecurityNews from '../views/CyberSecurityNews.vue'\r\nimport Login from '../views/Login.vue'\r\nimport Register from '../views/Register.vue'\r\nimport Admin from '../views/Admin.vue'\r\nimport Playground from '../views/Playground.vue'\r\nimport Dashboard from '../views/Dashboard.vue'\r\nimport Test from '../views/Test.vue'\r\n\r\nconst routes = [\r\n  {\r\n    path: '/',\r\n    name: 'Home',\r\n    component: Home,\r\n  },\r\n  {\r\n    path: '/club-culture',\r\n    name: 'ClubCulture',\r\n    component: ClubCulture,\r\n  },\r\n  {\r\n    path: '/learning-resources',\r\n    name: 'LearningResources',\r\n    component: LearningResources,\r\n  },\r\n  {\r\n    path: '/past-activities',\r\n    name: 'PastActivities',\r\n    component: PastActivities,\r\n  },\r\n  {\r\n    path: '/cyber-security-news',\r\n    name: 'CyberSecurityNews',\r\n    component: CyberSecurityNews,\r\n  },\r\n  {\r\n    path: '/login',\r\n    name: 'Login',\r\n    component: Login,\r\n  },\r\n  {\r\n    path: '/register',\r\n    name: 'Register',\r\n    component: Register,\r\n  },\r\n  {\r\n    path: '/admin',\r\n    name: 'Admin',\r\n    component: Admin,\r\n    meta: { requiresAuth: true, authorize: ['admin'] }, // 需要认证且是管理员\r\n  },\r\n  {\r\n    path: '/playground',\r\n    name: 'Playground',\r\n    component: Playground,\r\n    meta: { requiresAuth: true }, // 需要认证\r\n  },\r\n  {\r\n    path: '/dashboard',\r\n    name: 'Dashboard',\r\n    component: Dashboard,\r\n    meta: { requiresAuth: true }, // 需要认证\r\n  },\r\n  {\r\n    path: '/test',\r\n    name: 'Test',\r\n    component: Test,\r\n  },\r\n];\r\n\r\nconst router = createRouter({\r\n  history: createWebHistory(process.env.BASE_URL),\r\n  routes,\r\n});\r\n\r\nrouter.beforeEach(async (to, from, next) => {\r\n  const publicPages = ['/login', '/register', '/', '/club-culture', '/learning-resources', '/past-activities', '/cyber-security-news', '/test']\r\n  const authRequired = !publicPages.includes(to.path)\r\n\r\n  if (authRequired) {\r\n    try {\r\n      // 使用 API 服务检查认证状态\r\n      const response = await authAPI.checkAuth()\r\n\r\n      if (response.status === 200) {\r\n        // 认证成功，获取用户角色并检查权限\r\n        const userRole = response.data.user.role\r\n\r\n        if (to.meta.authorize && !to.meta.authorize.includes(userRole)) {\r\n          return next('/') // 无权限，重定向到首页\r\n        }\r\n      }\r\n    } catch (error) {\r\n      if (error.response) {\r\n        if (error.response.status === 401) {\r\n          // 未认证，重定向到登录页\r\n          return next('/login')\r\n        } else if (error.response.status === 403) {\r\n          // 未授权，重定向到首页\r\n          return next('/')\r\n        }\r\n      } else {\r\n        console.error('认证检查失败:', error)\r\n        // 网络错误或后端不可达，也重定向到登录页\r\n        return next('/login')\r\n      }\r\n    }\r\n  }\r\n\r\n  next() // 继续导航\r\n})\r\n\r\nexport default router"], "mappings": "AAAA,SAASA,YAAY,EAAEC,gBAAgB,QAAQ,YAAY;AAC3D,SAASC,OAAO,QAAQ,iBAAiB;AACzC,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,IAAI,MAAM,mBAAmB;AAEpC,MAAMC,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,GAAG;EACTC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAEd;AACb,CAAC,EACD;EACEY,IAAI,EAAE,eAAe;EACrBC,IAAI,EAAE,aAAa;EACnBC,SAAS,EAAEb;AACb,CAAC,EACD;EACEW,IAAI,EAAE,qBAAqB;EAC3BC,IAAI,EAAE,mBAAmB;EACzBC,SAAS,EAAEZ;AACb,CAAC,EACD;EACEU,IAAI,EAAE,kBAAkB;EACxBC,IAAI,EAAE,gBAAgB;EACtBC,SAAS,EAAEX;AACb,CAAC,EACD;EACES,IAAI,EAAE,sBAAsB;EAC5BC,IAAI,EAAE,mBAAmB;EACzBC,SAAS,EAAEV;AACb,CAAC,EACD;EACEQ,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,OAAO;EACbC,SAAS,EAAET;AACb,CAAC,EACD;EACEO,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAER;AACb,CAAC,EACD;EACEM,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEP,KAAK;EAChBQ,IAAI,EAAE;IAAEC,YAAY,EAAE,IAAI;IAAEC,SAAS,EAAE,CAAC,OAAO;EAAE,CAAC,CAAE;AACtD,CAAC,EACD;EACEL,IAAI,EAAE,aAAa;EACnBC,IAAI,EAAE,YAAY;EAClBC,SAAS,EAAEN,UAAU;EACrBO,IAAI,EAAE;IAAEC,YAAY,EAAE;EAAK,CAAC,CAAE;AAChC,CAAC,EACD;EACEJ,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAEL,SAAS;EACpBM,IAAI,EAAE;IAAEC,YAAY,EAAE;EAAK,CAAC,CAAE;AAChC,CAAC,EACD;EACEJ,IAAI,EAAE,OAAO;EACbC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAEJ;AACb,CAAC,CACF;AAED,MAAMQ,MAAM,GAAGrB,YAAY,CAAC;EAC1BsB,OAAO,EAAErB,gBAAgB,CAACsB,OAAO,CAACC,GAAG,CAACC,QAAQ,CAAC;EAC/CX;AACF,CAAC,CAAC;AAEFO,MAAM,CAACK,UAAU,CAAC,OAAOC,EAAE,EAAEC,IAAI,EAAEC,IAAI,KAAK;EAC1C,MAAMC,WAAW,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,GAAG,EAAE,eAAe,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,sBAAsB,EAAE,OAAO,CAAC;EAC7I,MAAMC,YAAY,GAAG,CAACD,WAAW,CAACE,QAAQ,CAACL,EAAE,CAACZ,IAAI,CAAC;EAEnD,IAAIgB,YAAY,EAAE;IAChB,IAAI;MACF;MACA,MAAME,QAAQ,GAAG,MAAM/B,OAAO,CAACgC,SAAS,CAAC,CAAC;MAE1C,IAAID,QAAQ,CAACE,MAAM,KAAK,GAAG,EAAE;QAC3B;QACA,MAAMC,QAAQ,GAAGH,QAAQ,CAACI,IAAI,CAACC,IAAI,CAACC,IAAI;QAExC,IAAIZ,EAAE,CAACT,IAAI,CAACE,SAAS,IAAI,CAACO,EAAE,CAACT,IAAI,CAACE,SAAS,CAACY,QAAQ,CAACI,QAAQ,CAAC,EAAE;UAC9D,OAAOP,IAAI,CAAC,GAAG,CAAC,EAAC;QACnB;MACF;IACF,CAAC,CAAC,OAAOW,KAAK,EAAE;MACd,IAAIA,KAAK,CAACP,QAAQ,EAAE;QAClB,IAAIO,KAAK,CAACP,QAAQ,CAACE,MAAM,KAAK,GAAG,EAAE;UACjC;UACA,OAAON,IAAI,CAAC,QAAQ,CAAC;QACvB,CAAC,MAAM,IAAIW,KAAK,CAACP,QAAQ,CAACE,MAAM,KAAK,GAAG,EAAE;UACxC;UACA,OAAON,IAAI,CAAC,GAAG,CAAC;QAClB;MACF,CAAC,MAAM;QACLY,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;QAC/B;QACA,OAAOX,IAAI,CAAC,QAAQ,CAAC;MACvB;IACF;EACF;EAEAA,IAAI,CAAC,CAAC,EAAC;AACT,CAAC,CAAC;AAEF,eAAeR,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}