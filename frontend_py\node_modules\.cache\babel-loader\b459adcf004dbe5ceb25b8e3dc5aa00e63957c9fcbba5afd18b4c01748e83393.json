{"ast": null, "code": "import DOMPurify from 'dompurify';\nimport { pastActivityAPI } from '../services/api';\nexport default {\n  name: 'PastActivities',\n  data() {\n    return {\n      activities: [],\n      loading: false,\n      error: null\n    };\n  },\n  async mounted() {\n    await this.loadActivities();\n  },\n  methods: {\n    async loadActivities() {\n      this.loading = true;\n      this.error = null;\n      try {\n        const response = await pastActivityAPI.getPastActivities();\n        this.activities = response.data;\n      } catch (error) {\n        console.error('加载往期活动失败:', error);\n        this.error = '加载往期活动失败，请稍后重试';\n      } finally {\n        this.loading = false;\n      }\n    },\n    purifyContent(content) {\n      return DOMPurify.sanitize(content);\n    }\n  }\n};", "map": {"version": 3, "names": ["DOMPurify", "pastActivityAPI", "name", "data", "activities", "loading", "error", "mounted", "loadActivities", "methods", "response", "getPastActivities", "console", "purifyContent", "content", "sanitize"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\PastActivities.vue"], "sourcesContent": ["<template>\r\n  <div class=\"past-activities\">\r\n    <h1>往期活动回顾</h1>\r\n    <p>这里展示社团过往活动的图片、视频、文字记录。</p>\r\n\r\n    <div v-if=\"loading\" class=\"loading\">\r\n      <p>加载中...</p>\r\n    </div>\r\n\r\n    <div v-else-if=\"error\" class=\"error\">\r\n      <p>{{ error }}</p>\r\n      <button @click=\"loadActivities\" class=\"btn btn-primary\">重试</button>\r\n    </div>\r\n\r\n    <div v-else class=\"activities-list\">\r\n      <div v-if=\"activities.length === 0\" class=\"no-data\">\r\n        <p>暂无往期活动记录</p>\r\n      </div>\r\n      <div v-else>\r\n        <div v-for=\"activity in activities\" :key=\"activity.id\" class=\"activity-item\">\r\n          <h3>{{ activity.title }}</h3>\r\n          <div class=\"activity-date\">\r\n            <strong>活动时间:</strong> {{ formatDate(activity.activity_date) }}\r\n          </div>\r\n          <div class=\"activity-description\">\r\n            <div v-html=\"purifyContent(activity.description)\"></div>\r\n          </div>\r\n          <div class=\"activity-meta\">\r\n            <small>发布时间: {{ formatDate(activity.created_at) }}</small>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport DOMPurify from 'dompurify'\r\nimport { pastActivityAPI } from '../services/api'\r\n\r\nexport default {\r\n  name: 'PastActivities',\r\n  data() {\r\n    return {\r\n      activities: [],\r\n      loading: false,\r\n      error: null\r\n    }\r\n  },\r\n  async mounted() {\r\n    await this.loadActivities()\r\n  },\r\n  methods: {\r\n    async loadActivities() {\r\n      this.loading = true\r\n      this.error = null\r\n      try {\r\n        const response = await pastActivityAPI.getPastActivities()\r\n        this.activities = response.data\r\n      } catch (error) {\r\n        console.error('加载往期活动失败:', error)\r\n        this.error = '加载往期活动失败，请稍后重试'\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    purifyContent(content) {\r\n      return DOMPurify.sanitize(content)\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.past-activities {\r\n  padding: 20px;\r\n}\r\n</style>"], "mappings": "AAqCA,OAAOA,SAAQ,MAAO,WAAU;AAChC,SAASC,eAAc,QAAS,iBAAgB;AAEhD,eAAe;EACbC,IAAI,EAAE,gBAAgB;EACtBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,UAAU,EAAE,EAAE;MACdC,OAAO,EAAE,KAAK;MACdC,KAAK,EAAE;IACT;EACF,CAAC;EACD,MAAMC,OAAOA,CAAA,EAAG;IACd,MAAM,IAAI,CAACC,cAAc,CAAC;EAC5B,CAAC;EACDC,OAAO,EAAE;IACP,MAAMD,cAAcA,CAAA,EAAG;MACrB,IAAI,CAACH,OAAM,GAAI,IAAG;MAClB,IAAI,CAACC,KAAI,GAAI,IAAG;MAChB,IAAI;QACF,MAAMI,QAAO,GAAI,MAAMT,eAAe,CAACU,iBAAiB,CAAC;QACzD,IAAI,CAACP,UAAS,GAAIM,QAAQ,CAACP,IAAG;MAChC,EAAE,OAAOG,KAAK,EAAE;QACdM,OAAO,CAACN,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC,IAAI,CAACA,KAAI,GAAI,gBAAe;MAC9B,UAAU;QACR,IAAI,CAACD,OAAM,GAAI,KAAI;MACrB;IACF,CAAC;IAEDQ,aAAaA,CAACC,OAAO,EAAE;MACrB,OAAOd,SAAS,CAACe,QAAQ,CAACD,OAAO;IACnC;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}