{"ast": null, "code": "import { createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"playground\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[0] || (_cache[0] = _createElementVNode(\"h1\", null, \"实操靶场\", -1 /* CACHED */)), _cache[1] || (_cache[1] = _createElementVNode(\"p\", null, \"这里是实操靶场的预留区域，功能正在开发中，敬请期待！\", -1 /* CACHED */)), _createCommentVNode(\" 靶场框架内容 \")]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_createCommentVNode"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Playground.vue"], "sourcesContent": ["<template>\r\n  <div class=\"playground\">\r\n    <h1>实操靶场</h1>\r\n    <p>这里是实操靶场的预留区域，功能正在开发中，敬请期待！</p>\r\n    <!-- 靶场框架内容 -->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'Playground',\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.playground {\r\n  padding: 20px;\r\n}\r\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAY;;uBAAvBC,mBAAA,CAIM,OAJNC,UAIM,G,0BAHJC,mBAAA,CAAa,YAAT,MAAI,qB,0BACRA,mBAAA,CAAiC,WAA9B,4BAA0B,qBAC7BC,mBAAA,YAAe,C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}