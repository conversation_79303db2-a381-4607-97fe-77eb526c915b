<template>
  <div class="login">
    <h1>用户登录</h1>
    <form @submit.prevent="login">
      <div class="form-group">
        <label for="username">用户名:</label>
        <input type="text" id="username" v-model="username" required>
      </div>
      <div class="form-group">
        <label for="password">密码:</label>
        <input type="password" id="password" v-model="password" required>
      </div>
      <button type="submit">登录</button>
    </form>
    <p v-if="error" class="error-message">{{ error }}</p>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  name: 'Login',
  data() {
    return {
      username: '',
      password: '',
      error: null,
    };
  },
  methods: {
    async login() {
      this.error = null; // 清除之前的错误信息

      // 前端输入验证
      if (!this.username) {
        this.error = '用户名不能为空。';
        return;
      }
      if (!this.password) {
        this.error = '密码不能为空。';
        return;
      }

      try {
        const res = await axios.post('http://localhost:5000/api/auth/login', {
          username: this.username,
          password: this.password,
        });
        // 登录成功后，token 会通过 HttpOnly cookie 自动设置，前端无需手动处理
        this.$router.push('/'); // 示例：跳转到首页
      } catch (err) {
        this.error = err.response.data.msg || '登录失败，请检查用户名和密码。';
        console.error(err);
      }
    },
  },
};
</script>

<style scoped>
.login {
  padding: 20px;
  max-width: 400px;
  margin: 50px auto;
  border: 1px solid #ccc;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 15px;
  text-align: left;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input[type="text"],
.form-group input[type="password"] {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-sizing: border-box; /* Ensures padding doesn't increase overall width */
}

button {
  background-color: #42b983;
  color: white;
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
}

button:hover {
  background-color: #369f6e;
}

.error-message {
  color: red;
  margin-top: 10px;
}
</style>