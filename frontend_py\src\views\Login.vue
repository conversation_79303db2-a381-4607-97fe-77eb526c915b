<template>
  <div class="login">
    <h1>用户登录</h1>
    <form @submit.prevent="login">
      <div class="form-group">
        <label for="username">用户名:</label>
        <input type="text" id="username" v-model="username" required>
      </div>
      <div class="form-group">
        <label for="password">密码:</label>
        <input type="password" id="password" v-model="password" required>
      </div>
      <button type="submit" :disabled="loading">
        {{ loading ? '登录中...' : '登录' }}
      </button>
    </form>
    <p v-if="error" class="error-message">{{ error }}</p>

    <div class="register-link">
      <p>还没有账户？<router-link to="/register">立即注册</router-link></p>
      <p class="admin-hint">管理员账户：admin / Admin123!</p>
    </div>
  </div>
</template>

<script>
import { authAPI } from '../services/api'

export default {
  name: 'Login',
  data() {
    return {
      username: '',
      password: '',
      error: null,
      loading: false
    }
  },
  methods: {
    async login() {
      this.error = null
      this.loading = true

      // 前端输入验证
      if (!this.username) {
        this.error = '用户名不能为空。'
        this.loading = false
        return
      }
      if (!this.password) {
        this.error = '密码不能为空。'
        this.loading = false
        return
      }

      try {
        const response = await authAPI.login({
          username: this.username,
          password: this.password
        })

        // 登录成功，检查用户角色并跳转到相应页面
        const authResponse = await authAPI.checkAuth()
        const userRole = authResponse.data.user.role

        if (userRole === 'admin') {
          this.$router.push('/admin')
        } else {
          this.$router.push('/dashboard')
        }
      } catch (err) {
        this.error = err.response?.data?.msg || '登录失败，请检查用户名和密码。'
        console.error(err)
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.login {
  padding: 20px;
  max-width: 400px;
  margin: 50px auto;
  border: 1px solid #ccc;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 15px;
  text-align: left;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input[type="text"],
.form-group input[type="password"] {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-sizing: border-box; /* Ensures padding doesn't increase overall width */
}

button {
  background-color: #42b983;
  color: white;
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
}

button:hover:not(:disabled) {
  background-color: #369f6e;
}

button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.error-message {
  color: red;
  margin-top: 10px;
}

.register-link {
  margin-top: 20px;
  text-align: center;
}

.register-link a {
  color: #42b983;
  text-decoration: none;
}

.register-link a:hover {
  text-decoration: underline;
}

.admin-hint {
  font-size: 12px;
  color: #666;
  margin-top: 10px;
}
</style>