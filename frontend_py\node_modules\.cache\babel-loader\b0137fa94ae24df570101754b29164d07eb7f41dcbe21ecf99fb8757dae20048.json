{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createTextVNode as _createTextVNode, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createElementBlock as _createElementBlock, normalizeClass as _normalizeClass } from \"vue\";\nimport _imports_0 from '@/assets/logo.png';\nconst _hoisted_1 = {\n  class: \"navigation\"\n};\nconst _hoisted_2 = {\n  class: \"nav-container\"\n};\nconst _hoisted_3 = {\n  class: \"nav-brand\"\n};\nconst _hoisted_4 = {\n  key: 0,\n  class: \"nav-user\"\n};\nconst _hoisted_5 = {\n  key: 1,\n  class: \"nav-auth\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_router_link = _resolveComponent(\"router-link\");\n  return _openBlock(), _createElementBlock(\"nav\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_router_link, {\n    to: \"/\",\n    class: \"brand-link\"\n  }, {\n    default: _withCtx(() => _cache[2] || (_cache[2] = [_createElementVNode(\"img\", {\n      src: _imports_0,\n      alt: \"NIS\",\n      class: \"brand-logo\"\n    }, null, -1 /* CACHED */), _createElementVNode(\"span\", {\n      class: \"brand-text\"\n    }, \"NIS 社团\", -1 /* CACHED */)])),\n    _: 1 /* STABLE */,\n    __: [2]\n  })]), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"nav-menu\", {\n      active: $data.menuOpen\n    }])\n  }, [_createVNode(_component_router_link, {\n    to: \"/\",\n    class: \"nav-link\"\n  }, {\n    default: _withCtx(() => _cache[3] || (_cache[3] = [_createTextVNode(\"首页\")])),\n    _: 1 /* STABLE */,\n    __: [3]\n  }), _createVNode(_component_router_link, {\n    to: \"/club-culture\",\n    class: \"nav-link\"\n  }, {\n    default: _withCtx(() => _cache[4] || (_cache[4] = [_createTextVNode(\"社团文化\")])),\n    _: 1 /* STABLE */,\n    __: [4]\n  }), _createVNode(_component_router_link, {\n    to: \"/learning-resources\",\n    class: \"nav-link\"\n  }, {\n    default: _withCtx(() => _cache[5] || (_cache[5] = [_createTextVNode(\"学习资源\")])),\n    _: 1 /* STABLE */,\n    __: [5]\n  }), _createVNode(_component_router_link, {\n    to: \"/past-activities\",\n    class: \"nav-link\"\n  }, {\n    default: _withCtx(() => _cache[6] || (_cache[6] = [_createTextVNode(\"过往活动\")])),\n    _: 1 /* STABLE */,\n    __: [6]\n  }), _createVNode(_component_router_link, {\n    to: \"/cyber-security-news\",\n    class: \"nav-link\"\n  }, {\n    default: _withCtx(() => _cache[7] || (_cache[7] = [_createTextVNode(\"安全资讯\")])),\n    _: 1 /* STABLE */,\n    __: [7]\n  }), $data.currentUser ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [_createVNode(_component_router_link, {\n    to: \"/dashboard\",\n    class: \"nav-link\"\n  }, {\n    default: _withCtx(() => _cache[8] || (_cache[8] = [_createTextVNode(\"仪表板\")])),\n    _: 1 /* STABLE */,\n    __: [8]\n  }), _createVNode(_component_router_link, {\n    to: \"/playground\",\n    class: \"nav-link\"\n  }, {\n    default: _withCtx(() => _cache[9] || (_cache[9] = [_createTextVNode(\"练习场\")])),\n    _: 1 /* STABLE */,\n    __: [9]\n  }), $data.currentUser.role === 'admin' ? (_openBlock(), _createBlock(_component_router_link, {\n    key: 0,\n    to: \"/admin\",\n    class: \"nav-link admin-link\"\n  }, {\n    default: _withCtx(() => _cache[10] || (_cache[10] = [_createTextVNode(\"管理后台\")])),\n    _: 1 /* STABLE */,\n    __: [10]\n  })) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"button\", {\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.logout && $options.logout(...args)),\n    class: \"nav-link logout-btn\"\n  }, \"退出\")])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [_createVNode(_component_router_link, {\n    to: \"/login\",\n    class: \"nav-link\"\n  }, {\n    default: _withCtx(() => _cache[11] || (_cache[11] = [_createTextVNode(\"登录\")])),\n    _: 1 /* STABLE */,\n    __: [11]\n  }), _createVNode(_component_router_link, {\n    to: \"/register\",\n    class: \"nav-link register-btn\"\n  }, {\n    default: _withCtx(() => _cache[12] || (_cache[12] = [_createTextVNode(\"注册\")])),\n    _: 1 /* STABLE */,\n    __: [12]\n  })]))], 2 /* CLASS */), _createElementVNode(\"button\", {\n    class: \"nav-toggle\",\n    onClick: _cache[1] || (_cache[1] = $event => $data.menuOpen = !$data.menuOpen)\n  }, _cache[13] || (_cache[13] = [_createElementVNode(\"span\", null, null, -1 /* CACHED */), _createElementVNode(\"span\", null, null, -1 /* CACHED */), _createElementVNode(\"span\", null, null, -1 /* CACHED */)]))])]);\n}", "map": {"version": 3, "names": ["_imports_0", "class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_createVNode", "_component_router_link", "to", "_cache", "src", "alt", "_normalizeClass", "active", "$data", "menuOpen", "currentUser", "_hoisted_4", "role", "_createBlock", "onClick", "args", "$options", "logout", "_hoisted_5", "$event"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\components\\Navigation.vue"], "sourcesContent": ["<template>\n  <nav class=\"navigation\">\n    <div class=\"nav-container\">\n      <div class=\"nav-brand\">\n        <router-link to=\"/\" class=\"brand-link\">\n          <img src=\"@/assets/logo.png\" alt=\"NIS\" class=\"brand-logo\">\n          <span class=\"brand-text\">NIS 社团</span>\n        </router-link>\n      </div>\n      \n      <div class=\"nav-menu\" :class=\"{ active: menuOpen }\">\n        <router-link to=\"/\" class=\"nav-link\">首页</router-link>\n        <router-link to=\"/club-culture\" class=\"nav-link\">社团文化</router-link>\n        <router-link to=\"/learning-resources\" class=\"nav-link\">学习资源</router-link>\n        <router-link to=\"/past-activities\" class=\"nav-link\">过往活动</router-link>\n        <router-link to=\"/cyber-security-news\" class=\"nav-link\">安全资讯</router-link>\n        \n        <div v-if=\"currentUser\" class=\"nav-user\">\n          <router-link to=\"/dashboard\" class=\"nav-link\">仪表板</router-link>\n          <router-link to=\"/playground\" class=\"nav-link\">练习场</router-link>\n          <router-link v-if=\"currentUser.role === 'admin'\" to=\"/admin\" class=\"nav-link admin-link\">管理后台</router-link>\n          <button @click=\"logout\" class=\"nav-link logout-btn\">退出</button>\n        </div>\n        <div v-else class=\"nav-auth\">\n          <router-link to=\"/login\" class=\"nav-link\">登录</router-link>\n          <router-link to=\"/register\" class=\"nav-link register-btn\">注册</router-link>\n        </div>\n      </div>\n      \n      <button class=\"nav-toggle\" @click=\"menuOpen = !menuOpen\">\n        <span></span>\n        <span></span>\n        <span></span>\n      </button>\n    </div>\n  </nav>\n</template>\n\n<script>\nimport { authAPI } from '../services/api'\n\nexport default {\n  name: 'Navigation',\n  data() {\n    return {\n      currentUser: null,\n      menuOpen: false\n    }\n  },\n  async mounted() {\n    await this.checkAuth()\n  },\n  methods: {\n    async checkAuth() {\n      try {\n        const response = await authAPI.checkAuth()\n        this.currentUser = response.data.user\n      } catch (error) {\n        this.currentUser = null\n      }\n    },\n    \n    async logout() {\n      try {\n        await authAPI.logout()\n        this.currentUser = null\n        this.$router.push('/')\n      } catch (error) {\n        console.error('登出失败:', error)\n      }\n    }\n  },\n  watch: {\n    $route() {\n      this.menuOpen = false\n      this.checkAuth()\n    }\n  }\n}\n</script>\n\n<style scoped>\n.navigation {\n  background: white;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  position: sticky;\n  top: 0;\n  z-index: 1000;\n}\n\n.nav-container {\n  max-width: 1200px;\n  margin: 0 auto;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0 20px;\n  height: 70px;\n}\n\n.nav-brand {\n  display: flex;\n  align-items: center;\n}\n\n.brand-link {\n  display: flex;\n  align-items: center;\n  text-decoration: none;\n  color: #333;\n}\n\n.brand-logo {\n  width: 40px;\n  height: 40px;\n  margin-right: 10px;\n}\n\n.brand-text {\n  font-size: 1.5rem;\n  font-weight: bold;\n  color: #42b983;\n}\n\n.nav-menu {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n}\n\n.nav-link {\n  text-decoration: none;\n  color: #333;\n  font-weight: 500;\n  padding: 8px 16px;\n  border-radius: 4px;\n  transition: all 0.3s ease;\n  border: none;\n  background: none;\n  cursor: pointer;\n  font-size: 14px;\n}\n\n.nav-link:hover {\n  color: #42b983;\n  background-color: #f8f9fa;\n}\n\n.nav-link.router-link-active {\n  color: #42b983;\n  background-color: #e8f5e8;\n}\n\n.admin-link {\n  background-color: #ff6b6b !important;\n  color: white !important;\n}\n\n.admin-link:hover {\n  background-color: #ee5a24 !important;\n}\n\n.register-btn {\n  background-color: #42b983;\n  color: white;\n}\n\n.register-btn:hover {\n  background-color: #369f6e;\n}\n\n.logout-btn {\n  background-color: #6c757d;\n  color: white;\n}\n\n.logout-btn:hover {\n  background-color: #545b62;\n}\n\n.nav-user, .nav-auth {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.nav-toggle {\n  display: none;\n  flex-direction: column;\n  background: none;\n  border: none;\n  cursor: pointer;\n  padding: 5px;\n}\n\n.nav-toggle span {\n  width: 25px;\n  height: 3px;\n  background-color: #333;\n  margin: 3px 0;\n  transition: 0.3s;\n}\n\n@media (max-width: 768px) {\n  .nav-menu {\n    position: fixed;\n    top: 70px;\n    left: -100%;\n    width: 100%;\n    height: calc(100vh - 70px);\n    background: white;\n    flex-direction: column;\n    justify-content: flex-start;\n    align-items: center;\n    padding-top: 50px;\n    transition: left 0.3s ease;\n    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  }\n  \n  .nav-menu.active {\n    left: 0;\n  }\n  \n  .nav-link {\n    padding: 15px 30px;\n    width: 200px;\n    text-align: center;\n    margin-bottom: 10px;\n  }\n  \n  .nav-toggle {\n    display: flex;\n  }\n  \n  .nav-user, .nav-auth {\n    flex-direction: column;\n    gap: 10px;\n    margin-top: 20px;\n  }\n}\n</style>\n"], "mappings": ";OAKeA,UAAuB;;EAJ/BC,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAW;;;EAcIA,KAAK,EAAC;;;;EAMlBA,KAAK,EAAC;;;;uBAtBxBC,mBAAA,CAkCM,OAlCNC,UAkCM,GAjCJC,mBAAA,CAgCM,OAhCNC,UAgCM,GA/BJD,mBAAA,CAKM,OALNE,UAKM,GAJJC,YAAA,CAGcC,sBAAA;IAHDC,EAAE,EAAC,GAAG;IAACR,KAAK,EAAC;;sBACxB,MAA0DS,MAAA,QAAAA,MAAA,OAA1DN,mBAAA,CAA0D;MAArDO,GAAuB,EAAvBX,UAAuB;MAACY,GAAG,EAAC,KAAK;MAACX,KAAK,EAAC;+BAC7CG,mBAAA,CAAsC;MAAhCH,KAAK,EAAC;IAAY,GAAC,QAAM,mB;;;QAInCG,mBAAA,CAiBM;IAjBDH,KAAK,EAAAY,eAAA,EAAC,UAAU;MAAAC,MAAA,EAAmBC,KAAA,CAAAC;IAAQ;MAC9CT,YAAA,CAAqDC,sBAAA;IAAxCC,EAAE,EAAC,GAAG;IAACR,KAAK,EAAC;;sBAAW,MAAES,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;MACvCH,YAAA,CAAmEC,sBAAA;IAAtDC,EAAE,EAAC,eAAe;IAACR,KAAK,EAAC;;sBAAW,MAAIS,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;MACrDH,YAAA,CAAyEC,sBAAA;IAA5DC,EAAE,EAAC,qBAAqB;IAACR,KAAK,EAAC;;sBAAW,MAAIS,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;MAC3DH,YAAA,CAAsEC,sBAAA;IAAzDC,EAAE,EAAC,kBAAkB;IAACR,KAAK,EAAC;;sBAAW,MAAIS,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;MACxDH,YAAA,CAA0EC,sBAAA;IAA7DC,EAAE,EAAC,sBAAsB;IAACR,KAAK,EAAC;;sBAAW,MAAIS,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;MAEjDK,KAAA,CAAAE,WAAW,I,cAAtBf,mBAAA,CAKM,OALNgB,UAKM,GAJJX,YAAA,CAA+DC,sBAAA;IAAlDC,EAAE,EAAC,YAAY;IAACR,KAAK,EAAC;;sBAAW,MAAGS,MAAA,QAAAA,MAAA,O,iBAAH,KAAG,E;;;MACjDH,YAAA,CAAgEC,sBAAA;IAAnDC,EAAE,EAAC,aAAa;IAACR,KAAK,EAAC;;sBAAW,MAAGS,MAAA,QAAAA,MAAA,O,iBAAH,KAAG,E;;;MAC/BK,KAAA,CAAAE,WAAW,CAACE,IAAI,gB,cAAnCC,YAAA,CAA2GZ,sBAAA;;IAA1DC,EAAE,EAAC,QAAQ;IAACR,KAAK,EAAC;;sBAAsB,MAAIS,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;2CAC7FN,mBAAA,CAA+D;IAAtDiB,OAAK,EAAAX,MAAA,QAAAA,MAAA,UAAAY,IAAA,KAAEC,QAAA,CAAAC,MAAA,IAAAD,QAAA,CAAAC,MAAA,IAAAF,IAAA,CAAM;IAAErB,KAAK,EAAC;KAAsB,IAAE,E,oBAExDC,mBAAA,CAGM,OAHNuB,UAGM,GAFJlB,YAAA,CAA0DC,sBAAA;IAA7CC,EAAE,EAAC,QAAQ;IAACR,KAAK,EAAC;;sBAAW,MAAES,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;MAC5CH,YAAA,CAA0EC,sBAAA;IAA7DC,EAAE,EAAC,WAAW;IAACR,KAAK,EAAC;;sBAAwB,MAAES,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;0BAIhEN,mBAAA,CAIS;IAJDH,KAAK,EAAC,YAAY;IAAEoB,OAAK,EAAAX,MAAA,QAAAA,MAAA,MAAAgB,MAAA,IAAEX,KAAA,CAAAC,QAAQ,IAAID,KAAA,CAAAC,QAAQ;kCACrDZ,mBAAA,CAAa,sCACbA,mBAAA,CAAa,sCACbA,mBAAA,CAAa,qC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}