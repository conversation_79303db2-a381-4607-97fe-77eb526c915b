import os
import sys

# 切换到python_backend目录
os.chdir('python_backend')

try:
    from app import app
    
    print("应用导入成功")
    
    # 测试应用上下文
    with app.app_context():
        print("应用上下文创建成功")
        
        # 测试数据库
        from models import db, User
        print("模型导入成功")
        
        # 创建表
        db.create_all()
        print("数据库表创建成功")
        
        # 检查管理员用户
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            print("创建管理员用户...")
            admin = User(
                username='admin',
                student_id='ADMIN001',
                name='系统管理员',
                role='admin'
            )
            admin.set_password('Admin123!')
            db.session.add(admin)
            db.session.commit()
            print("管理员用户创建成功")
        else:
            print("管理员用户已存在")
            
        # 测试密码验证
        is_valid = admin.check_password('Admin123!')
        print(f"密码验证: {is_valid}")
        
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()
