[{"C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\main.js": "1", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\App.vue": "2", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\router\\index.js": "3", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Home.vue": "4", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\LearningResources.vue": "5", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\CyberSecurityNews.vue": "6", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\PastActivities.vue": "7", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\ClubCulture.vue": "8", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Login.vue": "9", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Admin.vue": "10", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Dashboard.vue": "11", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Playground.vue": "12", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Test.vue": "13", "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\services\\api.js": "14"}, {"size": 153, "mtime": 1752858518037, "results": "15", "hashOfConfig": "16"}, {"size": 1059, "mtime": 1752806841845, "results": "17", "hashOfConfig": "16"}, {"size": 3040, "mtime": 1752858687967, "results": "18", "hashOfConfig": "16"}, {"size": 850, "mtime": 1752834313718, "results": "19", "hashOfConfig": "16"}, {"size": 1176, "mtime": 1752834434777, "results": "20", "hashOfConfig": "16"}, {"size": 1170, "mtime": 1752834552200, "results": "21", "hashOfConfig": "16"}, {"size": 1167, "mtime": 1752834532539, "results": "22", "hashOfConfig": "16"}, {"size": 1289, "mtime": 1752858786905, "results": "23", "hashOfConfig": "16"}, {"size": 2477, "mtime": 1752833728952, "results": "24", "hashOfConfig": "16"}, {"size": 373, "mtime": 1752809324778, "results": "25", "hashOfConfig": "16"}, {"size": 403, "mtime": 1752809387822, "results": "26", "hashOfConfig": "16"}, {"size": 343, "mtime": 1752809375981, "results": "27", "hashOfConfig": "16"}, {"size": 7889, "mtime": 1752858661970, "results": "28", "hashOfConfig": "16"}, {"size": 4150, "mtime": 1752858620916, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "16x1bl0", {"filePath": "32", "messages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "38", "messages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "50", "messages": "51", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\main.js", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\App.vue", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\router\\index.js", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Home.vue", ["58", "59"], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\LearningResources.vue", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\CyberSecurityNews.vue", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\PastActivities.vue", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\ClubCulture.vue", [], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Login.vue", ["60", "61"], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Admin.vue", ["62"], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Dashboard.vue", ["63"], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Playground.vue", ["64"], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Test.vue", ["65"], "C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\services\\api.js", [], {"ruleId": "66", "severity": 2, "message": "67", "line": 14, "column": 9, "nodeType": "68", "messageId": "69", "endLine": 14, "endColumn": 15}, {"ruleId": "70", "severity": 2, "message": "71", "line": 18, "column": 88, "nodeType": "68", "messageId": "72", "endLine": 18, "endColumn": 89, "suggestions": "73"}, {"ruleId": "66", "severity": 2, "message": "74", "line": 23, "column": 9, "nodeType": "68", "messageId": "69", "endLine": 23, "endColumn": 16}, {"ruleId": "75", "severity": 2, "message": "76", "line": 46, "column": 15, "nodeType": "77", "messageId": "78", "endLine": 46, "endColumn": 18}, {"ruleId": "66", "severity": 2, "message": "79", "line": 11, "column": 9, "nodeType": "68", "messageId": "69", "endLine": 11, "endColumn": 16}, {"ruleId": "66", "severity": 2, "message": "80", "line": 11, "column": 9, "nodeType": "68", "messageId": "69", "endLine": 11, "endColumn": 20}, {"ruleId": "66", "severity": 2, "message": "81", "line": 11, "column": 9, "nodeType": "68", "messageId": "69", "endLine": 11, "endColumn": 21}, {"ruleId": "66", "severity": 2, "message": "82", "line": 111, "column": 9, "nodeType": "68", "messageId": "69", "endLine": 111, "endColumn": 15}, "vue/multi-word-component-names", "Component name \"Home\" should always be multi-word.", "Literal", "unexpected", "no-useless-escape", "Unnecessary escape character: \\/.", "unnecessaryEscape", ["83", "84"], "Component name \"Login\" should always be multi-word.", "no-unused-vars", "'res' is assigned a value but never used.", "Identifier", "unusedVar", "Component name \"<PERSON><PERSON>\" should always be multi-word.", "Component name \"Dashboard\" should always be multi-word.", "Component name \"Playground\" should always be multi-word.", "Component name \"Test\" should always be multi-word.", {"messageId": "85", "fix": "86", "desc": "87"}, {"messageId": "88", "fix": "89", "desc": "90"}, "removeEscape", {"range": "91", "text": "92"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "93", "text": "94"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", [457, 458], "", [457, 457], "\\"]