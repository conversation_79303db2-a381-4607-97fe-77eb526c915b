{"ast": null, "code": "import DOMPurify from 'dompurify'; // 引入 DOMPurify\n\nexport default {\n  name: 'Home',\n  data() {\n    return {\n      // 示例公告内容，实际应从后端获取\n      announcementContent: '<p>欢迎参加**网络信息安全社团**的招新活动！</p><script>alert(\"XSS Attack!\");<\\/script>'\n    };\n  },\n  computed: {\n    purifiedAnnouncementContent() {\n      // 使用 DOMPurify 清理公告内容\n      return DOMPurify.sanitize(this.announcementContent);\n    }\n  }\n};", "map": {"version": 3, "names": ["DOMPurify", "name", "data", "announcementContent", "computed", "purifiedAnnouncementContent", "sanitize"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Home.vue"], "sourcesContent": ["<template>\r\n  <div class=\"home\">\r\n    <!-- 头部横幅 -->\r\n    <div class=\"hero-section\">\r\n      <h1>网络信息安全社团</h1>\r\n      <p class=\"hero-subtitle\">成都工业职业技术学院（金堂校区）</p>\r\n      <div class=\"hero-buttons\">\r\n        <router-link to=\"/login\" class=\"btn btn-primary\">立即登录</router-link>\r\n        <router-link to=\"/register\" class=\"btn btn-secondary\">注册账户</router-link>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 社团介绍 -->\r\n    <div class=\"section\">\r\n      <h2>社团介绍</h2>\r\n      <div class=\"intro-grid\">\r\n        <div class=\"intro-card\">\r\n          <h3>🛡️ 网络安全</h3>\r\n          <p>学习网络安全知识，掌握防护技能</p>\r\n        </div>\r\n        <div class=\"intro-card\">\r\n          <h3>🔧 技术实践</h3>\r\n          <p>动手实践，提升技术能力</p>\r\n        </div>\r\n        <div class=\"intro-card\">\r\n          <h3>👥 团队协作</h3>\r\n          <p>与志同道合的伙伴一起成长</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 最新公告 -->\r\n    <div class=\"section\">\r\n      <h2>最新公告</h2>\r\n      <div class=\"announcements\">\r\n        <div v-if=\"loading\" class=\"loading\">加载中...</div>\r\n        <div v-else-if=\"announcements.length === 0\" class=\"no-data\">暂无公告</div>\r\n        <div v-else>\r\n          <div v-for=\"announcement in announcements.slice(0, 3)\" :key=\"announcement.id\" class=\"announcement-card\">\r\n            <h3>{{ announcement.title }}</h3>\r\n            <p class=\"announcement-content\" v-html=\"purifyContent(announcement.content)\"></p>\r\n            <p class=\"announcement-date\">{{ formatDate(announcement.created_at) }}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 联系信息 -->\r\n    <div class=\"section contact-section\">\r\n      <h2>联系我们</h2>\r\n      <div class=\"contact-info\">\r\n        <p><strong>学校：</strong>成都工业职业技术学院（金堂校区）</p>\r\n        <p><strong>社团群号：</strong>242050951</p>\r\n        <p><strong>抖音：</strong>21647629167</p>\r\n        <p><strong>主要活动：</strong>组网技术，网络攻防，安全科普</p>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport DOMPurify from 'dompurify'; // 引入 DOMPurify\r\n\r\nexport default {\r\n  name: 'Home',\r\n  data() {\r\n    return {\r\n      // 示例公告内容，实际应从后端获取\r\n      announcementContent: '<p>欢迎参加**网络信息安全社团**的招新活动！</p><script>alert(\"XSS Attack!\");<\\/script>',\r\n    };\r\n  },\r\n  computed: {\r\n    purifiedAnnouncementContent() {\r\n      // 使用 DOMPurify 清理公告内容\r\n      return DOMPurify.sanitize(this.announcementContent);\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.home {\r\n  padding: 20px;\r\n}\r\n</style>"], "mappings": "AA6DA,OAAOA,SAAQ,MAAO,WAAW,EAAE;;AAEnC,eAAe;EACbC,IAAI,EAAE,MAAM;EACZC,IAAIA,CAAA,EAAG;IACL,OAAO;MACL;MACAC,mBAAmB,EAAE;IACvB,CAAC;EACH,CAAC;EACDC,QAAQ,EAAE;IACRC,2BAA2BA,CAAA,EAAG;MAC5B;MACA,OAAOL,SAAS,CAACM,QAAQ,CAAC,IAAI,CAACH,mBAAmB,CAAC;IACrD;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}