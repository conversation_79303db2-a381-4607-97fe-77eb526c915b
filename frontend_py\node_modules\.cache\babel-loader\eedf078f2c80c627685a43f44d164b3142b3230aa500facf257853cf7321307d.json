{"ast": null, "code": "import DOMPurify from 'dompurify';\nimport { announcementAPI } from '../services/api';\nexport default {\n  name: 'Home',\n  data() {\n    return {\n      announcements: [],\n      loading: false\n    };\n  },\n  async mounted() {\n    await this.loadAnnouncements();\n  },\n  methods: {\n    async loadAnnouncements() {\n      this.loading = true;\n      try {\n        const response = await announcementAPI.getAnnouncements();\n        this.announcements = response.data;\n      } catch (error) {\n        console.error('加载公告失败:', error);\n        // 如果API调用失败，显示示例数据\n        this.announcements = [{\n          id: 1,\n          title: '社团招新公告',\n          content: '<p>欢迎参加网络信息安全社团的招新活动！我们致力于网络安全技术的学习和实践。</p>',\n          created_at: new Date().toISOString()\n        }];\n      } finally {\n        this.loading = false;\n      }\n    },\n    purifyContent(content) {\n      return DOMPurify.sanitize(content);\n    },\n    formatDate(dateString) {\n      const date = new Date(dateString);\n      return date.toLocaleDateString('zh-CN');\n    }\n  }\n};", "map": {"version": 3, "names": ["DOMPurify", "announcementAPI", "name", "data", "announcements", "loading", "mounted", "loadAnnouncements", "methods", "response", "getAnnouncements", "error", "console", "id", "title", "content", "created_at", "Date", "toISOString", "purifyContent", "sanitize", "formatDate", "dateString", "date", "toLocaleDateString"], "sources": ["C:\\Users\\<USER>\\Desktop\\cs\\pacong_py\\frontend_py\\src\\views\\Home.vue"], "sourcesContent": ["<template>\r\n  <div class=\"home\">\r\n    <!-- 头部横幅 -->\r\n    <div class=\"hero-section\">\r\n      <h1>网络信息安全社团</h1>\r\n      <p class=\"hero-subtitle\">成都工业职业技术学院（金堂校区）</p>\r\n      <div class=\"hero-buttons\">\r\n        <router-link to=\"/login\" class=\"btn btn-primary\">立即登录</router-link>\r\n        <router-link to=\"/register\" class=\"btn btn-secondary\">注册账户</router-link>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 社团介绍 -->\r\n    <div class=\"section\">\r\n      <h2>社团介绍</h2>\r\n      <div class=\"intro-grid\">\r\n        <div class=\"intro-card\">\r\n          <h3>🛡️ 网络安全</h3>\r\n          <p>学习网络安全知识，掌握防护技能</p>\r\n        </div>\r\n        <div class=\"intro-card\">\r\n          <h3>🔧 技术实践</h3>\r\n          <p>动手实践，提升技术能力</p>\r\n        </div>\r\n        <div class=\"intro-card\">\r\n          <h3>👥 团队协作</h3>\r\n          <p>与志同道合的伙伴一起成长</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 最新公告 -->\r\n    <div class=\"section\">\r\n      <h2>最新公告</h2>\r\n      <div class=\"announcements\">\r\n        <div v-if=\"loading\" class=\"loading\">加载中...</div>\r\n        <div v-else-if=\"announcements.length === 0\" class=\"no-data\">暂无公告</div>\r\n        <div v-else>\r\n          <div v-for=\"announcement in announcements.slice(0, 3)\" :key=\"announcement.id\" class=\"announcement-card\">\r\n            <h3>{{ announcement.title }}</h3>\r\n            <p class=\"announcement-content\" v-html=\"purifyContent(announcement.content)\"></p>\r\n            <p class=\"announcement-date\">{{ formatDate(announcement.created_at) }}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 联系信息 -->\r\n    <div class=\"section contact-section\">\r\n      <h2>联系我们</h2>\r\n      <div class=\"contact-info\">\r\n        <p><strong>学校：</strong>成都工业职业技术学院（金堂校区）</p>\r\n        <p><strong>社团群号：</strong>242050951</p>\r\n        <p><strong>抖音：</strong>21647629167</p>\r\n        <p><strong>主要活动：</strong>组网技术，网络攻防，安全科普</p>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport DOMPurify from 'dompurify'\r\nimport { announcementAPI } from '../services/api'\r\n\r\nexport default {\r\n  name: 'Home',\r\n  data() {\r\n    return {\r\n      announcements: [],\r\n      loading: false\r\n    }\r\n  },\r\n  async mounted() {\r\n    await this.loadAnnouncements()\r\n  },\r\n  methods: {\r\n    async loadAnnouncements() {\r\n      this.loading = true\r\n      try {\r\n        const response = await announcementAPI.getAnnouncements()\r\n        this.announcements = response.data\r\n      } catch (error) {\r\n        console.error('加载公告失败:', error)\r\n        // 如果API调用失败，显示示例数据\r\n        this.announcements = [\r\n          {\r\n            id: 1,\r\n            title: '社团招新公告',\r\n            content: '<p>欢迎参加网络信息安全社团的招新活动！我们致力于网络安全技术的学习和实践。</p>',\r\n            created_at: new Date().toISOString()\r\n          }\r\n        ]\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    purifyContent(content) {\r\n      return DOMPurify.sanitize(content)\r\n    },\r\n    formatDate(dateString) {\r\n      const date = new Date(dateString)\r\n      return date.toLocaleDateString('zh-CN')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.home {\r\n  padding: 20px;\r\n}\r\n</style>"], "mappings": "AA6DA,OAAOA,SAAQ,MAAO,WAAU;AAChC,SAASC,eAAc,QAAS,iBAAgB;AAEhD,eAAe;EACbC,IAAI,EAAE,MAAM;EACZC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,aAAa,EAAE,EAAE;MACjBC,OAAO,EAAE;IACX;EACF,CAAC;EACD,MAAMC,OAAOA,CAAA,EAAG;IACd,MAAM,IAAI,CAACC,iBAAiB,CAAC;EAC/B,CAAC;EACDC,OAAO,EAAE;IACP,MAAMD,iBAAiBA,CAAA,EAAG;MACxB,IAAI,CAACF,OAAM,GAAI,IAAG;MAClB,IAAI;QACF,MAAMI,QAAO,GAAI,MAAMR,eAAe,CAACS,gBAAgB,CAAC;QACxD,IAAI,CAACN,aAAY,GAAIK,QAAQ,CAACN,IAAG;MACnC,EAAE,OAAOQ,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;QAC9B;QACA,IAAI,CAACP,aAAY,GAAI,CACnB;UACES,EAAE,EAAE,CAAC;UACLC,KAAK,EAAE,QAAQ;UACfC,OAAO,EAAE,6CAA6C;UACtDC,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QACrC,EACF;MACF,UAAU;QACR,IAAI,CAACb,OAAM,GAAI,KAAI;MACrB;IACF,CAAC;IACDc,aAAaA,CAACJ,OAAO,EAAE;MACrB,OAAOf,SAAS,CAACoB,QAAQ,CAACL,OAAO;IACnC,CAAC;IACDM,UAAUA,CAACC,UAAU,EAAE;MACrB,MAAMC,IAAG,GAAI,IAAIN,IAAI,CAACK,UAAU;MAChC,OAAOC,IAAI,CAACC,kBAAkB,CAAC,OAAO;IACxC;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}